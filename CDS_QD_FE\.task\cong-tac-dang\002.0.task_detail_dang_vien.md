# Quy trình xây dựng giao diện trang "Xem chi tiết Hồ sơ Đảng viên"

---

**Trang: Xem chi tiết Hồ sơ Đảng viên (`/${locale}/cong-tac-dang/chi-tiet-dang-vien/{idDangVien}`)**

**I. <PERSON><PERSON> liệu và Trường Dữ liệu Liên quan (Rà soát chi tiết theo tài liệu Công tác Đ<PERSON>):**

1.  **Bảng `DangVien` (PartyMembers)**

    - `MaDang<PERSON>ien` (VARCHAR(20), PK)
    - `SoHieuQuanNhan` (VARCHAR(20), FK NULL, UNIQUE REFERENCES QuanNhan(SoHieuQuanNhan))
    - `HoVaTen` (NVARCHAR(100), NOT NULL) - _Lưu ý: <PERSON> tà<PERSON>, trường này lấy từ <PERSON><PERSON> sơ QN nếu có. Cần làm rõ nếu đảng viên không phải QN thì trường này có tồn tại ở bảng DangVien không, hay luôn phải có hồ sơ QN liên kết. Giả định hiện tại là có thể có cho ĐV ngoài QĐ._
    - `TenThuongDung` (NVARCHAR(100)) - _Tương tự HoVaTen_
    - `NgaySinh` (DATE) - _Tương tự HoVaTen_
    - `GioiTinh` (INT) - _Tương tự HoVaTen_
    - `NgayVaoDangChinhThuc` (DATE)
    - `NgayVaoDangDuBi` (DATE, NOT NULL)
    - `SoTheDangVien` (VARCHAR(20), UNIQUE)
    - `ToChucDangSinhHoatID` (BIGINT, FK REFERENCES ToChucDang(ID), NOT NULL)
    - `TrangThaiDangTichID` (INT, FK REFERENCES DmTrangThaiDangTich(ID), NOT NULL)
    - `ChucVuDangHienTaiID` (INT, FK REFERENCES DmChucVuDang(ID) NULL)
    - `NgayTao` (TIMESTAMP, DEFAULT CURRENT_TIMESTAMP)
    - `NguoiTaoID` (VARCHAR(50))
    - `NgayCapNhat` (TIMESTAMP, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)
    - `NguoiCapNhatID` (VARCHAR(50))
    - `IsEncrypted` (BOOLEAN, DEFAULT FALSE)

2.  **Bảng `HoSoDangVien` (PartyMemberProfiles)** (Liên kết 1-1 với `DangVien` qua `DangVienID`)

    - `DangVienID` (VARCHAR(20), PK, FK REFERENCES DangVien(MaDangVien))
    - `LyLichNguoiXinVaoDang_URL` (VARCHAR(255))
    - `DonXinVaoDang_URL` (VARCHAR(255))
    - `GiayGioiThieu1_URL` (VARCHAR(255))
    - `GiayGioiThieu2_URL` (VARCHAR(255))
    - `NghiQuyetGioiThieu_URL` (VARCHAR(255))
    - `LyLichDangVien_M1HSĐV_URL` (VARCHAR(255))
    - `LyLichDangVien_NoiDung` (NTEXT)
    - `PhieuDangVien_M2HSĐV_URL` (VARCHAR(255))
    - `VanBangLyLuanChinhTri_CaoCap_URL` (VARCHAR(255))
    - `VanBangLyLuanChinhTri_TrungCap_URL` (VARCHAR(255))
    - `VanBangLyLuanChinhTri_SoCap_URL` (VARCHAR(255))
    - `ChungNhanBoiDuongCamTinhDang_URL` (VARCHAR(255))
    - `ChungNhanBoiDuongDangVienMoi_URL` (VARCHAR(255))
    - `ThongTinNopDangPhi_Raw` (NTEXT)
    - `GhiChuHoSo` (NTEXT)

3.  **Bảng `BanKiemDiemDangVien` (PartyMemberSelfCriticisms)** (Liên kết với `DangVien` qua `DangVienID`)

    - `ID` (BIGINT, PK, AUTO_INCREMENT)
    - `DangVienID` (VARCHAR(20), NOT NULL, FK REFERENCES DangVien(MaDangVien))
    - `NamDanhGia` (INT, NOT NULL)
    - `NoiDungTuKiemDiem_URL` (VARCHAR(255))
    - `NoiDungTuKiemDiem_Text` (NTEXT)
    - `TuNhanXepLoaiID` (INT, FK REFERENCES DmXepLoaiChatLuong(ID))
    - `NgayNop` (DATE)

4.  **Bảng `DanhGiaXepLoaiDangVien` (PartyMemberRatings)** (Liên kết với `DangVien` qua `DangVienID` và `BanKiemDiemDangVien` qua `BanKiemDiemID` (nếu có), hoặc trực tiếp với `DangVienID` và `NamDanhGia`)

    - `ID` (BIGINT, PK, AUTO_INCREMENT)
    - `DangVienID` (VARCHAR(20), NOT NULL, FK REFERENCES DangVien(MaDangVien))
    - `NamDanhGia` (INT, NOT NULL)
    - `DotDanhGiaID` (BIGINT, FK NULL REFERENCES DotDanhGiaXepLoai(ID))
    - `BanKiemDiemID` (BIGINT, FK NULL REFERENCES BanKiemDiemDangVien(ID))
    - `XepLoaiChiBoDeNghiID` (INT, FK REFERENCES DmXepLoaiChatLuong(ID))
    - `YKienDongGopCuaChiBo` (NTEXT)
    - `FileBienBanHopChiBoXetURL` (VARCHAR(255) NULL)
    - `XepLoaiCapUyCongNhanID` (INT, FK REFERENCES DmXepLoaiChatLuong(ID))
    - `SoQuyetDinhCongNhan` (VARCHAR(50) NULL)
    - `NgayQuyetDinhCongNhan` (DATE NULL)
    - `FileQuyetDinhCongNhanURL` (VARCHAR(255) NULL)
    - `GhiChu` (NTEXT)

5.  **Bảng `TheoDoiSinhHoatCaNhan` (IndividualPartyActivityTracking)** (Liên kết với `DangVien` qua `DangVienID` và `BuoiSinhHoatDang` qua `BuoiSinhHoatID`)

    - `ID` (BIGINT, PK, AUTO_INCREMENT)
    - `BuoiSinhHoatID` (BIGINT, NOT NULL, FK REFERENCES BuoiSinhHoatDang(ID))
    - `DangVienID` (VARCHAR(20), NOT NULL, FK REFERENCES DangVien(MaDangVien))
    - `CoMat` (BOOLEAN, DEFAULT TRUE)
    - `LyDoVangMatID` (INT, FK NULL REFERENCES DmLyDoVangMat(ID))
    - `YKienPhatBieu` (NTEXT)
    - `ThucHienNhiemVuGiao` (NTEXT)
    - `TuDanhGiaChiBoNhanXet` (NTEXT)
    - `GhiChu` (NTEXT)

6.  **Bảng `ThuChiDangPhi` (PartyFeeTransactions)** (Liên kết với `DangVien` qua `DangVienNopID` cho giao dịch thu)

    - `ID` (BIGINT, PK, AUTO_INCREMENT)
    - `DangVienNopID` (VARCHAR(20), FK NULL REFERENCES DangVien(MaDangVien))
    - `ToChucDangQuanLyID` (BIGINT, NOT NULL, FK REFERENCES ToChucDang(ID))
    - `LoaiGiaoDichID` (INT, NOT NULL, FK REFERENCES DmLoaiGiaoDichDangPhi(ID))
    - `KyThuNop` (VARCHAR(7) NOT NULL) (YYYY-MM)
    - `NgayGiaoDich` (DATE, NOT NULL)
    - `SoTien` (DECIMAL(15,2), NOT NULL)
    - `NoiDungGiaoDich` (NVARCHAR(500))
    - `NguoiThuNguoiChiNguoiNop` (NVARCHAR(100) NULL)
    - `FileChungTuKemTheoURL` (VARCHAR(255) NULL)
    - `SoDuSauGiaoDich` (DECIMAL(15,2) NULL)
    - `GhiChu` (NTEXT)

7.  **Bảng `KhenThuongDang` (PartyCommendations)** (Liên kết với `DangVien` qua `DoiTuongDangVienID`)

    - `ID` (BIGINT, PK, AUTO_INCREMENT)
    - `DoiTuongDangVienID` (VARCHAR(20), FK NULL REFERENCES DangVien(MaDangVien))
    - `DoiTuongToChucDangID` (BIGINT, FK NULL REFERENCES ToChucDang(ID))
    - `SoQuyetDinh` (VARCHAR(50), NOT NULL)
    - `NgayQuyetDinh` (DATE, NOT NULL)
    - `CapRaQuyetDinhID` (INT, NOT NULL, FK REFERENCES DmCapUyBanHanhQDDang(ID))
    - `HinhThucKhenThuongDangID` (INT, NOT NULL, FK REFERENCES DmHinhThucKhenThuongDang(ID))
    - `LyDoThanhTich` (NTEXT, NOT NULL)
    - `FileQuyetDinhURL` (VARCHAR(255) NULL)
    - `GhiChu` (NTEXT)

8.  **Bảng `KyLuatDang` (PartyDisciplines)** (Liên kết với `DangVien` qua `DoiTuongDangVienID`)

    - `ID` (BIGINT, PK, AUTO_INCREMENT)
    - `DoiTuongDangVienID` (VARCHAR(20), FK NULL REFERENCES DangVien(MaDangVien))
    - `DoiTuongToChucDangID` (BIGINT, FK NULL REFERENCES ToChucDang(ID))
    - `VuViecKiemTraID` (BIGINT, FK NULL REFERENCES CuocKiemTraGiamSat(ID))
    - `SoQuyetDinh` (VARCHAR(50), NOT NULL)
    - `NgayQuyetDinh` (DATE, NOT NULL)
    - `CapRaQuyetDinhID` (INT, NOT NULL, FK REFERENCES DmCapUyBanHanhQDDang(ID))
    - `HinhThucKyLuatDangID` (INT, NOT NULL, FK REFERENCES DmHinhThucKyLuatDang(ID))
    - `NoiDungViPham` (NTEXT, NOT NULL)
    - `ThoiHanKyLuat` (VARCHAR(100) NULL)
    - `NgayHetHieuLuc` (DATE NULL)
    - `FileQuyetDinhURL` (VARCHAR(255) NULL)
    - `TrangThaiKyLuatID` (INT, FK REFERENCES DmTrangThaiKyLuat(ID))
    - `GhiChu` (NTEXT)

9.  **Bảng `HoSoPhatTrienDangVien` (PartyAdmissionDossiers)** (Liên kết với `DangVien` (nếu đã thành ĐV) hoặc lưu thông tin đối tượng nếu chưa)

    - `ID` (BIGINT, PK, AUTO_INCREMENT)
    - `DoiTuongQuanNhanID` (VARCHAR(20), FK NULL REFERENCES QuanNhan(SoHieuQuanNhan))
    - `HoTenDoiTuongNgoai` (NVARCHAR(100) NULL)
    - `NgaySinhDoiTuongNgoai` (DATE NULL)
    - `DonViCongTacDoiTuongNgoai` (NVARCHAR(255) NULL)
    - `NgayTaoHoSo` (DATE NOT NULL)
    - `TrangThaiQuyTrinhID` (INT NOT NULL, FK REFERENCES DmTrangThaiPhatTrienDang(ID))
    - `NguoiHuongDan1ID` (VARCHAR(20), FK NULL REFERENCES DangVien(MaDangVien))
    - `NguoiHuongDan2ID` (VARCHAR(20), FK NULL REFERENCES DangVien(MaDangVien))
    - `ToChucDangDeNghiKetNapID` (BIGINT, FK NULL REFERENCES ToChucDang(ID))
    - `NgayQuyetDinhKetNap` (DATE NULL)
    - `SoQuyetDinhKetNap` (VARCHAR(50) NULL)
    - `NgayCongNhanChinhThucDuKien` (DATE NULL)
    - `NgayQuyetDinhChinhThuc` (DATE NULL)
    - `SoQuyetDinhChinhThuc` (VARCHAR(50) NULL)
    - `LyDoTamDungTraLai` (NTEXT NULL)
    - `GhiChuQuyTrinh` (NTEXT)

10. **Bảng `TaiLieuPhatTrienDangVien` (PartyAdmissionDocuments)** (Liên kết với `HoSoPhatTrienDangVien` qua `HoSoPhatTrienID`)

    - `ID` (BIGINT, PK, AUTO_INCREMENT)
    - `HoSoPhatTrienID` (BIGINT, NOT NULL, FK REFERENCES HoSoPhatTrienDangVien(ID))
    - `TenTaiLieu` (NVARCHAR(255) NOT NULL)
    - `LoaiTaiLieuPhatTrienID` (INT NOT NULL, FK REFERENCES DmLoaiTaiLieuPhatTrienDang(ID))
    - `FileURL` (VARCHAR(255) NOT NULL)
    - `NgayPhatHanhTaiLieu` (DATE NULL)
    - `NgayNopTaiLieu` (DATE NULL)
    - `GhiChu` (NTEXT)

11. **Bảng `QuanNhan` (Servicemen)** (nếu `DangVien.SoHieuQuanNhan` có giá trị)

    - `SoHieuQuanNhan` (PK)
    - `HoVaTenKhaiSinh` (NVARCHAR(100), NOT NULL)
    - `TenThuongDung` (NVARCHAR(100))
    - `NgaySinh` (DATE, NOT NULL)
    - `GioiTinh` (INT, NOT NULL)
    - (Các trường khác của QN có thể được tham chiếu nếu cần thiết, ví dụ: `DonViID`, `CapBacHienTaiID`...)

12. **Bảng `ToChucDang` (PartyOrganizations)**

    - `ID` (PK)
    - `TenToChucDang` (NVARCHAR(255), NOT NULL)

13. **Các bảng Danh mục (Dm\*) liên quan:**
    - `DmTrangThaiDangTich`: `ID`, `TenTrangThai` (hoặc tên tương ứng)
    - `DmChucVuDang`: `ID`, `TenChucVu` (hoặc tên tương ứng)
    - `DmXepLoaiChatLuong`: `ID`, `TenXepLoai` (hoặc tên tương ứng)
    - `DmLyDoVangMat`: `ID`, `TenLyDo` (hoặc tên tương ứng)
    - `DmLoaiGiaoDichDangPhi`: `ID`, `TenLoaiGiaoDich` (hoặc tên tương ứng)
    - `DmCapUyBanHanhQDDang`: `ID`, `TenCapUy` (hoặc tên tương ứng)
    - `DmHinhThucKhenThuongDang`: `ID`, `TenHinhThuc` (hoặc tên tương ứng)
    - `DmHinhThucKyLuatDang`: `ID`, `TenHinhThuc` (hoặc tên tương ứng)
    - `DmTrangThaiKyLuat`: `ID`, `TenTrangThai` (hoặc tên tương ứng)
    - `DmTrangThaiPhatTrienDang`: `ID`, `TenTrangThai` (hoặc tên tương ứng)
    - `DmLoaiTaiLieuPhatTrienDang`: `ID`, `TenLoaiTaiLieu` (hoặc tên tương ứng)

**II. Bố cục Chính của Trang:** (Như đã mô tả ở câu trả lời trước) 1. Khu vực Điều hướng Phân cấp (Breadcrumbs) 2. Khu vực Header Thông tin Tổng quan Đảng viên 3. Khu vực Nội dung Chi tiết dạng Tab

**III. Danh sách Tên các Tab (đã cập nhật):**

1.  **Tab "Thông tin Đảng viên"**
2.  **Tab "Hồ sơ Gốc"**
3.  **Tab "Theo dõi Sinh hoạt"**
4.  **Tab "Tự kiểm điểm & Đánh giá"**
5.  **Tab "Đảng phí"**
6.  **Tab "Khen thưởng Đảng"**
7.  **Tab "Kỷ luật Đảng"**
8.  **Tab "Quá trình Phát triển Đảng"**
9.  **(Tùy chọn) Tab "Lịch sử Thay đổi"**

---

**IV. Chi tiết Nội dung Tab 1: "Thông tin Đảng viên"**

- **Mục đích:** Hiển thị các thông tin cơ bản, định danh và thông tin Đảng cốt lõi của đảng viên.
- **Bố cục:** Dạng danh sách các cặp "Tên trường thông tin: Giá trị", chia thành các nhóm logic. Tất cả thông tin ở đây là dạng hiển thị (read-only).

- **Các trường dữ liệu hiển thị (sử dụng dữ liệu từ các bảng đã liệt kê ở Mục I):**

  - **Nhóm "Thông tin Cơ bản Đảng viên" (Từ bảng `DangVien` và `QuanNhan`):**

    - **Mã Đảng viên:** `DangVien.MaDangVien`
    - **Số thẻ Đảng viên:** `DangVien.SoTheDangVien`
    - **Họ và tên Khai sinh:** Lấy từ `QuanNhan.HoVaTenKhaiSinh` (nếu `DangVien.SoHieuQuanNhan` tồn tại) hoặc `DangVien.HoVaTen`.
    - **Tên thường dùng:** Lấy từ `QuanNhan.TenThuongDung` (nếu `DangVien.SoHieuQuanNhan` tồn tại) hoặc `DangVien.TenThuongDung`.
    - **Ngày sinh:** Lấy từ `QuanNhan.NgaySinh` (nếu `DangVien.SoHieuQuanNhan` tồn tại) hoặc `DangVien.NgaySinh`.
    - **Giới tính:** Lấy từ `QuanNhan.GioiTinh` (hiển thị tên, ví dụ "Nam", "Nữ") (nếu `DangVien.SoHieuQuanNhan` tồn tại) hoặc `DangVien.GioiTinh`.
    - **Số hiệu Quân nhân (nếu có):** `DangVien.SoHieuQuanNhan`.

  - **Nhóm "Thông tin Đảng tịch và Sinh hoạt" (Từ bảng `DangVien`, `ToChucDang`, `DmTrangThaiDangTich`, `DmChucVuDang`):**

    - **Ngày vào Đảng (Kết nạp/Dự bị):** `DangVien.NgayVaoDangDuBi` (định dạng dd/mm/yyyy).
    - **Ngày vào Đảng chính thức:** `DangVien.NgayVaoDangChinhThuc` (định dạng dd/mm/yyyy).
    - **Tổ chức Đảng Sinh hoạt:** `ToChucDang.TenToChucDang` (liên kết qua `DangVien.ToChucDangSinhHoatID`).
    - **Trạng thái Đảng tịch:** Tên trạng thái từ `DmTrangThaiDangTich` (liên kết qua `DangVien.TrangThaiDangTichID`).
    - **Chức vụ Đảng hiện tại (nếu có):** Tên chức vụ từ `DmChucVuDang` (liên kết qua `DangVien.ChucVuDangHienTaiID`).

  - **Nhóm "Thông tin Quản lý Hồ sơ" (Từ bảng `DangVien` - thường chỉ hiển thị cho người có quyền quản trị):**
    - **Ngày tạo Hồ sơ:** `DangVien.NgayTao`.
    - **Người tạo Hồ sơ:** `DangVien.NguoiTaoID`.
    - **Ngày cập nhật cuối:** `DangVien.NgayCapNhat`.
    - **Người cập nhật cuối:** `DangVien.NguoiCapNhatID`.
    - **Có thông tin mã hóa:** "Có" nếu `DangVien.IsEncrypted` là true, "Không" nếu false.

- **Chức năng trong Tab này:**
  - Chỉ hiển thị thông tin. Không có chức năng sửa đổi trực tiếp tại đây.

---
