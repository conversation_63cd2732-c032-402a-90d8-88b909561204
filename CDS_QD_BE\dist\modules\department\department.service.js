"use strict";Object.defineProperty(exports,"__esModule",{value:true});Object.defineProperty(exports,"DepartmentService",{enumerable:true,get:function(){return DepartmentService}});const _common=require("@nestjs/common");const _departmentrepository=require("../../database/typeorm/repositories/department.repository");const _userrepository=require("../../database/typeorm/repositories/user.repository");const _services=require("../../shared/services");function _ts_decorate(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect==="object"&&typeof Reflect.decorate==="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)if(d=decorators[i])r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r;return c>3&&r&&Object.defineProperty(target,key,r),r}function _ts_metadata(k,v){if(typeof Reflect==="object"&&typeof Reflect.metadata==="function")return Reflect.metadata(k,v)}let DepartmentService=class DepartmentService{create(createDepartmentDto){return this.departmentRepository.save(this.departmentRepository.create(createDepartmentDto))}async findAll(queries){const{builder,take,pagination}=this.utilService.getQueryBuilderAndPagination(this.departmentRepository,queries);if(!this.utilService.isEmpty(queries.search)){builder.andWhere("entity.name ILIKE :search",{search:`%${queries.search}%`})}builder.select(["entity"]);const[result,total]=await builder.getManyAndCount();const totalPages=Math.ceil(total/take);return{data:result,pagination:{...pagination,totalRecords:total,totalPages:totalPages}}}findOne(id){return this.departmentRepository.findOneBy({id})}update(id,updateDepartmentDto){return this.departmentRepository.update(id,updateDepartmentDto)}remove(id){return this.departmentRepository.delete(id)}constructor(departmentRepository,userRepository,utilService){this.departmentRepository=departmentRepository;this.userRepository=userRepository;this.utilService=utilService}};DepartmentService=_ts_decorate([(0,_common.Injectable)(),_ts_metadata("design:type",Function),_ts_metadata("design:paramtypes",[typeof _departmentrepository.DepartmentRepository==="undefined"?Object:_departmentrepository.DepartmentRepository,typeof _userrepository.UserRepository==="undefined"?Object:_userrepository.UserRepository,typeof _services.UtilService==="undefined"?Object:_services.UtilService])],DepartmentService);
//# sourceMappingURL=department.service.js.map