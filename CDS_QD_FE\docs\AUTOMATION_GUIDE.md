# Hướng dẫn tự động hóa đồng bộ API

## Tổng quan

Dự án sử dụng Orval để tự động tạo TypeScript definitions và API client từ Swagger/OpenAPI schema của Backend. Điều này giúp đảm bảo tính nhất quán giữa Frontend và Backend, giảm thiểu lỗi và tăng năng suất phát triển.

## Quy trình tự động hóa

### 1. Tự động hóa trong quá trình phát triển

Trong quá trình phát triển, bạn có thể chạy lệnh sau để tự động cập nhật API client khi file schema thay đổi:

```bash
pnpm generate:api:watch
```

Lệnh này sẽ theo dõi file schema và tự động tạo lại API client khi file schema thay đổi.

### 2. Tự động hóa trong CI/CD

Để tự động hóa trong CI/CD, bạn có thể thêm lệnh sau vào file workflow:

```yaml
# .github/workflows/build.yml
name: Build

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'pnpm'
      - name: Install dependencies
        run: pnpm install
      - name: Generate API client
        run: pnpm generate:api
      - name: Build
        run: pnpm build
```

### 3. Tự động hóa trong pre-commit hook

Bạn có thể thêm lệnh sau vào file package.json để tự động tạo API client trước khi commit:

```json
{
  "scripts": {
    "prepare": "husky install",
    "pre-commit": "pnpm generate:api && git add src/api/generated"
  },
  "husky": {
    "hooks": {
      "pre-commit": "pnpm pre-commit"
    }
  }
}
```

### 4. Tự động hóa trong quá trình build

Bạn có thể thêm lệnh sau vào file package.json để tự động tạo API client trước khi build:

```json
{
  "scripts": {
    "prebuild": "pnpm generate:api",
    "build": "next build"
  }
}
```

## Tự động hóa đồng bộ giữa Backend và Frontend

### 1. Sử dụng shared schema

Bạn có thể tạo một package riêng để chia sẻ schema giữa Backend và Frontend:

```
shared-schema/
├── package.json
├── tsconfig.json
└── src/
    ├── index.ts
    └── schemas/
        ├── user.schema.ts
        ├── auth.schema.ts
        └── ...
```

Sau đó, cả Backend và Frontend đều sử dụng package này để đảm bảo tính nhất quán.

### 2. Sử dụng webhook

Backend có thể gửi webhook đến Frontend khi schema thay đổi để Frontend tự động cập nhật API client.

### 3. Sử dụng monorepo

Nếu dự án của bạn là monorepo, bạn có thể sử dụng các công cụ như Nx, Turborepo để tự động hóa việc đồng bộ giữa Backend và Frontend.

## Quy trình làm việc

1. Backend developer cập nhật API và schema
2. Backend developer chạy lệnh để tạo file schema mới
3. Frontend developer chạy lệnh để tạo API client từ file schema mới
4. Frontend developer sử dụng API client mới trong code

Hoặc tự động hóa quy trình này bằng cách sử dụng CI/CD, pre-commit hook, hoặc các công cụ khác.

## Lưu ý

- Luôn đảm bảo file schema là nguồn dữ liệu chính xác
- Không chỉnh sửa trực tiếp các file được tạo tự động
- Sử dụng các custom hooks để bọc API client để dễ dàng mở rộng và bảo trì
