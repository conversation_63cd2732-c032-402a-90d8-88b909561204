# Quy trình xây dựng giao diện trang "Xem chi tiết Hồ sơ Đảng viên"

## **Tab 1: "<PERSON><PERSON> sơ Gốc"**

**Trang: Xem chi tiết Hồ sơ Đảng viên (`/${locale}/cong-tac-dang/chi-tiet-dang-vien/{idDangVien}`)**

**IV. Chi tiết Nội dung Tab 1: "Hồ sơ Gốc"**

- **M<PERSON><PERSON> đích:** Hiển thị và cho phép quản lý (với người có quyền) các tài liệu gốc, văn bản quan trọng trong hồ sơ của đảng viên. Dữ liệu chủ yếu từ bảng `HoSoDangVien`.
- **B<PERSON> cục:** <PERSON><PERSON> thể chia thành hai phần chính:
  1.  **Thông tin dạng văn bản/text:** <PERSON><PERSON><PERSON> thị các nội dung text dài.
  2.  **<PERSON><PERSON> sách các file tài liệu đính kèm:** <PERSON><PERSON><PERSON> thị dưới dạng danh sách hoặc lưới các file có thể xem/tải.
- **Nút hành động chung cho Tab (nếu có quyền chỉnh sửa hồ sơ và được phép cập nhật các file này):** "Quản lý Files Hồ sơ gốc" hoặc "Cập nhật Hồ sơ gốc". Khi nhấn vào, có thể:

  - Cho phép upload file mới/thay thế file cũ cho từng mục tài liệu.
  - Cho phép chỉnh sửa các trường text.
  - Hoặc điều hướng sang một giao diện/trang riêng để quản lý chi tiết các file và nội dung này (tương tự trang Chỉnh sửa hồ sơ).

- **Các trường dữ liệu và chức năng hiển thị (read-only trên trang xem chi tiết, trừ khi vào chế độ sửa hoặc có nút hành động riêng):**

  1.  **Phần Thông tin Văn bản (Từ bảng `HoSoDangVien`):**

      - **Lý lịch Đảng viên (Nội dung chi tiết):**
        - **Tiêu đề:** "Nội dung Lý lịch Đảng viên (Mẫu 1-HSĐV)"
        - **Hiển thị:** Giá trị từ `HoSoDangVien.LyLichDangVien_NoiDung`. Đây là trường NTEXT, nên cần hiển thị trong một khu vực có thể cuộn nếu nội dung dài, giữ nguyên định dạng xuống dòng.
      - **Thông tin tổng hợp về Nộp Đảng phí:**
        - **Tiêu đề:** "Thông tin Tổng hợp Nộp Đảng phí"
        - **Hiển thị:** Giá trị từ `HoSoDangVien.ThongTinNopDangPhi_Raw`. Tương tự như trên, hiển thị nội dung text. (Có thể là một liên kết đến Tab "Đảng phí" để xem chi tiết hơn).
      - **Ghi chú Hồ sơ:**
        - **Tiêu đề:** "Ghi chú chung về Hồ sơ"
        - **Hiển thị:** Giá trị từ `HoSoDangVien.GhiChuHoSo`.

  2.  **Phần Danh sách File Tài liệu Đính kèm (Từ bảng `HoSoDangVien`, các trường `*_URL`):**

      - **Mục đích:** Liệt kê các tài liệu quan trọng đã được scan và lưu trữ.
      - **Bố cục:** Dạng danh sách hoặc lưới, mỗi mục là một loại tài liệu.
      - **Với mỗi loại tài liệu được liệt kê (nếu có file_URL):**

        - **Tên loại tài liệu (label):** Ví dụ: "Lý lịch người xin vào Đảng", "Đơn xin vào Đảng", "Giấy giới thiệu của người thứ nhất", v.v.
        - **Hành động "Xem":**
          - **Kiểu hiển thị:** Nút bấm hoặc biểu tượng (ví dụ: hình con mắt, hoặc icon PDF/Word).
          - **Chức năng:** Khi nhấn vào, mở file trong một tab mới hoặc một trình xem file tích hợp (ví dụ: PDF viewer). Cần kiểm soát quyền truy cập file và có thể giải mã file nếu file được lưu trữ mã hóa.
        - **Hành động "Tải về":**
          - **Kiểu hiển thị:** Nút bấm hoặc biểu tượng (ví dụ: hình mũi tên tải xuống).
          - **Chức năng:** Cho phép người dùng tải file về máy. Tương tự như "Xem", cần kiểm soát quyền và giải mã.
        - **(Thông tin bổ sung có thể hiển thị nếu cần):** Ngày cập nhật file, dung lượng file (ít phổ biến hơn).

      - **Danh sách các trường `*_URL` cụ thể cần hiển thị và quản lý:**
        - `HoSoDangVien.LyLichNguoiXinVaoDang_URL`: **Lý lịch người xin vào Đảng**
        - `HoSoDangVien.DonXinVaoDang_URL`: **Đơn xin vào Đảng**
        - `HoSoDangVien.GiayGioiThieu1_URL`: **Giấy giới thiệu của người thứ nhất**
        - `HoSoDangVien.GiayGioiThieu2_URL`: **Giấy giới thiệu của người thứ hai**
        - `HoSoDangVien.NghiQuyetGioiThieu_URL`: **Nghị quyết giới thiệu của Chi bộ**
        - `HoSoDangVien.LyLichDangVien_M1HSĐV_URL`: **Lý lịch Đảng viên (Mẫu 1-HSĐV)**
        - `HoSoDangVien.PhieuDangVien_M2HSĐV_URL`: **Phiếu Đảng viên (Mẫu 2-HSĐV)**
        - `HoSoDangVien.VanBangLyLuanChinhTri_CaoCap_URL`: **Bằng Cao cấp Lý luận Chính trị**
        - `HoSoDangVien.VanBangLyLuanChinhTri_TrungCap_URL`: **Bằng Trung cấp Lý luận Chính trị**
        - `HoSoDangVien.VanBangLyLuanChinhTri_SoCap_URL`: **Bằng Sơ cấp Lý luận Chính trị**
        - `HoSoDangVien.ChungNhanBoiDuongCamTinhDang_URL`: **Giấy chứng nhận học lớp Bồi dưỡng nhận thức về Đảng**
        - `HoSoDangVien.ChungNhanBoiDuongDangVienMoi_URL`: **Giấy chứng nhận học lớp Bồi dưỡng Đảng viên mới**

- **Chức năng trong Tab này (cho người dùng có quyền chỉnh sửa hồ sơ, thông qua nút "Quản lý Files Hồ sơ gốc" hoặc khi ở trang chỉnh sửa):**
  - **Upload/Thay thế file:** Cho từng loại tài liệu (ví dụ: `LyLichNguoiXinVaoDang_URL`), cho phép người dùng chọn file từ máy tính để tải lên. Nếu đã có file, hệ thống sẽ hỏi xác nhận thay thế.
  - **Xóa file:** Cho phép xóa một file đã upload (cần xác nhận).
  - **Chỉnh sửa nội dung text:** Cho phép sửa các trường `LyLichDangVien_NoiDung`, `ThongTinNopDangPhi_Raw`, `GhiChuHoSo`.

**Lưu ý quan trọng về bảo mật:**

- Tất cả các file trong "Hồ sơ Gốc" đều mang tính chất nhạy cảm. Việc truy cập (xem, tải) phải được kiểm soát chặt chẽ dựa trên quyền của người dùng.
- Các file khi lưu trữ nên được mã hóa (như đã đề cập trong tài liệu). Quá trình xem/tải về cần giải mã động.
- Giao diện cần có cảnh báo rõ ràng khi người dùng thực hiện các thao tác upload, thay thế, hoặc xóa file.

---
