"use strict";Object.defineProperty(exports,"__esModule",{value:true});function _export(target,all){for(var name in all)Object.defineProperty(target,name,{enumerable:true,get:Object.getOwnPropertyDescriptor(all,name).get})}_export(exports,{get ASC(){return ASC},get BYPASS_PERMISSION(){return BYPASS_PERMISSION},get DEFAULT_PAGE(){return DEFAULT_PAGE},get DEFAULT_PER_PAGE(){return DEFAULT_PER_PAGE},get DEFAULT_TIMESTAMP_FORMAT(){return DEFAULT_TIMESTAMP_FORMAT},get DESC(){return DESC},get FROM_DEFAULT(){return FROM_DEFAULT},get HUMAN_TIMESTAMP_FORMAT(){return HUMAN_TIMESTAMP_FORMAT},get NoPermissionResponse(){return NoPermissionResponse},get ONLY_ADMIN(){return ONLY_ADMIN},get TO_DEFAULT(){return TO_DEFAULT}});const NoPermissionResponse={result:false,message:"No permission."};const DESC="DESC";const ASC="ASC";const FROM_DEFAULT="2021-01-01 00:00:00";const TO_DEFAULT="2121-12-31 23:59:59";const DEFAULT_PAGE=1;const DEFAULT_PER_PAGE=10;const BYPASS_PERMISSION="BYPASS_PERMISSION";const ONLY_ADMIN="ONLY_ADMIN";const DEFAULT_TIMESTAMP_FORMAT="YYYY-MM-DD HH:mm:ss";const HUMAN_TIMESTAMP_FORMAT="HH:mm DD/MM/YYYY";
//# sourceMappingURL=constant.js.map