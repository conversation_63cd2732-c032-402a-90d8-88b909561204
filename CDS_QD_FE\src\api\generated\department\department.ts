/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 *  Swagger
 * The  API documents
 * OpenAPI spec version: 1.0
 */
import { useInfiniteQuery, useMutation, useQuery } from '@tanstack/react-query'
import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseInfiniteQueryResult,
  DefinedUseQueryResult,
  InfiniteData,
  MutationFunction,
  QueryClient,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseInfiniteQueryOptions,
  UseInfiniteQueryResult,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult
} from '@tanstack/react-query'

import type { CreateDepartmentDto, DepartmentControllerFindAllParams, UpdateDepartmentDto } from '.././model'

import { customInstance } from '../../mutator/custom-instance'
import type { ApiResponse } from '../../types'

export const departmentControllerCreate = (createDepartmentDto: CreateDepartmentDto, signal?: AbortSignal) => {
  return customInstance<ApiResponse<void>>({
    url: `/department`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: createDepartmentDto,
    signal
  })
}

export const getDepartmentControllerCreateMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof departmentControllerCreate>>,
    TError,
    { data: CreateDepartmentDto },
    TContext
  >
}): UseMutationOptions<
  Awaited<ReturnType<typeof departmentControllerCreate>>,
  TError,
  { data: CreateDepartmentDto },
  TContext
> => {
  const mutationKey = ['departmentControllerCreate']
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } }

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof departmentControllerCreate>>,
    { data: CreateDepartmentDto }
  > = props => {
    const { data } = props ?? {}

    return departmentControllerCreate(data)
  }

  return { mutationFn, ...mutationOptions }
}

export type DepartmentControllerCreateMutationResult = NonNullable<
  Awaited<ReturnType<typeof departmentControllerCreate>>
>
export type DepartmentControllerCreateMutationBody = CreateDepartmentDto
export type DepartmentControllerCreateMutationError = unknown

export const useDepartmentControllerCreate = <TError = unknown, TContext = unknown>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof departmentControllerCreate>>,
      TError,
      { data: CreateDepartmentDto },
      TContext
    >
  },
  queryClient?: QueryClient
): UseMutationResult<
  Awaited<ReturnType<typeof departmentControllerCreate>>,
  TError,
  { data: CreateDepartmentDto },
  TContext
> => {
  const mutationOptions = getDepartmentControllerCreateMutationOptions(options)

  return useMutation(mutationOptions, queryClient)
}
export const departmentControllerFindAll = (params?: DepartmentControllerFindAllParams, signal?: AbortSignal) => {
  return customInstance<ApiResponse<void>>({ url: `/department`, method: 'GET', params, signal })
}

export const getDepartmentControllerFindAllQueryKey = (params?: DepartmentControllerFindAllParams) => {
  return [`/department`, ...(params ? [params] : [])] as const
}

export const getDepartmentControllerFindAllInfiniteQueryOptions = <
  TData = InfiniteData<
    Awaited<ReturnType<typeof departmentControllerFindAll>>,
    DepartmentControllerFindAllParams['page']
  >,
  TError = unknown
>(
  params?: DepartmentControllerFindAllParams,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof departmentControllerFindAll>>,
        TError,
        TData,
        Awaited<ReturnType<typeof departmentControllerFindAll>>,
        QueryKey,
        DepartmentControllerFindAllParams['page']
      >
    >
  }
) => {
  const { query: queryOptions } = options ?? {}

  const queryKey = queryOptions?.queryKey ?? getDepartmentControllerFindAllQueryKey(params)

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof departmentControllerFindAll>>,
    QueryKey,
    DepartmentControllerFindAllParams['page']
  > = ({ signal, pageParam }) => departmentControllerFindAll({ ...params, page: pageParam || params?.['page'] }, signal)

  return { queryKey, queryFn, staleTime: 10000, ...queryOptions } as UseInfiniteQueryOptions<
    Awaited<ReturnType<typeof departmentControllerFindAll>>,
    TError,
    TData,
    Awaited<ReturnType<typeof departmentControllerFindAll>>,
    QueryKey,
    DepartmentControllerFindAllParams['page']
  > & { queryKey: DataTag<QueryKey, TData, TError> }
}

export type DepartmentControllerFindAllInfiniteQueryResult = NonNullable<
  Awaited<ReturnType<typeof departmentControllerFindAll>>
>
export type DepartmentControllerFindAllInfiniteQueryError = unknown

export function useDepartmentControllerFindAllInfinite<
  TData = InfiniteData<
    Awaited<ReturnType<typeof departmentControllerFindAll>>,
    DepartmentControllerFindAllParams['page']
  >,
  TError = unknown
>(
  params: undefined | DepartmentControllerFindAllParams,
  options: {
    query: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof departmentControllerFindAll>>,
        TError,
        TData,
        Awaited<ReturnType<typeof departmentControllerFindAll>>,
        QueryKey,
        DepartmentControllerFindAllParams['page']
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof departmentControllerFindAll>>,
          TError,
          Awaited<ReturnType<typeof departmentControllerFindAll>>,
          QueryKey
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): DefinedUseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useDepartmentControllerFindAllInfinite<
  TData = InfiniteData<
    Awaited<ReturnType<typeof departmentControllerFindAll>>,
    DepartmentControllerFindAllParams['page']
  >,
  TError = unknown
>(
  params?: DepartmentControllerFindAllParams,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof departmentControllerFindAll>>,
        TError,
        TData,
        Awaited<ReturnType<typeof departmentControllerFindAll>>,
        QueryKey,
        DepartmentControllerFindAllParams['page']
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof departmentControllerFindAll>>,
          TError,
          Awaited<ReturnType<typeof departmentControllerFindAll>>,
          QueryKey
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useDepartmentControllerFindAllInfinite<
  TData = InfiniteData<
    Awaited<ReturnType<typeof departmentControllerFindAll>>,
    DepartmentControllerFindAllParams['page']
  >,
  TError = unknown
>(
  params?: DepartmentControllerFindAllParams,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof departmentControllerFindAll>>,
        TError,
        TData,
        Awaited<ReturnType<typeof departmentControllerFindAll>>,
        QueryKey,
        DepartmentControllerFindAllParams['page']
      >
    >
  },
  queryClient?: QueryClient
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }

export function useDepartmentControllerFindAllInfinite<
  TData = InfiniteData<
    Awaited<ReturnType<typeof departmentControllerFindAll>>,
    DepartmentControllerFindAllParams['page']
  >,
  TError = unknown
>(
  params?: DepartmentControllerFindAllParams,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof departmentControllerFindAll>>,
        TError,
        TData,
        Awaited<ReturnType<typeof departmentControllerFindAll>>,
        QueryKey,
        DepartmentControllerFindAllParams['page']
      >
    >
  },
  queryClient?: QueryClient
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
  const queryOptions = getDepartmentControllerFindAllInfiniteQueryOptions(params, options)

  const query = useInfiniteQuery(queryOptions, queryClient) as UseInfiniteQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>
  }

  query.queryKey = queryOptions.queryKey

  return query
}

export const getDepartmentControllerFindAllQueryOptions = <
  TData = Awaited<ReturnType<typeof departmentControllerFindAll>>,
  TError = unknown
>(
  params?: DepartmentControllerFindAllParams,
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof departmentControllerFindAll>>, TError, TData>> }
) => {
  const { query: queryOptions } = options ?? {}

  const queryKey = queryOptions?.queryKey ?? getDepartmentControllerFindAllQueryKey(params)

  const queryFn: QueryFunction<Awaited<ReturnType<typeof departmentControllerFindAll>>> = ({ signal }) =>
    departmentControllerFindAll(params, signal)

  return { queryKey, queryFn, staleTime: 10000, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof departmentControllerFindAll>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> }
}

export type DepartmentControllerFindAllQueryResult = NonNullable<
  Awaited<ReturnType<typeof departmentControllerFindAll>>
>
export type DepartmentControllerFindAllQueryError = unknown

export function useDepartmentControllerFindAll<
  TData = Awaited<ReturnType<typeof departmentControllerFindAll>>,
  TError = unknown
>(
  params: undefined | DepartmentControllerFindAllParams,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof departmentControllerFindAll>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof departmentControllerFindAll>>,
          TError,
          Awaited<ReturnType<typeof departmentControllerFindAll>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useDepartmentControllerFindAll<
  TData = Awaited<ReturnType<typeof departmentControllerFindAll>>,
  TError = unknown
>(
  params?: DepartmentControllerFindAllParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof departmentControllerFindAll>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof departmentControllerFindAll>>,
          TError,
          Awaited<ReturnType<typeof departmentControllerFindAll>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useDepartmentControllerFindAll<
  TData = Awaited<ReturnType<typeof departmentControllerFindAll>>,
  TError = unknown
>(
  params?: DepartmentControllerFindAllParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof departmentControllerFindAll>>, TError, TData>>
  },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }

export function useDepartmentControllerFindAll<
  TData = Awaited<ReturnType<typeof departmentControllerFindAll>>,
  TError = unknown
>(
  params?: DepartmentControllerFindAllParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof departmentControllerFindAll>>, TError, TData>>
  },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
  const queryOptions = getDepartmentControllerFindAllQueryOptions(params, options)

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>
  }

  query.queryKey = queryOptions.queryKey

  return query
}

export const departmentControllerFindOne = (id: string, signal?: AbortSignal) => {
  return customInstance<ApiResponse<ApiResponse<void>>>({ url: `/department/${id}`, method: 'GET', signal })
}

export const getDepartmentControllerFindOneQueryKey = (id: string) => {
  return [`/department/${id}`] as const
}

export const getDepartmentControllerFindOneInfiniteQueryOptions = <
  TData = InfiniteData<Awaited<ReturnType<typeof departmentControllerFindOne>>>,
  TError = unknown
>(
  id: string,
  options?: {
    query?: Partial<UseInfiniteQueryOptions<Awaited<ReturnType<typeof departmentControllerFindOne>>, TError, TData>>
  }
) => {
  const { query: queryOptions } = options ?? {}

  const queryKey = queryOptions?.queryKey ?? getDepartmentControllerFindOneQueryKey(id)

  const queryFn: QueryFunction<Awaited<ReturnType<typeof departmentControllerFindOne>>> = ({ signal }) =>
    departmentControllerFindOne(id, signal)

  return { queryKey, queryFn, enabled: !!id, staleTime: 10000, ...queryOptions } as UseInfiniteQueryOptions<
    Awaited<ReturnType<typeof departmentControllerFindOne>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> }
}

export type DepartmentControllerFindOneInfiniteQueryResult = NonNullable<
  Awaited<ReturnType<typeof departmentControllerFindOne>>
>
export type DepartmentControllerFindOneInfiniteQueryError = unknown

export function useDepartmentControllerFindOneInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof departmentControllerFindOne>>>,
  TError = unknown
>(
  id: string,
  options: {
    query: Partial<UseInfiniteQueryOptions<Awaited<ReturnType<typeof departmentControllerFindOne>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof departmentControllerFindOne>>,
          TError,
          Awaited<ReturnType<typeof departmentControllerFindOne>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): DefinedUseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useDepartmentControllerFindOneInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof departmentControllerFindOne>>>,
  TError = unknown
>(
  id: string,
  options?: {
    query?: Partial<UseInfiniteQueryOptions<Awaited<ReturnType<typeof departmentControllerFindOne>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof departmentControllerFindOne>>,
          TError,
          Awaited<ReturnType<typeof departmentControllerFindOne>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useDepartmentControllerFindOneInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof departmentControllerFindOne>>>,
  TError = unknown
>(
  id: string,
  options?: {
    query?: Partial<UseInfiniteQueryOptions<Awaited<ReturnType<typeof departmentControllerFindOne>>, TError, TData>>
  },
  queryClient?: QueryClient
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }

export function useDepartmentControllerFindOneInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof departmentControllerFindOne>>>,
  TError = unknown
>(
  id: string,
  options?: {
    query?: Partial<UseInfiniteQueryOptions<Awaited<ReturnType<typeof departmentControllerFindOne>>, TError, TData>>
  },
  queryClient?: QueryClient
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
  const queryOptions = getDepartmentControllerFindOneInfiniteQueryOptions(id, options)

  const query = useInfiniteQuery(queryOptions, queryClient) as UseInfiniteQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>
  }

  query.queryKey = queryOptions.queryKey

  return query
}

export const getDepartmentControllerFindOneQueryOptions = <
  TData = Awaited<ReturnType<typeof departmentControllerFindOne>>,
  TError = unknown
>(
  id: string,
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof departmentControllerFindOne>>, TError, TData>> }
) => {
  const { query: queryOptions } = options ?? {}

  const queryKey = queryOptions?.queryKey ?? getDepartmentControllerFindOneQueryKey(id)

  const queryFn: QueryFunction<Awaited<ReturnType<typeof departmentControllerFindOne>>> = ({ signal }) =>
    departmentControllerFindOne(id, signal)

  return { queryKey, queryFn, enabled: !!id, staleTime: 10000, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof departmentControllerFindOne>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> }
}

export type DepartmentControllerFindOneQueryResult = NonNullable<
  Awaited<ReturnType<typeof departmentControllerFindOne>>
>
export type DepartmentControllerFindOneQueryError = unknown

export function useDepartmentControllerFindOne<
  TData = Awaited<ReturnType<typeof departmentControllerFindOne>>,
  TError = unknown
>(
  id: string,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof departmentControllerFindOne>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof departmentControllerFindOne>>,
          TError,
          Awaited<ReturnType<typeof departmentControllerFindOne>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useDepartmentControllerFindOne<
  TData = Awaited<ReturnType<typeof departmentControllerFindOne>>,
  TError = unknown
>(
  id: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof departmentControllerFindOne>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof departmentControllerFindOne>>,
          TError,
          Awaited<ReturnType<typeof departmentControllerFindOne>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useDepartmentControllerFindOne<
  TData = Awaited<ReturnType<typeof departmentControllerFindOne>>,
  TError = unknown
>(
  id: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof departmentControllerFindOne>>, TError, TData>>
  },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }

export function useDepartmentControllerFindOne<
  TData = Awaited<ReturnType<typeof departmentControllerFindOne>>,
  TError = unknown
>(
  id: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof departmentControllerFindOne>>, TError, TData>>
  },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
  const queryOptions = getDepartmentControllerFindOneQueryOptions(id, options)

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>
  }

  query.queryKey = queryOptions.queryKey

  return query
}

export const departmentControllerUpdate = (id: string, updateDepartmentDto: UpdateDepartmentDto) => {
  return customInstance<ApiResponse<ApiResponse<void>>>({
    url: `/department/${id}`,
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json' },
    data: updateDepartmentDto
  })
}

export const getDepartmentControllerUpdateMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof departmentControllerUpdate>>,
    TError,
    { id: string; data: UpdateDepartmentDto },
    TContext
  >
}): UseMutationOptions<
  Awaited<ReturnType<typeof departmentControllerUpdate>>,
  TError,
  { id: string; data: UpdateDepartmentDto },
  TContext
> => {
  const mutationKey = ['departmentControllerUpdate']
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } }

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof departmentControllerUpdate>>,
    { id: string; data: UpdateDepartmentDto }
  > = props => {
    const { id, data } = props ?? {}

    return departmentControllerUpdate(id, data)
  }

  return { mutationFn, ...mutationOptions }
}

export type DepartmentControllerUpdateMutationResult = NonNullable<
  Awaited<ReturnType<typeof departmentControllerUpdate>>
>
export type DepartmentControllerUpdateMutationBody = UpdateDepartmentDto
export type DepartmentControllerUpdateMutationError = unknown

export const useDepartmentControllerUpdate = <TError = unknown, TContext = unknown>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof departmentControllerUpdate>>,
      TError,
      { id: string; data: UpdateDepartmentDto },
      TContext
    >
  },
  queryClient?: QueryClient
): UseMutationResult<
  Awaited<ReturnType<typeof departmentControllerUpdate>>,
  TError,
  { id: string; data: UpdateDepartmentDto },
  TContext
> => {
  const mutationOptions = getDepartmentControllerUpdateMutationOptions(options)

  return useMutation(mutationOptions, queryClient)
}
export const departmentControllerRemove = (id: string) => {
  return customInstance<ApiResponse<void>>({ url: `/department/${id}`, method: 'DELETE' })
}

export const getDepartmentControllerRemoveMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof departmentControllerRemove>>,
    TError,
    { id: string },
    TContext
  >
}): UseMutationOptions<Awaited<ReturnType<typeof departmentControllerRemove>>, TError, { id: string }, TContext> => {
  const mutationKey = ['departmentControllerRemove']
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } }

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof departmentControllerRemove>>,
    { id: string }
  > = props => {
    const { id } = props ?? {}

    return departmentControllerRemove(id)
  }

  return { mutationFn, ...mutationOptions }
}

export type DepartmentControllerRemoveMutationResult = NonNullable<
  Awaited<ReturnType<typeof departmentControllerRemove>>
>

export type DepartmentControllerRemoveMutationError = unknown

export const useDepartmentControllerRemove = <TError = unknown, TContext = unknown>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof departmentControllerRemove>>,
      TError,
      { id: string },
      TContext
    >
  },
  queryClient?: QueryClient
): UseMutationResult<Awaited<ReturnType<typeof departmentControllerRemove>>, TError, { id: string }, TContext> => {
  const mutationOptions = getDepartmentControllerRemoveMutationOptions(options)

  return useMutation(mutationOptions, queryClient)
}
