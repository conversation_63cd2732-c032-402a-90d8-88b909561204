import { Injectable } from '@nestjs/common';
import { CACHE_TIME } from '~/common/enums/enum';
import { AccountRepository } from '~/database/typeorm/repositories/account.repository';
import { DepartmentRepository } from '~/database/typeorm/repositories/department.repository';
import { MediaRepository } from '~/database/typeorm/repositories/media.repository';
import { PermissionRepository } from '~/database/typeorm/repositories/permission.repository';
import { ProviderRepository } from '~/database/typeorm/repositories/provider.repository';
import { RoleRepository } from '~/database/typeorm/repositories/role.repository';
import { UserRepository } from '~/database/typeorm/repositories/user.repository';
import { UserLogRepository } from '~/database/typeorm/repositories/userLog.repository';
import { WarehouseRepository } from '~/database/typeorm/repositories/warehouse.repository';
import { WarehouseTypeRepository } from '~/database/typeorm/repositories/warehouseType.repository';
import { CacheService } from '~/shared/services/cache.service';
import { LoaiDanhMucRepository } from '~/database/typeorm/repositories/loaiDanhMuc.repository';
import { GiaTriDanhMucRepository } from '~/database/typeorm/repositories/giaTriDanhMuc.repository';
import { ThamSoHeThongRepository } from '~/database/typeorm/repositories/thamSoHeThong.repository';
import { RolesPermissionRepository } from '~/database/typeorm/repositories/rolesPermission.repository';
import { QuanNhanRepository } from '~/database/typeorm/repositories/quanNhan.repository';
import { LyLichCanNhanRepository } from '~/database/typeorm/repositories/lyLichCanNhan.repository';
import { QuaTrinhCongTacRepository } from '~/database/typeorm/repositories/quaTrinhCongTac.repository';
import { QuaTrinhDaoTaoRepository } from '~/database/typeorm/repositories/quaTrinhDaoTao.repository';
import { DeXuatKhenThuongRepository } from '~/database/typeorm/repositories/deXuatKhenThuong.repository';
import { QuyetDinhKhenThuongRepository } from '~/database/typeorm/repositories/quyetDinhKhenThuong.repository';
import { HoSoVuViecKyLuatRepository } from '~/database/typeorm/repositories/hoSoVuViecKyLuat.repository';
import { BienBanHoiDongKyLuatRepository } from '~/database/typeorm/repositories/bienBanHoiDongKyLuat.repository';
import { TaiLieuVuViecKyLuatRepository } from '~/database/typeorm/repositories/taiLieuVuViecKyLuat.repository';
import { QuyetDinhKyLuatRepository } from '~/database/typeorm/repositories/quyetDinhKyLuat.repository';
import { HoSoSucKhoeRepository } from '~/database/typeorm/repositories/hoSoSucKhoe.repository';
import { QuanHeGiaDinhRepository } from '~/database/typeorm/repositories/quanHeGiaDinh.repository';
import { TheoDoiCheDoChinhSachRepository } from '~/database/typeorm/repositories/theoDoiCheDoChinhSach.repository';
import { DangVienRepository } from '~/database/typeorm/repositories/dangVien.repository';
import { HoSoDangVienRepository } from '~/database/typeorm/repositories/hoSoDangVien.repository';
import { DeXuatKhenThuongDangVienRepository } from '~/database/typeorm/repositories/deXuatKhenThuongDangVien.repository';
import { QuyetDinhKhenThuongDangVienRepository } from '~/database/typeorm/repositories/quyetDinhKhenThuongDangVien.repository';
import { BanKiemDiemDangVienRepository } from '~/database/typeorm/repositories/banKiemDiemDangVien.repository';
import { HoSoVuViecKyLuatDangVienRepository } from '~/database/typeorm/repositories/hoSoVuViecKyLuatDangVien.repository';
import { BienBanHoiDongKyLuatDangRepository } from '~/database/typeorm/repositories/bienBanHoiDongKyLuatDang.repository';
import { QuyetDinhKyLuatDangVienRepository } from '~/database/typeorm/repositories/quyetDinhKyLuatDangVien.repository';
import { CapUyNhiemKyRepository } from '~/database/typeorm/repositories/capUyNhiemKy.repository';
import { ThanhVienCapUyRepository } from '~/database/typeorm/repositories/thanhVienCapUy.repository';
import { KeHoachSinhHoatDangRepository } from '~/database/typeorm/repositories/keHoachSinhHoatDang.repository';
import { BuoiSinhHoatDangRepository } from '~/database/typeorm/repositories/buoiSinhHoatDang.repository';
import { NghiQuyetDangRepository } from '~/database/typeorm/repositories/nghiQuyetDang.repository';
import { HoSoPhatTrienDangVienRepository } from '~/database/typeorm/repositories/hoSoPhatTrienDangVien.repository';
import { DanhGiaXepLoaiDangVienRepository } from '~/database/typeorm/repositories/danhGiaXepLoaiDangVien.repository';
import { DanhGiaXepLoaiToChucDangRepository } from '~/database/typeorm/repositories/danhGiaXepLoaiToChucDang.repository';
import { ThuChiDangPhiRepository } from '~/database/typeorm/repositories/thuChiDangPhi.repository';
import { KeHoachKiemTraGiamSatDangRepository } from '~/database/typeorm/repositories/keHoachKiemTraGiamSatDang.repository';
import { CuocKiemTraGiamSatDangRepository } from '~/database/typeorm/repositories/cuocKiemTraGiamSatDang.repository';
import { KeHoachGiaoDucChinhTriRepository } from '~/database/typeorm/repositories/keHoachGiaoDucChinhTri.repository';
import { HoatDongGiaoDucChinhTriRepository } from '~/database/typeorm/repositories/hoatDongGiaoDucChinhTri.repository';
import { ThamGiaGiaoDucChinhTriRepository } from '~/database/typeorm/repositories/thamGiaGiaoDucChinhTri.repository';
import { KetQuaHocTapChinhTriRepository } from '~/database/typeorm/repositories/ketQuaHocTapChinhTri.repository';
import { GhiNhanTinhHinhTuTuongRepository } from '~/database/typeorm/repositories/ghiNhanTinhHinhTuTuong.repository';
import { BienPhapTacDongTuTuongRepository } from '~/database/typeorm/repositories/bienPhapTacDongTuTuong.repository';
import { KeHoachTuyenTruyenVanHoaRepository } from '~/database/typeorm/repositories/keHoachTuyenTruyenVanHoa.repository';
import { HoatDongTuyenTruyenVanHoaRepository } from '~/database/typeorm/repositories/hoatDongTuyenTruyenVanHoa.repository';
import { PhongTraoThiDuaChinhTriRepository } from '~/database/typeorm/repositories/phongTraoThiDuaChinhTri.repository';
import { ChinhSachHauPhuongRepository } from '~/database/typeorm/repositories/chinhSachHauPhuong.repository';
import { DoiTuongChinhSachHauPhuongRepository } from '~/database/typeorm/repositories/doiTuongChinhSachHauPhuong.repository';
import { ThongBaoRepository } from '~/database/typeorm/repositories/thongBao.repository';
import { QuyTrinhPheDuyetRepository } from '~/database/typeorm/repositories/quyTrinhPheDuyet.repository';
import { YeuCauPheDuyetRepository } from '~/database/typeorm/repositories/yeuCauPheDuyet.repository';
import { BuocPheDuyetRepository } from '~/database/typeorm/repositories/buocPheDuyet.repository';
import { LichSuPheDuyetRepository } from '~/database/typeorm/repositories/lichSuPheDuyet.repository';
import { NhatKyHeThongRepository } from '~/database/typeorm/repositories/nhatKyHeThong.repository';
import { DuLieuTepTinRepository } from '~/database/typeorm/repositories/duLieuTepTin.repository';

@Injectable()
export class DatabaseService {
    constructor(
        public readonly department: DepartmentRepository,
        public readonly user: UserRepository,
        public readonly account: AccountRepository,
        public readonly media: MediaRepository,
        public readonly permission: PermissionRepository,
        public readonly role: RoleRepository,
        public readonly userLog: UserLogRepository,
        public readonly warehouse: WarehouseRepository,
        public readonly warehouseType: WarehouseTypeRepository,
        public readonly provider: ProviderRepository,
        private readonly cacheService: CacheService,
        public readonly loaiDanhMuc: LoaiDanhMucRepository,
        public readonly giaTriDanhMuc: GiaTriDanhMucRepository,
        public readonly thamSoHeThong: ThamSoHeThongRepository,
        public readonly rolesPermission: RolesPermissionRepository,
        public readonly quanNhan: QuanNhanRepository,
        public readonly lyLichCanNhan: LyLichCanNhanRepository,
        public readonly quaTrinhCongTac: QuaTrinhCongTacRepository,
        public readonly quaTrinhDaoTao: QuaTrinhDaoTaoRepository,
        public readonly deXuatKhenThuong: DeXuatKhenThuongRepository,
        public readonly quyetDinhKhenThuong: QuyetDinhKhenThuongRepository,
        public readonly hoSoVuViecKyLuat: HoSoVuViecKyLuatRepository,
        public readonly bienBanHoiDongKyLuat: BienBanHoiDongKyLuatRepository,
        public readonly taiLieuVuViecKyLuat: TaiLieuVuViecKyLuatRepository,
        public readonly quyetDinhKyLuat: QuyetDinhKyLuatRepository,
        public readonly hoSoSucKhoe: HoSoSucKhoeRepository,
        public readonly quanHeGiaDinh: QuanHeGiaDinhRepository,
        public readonly theoDoiCheDoChinhSach: TheoDoiCheDoChinhSachRepository,
        public readonly dangVien: DangVienRepository,
        public readonly hoSoDangVien: HoSoDangVienRepository,
        public readonly deXuatKhenThuongDangVien: DeXuatKhenThuongDangVienRepository,
        public readonly quyetDinhKhenThuongDangVien: QuyetDinhKhenThuongDangVienRepository,
        public readonly banKiemDiemDangVien: BanKiemDiemDangVienRepository,
        public readonly hoSoVuViecKyLuatDangVien: HoSoVuViecKyLuatDangVienRepository,
        public readonly bienBanHoiDongKyLuatDang: BienBanHoiDongKyLuatDangRepository,
        public readonly quyetDinhKyLuatDangVien: QuyetDinhKyLuatDangVienRepository,
        public readonly capUyNhiemKy: CapUyNhiemKyRepository,
        public readonly thanhVienCapUy: ThanhVienCapUyRepository,
        public readonly keHoachSinhHoatDang: KeHoachSinhHoatDangRepository,
        public readonly buoiSinhHoatDang: BuoiSinhHoatDangRepository,
        public readonly nghiQuyetDang: NghiQuyetDangRepository,
        public readonly hoSoPhatTrienDangVien: HoSoPhatTrienDangVienRepository,
        public readonly danhGiaXepLoaiDangVien: DanhGiaXepLoaiDangVienRepository,
        public readonly danhGiaXepLoaiToChucDang: DanhGiaXepLoaiToChucDangRepository,
        public readonly thuChiDangPhi: ThuChiDangPhiRepository,
        public readonly keHoachKiemTraGiamSatDang: KeHoachKiemTraGiamSatDangRepository,
        public readonly cuocKiemTraGiamSatDang: CuocKiemTraGiamSatDangRepository,
        public readonly keHoachGiaoDucChinhTri: KeHoachGiaoDucChinhTriRepository,
        public readonly hoatDongGiaoDucChinhTri: HoatDongGiaoDucChinhTriRepository,
        public readonly thamGiaGiaoDucChinhTri: ThamGiaGiaoDucChinhTriRepository,
        public readonly ketQuaHocTapChinhTri: KetQuaHocTapChinhTriRepository,
        public readonly ghiNhanTinhHinhTuTuong: GhiNhanTinhHinhTuTuongRepository,
        public readonly bienPhapTacDongTuTuong: BienPhapTacDongTuTuongRepository,
        public readonly keHoachTuyenTruyenVanHoa: KeHoachTuyenTruyenVanHoaRepository,
        public readonly hoatDongTuyenTruyenVanHoa: HoatDongTuyenTruyenVanHoaRepository,
        public readonly phongTraoThiDuaChinhTri: PhongTraoThiDuaChinhTriRepository,
        public readonly chinhSachHauPhuong: ChinhSachHauPhuongRepository,
        public readonly doiTuongChinhSachHauPhuong: DoiTuongChinhSachHauPhuongRepository,
        public readonly thongBao: ThongBaoRepository,
        public readonly quyTrinhPheDuyet: QuyTrinhPheDuyetRepository,
        public readonly yeuCauPheDuyet: YeuCauPheDuyetRepository,
        public readonly buocPheDuyet: BuocPheDuyetRepository,
        public readonly lichSuPheDuyet: LichSuPheDuyetRepository,
        public readonly nhatKyHeThong: NhatKyHeThongRepository,
        public readonly duLieuTepTin: DuLieuTepTinRepository,
    ) {
        // load all departments to cache
        // this.loadDepartmentsToCache();
        // this.loadPermissionsByRoleToCache();
    }

    private loadDepartmentsToCache() {
        this.department.find().then((departments) => {
            departments.forEach((department) => {
                this.cacheService.setJson(`department:${department.id}`, department, CACHE_TIME.ONE_MONTH);
            });
        });
    }

    private loadPermissionsByRoleToCache() {
        this.role.find({ relations: ['permissions'] }).then((roles) => {
            roles.forEach((role) => {
                this.cacheService.setJson(
                    `permissions:${role.id}`,
                    role.permissions.map((p) => p.action),
                    CACHE_TIME.ONE_MONTH,
                );
            });
        });
    }
}
