/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { DuLieuTepTinEntity } from '~/database/typeorm/entities/duLieuTepTin.entity';

@Injectable()
export class DuLieuTepTinRepository extends Repository<DuLieuTepTinEntity> {
    constructor(private dataSource: DataSource) {
        super(DuLieuTepTinEntity, dataSource.createEntityManager());
    }
}
