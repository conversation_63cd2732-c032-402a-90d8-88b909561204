/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { DangVienEntity } from '~/database/typeorm/entities/dangVien.entity';

@Injectable()
export class DangVienRepository extends Repository<DangVienEntity> {
    constructor(private dataSource: DataSource) {
        super(DangVienEntity, dataSource.createEntityManager());
    }
}
