# Hướng dẫn sử dụng Dialog trong dự án

## 1. <PERSON><PERSON><PERSON><PERSON> thi<PERSON> (hộp thoại) được sử dụng để hiển thị thông tin hoặc form nhập liệu trong một layer nổi lên trên giao diện chính. Dự án sử dụng Material-UI làm thư viện UI chính.

## 2. Các loại Dialog

### 2.1. BaseDialog
Component cơ sở để tạo các dialog tùy chỉnh.

```tsx
// Cách sử dụng cơ bản
<BaseDialog
  open={open}
  onClose={handleClose}
  title="Tiêu đề dialog"
  subtitle="Mô tả ngắn (tùy chọn)"
  maxWidth="md" // xs, sm, md, lg, xl, false
  fullWidth
  actions={{
    cancel: {
      label: 'Hủy',
      onClick: handleClose,
      color: 'inherit'
    },
    submit: {
      label: '<PERSON><PERSON><PERSON> nhậ<PERSON>',
      onClick: handleSubmit,
      color: 'primary',
      variant: 'contained'
    }
  }}
>
  {/* Nội dung dialog */}
  <div>Đ<PERSON>y là nội dung dialog</div>
</BaseDialog>
```

### 2.2. Dialog xác nhận
Sử dụng khi cần xác nhận hành động từ người dùng.

```tsx
<ConfirmDialog
  open={open}
  onClose={handleClose}
  onConfirm={handleConfirm}
  title="Xác nhận xóa"
  content="Bạn có chắc chắn muốn xóa mục này?"
  confirmText="Xác nhận"
  cancelText="Hủy"
  confirmColor="error"
/>
```

### 2.3. Dialog form
Sử dụng cho các form nhập liệu.

```tsx
<FormDialog
  open={open}
  onClose={handleClose}
  onSubmit={handleSubmit}
  title="Thêm mới"
  loading={isSubmitting}
  maxWidth="md"
>
  <form onSubmit={handleSubmit}>
    <Grid container spacing={3}>
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          label="Tên"
          name="name"
          value={formData.name}
          onChange={handleChange}
        />
      </Grid>
      {/* Các trường khác */}
    </Grid>
  </form>
</FormDialog>
```

## 3. Quy ước sử dụng

### 3.1. Đặt tên
- Đặt tên file dialog theo cú pháp: `Dialog[Chức năng].tsx`
- Đặt tên biến state theo dạng: `[tên]DialogOpen`

### 3.2. Quản lý state
- Sử dụng `useState` để quản lý trạng thái đóng/mở của dialog
- Đóng dialog sau khi xử lý xong

```tsx
const [openDialog, setOpenDialog] = useState(false);

const handleOpen = () => setOpenDialog(true);
const handleClose = () => setOpenDialog(false);
const handleSubmit = async () => {
  try {
    // Xử lý submit
    handleClose();
  } catch (error) {
    // Xử lý lỗi
  }
};
```

## 4. Best Practices

### 4.1. Performance
- Sử dụng `React.memo` cho các dialog phức tạp
- Chỉ hiển thị dialog khi cần thiết

### 4.2. Accessibility
- Thêm `aria-labelledby` và `aria-describedby`
- Đảm bảo có thể đóng bằng phím ESC
- Tự động focus vào phần tử đầu tiên khi mở dialog

## 5. Ví dụ thực tế

### 5.1. Dialog xóa
```tsx
const DeleteDialog = ({ open, onClose, onConfirm, itemName }) => (
  <BaseDialog
    open={open}
    onClose={onClose}
    title="Xác nhận xóa"
    maxWidth="sm"
    actions={{
      cancel: {
        label: 'Hủy',
        onClick: onClose,
        color: 'inherit'
      },
      submit: {
        label: 'Xóa',
        onClick: onConfirm,
        color: 'error',
        variant: 'contained',
        startIcon: <DeleteIcon />
      }
    }}
  >
    <Typography>
      Bạn có chắc chắn muốn xóa <strong>{itemName}</strong>? Hành động này không thể hoàn tác.
    </Typography>
  </BaseDialog>
);
```

### 5.2. Dialog thông báo
```tsx
const AlertDialog = ({ open, onClose, title, message, severity = 'info' }) => (
  <BaseDialog
    open={open}
    onClose={onClose}
    title={
      <Box display="flex" alignItems="center">
        {severity === 'error' && <ErrorOutline color="error" sx={{ mr: 1 }} />}
        {severity === 'success' && <CheckCircleOutline color="success" sx={{ mr: 1 }} />}
        {title}
      </Box>
    }
    maxWidth="sm"
    actions={{
      submit: {
        label: 'Đóng',
        onClick: onClose,
        color: severity === 'error' ? 'error' : 'primary'
      }
    }}
  >
    <Typography>{message}</Typography>
  </BaseDialog>
);
```

## 6. Ghi chú phát triển

- Luôn đảm bảo dialog có thể đóng được bằng cả nút đóng và phím ESC
- Kiểm tra kỹ focus management để đảm bảo trải nghiệm người dùng tốt
- Test trên nhiều kích thước màn hình khác nhau
