import { Column, Entity, JoinTable, ManyToMany, PrimaryGeneratedColumn, Relation } from 'typeorm';
import { AbstractEntity } from './abstract.entity';
import { RoleEntity } from './role.entity';

@Entity({ name: 'permissions' })
export class PermissionEntity extends AbstractEntity {
    @PrimaryGeneratedColumn('increment', { name: 'id', type: 'int', unsigned: true })
    id: number;

    @Column({ name: 'name', type: 'varchar', length: 255, nullable: false })
    name: string;

    @Column({ name: 'type', type: 'varchar', length: 255, nullable: true })
    type: string;

    @Column({ name: 'action', type: 'varchar', length: 255, nullable: false })
    action: string;

    @Column({ name: 'mo_ta', type: 'varchar', length: 1000, nullable: true })
    moTa?: string;

    @Column({ name: 'module_lien_quan', type: 'varchar', length: 50, nullable: true })
    moduleLienQuan?: string;

    @Column({ name: 'nhom_quyen_han', type: 'varchar', length: 50, nullable: true })
    nhomQuyenHan?: string;

    /* RELATION */
    @ManyToMany(() => RoleEntity, (roles) => roles.permissions, {
        onDelete: 'NO ACTION',
        onUpdate: 'CASCADE',
        createForeignKeyConstraints: false,
    })
    @JoinTable({
        name: 'roles_permissions',
        joinColumn: { name: 'permission_id', referencedColumnName: 'id' },
        inverseJoinColumn: { name: 'role_id', referencedColumnName: 'id' },
    })
    role: Relation<RoleEntity>[];
}
