/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { NhatKyHeThongEntity } from '~/database/typeorm/entities/nhatKyHeThong.entity';

@Injectable()
export class NhatKyHeThongRepository extends Repository<NhatKyHeThongEntity> {
    constructor(private dataSource: DataSource) {
        super(NhatKyHeThongEntity, dataSource.createEntityManager());
    }
}
