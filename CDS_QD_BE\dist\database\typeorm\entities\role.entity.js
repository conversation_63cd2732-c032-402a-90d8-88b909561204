"use strict";Object.defineProperty(exports,"__esModule",{value:true});Object.defineProperty(exports,"RoleEntity",{enumerable:true,get:function(){return RoleEntity}});const _typeorm=require("typeorm");const _permissionentity=require("./permission.entity");const _userentity=require("./user.entity");const _usersRoleentity=require("./usersRole.entity");const _abstractentity=require("./abstract.entity");function _ts_decorate(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect==="object"&&typeof Reflect.decorate==="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)if(d=decorators[i])r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r;return c>3&&r&&Object.defineProperty(target,key,r),r}function _ts_metadata(k,v){if(typeof Reflect==="object"&&typeof Reflect.metadata==="function")return Reflect.metadata(k,v)}let RoleEntity=class RoleEntity extends _abstractentity.AbstractEntity{};_ts_decorate([(0,_typeorm.PrimaryGeneratedColumn)("increment",{name:"id",type:"int",unsigned:true}),_ts_metadata("design:type",Number)],RoleEntity.prototype,"id",void 0);_ts_decorate([(0,_typeorm.Column)({name:"name",type:"varchar",length:255,nullable:false}),_ts_metadata("design:type",String)],RoleEntity.prototype,"name",void 0);_ts_decorate([(0,_typeorm.Column)({name:"description",type:"varchar",length:255,nullable:true}),_ts_metadata("design:type",String)],RoleEntity.prototype,"description",void 0);_ts_decorate([(0,_typeorm.ManyToMany)(()=>_permissionentity.PermissionEntity,permissions=>permissions.role,{onDelete:"NO ACTION",onUpdate:"CASCADE",createForeignKeyConstraints:false}),(0,_typeorm.JoinTable)({name:"roles_permissions",joinColumn:{name:"role_id",referencedColumnName:"id"},inverseJoinColumn:{name:"permission_id",referencedColumnName:"id"}}),_ts_metadata("design:type",Array)],RoleEntity.prototype,"permissions",void 0);_ts_decorate([(0,_typeorm.OneToMany)(()=>_userentity.UserEntity,users=>users.role,{createForeignKeyConstraints:false}),_ts_metadata("design:type",Array)],RoleEntity.prototype,"users",void 0);_ts_decorate([(0,_typeorm.OneToMany)(()=>_usersRoleentity.UsersRoleEntity,userRole=>userRole.role,{cascade:true,createForeignKeyConstraints:false}),_ts_metadata("design:type",typeof _typeorm.Relation==="undefined"?Object:_typeorm.Relation)],RoleEntity.prototype,"userRoles",void 0);RoleEntity=_ts_decorate([(0,_typeorm.Entity)({name:"roles"})],RoleEntity);
//# sourceMappingURL=role.entity.js.map