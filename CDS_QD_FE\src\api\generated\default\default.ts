/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 *  Swagger
 * The  API documents
 * OpenAPI spec version: 1.0
 */
import { useInfiniteQuery, useQuery } from '@tanstack/react-query'
import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseInfiniteQueryResult,
  DefinedUseQueryResult,
  InfiniteData,
  QueryClient,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseInfiniteQueryOptions,
  UseInfiniteQueryResult,
  UseQueryOptions,
  UseQueryResult
} from '@tanstack/react-query'

import type { AppControllerTestParams } from '.././model'

import { customInstance } from '../../mutator/custom-instance'
import type { ApiResponse } from '../../types'

export const appControllerTest = (params: AppControllerTestParams, signal?: AbortSignal) => {
  return customInstance<ApiResponse<void>>({ url: `/test`, method: 'GET', params, signal })
}

export const getAppControllerTestQueryKey = (params: AppControllerTestParams) => {
  return [`/test`, ...(params ? [params] : [])] as const
}

export const getAppControllerTestInfiniteQueryOptions = <
  TData = InfiniteData<Awaited<ReturnType<typeof appControllerTest>>, AppControllerTestParams['page']>,
  TError = unknown
>(
  params: AppControllerTestParams,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof appControllerTest>>,
        TError,
        TData,
        Awaited<ReturnType<typeof appControllerTest>>,
        QueryKey,
        AppControllerTestParams['page']
      >
    >
  }
) => {
  const { query: queryOptions } = options ?? {}

  const queryKey = queryOptions?.queryKey ?? getAppControllerTestQueryKey(params)

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof appControllerTest>>,
    QueryKey,
    AppControllerTestParams['page']
  > = ({ signal, pageParam }) => appControllerTest({ ...params, page: pageParam || params?.['page'] }, signal)

  return { queryKey, queryFn, staleTime: 10000, ...queryOptions } as UseInfiniteQueryOptions<
    Awaited<ReturnType<typeof appControllerTest>>,
    TError,
    TData,
    Awaited<ReturnType<typeof appControllerTest>>,
    QueryKey,
    AppControllerTestParams['page']
  > & { queryKey: DataTag<QueryKey, TData, TError> }
}

export type AppControllerTestInfiniteQueryResult = NonNullable<Awaited<ReturnType<typeof appControllerTest>>>
export type AppControllerTestInfiniteQueryError = unknown

export function useAppControllerTestInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof appControllerTest>>, AppControllerTestParams['page']>,
  TError = unknown
>(
  params: AppControllerTestParams,
  options: {
    query: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof appControllerTest>>,
        TError,
        TData,
        Awaited<ReturnType<typeof appControllerTest>>,
        QueryKey,
        AppControllerTestParams['page']
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof appControllerTest>>,
          TError,
          Awaited<ReturnType<typeof appControllerTest>>,
          QueryKey
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): DefinedUseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useAppControllerTestInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof appControllerTest>>, AppControllerTestParams['page']>,
  TError = unknown
>(
  params: AppControllerTestParams,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof appControllerTest>>,
        TError,
        TData,
        Awaited<ReturnType<typeof appControllerTest>>,
        QueryKey,
        AppControllerTestParams['page']
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof appControllerTest>>,
          TError,
          Awaited<ReturnType<typeof appControllerTest>>,
          QueryKey
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useAppControllerTestInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof appControllerTest>>, AppControllerTestParams['page']>,
  TError = unknown
>(
  params: AppControllerTestParams,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof appControllerTest>>,
        TError,
        TData,
        Awaited<ReturnType<typeof appControllerTest>>,
        QueryKey,
        AppControllerTestParams['page']
      >
    >
  },
  queryClient?: QueryClient
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }

export function useAppControllerTestInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof appControllerTest>>, AppControllerTestParams['page']>,
  TError = unknown
>(
  params: AppControllerTestParams,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof appControllerTest>>,
        TError,
        TData,
        Awaited<ReturnType<typeof appControllerTest>>,
        QueryKey,
        AppControllerTestParams['page']
      >
    >
  },
  queryClient?: QueryClient
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
  const queryOptions = getAppControllerTestInfiniteQueryOptions(params, options)

  const query = useInfiniteQuery(queryOptions, queryClient) as UseInfiniteQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>
  }

  query.queryKey = queryOptions.queryKey

  return query
}

export const getAppControllerTestQueryOptions = <
  TData = Awaited<ReturnType<typeof appControllerTest>>,
  TError = unknown
>(
  params: AppControllerTestParams,
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof appControllerTest>>, TError, TData>> }
) => {
  const { query: queryOptions } = options ?? {}

  const queryKey = queryOptions?.queryKey ?? getAppControllerTestQueryKey(params)

  const queryFn: QueryFunction<Awaited<ReturnType<typeof appControllerTest>>> = ({ signal }) =>
    appControllerTest(params, signal)

  return { queryKey, queryFn, staleTime: 10000, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof appControllerTest>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> }
}

export type AppControllerTestQueryResult = NonNullable<Awaited<ReturnType<typeof appControllerTest>>>
export type AppControllerTestQueryError = unknown

export function useAppControllerTest<TData = Awaited<ReturnType<typeof appControllerTest>>, TError = unknown>(
  params: AppControllerTestParams,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof appControllerTest>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof appControllerTest>>,
          TError,
          Awaited<ReturnType<typeof appControllerTest>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useAppControllerTest<TData = Awaited<ReturnType<typeof appControllerTest>>, TError = unknown>(
  params: AppControllerTestParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof appControllerTest>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof appControllerTest>>,
          TError,
          Awaited<ReturnType<typeof appControllerTest>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useAppControllerTest<TData = Awaited<ReturnType<typeof appControllerTest>>, TError = unknown>(
  params: AppControllerTestParams,
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof appControllerTest>>, TError, TData>> },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }

export function useAppControllerTest<TData = Awaited<ReturnType<typeof appControllerTest>>, TError = unknown>(
  params: AppControllerTestParams,
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof appControllerTest>>, TError, TData>> },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
  const queryOptions = getAppControllerTestQueryOptions(params, options)

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>
  }

  query.queryKey = queryOptions.queryKey

  return query
}
