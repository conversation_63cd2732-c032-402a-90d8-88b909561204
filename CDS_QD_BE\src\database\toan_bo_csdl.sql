-- SQL Schema generated from Markdown
-- Target: PostgreSQL

CREATE TABLE IF NOT EXISTS accounts (
    'id'          BIGSERIAL PRIMARY KEY,
    'username'   VARCHAR(255) NOT NULL,   -- username
    'password'        VA<PERSON>HA<PERSON>(255),            -- password
    'salt'         VARCHAR(255),            -- salt
    'secret_token'       VARCHAR(255),            -- secret_token
    'is_active'  BOOLEAN DEFAULT TRUE
);

CREATE TABLE IF NOT EXISTS users (
  'id' BIGSERIAL PRIMARY KEY,
  'account_id' BIGINT NOT NULL REFERENCES 'accounts(id)',
  'avatar_id' UUID REFERENCES  'du_lieu_tep_tins(id)',
  'ho_ten' VARCHAR(100) NOT NULL,
  'don_vi_id' BIGINT REFERENCES 'gia_tri_danh_mucs(id)',
  'email' VARCHAR(100) UNIQUE,
  'so_dien_thoai' VARCHAR(15),
  'quan_nhan_id' VARCHAR(20) UNIQUE REFERENCES 'quan_nhans(so_hieu_quan_nhan)',
  'trang_thai_dang_nhap_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)',
  'lan_dang_nhap_cuoi' TIMESTAMP WITH TIME ZONE,
  'so_lan_dang_nhap_sai' INTEGER DEFAULT 0,
  'thoi_gian_khoa_tai_khoan' TIMESTAMP WITH TIME ZONE,
  'yeu_cau_doi_mat_khau' BOOLEAN DEFAULT FALSE
);

COMMENT ON TABLE users IS 'Quản lý thông tin người dùng.';
COMMENT ON COLUMN users.'id' IS 'Khóa chính';
COMMENT ON COLUMN users.'ho_ten' IS 'Họ và tên đầy đủ của người dùng';
COMMENT ON COLUMN users.'don_vi_id' IS 'ID Đơn vị công tác của người dùng (tham chiếu 'gia_tri_danh_mucs' mã 'DMDONVI')';
COMMENT ON COLUMN users.'email' IS 'Địa chỉ email ';
COMMENT ON COLUMN users.'so_dien_thoai' IS 'Số điện thoại liên hệ';
COMMENT ON COLUMN users.'quan_nhan_id' IS 'Liên kết với hồ sơ quân nhân (nếu người dùng là quân nhân)';
COMMENT ON COLUMN users.'trang_thai_dang_nhap_id' IS 'ID Trạng thái tài khoản (tham chiếu 'gia_tri_danh_mucs' mã 'DMTRANGTHAITAIKHOAN')';
COMMENT ON COLUMN users.'lan_dang_nhap_cuoi' IS 'Thời điểm đăng nhập thành công lần cuối';
COMMENT ON COLUMN users.'so_lan_dang_nhap_sai' IS 'Số lần đăng nhập sai liên tiếp';
COMMENT ON COLUMN users.'thoi_gian_khoa_tai_khoan' IS 'Thời gian tài khoản bị khóa tạm thời đến';
COMMENT ON COLUMN users.'yeu_cau_doi_mat_khau' IS 'Cờ yêu cầu người dùng đổi mật khẩu ở lần đăng nhập tiếp theo';

-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS loai_danh_mucs (
  'id' BIGSERIAL PRIMARY KEY,
  'ma_danh_muc' VARCHAR(50) UNIQUE NOT NULL,
  'ten_danh_muc' VARCHAR(255) NOT NULL,
  'mo_ta' VARCHAR(500),
  'is_cau_truc_cay' BOOLEAN DEFAULT FALSE,
  'modules_su_dung' VARCHAR(255)
);

COMMENT ON TABLE loai_danh_mucs IS 'Định nghĩa các loại danh mục khác nhau trong hệ thống.';
COMMENT ON COLUMN loai_danh_mucs.'id' IS 'Khóa chính';
COMMENT ON COLUMN loai_danh_mucs.'ma_danh_muc' IS 'Mã duy nhất của loại danh mục';
COMMENT ON COLUMN loai_danh_mucs.'ten_danh_muc' IS 'Tên hiển thị của loại danh mục';
COMMENT ON COLUMN loai_danh_mucs.'mo_ta' IS 'Mô tả chi tiết về loại danh mục';
COMMENT ON COLUMN loai_danh_mucs.'is_cau_truc_cay' IS 'Đánh dấu nếu danh mục này có cấu trúc cây';
COMMENT ON COLUMN loai_danh_mucs.'modules_su_dung' IS 'Các module sử dụng danh mục này (ví dụ: "QLQN, CTD")';

-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS gia_tri_danh_mucs (
  'id' BIGSERIAL PRIMARY KEY,
  'danh_muc_id' BIGINT NOT NULL REFERENCES 'loai_danh_mucs(id)' ON DELETE CASCADE,
  'ma_gia_tri' VARCHAR(100) NOT NULL,
  'ten_gia_tri' VARCHAR(255) NOT NULL,
  'gia_tri_cha_id' BIGINT REFERENCES 'gia_tri_danh_mucs(id)' ON DELETE SET NULL,
  'mo_ta' VARCHAR(1000),
  'thu_tu_hien_thi' INTEGER DEFAULT 0,
  'con_hieu_luc' BOOLEAN DEFAULT TRUE,
  'thuoc_tinh_mo_rong' JSONB,
  UNIQUE (danh_muc_id, ma_gia_tri)
);

COMMENT ON TABLE gia_tri_danh_mucs IS 'Lưu trữ các giá trị cụ thể cho từng loại danh mục.';
COMMENT ON COLUMN gia_tri_danh_mucs.'id' IS 'Khóa chính';
COMMENT ON COLUMN gia_tri_danh_mucs.'danh_muc_id' IS 'Liên kết với loại danh mục';
COMMENT ON COLUMN gia_tri_danh_mucs.'ma_gia_tri' IS 'Mã giá trị (duy nhất trong cùng 'danh_muc_id')';
COMMENT ON COLUMN gia_tri_danh_mucs.'ten_gia_tri' IS 'Tên hiển thị của giá trị';
COMMENT ON COLUMN gia_tri_danh_mucs.'gia_tri_cha_id' IS 'ID của giá trị cha (nếu danh mục có cấu trúc cây)';
COMMENT ON COLUMN gia_tri_danh_mucs.'mo_ta' IS 'Mô tả';
COMMENT ON COLUMN gia_tri_danh_mucs.'thu_tu_hien_thi' IS 'Thứ tự hiển thị';
COMMENT ON COLUMN gia_tri_danh_mucs.'con_hieu_luc' IS 'Còn sử dụng hay không';
COMMENT ON COLUMN gia_tri_danh_mucs.'thuoc_tinh_mo_rong' IS 'Các thuộc tính mở rộng dưới dạng JSON';

-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS roles (
  'id' BIGSERIAL PRIMARY KEY,
  'ma_vai_tro' VARCHAR(50) UNIQUE NOT NULL,
  'ten_vai_tro' VARCHAR(100) NOT NULL,
  'mo_ta' VARCHAR(500),
  'is_vai_tro_he_thong' BOOLEAN DEFAULT FALSE,
);

COMMENT ON TABLE roles IS 'Quản lý các vai trò trong hệ thống.';
COMMENT ON COLUMN roles.'id' IS 'Khóa chính';
COMMENT ON COLUMN roles.'ma_vai_tro' IS 'Mã định danh duy nhất cho vai trò';
COMMENT ON COLUMN roles.'ten_vai_tro' IS 'Tên hiển thị của vai trò';
COMMENT ON COLUMN roles.'mo_ta' IS 'Mô tả chi tiết về vai trò';
COMMENT ON COLUMN roles.'is_vai_tro_he_thong' IS 'Đánh dấu vai trò hệ thống (không cho xóa/sửa)';

-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS permissions (
  'id' BIGSERIAL PRIMARY KEY,
  'ten_quyen_han' VARCHAR(255) NOT NULL,
  'mo_ta' VARCHAR(1000),  
  'module_lien_quan' VARCHAR(50),
  'nhom_quyen_han' VARCHAR(50)
);

COMMENT ON TABLE permissions IS 'Quản lý các quyền hạn chi tiết.';
COMMENT ON COLUMN permissions.'id' IS 'Khóa chính';
COMMENT ON COLUMN permissions.'ten_quyen_han' IS 'Tên hiển thị của quyền hạn';
COMMENT ON COLUMN permissions.'mo_ta' IS 'Mô tả chi tiết về quyền hạn';
COMMENT ON COLUMN permissions.'module_lien_quan' IS 'Module mà quyền hạn này thuộc về (ví dụ: "QLQN")';
COMMENT ON COLUMN permissions.'nhom_quyen_han' IS 'Nhóm chức năng liên quan (ví dụ: "HoSoQuanNhan")';

-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS users_roles (
  'user_id' BIGINT NOT NULL REFERENCES 'users(id)' ON DELETE CASCADE,
  'role_id' BIGINT NOT NULL REFERENCES 'roles(id)' ON DELETE CASCADE,
  PRIMARY KEY (user_id, role_id)
);

COMMENT ON TABLE users_roles IS 'Bảng gán vai trò cho người dùng.';
COMMENT ON COLUMN users_roles.'user_id' IS 'Khóa ngoại, tham chiếu người dùng';
COMMENT ON COLUMN users_roles.'role_id' IS 'Khóa ngoại, tham chiếu vai trò';
  
-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS roles_permissions (
  'role_id' BIGINT NOT NULL REFERENCES 'roles(id)' ON DELETE CASCADE,
  'permission_id' BIGINT NOT NULL REFERENCES 'permissions(id)' ON DELETE CASCADE,
  PRIMARY KEY (role_id, permission_id)
);

COMMENT ON TABLE roles_permissions IS 'Bảng gán quyền hạn cho vai trò.';
COMMENT ON COLUMN roles_permissions.'role_id' IS 'Khóa ngoại, tham chiếu vai trò';
COMMENT ON COLUMN roles_permissions.'permission_id' IS 'Khóa ngoại, tham chiếu quyền hạn';

-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS tham_so_he_thongs (
  'id' BIGSERIAL PRIMARY KEY,
  'ma_tham_so' VARCHAR(100) UNIQUE NOT NULL,
  'ten_tham_so' VARCHAR(255) NOT NULL,
  'gia_tri_tham_so' TEXT,
  'kieu_du_lieu' VARCHAR(50) NOT NULL DEFAULT 'TEXT',
  'mo_ta' VARCHAR(500),
  'sua_doi_boi_admin' BOOLEAN DEFAULT TRUE,
  'pham_vi_module' VARCHAR(50),
);

COMMENT ON TABLE tham_so_he_thongs IS 'Lưu trữ các tham số cấu hình hệ thống.';
COMMENT ON COLUMN tham_so_he_thongs.'id' IS 'Khóa chính';
COMMENT ON COLUMN tham_so_he_thongs.'ma_tham_so' IS 'Mã duy nhất của tham số';
COMMENT ON COLUMN tham_so_he_thongs.'ten_tham_so' IS 'Tên hiển thị của tham số';
COMMENT ON COLUMN tham_so_he_thongs.'gia_tri_tham_so' IS 'Giá trị của tham số';
COMMENT ON COLUMN tham_so_he_thongs.'kieu_du_lieu' IS 'Kiểu dữ liệu (TEXT, NUMBER, BOOLEAN, JSON)';
COMMENT ON COLUMN tham_so_he_thongs.'mo_ta' IS 'Mô tả chi tiết về tham số';
COMMENT ON COLUMN tham_so_he_thongs.'sua_doi_boi_admin' IS 'Cho phép quản trị viên sửa giá trị hay không';
COMMENT ON COLUMN tham_so_he_thongs.'pham_vi_module' IS 'Module mà tham số này áp dụng (nếu có)';


-- -----------------------------------------------------


CREATE TABLE IF NOT EXISTS quan_nhans (
  'so_hieu_quan_nhan' VARCHAR(20) PRIMARY KEY,
  'ho_ten_khai_sinh' VARCHAR(100) NOT NULL,
  'ten_thuong_dung' VARCHAR(100),
  'ngay_sinh' DATE NOT NULL,
  'gioi_tinh_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)',
  'so_cccd_cmtd' VARCHAR(20) UNIQUE,
  'ngay_nhap_ngu' DATE,
  'don_vi_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)',
  'cap_bac_hien_tai_id' BIGINT REFERENCES 'gia_tri_danh_mucs(id)',
  'chuc_vu_hien_tai_id' BIGINT REFERENCES 'gia_tri_danh_mucs(id)',
  'trang_thai_ho_so_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)',
  'anh_chan_dung_tep_tin_id' UUID REFERENCES 'du_lieu_tep_tins(id)'
  'email' VARCHAR(100) UNIQUE,
  'so_dien_thoai' VARCHAR(15),
);

COMMENT ON TABLE quan_nhans IS 'Thông tin chính của quân nhân.';
COMMENT ON COLUMN quan_nhans.'so_hieu_quan_nhan' IS 'Số hiệu quân nhân, định danh duy nhất';
COMMENT ON COLUMN quan_nhans.'ho_ten_khai_sinh' IS 'Họ và tên khai sinh';
COMMENT ON COLUMN quan_nhans.'ten_thuong_dung' IS 'Tên thường dùng';
COMMENT ON COLUMN quan_nhans.'ngay_sinh' IS 'Ngày sinh';
COMMENT ON COLUMN quan_nhans.'gioi_tinh_id' IS 'ID Giới tính (tham chiếu 'gia_tri_danh_mucs' mã 'DMGIOITINH')';
COMMENT ON COLUMN quan_nhans.'so_cccd_cmtd' IS 'Số Căn cước công dân/Chứng minh thư quân đội';
COMMENT ON COLUMN quan_nhans.'ngay_nhap_ngu' IS 'Ngày nhập ngũ';
COMMENT ON COLUMN quan_nhans.'don_vi_id' IS 'ID Đơn vị hiện tại (tham chiếu 'gia_tri_danh_mucs' mã 'DMDONVI')';
COMMENT ON COLUMN quan_nhans.'cap_bac_hien_tai_id' IS 'ID Cấp bậc hiện tại (tham chiếu 'gia_tri_danh_mucs' mã 'DMCAPBAC')';
COMMENT ON COLUMN quan_nhans.'chuc_vu_hien_tai_id' IS 'ID Chức vụ hiện tại (tham chiếu 'gia_tri_danh_mucs' mã 'DMCHUCVU')';
COMMENT ON COLUMN quan_nhans.'trang_thai_ho_so_id' IS 'ID Trạng thái hồ sơ (tham chiếu 'gia_tri_danh_mucs' mã 'DMTRANGTHAIHS')';
COMMENT ON COLUMN quan_nhans.'anh_chan_dung_tep_tin_id' IS 'Đường dẫn đến ảnh chân dung (path trên Supabase Storage)';
COMMENT ON COLUMN quan_nhans.'email' IS 'Địa chỉ email';
COMMENT ON COLUMN quan_nhans.'so_dien_thoai' IS 'Số điện thoại';

-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS ly_lich_can_nhans (
  'quan_nhan_id' VARCHAR(20) PRIMARY KEY REFERENCES 'quan_nhans(so_hieu_quan_nhan)' ON DELETE CASCADE,
  'que_quan_xa_id' BIGINT REFERENCES 'gia_tri_danh_mucs(id)',
  'que_quan_huyen_id' BIGINT REFERENCES 'gia_tri_danh_mucs(id)',
  'que_quan_tinh_id' BIGINT REFERENCES 'gia_tri_danh_mucs(id)',
  'noi_o_hien_nay_xa_id' BIGINT REFERENCES 'gia_tri_danh_mucs(id)',
  'noi_o_hien_nay_huyen_id' BIGINT REFERENCES 'gia_tri_danh_mucs(id)',
  'noi_o_hien_nay_tinh_id' BIGINT REFERENCES 'gia_tri_danh_mucs(id)',
  'dan_toc_id' BIGINT REFERENCES 'gia_tri_danh_mucs(id)',
  'ton_giao_id' BIGINT REFERENCES 'gia_tri_danh_mucs(id)',
  'thanh_phan_gia_dinh_id' BIGINT REFERENCES 'gia_tri_danh_mucs(id)',
  'nghe_nghiep_truoc_nhap_ngu_id' BIGINT REFERENCES 'gia_tri_danh_mucs(id)',
  'trinh_do_van_hoa_pho_thong' VARCHAR(50),
  'trinh_do_ly_luan_chinh_tri_id' BIGINT REFERENCES 'gia_tri_danh_mucs(id)',
  'trinh_do_ngoai_ngu' VARCHAR(255),
  'trinh_do_tin_hoc' VARCHAR(255),
  'nang_khieu_so_truong' TEXT,
  'tinh_hinh_chinh_tri_lich_su_ban_than' TEXT,
  'ngay_vao_doan' DATE,
  'ngay_vao_dang_chinh_thuc' DATE,
  'so_ho_khau' VARCHAR(50),
  'tinh_trang_hon_nhan_id' BIGINT REFERENCES 'gia_tri_danh_mucs(id)',
  'ghi_chu' TEXT,
);

COMMENT ON TABLE ly_lich_can_nhans IS 'Lý lịch cá nhân chi tiết của quân nhân.';
COMMENT ON COLUMN ly_lich_can_nhans.'quan_nhan_id' IS 'Liên kết với quân nhân';
COMMENT ON COLUMN ly_lich_can_nhans.'que_quan_xa_id' IS 'Quê quán -- Cấp xã/phường (tham chiếu 'gia_tri_danh_mucs' mã 'DMDIACHINHHANHCHINH')';
COMMENT ON COLUMN ly_lich_can_nhans.'que_quan_huyen_id' IS 'Quê quán -- Cấp quận/huyện (tham chiếu 'gia_tri_danh_mucs' mã 'DMDIACHINHHANHCHINH')';
COMMENT ON COLUMN ly_lich_can_nhans.'que_quan_tinh_id' IS 'Quê quán -- Cấp tỉnh/thành phố (tham chiếu 'gia_tri_danh_mucs' mã 'DMDIACHINHHANHCHINH')';
COMMENT ON COLUMN ly_lich_can_nhans.'noi_o_hien_nay_xa_id' IS 'Nơi ở hiện nay -- Cấp xã/phường';
COMMENT ON COLUMN ly_lich_can_nhans.'noi_o_hien_nay_huyen_id' IS 'Nơi ở hiện nay -- Cấp quận/huyện';
COMMENT ON COLUMN ly_lich_can_nhans.'noi_o_hien_nay_tinh_id' IS 'Nơi ở hiện nay -- Cấp tỉnh/thành phố';
COMMENT ON COLUMN ly_lich_can_nhans.'dan_toc_id' IS 'ID Dân tộc (tham chiếu 'gia_tri_danh_mucs' mã 'DMDANTOC')';
COMMENT ON COLUMN ly_lich_can_nhans.'ton_giao_id' IS 'ID Tôn giáo (tham chiếu 'gia_tri_danh_mucs' mã 'DMTONGIAO')';
COMMENT ON COLUMN ly_lich_can_nhans.'thanh_phan_gia_dinh_id' IS 'ID Thành phần gia đình khi nhập ngũ (tham chiếu 'gia_tri_danh_mucs' mã 'DMTHANHPHANGD')';
COMMENT ON COLUMN ly_lich_can_nhans.'nghe_nghiep_truoc_nhap_ngu_id' IS 'ID Nghề nghiệp trước khi nhập ngũ (tham chiếu 'gia_tri_danh_mucs' mã 'DMNGHENGHIEP')';
COMMENT ON COLUMN ly_lich_can_nhans.'trinh_do_van_hoa_pho_thong' IS 'Trình độ văn hóa phổ thông (ví dụ: 12/12)';
COMMENT ON COLUMN ly_lich_can_nhans.'trinh_do_ly_luan_chinh_tri_id' IS 'ID Trình độ lý luận chính trị (tham chiếu 'gia_tri_danh_mucs' mã 'DMTRINHDOLLCT')';
COMMENT ON COLUMN ly_lich_can_nhans.'trinh_do_ngoai_ngu' IS 'Trình độ ngoại ngữ';
COMMENT ON COLUMN ly_lich_can_nhans.'trinh_do_tin_hoc' IS 'Trình độ tin học';
COMMENT ON COLUMN ly_lich_can_nhans.'nang_khieu_so_truong' IS 'Năng khiếu, sở trường';
COMMENT ON COLUMN ly_lich_can_nhans.'tinh_hinh_chinh_tri_lich_su_ban_than' IS 'Đặc điểm lịch sử bản thân';
COMMENT ON COLUMN ly_lich_can_nhans.'ngay_vao_doan' IS 'Ngày vào Đoàn TNCS Hồ Chí Minh';
COMMENT ON COLUMN ly_lich_can_nhans.'ngay_vao_dang_chinh_thuc' IS 'Ngày vào Đảng chính thức (đồng bộ từ module Công tác Đảng)';
COMMENT ON COLUMN ly_lich_can_nhans.'so_ho_khau' IS 'Số sổ hộ khẩu';
COMMENT ON COLUMN ly_lich_can_nhans.'tinh_trang_hon_nhan_id' IS 'ID Tình trạng hôn nhân (tham chiếu 'gia_tri_danh_mucs' mã 'DMTINHTRANGHN')';
COMMENT ON COLUMN ly_lich_can_nhans.'ghi_chu' IS 'Ghi chú thêm về lý lịch';

-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS qua_trinh_cong_tacs (
  'id' BIGSERIAL PRIMARY KEY,
  'quan_nhan_id' VARCHAR(20) NOT NULL REFERENCES 'quan_nhans(so_hieu_quan_nhan)' ON DELETE CASCADE,
  'quyet_dinh_so' VARCHAR(50),
  'ngay_quyet_dinh' DATE,
  'co_quan_ra_quyet_dinh_id' BIGINT REFERENCES 'gia_tri_danh_mucs(id)',
  'loai_quyet_dinh_cong_tac_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)',
  'don_vi_cong_tac_id' BIGINT REFERENCES 'gia_tri_danh_mucs(id)',
  'chuc_vu_dam_nhiem_id' BIGINT REFERENCES 'gia_tri_danh_mucs(id)',
  'cap_bac_khi_dam_nhiem_id' BIGINT REFERENCES 'gia_tri_danh_mucs(id)',
  'thoi_gian_bat_dau' DATE NOT NULL,
  'thoi_gian_ket_thuc' DATE,
  'mo_ta_cong_viec_chinh' TEXT,
  'tap_tin_dinh_kem_id' UUID REFERENCES 'du_lieu_tep_tins(id)',
  'ghi_chu' TEXT,
);

COMMENT ON TABLE qua_trinh_cong_tacs IS 'Quá trình công tác của quân nhân.';
COMMENT ON COLUMN qua_trinh_cong_tacs.'id' IS 'Khóa chính';
COMMENT ON COLUMN qua_trinh_cong_tacs.'quan_nhan_id' IS 'Liên kết với quân nhân';
COMMENT ON COLUMN qua_trinh_cong_tacs.'quyet_dinh_so' IS 'Số hiệu quyết định';
COMMENT ON COLUMN qua_trinh_cong_tacs.'ngay_quyet_dinh' IS 'Ngày ra quyết định';
COMMENT ON COLUMN qua_trinh_cong_tacs.'co_quan_ra_quyet_dinh_id' IS 'ID Cơ quan/đơn vị ra quyết định (tham chiếu 'gia_tri_danh_mucs' mã 'DMCOQUANQD')';
COMMENT ON COLUMN qua_trinh_cong_tacs.'loai_quyet_dinh_cong_tac_id' IS 'ID Loại quyết định công tác (tham chiếu 'gia_tri_danh_mucs' mã 'DMLOAIQD_CT')';
COMMENT ON COLUMN qua_trinh_cong_tacs.'don_vi_cong_tac_id' IS 'ID Đơn vị công tác trong giai đoạn này (tham chiếu 'gia_tri_danh_mucs' mã 'DMDONVI')';
COMMENT ON COLUMN qua_trinh_cong_tacs.'chuc_vu_dam_nhiem_id' IS 'ID Chức vụ đảm nhiệm (tham chiếu 'gia_tri_danh_mucs' mã 'DMCHUCVU')';
COMMENT ON COLUMN qua_trinh_cong_tacs.'cap_bac_khi_dam_nhiem_id' IS 'ID Cấp bậc khi đảm nhiệm (tham chiếu 'gia_tri_danh_mucs' mã 'DMCAPBAC')';
COMMENT ON COLUMN qua_trinh_cong_tacs.'thoi_gian_bat_dau' IS 'Thời gian bắt đầu';
COMMENT ON COLUMN qua_trinh_cong_tacs.'thoi_gian_ket_thuc' IS 'Thời gian kết thúc';
COMMENT ON COLUMN qua_trinh_cong_tacs.'mo_ta_cong_viec_chinh' IS 'Mô tả công việc chính';
COMMENT ON COLUMN qua_trinh_cong_tacs.'tap_tin_dinh_kem_id' IS 'ID Tệp đính kèm quyết định (tham chiếu 'du_lieu_tep_tins')';
COMMENT ON COLUMN qua_trinh_cong_tacs.'ghi_chu' IS 'Ghi chú';

-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS qua_trinh_dao_taos (
  'id' BIGSERIAL PRIMARY KEY,
  'quan_nhan_id' VARCHAR(20) NOT NULL REFERENCES 'quan_nhans(so_hieu_quan_nhan)' ON DELETE CASCADE,
  'ten_khoa_hoc' VARCHAR(255) NOT NULL,
  'hinh_thuc_dao_tao_id' BIGINT REFERENCES 'gia_tri_danh_mucs(id)',
  'chuyen_nganh_dao_tao_id' BIGINT REFERENCES 'gia_tri_danh_mucs(id)',
  'co_so_dao_tao_id' BIGINT REFERENCES 'gia_tri_danh_mucs(id)',
  'nuoc_dao_tao_id' BIGINT REFERENCES 'gia_tri_danh_mucs(id)',
  'thoi_gian_bat_dau' DATE,
  'thoi_gian_ket_thuc' DATE,
  'ket_qua_dao_tao' VARCHAR(255),
  'van_bang_chung_chi_id' BIGINT REFERENCES 'gia_tri_danh_mucs(id)',
  'xep_loai_tot_nghiep_id' BIGINT REFERENCES 'gia_tri_danh_mucs(id)',
  'tap_tin_dinh_kem_id' UUID REFERENCES 'du_lieu_tep_tins(id)',
  'ghi_chu' TEXT,
);

COMMENT ON TABLE qua_trinh_dao_taos IS 'Quá trình đào tạo, bồi dưỡng của quân nhân.';
COMMENT ON COLUMN qua_trinh_dao_taos.'id' IS 'Khóa chính';
COMMENT ON COLUMN qua_trinh_dao_taos.'quan_nhan_id' IS 'Liên kết với quân nhân';
COMMENT ON COLUMN qua_trinh_dao_taos.'ten_khoa_hoc' IS 'Tên khóa học/lớp học';
COMMENT ON COLUMN qua_trinh_dao_taos.'hinh_thuc_dao_tao_id' IS 'ID Hình thức đào tạo (tham chiếu 'gia_tri_danh_mucs' mã 'DMHINHTHUCDT')';
COMMENT ON COLUMN qua_trinh_dao_taos.'chuyen_nganh_dao_tao_id' IS 'ID Chuyên ngành đào tạo (tham chiếu 'gia_tri_danh_mucs' mã 'DMCHUYENNGANHDT')';
COMMENT ON COLUMN qua_trinh_dao_taos.'co_so_dao_tao_id' IS 'ID Cơ sở đào tạo (tham chiếu 'gia_tri_danh_mucs' mã 'DMCOSODT')';
COMMENT ON COLUMN qua_trinh_dao_taos.'nuoc_dao_tao_id' IS 'ID Nước đào tạo (tham chiếu 'gia_tri_danh_mucs' mã 'DMQUOCGIA')';
COMMENT ON COLUMN qua_trinh_dao_taos.'thoi_gian_bat_dau' IS 'Thời gian bắt đầu';
COMMENT ON COLUMN qua_trinh_dao_taos.'thoi_gian_ket_thuc' IS 'Thời gian kết thúc';
COMMENT ON COLUMN qua_trinh_dao_taos.'ket_qua_dao_tao' IS 'Kết quả đào tạo (ví dụ: Giỏi, Khá)';
COMMENT ON COLUMN qua_trinh_dao_taos.'van_bang_chung_chi_id' IS 'ID Loại văn bằng/chứng chỉ (tham chiếu 'gia_tri_danh_mucs' mã 'DMLOAIVBCC')';
COMMENT ON COLUMN qua_trinh_dao_taos.'xep_loai_tot_nghiep_id' IS 'ID Xếp loại tốt nghiệp (tham chiếu 'gia_tri_danh_mucs' mã 'DMXLTN')';
COMMENT ON COLUMN qua_trinh_dao_taos.'tap_tin_dinh_kem_id' IS 'ID Tệp đính kèm văn bằng/chứng chỉ';
COMMENT ON COLUMN qua_trinh_dao_taos.'ghi_chu' IS 'Ghi chú';


-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS de_xuat_khen_thuongs (
  'id' BIGSERIAL PRIMARY KEY,
  'quan_nhan_khen_thuong_id' VARCHAR(20) REFERENCES 'quan_nhans(so_hieu_quan_nhan)',
  'don_vi_khen_thuong_id' BIGINT REFERENCES 'gia_tri_danh_mucs(id)',
  'ly_do_de_xuat' TEXT NOT NULL,
  'hinh_thuc_khen_thuong_de_xuat_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)',
  'nguoi_de_xuat_id' BIGINT NOT NULL REFERENCES 'users(id)',
  'don_vi_de_xuat_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)',
  'ngay_de_xuat' TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  'trang_thai_phe_duyet_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)',
  'to_trinh_dinh_kem_id' UUID REFERENCES 'du_lieu_tep_tins(id)',
  'ghi_chu' TEXT,
);

COMMENT ON TABLE de_xuat_khen_thuongs IS 'Các đề xuất khen thưởng cho quân nhân hoặc đơn vị.';
COMMENT ON COLUMN de_xuat_khen_thuongs.'id' IS 'Khóa chính';
COMMENT ON COLUMN de_xuat_khen_thuongs.'quan_nhan_khen_thuong_id' IS 'ID Quân nhân được đề xuất khen thưởng (nếu là cá nhân)';
COMMENT ON COLUMN de_xuat_khen_thuongs.'don_vi_khen_thuong_id' IS 'ID Đơn vị được đề xuất khen thưởng (nếu là tập thể, tham chiếu 'DMDONVI')';
COMMENT ON COLUMN de_xuat_khen_thuongs.'ly_do_de_xuat' IS 'Lý do/Thành tích đề xuất khen thưởng';
COMMENT ON COLUMN de_xuat_khen_thuongs.'hinh_thuc_khen_thuong_de_xuat_id' IS 'ID Hình thức khen thưởng đề xuất (tham chiếu 'gia_tri_danh_mucs' mã 'DMHINHTHUCKT')';
COMMENT ON COLUMN de_xuat_khen_thuongs.'nguoi_de_xuat_id' IS 'Người tạo đề xuất';
COMMENT ON COLUMN de_xuat_khen_thuongs.'don_vi_de_xuat_id' IS 'Đơn vị của người đề xuất (tham chiếu 'gia_tri_danh_mucs' mã 'DMDONVI')';
COMMENT ON COLUMN de_xuat_khen_thuongs.'ngay_de_xuat' IS 'Ngày tạo đề xuất';
COMMENT ON COLUMN de_xuat_khen_thuongs.'trang_thai_phe_duyet_id' IS 'ID Trạng thái phê duyệt (tham chiếu 'gia_tri_danh_mucs' mã 'DMTRANGTHAIPD')';
COMMENT ON COLUMN de_xuat_khen_thuongs.'to_trinh_dinh_kem_id' IS 'ID Tờ trình đính kèm';
COMMENT ON COLUMN de_xuat_khen_thuongs.'ghi_chu' IS 'Ghi chú';


-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS quyet_dinh_khen_thuongs (
  'id' BIGSERIAL PRIMARY KEY,
  'de_xuat_khen_thuong_id' BIGINT REFERENCES 'de_xuat_khen_thuongs(id)',
  'quan_nhan_khen_thuong_id' VARCHAR(20) REFERENCES 'quan_nhans(so_hieu_quan_nhan)',
  'don_vi_khen_thuong_id' BIGINT REFERENCES 'gia_tri_danh_mucs(id)',
  'so_quyet_dinh' VARCHAR(50) NOT NULL,
  'ngay_quyet_dinh' DATE NOT NULL,
  'cap_ra_quyet_dinh_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)',
  'hinh_thuc_khen_thuong_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)',
  'ly_do_khen_thuong' TEXT,
  'tap_tin_quyet_dinh_id' UUID REFERENCES 'du_lieu_tep_tins(id)',
);

COMMENT ON TABLE quyet_dinh_khen_thuongs IS 'Các quyết định khen thưởng.';
COMMENT ON COLUMN quyet_dinh_khen_thuongs.'id' IS 'Khóa chính';
COMMENT ON COLUMN quyet_dinh_khen_thuongs.'de_xuat_khen_thuong_id' IS 'Liên kết với đề xuất khen thưởng (nếu có)';
COMMENT ON COLUMN quyet_dinh_khen_thuongs.'quan_nhan_khen_thuong_id' IS 'ID Quân nhân được khen thưởng (nếu là cá nhân)';
COMMENT ON COLUMN quyet_dinh_khen_thuongs.'don_vi_khen_thuong_id' IS 'ID Đơn vị được khen thưởng (nếu là tập thể, tham chiếu 'DMDONVI')';
COMMENT ON COLUMN quyet_dinh_khen_thuongs.'so_quyet_dinh' IS 'Số hiệu quyết định khen thưởng';
COMMENT ON COLUMN quyet_dinh_khen_thuongs.'ngay_quyet_dinh' IS 'Ngày ra quyết định';
COMMENT ON COLUMN quyet_dinh_khen_thuongs.'cap_ra_quyet_dinh_id' IS 'ID Cấp ra quyết định (tham chiếu 'gia_tri_danh_mucs' mã 'DMCAPRAQD')';
COMMENT ON COLUMN quyet_dinh_khen_thuongs.'hinh_thuc_khen_thuong_id' IS 'ID Hình thức khen thưởng (tham chiếu 'gia_tri_danh_mucs' mã 'DMHINHTHUCKT')';
COMMENT ON COLUMN quyet_dinh_khen_thuongs.'ly_do_khen_thuong' IS 'Lý do khen thưởng';
COMMENT ON COLUMN quyet_dinh_khen_thuongs.'tap_tin_quyet_dinh_id' IS 'ID Tệp đính kèm quyết định';

-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS ho_so_vu_viec_ky_luats (
  'id' BIGSERIAL PRIMARY KEY,
  'quan_nhan_vi_pham_id' VARCHAR(20) NOT NULL REFERENCES 'quan_nhans(so_hieu_quan_nhan)',
  'don_vi_vi_pham_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)',
  'ngay_ghi_nhan' DATE NOT NULL,
  'noi_dung_vi_pham' TEXT NOT NULL,
  'bang_chung_ban_dau' TEXT,
  'trang_thai_xu_ly_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)',
  'nguoi_ghi_nhan_id' BIGINT NOT NULL REFERENCES 'users(id)',
);

COMMENT ON TABLE ho_so_vu_viec_ky_luats IS 'Hồ sơ các vụ việc cần xem xét kỷ luật.';
COMMENT ON COLUMN ho_so_vu_viec_ky_luats.'id' IS 'Khóa chính';
COMMENT ON COLUMN ho_so_vu_viec_ky_luats.'quan_nhan_vi_pham_id' IS 'ID Quân nhân vi phạm';
COMMENT ON COLUMN ho_so_vu_viec_ky_luats.'don_vi_vi_pham_id' IS 'Đơn vị của quân nhân tại thời điểm vi phạm (tham chiếu 'DMDONVI')';
COMMENT ON COLUMN ho_so_vu_viec_ky_luats.'ngay_ghi_nhan' IS 'Ngày ghi nhận/phát hiện vụ việc';
COMMENT ON COLUMN ho_so_vu_viec_ky_luats.'noi_dung_vi_pham' IS 'Nội dung, hành vi vi phạm';
COMMENT ON COLUMN ho_so_vu_viec_ky_luats.'bang_chung_ban_dau' IS 'Bằng chứng ban đầu (nếu có)';
COMMENT ON COLUMN ho_so_vu_viec_ky_luats.'trang_thai_xu_ly_id' IS 'ID Trạng thái xử lý vụ việc (tham chiếu 'gia_tri_danh_mucs' mã 'DMTRANGTHAIXLVV')';
COMMENT ON COLUMN ho_so_vu_viec_ky_luats.'nguoi_ghi_nhan_id' IS 'Người ghi nhận vụ việc';

-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS bien_ban_hoi_dong_ky_luats (
  'id' BIGSERIAL PRIMARY KEY,
  'ho_so_vu_viec_ky_luat_id' BIGINT NOT NULL REFERENCES 'ho_so_vu_viec_ky_luats(id)' ON DELETE CASCADE,
  'ngay_hop' DATE NOT NULL,
  'dia_diem_hop' VARCHAR(255),
  'thanh_phan_tham_gia' TEXT,
  'noi_dung_hop' TEXT,
  'ket_luan_hoi_dong' TEXT,
  'hinh_thuc_ky_luat_id' BIGINT REFERENCES 'gia_tri_danh_mucs(id)',
  'tap_tin_bien_ban_id' UUID REFERENCES 'du_lieu_tep_tins(id)',
);

COMMENT ON TABLE bien_ban_hoi_dong_ky_luats IS 'Biên bản các cuộc họp hội đồng kỷ luật.';
COMMENT ON COLUMN bien_ban_hoi_dong_ky_luats.'id' IS 'Khóa chính';
COMMENT ON COLUMN bien_ban_hoi_dong_ky_luats.'ho_so_vu_viec_ky_luat_id' IS 'Liên kết với hồ sơ vụ việc kỷ luật';
COMMENT ON COLUMN bien_ban_hoi_dong_ky_luats.'ngay_hop' IS 'Ngày họp hội đồng';
COMMENT ON COLUMN bien_ban_hoi_dong_ky_luats.'dia_diem_hop' IS 'Địa điểm họp';
COMMENT ON COLUMN bien_ban_hoi_dong_ky_luats.'thanh_phan_tham_gia' IS 'Thành phần tham gia hội đồng';
COMMENT ON COLUMN bien_ban_hoi_dong_ky_luats.'noi_dung_hop' IS 'Nội dung diễn biến cuộc họp';
COMMENT ON COLUMN bien_ban_hoi_dong_ky_luats.'ket_luan_hoi_dong' IS 'Kết luận của hội đồng';
COMMENT ON COLUMN bien_ban_hoi_dong_ky_luats.'hinh_thuc_ky_luat_id' IS 'ID Hình thức kỷ luật hội đồng đề nghị (tham chiếu 'gia_tri_danh_mucs' mã 'DMHINHTHUCKYLUAT')';
COMMENT ON COLUMN bien_ban_hoi_dong_ky_luats.'tap_tin_bien_ban_id' IS 'ID Tệp đính kèm biên bản';

-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS tai_lieu_vu_viec_ky_luats (
  'id' BIGSERIAL PRIMARY KEY,
  'ho_so_vu_viec_ky_luat_id' BIGINT NOT NULL REFERENCES 'ho_so_vu_viec_ky_luats(id)' ON DELETE CASCADE,
  'ten_tai_lieu' VARCHAR(255) NOT NULL,
  'mo_ta' TEXT,
  'tap_tin_dinh_kem_id' UUID NOT NULL REFERENCES 'du_lieu_tep_tins(id)',
);

COMMENT ON TABLE tai_lieu_vu_viec_ky_luats IS 'Tài liệu đính kèm cho vụ việc kỷ luật.';
COMMENT ON COLUMN tai_lieu_vu_viec_ky_luats.'id' IS 'Khóa chính';
COMMENT ON COLUMN tai_lieu_vu_viec_ky_luats.'ho_so_vu_viec_ky_luat_id' IS 'Liên kết với hồ sơ vụ việc kỷ luật';
COMMENT ON COLUMN tai_lieu_vu_viec_ky_luats.'ten_tai_lieu' IS 'Tên tài liệu';
COMMENT ON COLUMN tai_lieu_vu_viec_ky_luats.'mo_ta' IS 'Mô tả tài liệu';
COMMENT ON COLUMN tai_lieu_vu_viec_ky_luats.'tap_tin_dinh_kem_id' IS 'ID Tệp đính kèm';

-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS quyet_dinh_ky_luats (
  'id' BIGSERIAL PRIMARY KEY,
  'ho_so_vu_viec_ky_luat_id' BIGINT REFERENCES 'ho_so_vu_viec_ky_luats(id)',
  'quan_nhan_ky_luat_id' VARCHAR(20) NOT NULL REFERENCES 'quan_nhans(so_hieu_quan_nhan)',
  'so_quyet_dinh' VARCHAR(50) NOT NULL,
  'ngay_quyet_dinh' DATE NOT NULL,
  'cap_ra_quyet_dinh_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)',
  'hinh_thuc_ky_luat_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)',
  'ly_do_ky_luat' TEXT,
  'thoi_han_ky_luat' VARCHAR(100),
  'tap_tin_quyet_dinh_id' UUID REFERENCES 'du_lieu_tep_tins(id)',
);

COMMENT ON TABLE quyet_dinh_ky_luats IS 'Các quyết định kỷ luật.';
COMMENT ON COLUMN quyet_dinh_ky_luats.'id' IS 'Khóa chính';
COMMENT ON COLUMN quyet_dinh_ky_luats.'ho_so_vu_viec_ky_luat_id' IS 'Liên kết với hồ sơ vụ việc (nếu có)';
COMMENT ON COLUMN quyet_dinh_ky_luats.'quan_nhan_ky_luat_id' IS 'ID Quân nhân bị kỷ luật';
COMMENT ON COLUMN quyet_dinh_ky_luats.'so_quyet_dinh' IS 'Số hiệu quyết định kỷ luật';
COMMENT ON COLUMN quyet_dinh_ky_luats.'ngay_quyet_dinh' IS 'Ngày ra quyết định';
COMMENT ON COLUMN quyet_dinh_ky_luats.'cap_ra_quyet_dinh_id' IS 'ID Cấp ra quyết định (tham chiếu 'gia_tri_danh_mucs' mã 'DMCAPRAQD')';
COMMENT ON COLUMN quyet_dinh_ky_luats.'hinh_thuc_ky_luat_id' IS 'ID Hình thức kỷ luật (tham chiếu 'gia_tri_danh_mucs' mã 'DMHINHTHUCKYLUAT')';
COMMENT ON COLUMN quyet_dinh_ky_luats.'ly_do_ky_luat' IS 'Lý do kỷ luật';
COMMENT ON COLUMN quyet_dinh_ky_luats.'thoi_han_ky_luat' IS 'Thời hạn kỷ luật (nếu có)';
COMMENT ON COLUMN quyet_dinh_ky_luats.'tap_tin_quyet_dinh_id' IS 'ID Tệp đính kèm quyết định';


-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS ho_so_suc_khoes (
  'id' BIGSERIAL PRIMARY KEY,
  'quan_nhan_id' VARCHAR(20) NOT NULL REFERENCES 'quan_nhans(so_hieu_quan_nhan)' ON DELETE CASCADE,
  'ngay_kham' DATE NOT NULL,
  'loai_kiem_tra_id' BIGINT REFERENCES 'gia_tri_danh_mucs(id)',
  'ket_luan_suc_khoe' TEXT,
  'phan_loai_suc_khoe_id' BIGINT REFERENCES 'gia_tri_danh_mucs(id)',
  'ghi_chu' TEXT,
  'tap_tin_dinh_kem_id' UUID REFERENCES 'du_lieu_tep_tins(id)'
);

COMMENT ON TABLE ho_so_suc_khoes IS 'Thông tin theo dõi sức khỏe của quân nhân.';
COMMENT ON COLUMN ho_so_suc_khoes.'id' IS 'Khóa chính';
COMMENT ON COLUMN ho_so_suc_khoes.'quan_nhan_id' IS 'Liên kết với quân nhân';
COMMENT ON COLUMN ho_so_suc_khoes.'ngay_kham' IS 'Ngày khám sức khỏe';
COMMENT ON COLUMN ho_so_suc_khoes.'loai_kiem_tra_id' IS 'ID Loại kiểm tra sức khỏe (tham chiếu ''gia_tri_danh_mucs'' mã ''DMLOAIKTSK'')';
COMMENT ON COLUMN ho_so_suc_khoes.'ket_luan_suc_khoe' IS 'Kết luận chung về tình hình sức khỏe';
COMMENT ON COLUMN ho_so_suc_khoes.'phan_loai_suc_khoe_id' IS 'ID Phân loại sức khỏe (tham chiếu ''gia_tri_danh_mucs'' mã ''DMPHANLOAISK'')';
COMMENT ON COLUMN ho_so_suc_khoes.'ghi_chu' IS 'Ghi chú thêm';
COMMENT ON COLUMN ho_so_suc_khoes.'tap_tin_dinh_kem_id' IS 'ID Tệp đính kèm kết quả khám';

-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS quan_he_gia_dinhs (  
  'id' BIGSERIAL PRIMARY KEY,
  'quan_nhan_id' VARCHAR(20) NOT NULL REFERENCES 'quan_nhans(so_hieu_quan_nhan)' ON DELETE CASCADE,
  'ho_ten_nguoi_than' VARCHAR(100) NOT NULL,
  'moi_quan_he_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)',
  'nam_sinh' INTEGER,
  'nghe_nghiep' VARCHAR(255),
  'dia_chi_lien_lac' VARCHAR(500),
  'so_dien_thoai' VARCHAR(15),
  'ghi_chu' TEXT,
);

COMMENT ON TABLE quan_he_gia_dinhs IS 'Thông tin về các mối quan hệ gia đình của quân nhân.';
COMMENT ON COLUMN quan_he_gia_dinhs.'id' IS 'Khóa chính';
COMMENT ON COLUMN quan_he_gia_dinhs.'quan_nhan_id' IS 'Liên kết với quân nhân';
COMMENT ON COLUMN quan_he_gia_dinhs.'ho_ten_nguoi_than' IS 'Họ tên người thân';
COMMENT ON COLUMN quan_he_gia_dinhs.'moi_quan_he_id' IS 'ID Mối quan hệ (tham chiếu 'gia_tri_danh_mucs' mã 'DMMOIQUANHE')';
COMMENT ON COLUMN quan_he_gia_dinhs.'nam_sinh' IS 'Năm sinh người thân';
COMMENT ON COLUMN quan_he_gia_dinhs.'nghe_nghiep' IS 'Nghề nghiệp người thân';
COMMENT ON COLUMN quan_he_gia_dinhs.'dia_chi_lien_lac' IS 'Địa chỉ liên lạc người thân';
COMMENT ON COLUMN quan_he_gia_dinhs.'so_dien_thoai' IS 'Số điện thoại người thân';
COMMENT ON COLUMN quan_he_gia_dinhs.'ghi_chu' IS 'Ghi chú thêm';

-- -----------------------------------------------------


CREATE TABLE IF NOT EXISTS theo_doi_che_do_chinh_sachs (
  'id' BIGSERIAL PRIMARY KEY,
  'quan_nhan_id' VARCHAR(20) NOT NULL REFERENCES 'quan_nhans(so_hieu_quan_nhan)' ON DELETE CASCADE,
  'loai_chinh_sach_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)',
  'noi_dung_chinh_sach' TEXT,
  'thoi_gian_bat_dau_huong' DATE,
  'thoi_gian_ket_thuc_huong' DATE,
  'trang_thai_thuc_hien_id' BIGINT REFERENCES 'gia_tri_danh_mucs(id)',
  'ghi_chu' TEXT,
);

COMMENT ON TABLE theo_doi_che_do_chinh_sachs IS 'Theo dõi việc thực hiện các chế độ, chính sách cho quân nhân.';  
COMMENT ON COLUMN theo_doi_che_do_chinh_sachs.'id' IS 'Khóa chính';
COMMENT ON COLUMN theo_doi_che_do_chinh_sachs.'quan_nhan_id' IS 'Liên kết với quân nhân';
COMMENT ON COLUMN theo_doi_che_do_chinh_sachs.'loai_chinh_sach_id' IS 'ID Loại chế độ/chính sách (tham chiếu 'gia_tri_danh_mucs' mã 'DMLOAICDS')';
COMMENT ON COLUMN theo_doi_che_do_chinh_sachs.'noi_dung_chinh_sach' IS 'Nội dung chi tiết của chế độ/chính sách được hưởng';
COMMENT ON COLUMN theo_doi_che_do_chinh_sachs.'thoi_gian_bat_dau_huong' IS 'Thời gian bắt đầu hưởng';
COMMENT ON COLUMN theo_doi_che_do_chinh_sachs.'thoi_gian_ket_thuc_huong' IS 'Thời gian kết thúc hưởng';
COMMENT ON COLUMN theo_doi_che_do_chinh_sachs.'trang_thai_thuc_hien_id' IS 'ID Trạng thái thực hiện (tham chiếu 'gia_tri_danh_mucs' mã 'DMTRANGTHAITTHS')';
COMMENT ON COLUMN theo_doi_che_do_chinh_sachs.'ghi_chu' IS 'Ghi chú';


-- -----------------------------------------------------


CREATE TABLE IF NOT EXISTS dang_viens (
  'ma_dang_vien' VARCHAR(20) PRIMARY KEY,
  'so_hieu_quan_nhan' VARCHAR(20) UNIQUE REFERENCES 'quan_nhans(so_hieu_quan_nhan)' ON DELETE SET NULL,
  'ho_ten' VARCHAR(100) NOT NULL,
  'ngay_sinh' DATE,
  'gioi_tinh_id' BIGINT REFERENCES 'gia_tri_danh_mucs(id)',
  'ngay_vao_dang_chinh_thuc' DATE,
  'ngay_vao_dang_du_bi' DATE,
  'so_the_dang_vien' VARCHAR(20) UNIQUE,
  'to_chuc_dang_sinh_hoat_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)',
  'trang_thai_dang_tich_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)',
  'chuc_vu_dang_hien_tai_id' BIGINT REFERENCES 'gia_tri_danh_mucs(id)',
);

COMMENT ON TABLE dang_viens IS 'Thông tin chính của đảng viên.';
COMMENT ON COLUMN dang_viens.'ma_dang_vien' IS 'Mã đảng viên (có thể là số thẻ đảng viên), định danh duy nhất';
COMMENT ON COLUMN dang_viens.'so_hieu_quan_nhan' IS 'Số hiệu quân nhân (nếu đảng viên là quân nhân)';
COMMENT ON COLUMN dang_viens.'ho_ten' IS 'Họ và tên đảng viên';
COMMENT ON COLUMN dang_viens.'ngay_sinh' IS 'Ngày sinh';
COMMENT ON COLUMN dang_viens.'gioi_tinh_id' IS 'ID Giới tính (tham chiếu ''gia_tri_danh_mucs'' mã ''DMGIOITINH'')';
COMMENT ON COLUMN dang_viens.'ngay_vao_dang_chinh_thuc' IS 'Ngày vào Đảng chính thức';
COMMENT ON COLUMN dang_viens.'ngay_vao_dang_du_bi' IS 'Ngày vào Đảng dự bị';
COMMENT ON COLUMN dang_viens.'so_the_dang_vien' IS 'Số thẻ đảng viên';
COMMENT ON COLUMN dang_viens.'to_chuc_dang_sinh_hoat_id' IS 'ID Tổ chức đảng sinh hoạt (tham chiếu ''gia_tri_danh_mucs'' mã ''DMTOCHUCDANG'')';
COMMENT ON COLUMN dang_viens.'trang_thai_dang_tich_id' IS 'ID Trạng thái đảng tịch (tham chiếu ''gia_tri_danh_mucs'' mã ''DMTRANGTHAIDT'')';
COMMENT ON COLUMN dang_viens.'chuc_vu_dang_hien_tai_id' IS 'ID Chức vụ đảng hiện tại (tham chiếu ''gia_tri_danh_mucs'' mã ''DMCHUCVUDANG'')';

-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS ho_so_dang_viens ( 
  'dang_vien_id' VARCHAR(20) PRIMARY KEY REFERENCES 'dang_viens(ma_dang_vien)' ON DELETE CASCADE, 
  'nguyen_quan' VARCHAR(255), 
  'noi_o_hien_nay' VARCHAR(255), 
  'trinh_do_chuyen_mon' VARCHAR(255), 
  'ly_luan_chinh_tri' VARCHAR(255), 
  'ngoai_ngu' VARCHAR(255), 
  'tin_hoc' VARCHAR(255), 
  'qua_trinh_cong_tac_dang' TEXT, 
  'ghi_chu' TEXT, 
);

COMMENT ON TABLE ho_so_dang_viens IS 'Thông tin chi tiết, quá trình của đảng viên (có thể là bảng mở rộng của ''dang_viens'' hoặc chứa các thông tin bổ sung không có trong ''dang_viens'' và ''ly_lich_can_nhans'').'; 
COMMENT ON COLUMN ho_so_dang_viens.'dang_vien_id' IS 'Liên kết với đảng viên'; 
COMMENT ON COLUMN ho_so_dang_viens.'nguyen_quan' IS 'Nguyên quán (chi tiết hơn ''ly_lich_can_nhans'' nếu cần)'; 
COMMENT ON COLUMN ho_so_dang_viens.'noi_o_hien_nay' IS 'Nơi ở hiện nay (chi tiết hơn ''ly_lich_can_nhans'' nếu cần)'; 
COMMENT ON COLUMN ho_so_dang_viens.'trinh_do_chuyen_mon' IS 'Trình độ chuyên môn cao nhất'; 
COMMENT ON COLUMN ho_so_dang_viens.'ly_luan_chinh_tri' IS 'Trình độ lý luận chính trị (chi tiết hơn ''ly_lich_can_nhans'' nếu cần)'; 
COMMENT ON COLUMN ho_so_dang_viens.'ngoai_ngu' IS 'Trình độ ngoại ngữ (chi tiết hơn ''ly_lich_can_nhans'' nếu cần)'; 
COMMENT ON COLUMN ho_so_dang_viens.'tin_hoc' IS 'Trình độ tin học (chi tiết hơn ''ly_lich_can_nhans'' nếu cần)'; 
COMMENT ON COLUMN ho_so_dang_viens.'qua_trinh_cong_tac_dang' IS 'Quá trình công tác và giữ chức vụ trong Đảng'; 
COMMENT ON COLUMN ho_so_dang_viens.'ghi_chu' IS 'Các ghi chú khác liên quan đến hồ sơ đảng viên'; 

-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS de_xuat_khen_thuong_dang_viens (
  'id' BIGSERIAL PRIMARY KEY,
  'dang_vien_de_xuat_id' VARCHAR(20) REFERENCES 'dang_viens(ma_dang_vien)', -- Đảng viên được đề xuất (nếu là cá nhân)
  'to_chuc_dang_de_xuat_id' BIGINT REFERENCES 'gia_tri_danh_mucs(id)', -- Tổ chức Đảng được đề xuất (nếu là tập thể, tham chiếu 'DMTOCHUCDANG')
  'ly_do_de_xuat' TEXT NOT NULL,
  'hinh_thuc_khen_thuong_de_xuat_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)', -- Tham chiếu 'gia_tri_danh_mucs' mã 'DMHINHTHUCKT_DANG'
  'nguoi_de_xuat_id' BIGINT NOT NULL REFERENCES 'users(id)',
  'don_vi_nguoi_de_xuat_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)', -- Đơn vị của người đề xuất (tham chiếu 'DMDONVI')
  'ngay_de_xuat' TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  'trang_thai_phe_duyet_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)', -- Tham chiếu 'gia_tri_danh_mucs' mã 'DMTRANGTHAIPD'
  'to_trinh_dinh_kem_id' UUID REFERENCES 'du_lieu_tep_tins(id)',
  'ghi_chu' TEXT,
);

COMMENT ON TABLE de_xuat_khen_thuong_dang_viens IS 'Các đề xuất khen thưởng cho Đảng viên hoặc tổ chức Đảng.';
COMMENT ON COLUMN de_xuat_khen_thuong_dang_viens.'id' IS 'Khóa chính';
COMMENT ON COLUMN de_xuat_khen_thuong_dang_viens.'dang_vien_de_xuat_id' IS 'ID Đảng viên được đề xuất khen thưởng (nếu là cá nhân)';
COMMENT ON COLUMN de_xuat_khen_thuong_dang_viens.'to_chuc_dang_de_xuat_id' IS 'ID Tổ chức Đảng được đề xuất khen thưởng (nếu là tập thể, tham chiếu ''gia_tri_danh_mucs'' mã ''DMTOCHUCDANG'')';
COMMENT ON COLUMN de_xuat_khen_thuong_dang_viens.'ly_do_de_xuat' IS 'Lý do/Thành tích đề xuất khen thưởng';
COMMENT ON COLUMN de_xuat_khen_thuong_dang_viens.'hinh_thuc_khen_thuong_de_xuat_id' IS 'ID Hình thức khen thưởng Đảng đề xuất (tham chiếu ''gia_tri_danh_mucs'' mã ''DMHINHTHUCKT_DANG'')';
COMMENT ON COLUMN de_xuat_khen_thuong_dang_viens.'nguoi_de_xuat_id' IS 'Người tạo đề xuất';
COMMENT ON COLUMN de_xuat_khen_thuong_dang_viens.'don_vi_nguoi_de_xuat_id' IS 'Đơn vị của người đề xuất (tham chiếu ''gia_tri_danh_mucs'' mã ''DMDONVI'')';
COMMENT ON COLUMN de_xuat_khen_thuong_dang_viens.'ngay_de_xuat' IS 'Ngày tạo đề xuất';
COMMENT ON COLUMN de_xuat_khen_thuong_dang_viens.'trang_thai_phe_duyet_id' IS 'ID Trạng thái phê duyệt (tham chiếu ''gia_tri_danh_mucs'' mã ''DMTRANGTHAIPD'')';
COMMENT ON COLUMN de_xuat_khen_thuong_dang_viens.'to_trinh_dinh_kem_id' IS 'ID Tờ trình đính kèm';
COMMENT ON COLUMN de_xuat_khen_thuong_dang_viens.'ghi_chu' IS 'Ghi chú';

-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS quyet_dinh_khen_thuong_dang_viens (
  'id' BIGSERIAL PRIMARY KEY,
  'de_xuat_khen_thuong_dang_vien_id' BIGINT REFERENCES 'de_xuat_khen_thuong_dang_viens(id)' ON DELETE SET NULL,
  'dang_vien_khen_thuong_id' VARCHAR(20) REFERENCES 'dang_viens(ma_dang_vien)', -- Đảng viên được khen thưởng (nếu là cá nhân)
  'to_chuc_dang_khen_thuong_id' BIGINT REFERENCES 'gia_tri_danh_mucs(id)', -- Tổ chức Đảng được khen thưởng (nếu là tập thể, tham chiếu 'DMTOCHUCDANG')
  'so_quyet_dinh' VARCHAR(50) NOT NULL,
  'ngay_quyet_dinh' DATE NOT NULL,
  'cap_ra_quyet_dinh_dang_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)', -- Tham chiếu 'gia_tri_danh_mucs' mã 'DMCAPRAQD_DANG'
  'hinh_thuc_khen_thuong_dang_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)', -- Tham chiếu 'gia_tri_danh_mucs' mã 'DMHINHTHUCKT_DANG'
  'ly_do_khen_thuong' TEXT,
  'tap_tin_quyet_dinh_id' UUID REFERENCES 'du_lieu_tep_tins(id)',
  'ghi_chu' TEXT,

);

COMMENT ON TABLE quyet_dinh_khen_thuong_dang_viens IS 'Các quyết định khen thưởng của Đảng cho Đảng viên hoặc tổ chức Đảng.';
COMMENT ON COLUMN quyet_dinh_khen_thuong_dang_viens.'id' IS 'Khóa chính';
COMMENT ON COLUMN quyet_dinh_khen_thuong_dang_viens.'de_xuat_khen_thuong_dang_vien_id' IS 'Liên kết với đề xuất khen thưởng Đảng viên (nếu có)';
COMMENT ON COLUMN quyet_dinh_khen_thuong_dang_viens.'dang_vien_khen_thuong_id' IS 'ID Đảng viên được khen thưởng (nếu là cá nhân)';
COMMENT ON COLUMN quyet_dinh_khen_thuong_dang_viens.'to_chuc_dang_khen_thuong_id' IS 'ID Tổ chức Đảng được khen thưởng (nếu là tập thể, tham chiếu ''gia_tri_danh_mucs'' mã ''DMTOCHUCDANG'')';
COMMENT ON COLUMN quyet_dinh_khen_thuong_dang_viens.'so_quyet_dinh' IS 'Số hiệu quyết định khen thưởng';
COMMENT ON COLUMN quyet_dinh_khen_thuong_dang_viens.'ngay_quyet_dinh' IS 'Ngày ra quyết định';
COMMENT ON COLUMN quyet_dinh_khen_thuong_dang_viens.'cap_ra_quyet_dinh_dang_id' IS 'ID Cấp Đảng ra quyết định (tham chiếu ''gia_tri_danh_mucs'' mã ''DMCAPRAQD_DANG'')';
COMMENT ON COLUMN quyet_dinh_khen_thuong_dang_viens.'hinh_thuc_khen_thuong_dang_id' IS 'ID Hình thức khen thưởng của Đảng (tham chiếu ''gia_tri_danh_mucs'' mã ''DMHINHTHUCKT_DANG'')';
COMMENT ON COLUMN quyet_dinh_khen_thuong_dang_viens.'ly_do_khen_thuong' IS 'Lý do khen thưởng';
COMMENT ON COLUMN quyet_dinh_khen_thuong_dang_viens.'tap_tin_quyet_dinh_id' IS 'ID Tệp đính kèm quyết định';
COMMENT ON COLUMN quyet_dinh_khen_thuong_dang_viens.'ghi_chu' IS 'Ghi chú';

-- -----------------------------------------------------


CREATE TABLE IF NOT EXISTS ban_kiem_diem_dang_viens (
  'id' BIGSERIAL PRIMARY KEY,
  'dang_vien_id' VARCHAR(20) NOT NULL REFERENCES 'dang_viens(ma_dang_vien)' ON DELETE CASCADE, 
  'nam_kiem_diem' INTEGER NOT NULL, 
  'dot_kiem_diem_id' BIGINT REFERENCES 'gia_tri_danh_mucs(id)', 
  'noi_dung_tu_kiem_diem' TEXT, 
  'danh_gia_cua_chi_bo' TEXT, 
  'xep_loai_id' BIGINT REFERENCES 'gia_tri_danh_mucs(id)',
  'tap_tin_dinh_kem_id' UUID REFERENCES 'du_lieu_tep_tins(id)', 
);

COMMENT ON TABLE ban_kiem_diem_dang_viens IS 'Lưu trữ các bản kiểm điểm của đảng viên.';
COMMENT ON COLUMN ban_kiem_diem_dang_viens.'id' IS 'Khóa chính';
COMMENT ON COLUMN ban_kiem_diem_dang_viens.'dang_vien_id' IS 'Liên kết với đảng viên'; 
COMMENT ON COLUMN ban_kiem_diem_dang_viens.'nam_kiem_diem' IS 'Năm thực hiện kiểm điểm'; 
COMMENT ON COLUMN ban_kiem_diem_dang_viens.'dot_kiem_diem_id' IS 'Đợt kiểm điểm (tham chiếu ''gia_tri_danh_mucs'' mã ''DMDOTKIEMDIEM'')'; 
COMMENT ON COLUMN ban_kiem_diem_dang_viens.'noi_dung_tu_kiem_diem' IS 'Nội dung tự kiểm điểm của đảng viên'; 
COMMENT ON COLUMN ban_kiem_diem_dang_viens.'danh_gia_cua_chi_bo' IS 'Ý kiến đánh giá của chi bộ'; 
COMMENT ON COLUMN ban_kiem_diem_dang_viens.'xep_loai_id' IS 'Xếp loại chất lượng đảng viên (tham chiếu ''gia_tri_danh_mucs'' mã ''DMXEPLOAIDV'')'; 
COMMENT ON COLUMN ban_kiem_diem_dang_viens.'tap_tin_dinh_kem_id' IS 'ID Tệp đính kèm bản kiểm điểm'; 

-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS ho_so_vu_viec_ky_luat_dang_viens (
  'id' BIGSERIAL PRIMARY KEY,
  'dang_vien_vi_pham_id' VARCHAR(20) NOT NULL REFERENCES 'dang_viens(ma_dang_vien)',
  'to_chuc_dang_vi_pham_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)', -- Tổ chức Đảng của Đảng viên tại thời điểm vi phạm (tham chiếu 'DMTOCHUCDANG')
  'ngay_ghi_nhan_vu_viec' DATE NOT NULL,
  'noi_dung_vi_pham' TEXT NOT NULL,
  'bang_chung_ban_dau' TEXT,
  'trang_thai_xu_ly_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)', -- Tham chiếu 'gia_tri_danh_mucs' mã 'DMTRANGTHAIXLVV_DANG'
  'nguoi_ghi_nhan_id' BIGINT NOT NULL REFERENCES 'users(id)',
  'ghi_chu' TEXT,

);

COMMENT ON TABLE ho_so_vu_viec_ky_luat_dang_viens IS 'Hồ sơ các vụ việc cần xem xét kỷ luật của Đảng viên.';
COMMENT ON COLUMN ho_so_vu_viec_ky_luat_dang_viens.'id' IS 'Khóa chính';
COMMENT ON COLUMN ho_so_vu_viec_ky_luat_dang_viens.'dang_vien_vi_pham_id' IS 'ID Đảng viên vi phạm';
COMMENT ON COLUMN ho_so_vu_viec_ky_luat_dang_viens.'to_chuc_dang_vi_pham_id' IS 'Tổ chức Đảng của Đảng viên tại thời điểm vi phạm (tham chiếu ''gia_tri_danh_mucs'' mã ''DMTOCHUCDANG'')';
COMMENT ON COLUMN ho_so_vu_viec_ky_luat_dang_viens.'ngay_ghi_nhan_vu_viec' IS 'Ngày ghi nhận/phát hiện vụ việc';
COMMENT ON COLUMN ho_so_vu_viec_ky_luat_dang_viens.'noi_dung_vi_pham' IS 'Nội dung, hành vi vi phạm';
COMMENT ON COLUMN ho_so_vu_viec_ky_luat_dang_viens.'bang_chung_ban_dau' IS 'Bằng chứng ban đầu (nếu có)';
COMMENT ON COLUMN ho_so_vu_viec_ky_luat_dang_viens.'trang_thai_xu_ly_id' IS 'ID Trạng thái xử lý vụ việc (tham chiếu ''gia_tri_danh_mucs'' mã ''DMTRANGTHAIXLVV_DANG'')';
COMMENT ON COLUMN ho_so_vu_viec_ky_luat_dang_viens.'nguoi_ghi_nhan_id' IS 'Người ghi nhận vụ việc';
COMMENT ON COLUMN ho_so_vu_viec_ky_luat_dang_viens.'ghi_chu' IS 'Ghi chú';

-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS bien_ban_hoi_dong_ky_luat_dangs (
  'id' BIGSERIAL PRIMARY KEY,
  'ho_so_vu_viec_ky_luat_dang_vien_id' BIGINT NOT NULL REFERENCES 'ho_so_vu_viec_ky_luat_dang_viens(id)' ON DELETE CASCADE,
  'ngay_hop' DATE NOT NULL,
  'dia_diem_hop' VARCHAR(255),
  'thanh_phan_tham_gia' TEXT,
  'noi_dung_hop' TEXT,
  'ket_luan_hoi_dong' TEXT,
  'hinh_thuc_ky_luat_dang_de_nghi_id' BIGINT REFERENCES 'gia_tri_danh_mucs(id)', -- Tham chiếu 'gia_tri_danh_mucs' mã 'DMHINHTHUCKYLUAT_DANG'
  'tap_tin_bien_ban_id' UUID REFERENCES 'du_lieu_tep_tins(id)',
  'ghi_chu' TEXT
);
COMMENT ON TABLE bien_ban_hoi_dong_ky_luat_dangs IS 'Biên bản các cuộc họp hội đồng kỷ luật của Đảng.';
COMMENT ON COLUMN bien_ban_hoi_dong_ky_luat_dangs.'id' IS 'Khóa chính';
COMMENT ON COLUMN bien_ban_hoi_dong_ky_luat_dangs.'ho_so_vu_viec_ky_luat_dang_vien_id' IS 'ID Hồ sơ vụ việc kỷ luật Đảng viên';
COMMENT ON COLUMN bien_ban_hoi_dong_ky_luat_dangs.'ngay_hop' IS 'Ngày họp';
COMMENT ON COLUMN bien_ban_hoi_dong_ky_luat_dangs.'dia_diem_hop' IS 'Địa điểm họp';
COMMENT ON COLUMN bien_ban_hoi_dong_ky_luat_dangs.'thanh_phan_tham_gia' IS 'Thành phần tham gia';
COMMENT ON COLUMN bien_ban_hoi_dong_ky_luat_dangs.'noi_dung_hop' IS 'Nội dung họp';
COMMENT ON COLUMN bien_ban_hoi_dong_ky_luat_dangs.'ket_luan_hoi_dong' IS 'Kết luận của họp';
COMMENT ON COLUMN bien_ban_hoi_dong_ky_luat_dangs.'hinh_thuc_ky_luat_dang_de_nghi_id' IS 'ID Hình thức kỷ luật Đảng đề nghị (tham chiếu ''gia_tri_danh_mucs'' mã ''DMHINHTHUCKYLUAT_DANG'')';
COMMENT ON COLUMN bien_ban_hoi_dong_ky_luat_dangs.'tap_tin_bien_ban_id' IS 'ID Tệp đính kèm biên bản';
COMMENT ON COLUMN bien_ban_hoi_dong_ky_luat_dangs.'ghi_chu' IS 'Ghi chú';

-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS quyet_dinh_ky_luat_dang_viens (
  'id' BIGSERIAL PRIMARY KEY,
  'ho_so_vu_viec_ky_luat_dang_vien_id' BIGINT REFERENCES 'ho_so_vu_viec_ky_luat_dang_viens(id)' ON DELETE SET NULL,
  'dang_vien_ky_luat_id' VARCHAR(20) NOT NULL REFERENCES 'dang_viens(ma_dang_vien)',
  'so_quyet_dinh' VARCHAR(50) NOT NULL,
  'ngay_quyet_dinh' DATE NOT NULL,
  'cap_ra_quyet_dinh_dang_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)', -- Tham chiếu 'gia_tri_danh_mucs' mã 'DMCAPRAQD_KL_DANG'
  'hinh_thuc_ky_luat_dang_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)', -- Tham chiếu 'gia_tri_danh_mucs' mã 'DMHINHTHUCKYLUAT_DANG'
  'ly_do_ky_luat' TEXT,
  'thoi_han_ky_luat' VARCHAR(100), -- Thời hạn thi hành kỷ luật (nếu có)
  'tap_tin_quyet_dinh_id' UUID REFERENCES 'du_lieu_tep_tins(id)',
  'ghi_chu' TEXT,
  'ngay_tao' TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  'nguoi_tao_id' BIGINT REFERENCES 'users(id)',
  'ngay_cap_nhat' TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  'nguoi_cap_nhat_id' BIGINT REFERENCES 'users(id)'
);

COMMENT ON TABLE quyet_dinh_ky_luat_dang_viens IS 'Các quyết định kỷ luật của Đảng đối với Đảng viên.';
COMMENT ON COLUMN quyet_dinh_ky_luat_dang_viens.'id' IS 'Khóa chính';
COMMENT ON COLUMN quyet_dinh_ky_luat_dang_viens.'ho_so_vu_viec_ky_luat_dang_vien_id' IS 'Liên kết với hồ sơ vụ việc kỷ luật Đảng viên (nếu có)';
COMMENT ON COLUMN quyet_dinh_ky_luat_dang_viens.'dang_vien_ky_luat_id' IS 'ID Đảng viên bị kỷ luật';
COMMENT ON COLUMN quyet_dinh_ky_luat_dang_viens.'so_quyet_dinh' IS 'Số hiệu quyết định kỷ luật';
COMMENT ON COLUMN quyet_dinh_ky_luat_dang_viens.'ngay_quyet_dinh' IS 'Ngày ra quyết định';
COMMENT ON COLUMN quyet_dinh_ky_luat_dang_viens.'cap_ra_quyet_dinh_dang_id' IS 'ID Cấp Đảng ra quyết định kỷ luật (tham chiếu ''gia_tri_danh_mucs'' mã ''DMCAPRAQD_KL_DANG'')';
COMMENT ON COLUMN quyet_dinh_ky_luat_dang_viens.'hinh_thuc_ky_luat_dang_id' IS 'ID Hình thức kỷ luật của Đảng (tham chiếu ''gia_tri_danh_mucs'' mã ''DMHINHTHUCKYLUAT_DANG'')';
COMMENT ON COLUMN quyet_dinh_ky_luat_dang_viens.'ly_do_ky_luat' IS 'Lý do kỷ luật';
COMMENT ON COLUMN quyet_dinh_ky_luat_dang_viens.'thoi_han_ky_luat' IS 'Thời hạn thi hành kỷ luật (nếu có)';
COMMENT ON COLUMN quyet_dinh_ky_luat_dang_viens.'tap_tin_quyet_dinh_id' IS 'ID Tệp đính kèm quyết định';
COMMENT ON COLUMN quyet_dinh_ky_luat_dang_viens.'ghi_chu' IS 'Ghi chú';
COMMENT ON COLUMN quyet_dinh_ky_luat_dang_viens.'ngay_tao' IS 'Ngày tạo bản ghi';
COMMENT ON COLUMN quyet_dinh_ky_luat_dang_viens.'nguoi_tao_id' IS 'Người tạo bản ghi';
COMMENT ON COLUMN quyet_dinh_ky_luat_dang_viens.'ngay_cap_nhat' IS 'Ngày cập nhật bản ghi';
COMMENT ON COLUMN quyet_dinh_ky_luat_dang_viens.'nguoi_cap_nhat_id' IS 'Người cập nhật bản ghi';

-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS cap_uy_nhiem_kys ( 
  'id' BIGSERIAL PRIMARY KEY,
  'ten_cap_uy' VARCHAR(255) NOT NULL, 
  'loai_cap_uy_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)', 
  'don_vi_phu_trach_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)', 
  'nhiem_ky' VARCHAR(50) NOT NULL, 
  'ngay_bat_dau_nhiem_ky' DATE, 
  'ngay_ket_thuc_nhiem_ky' DATE, 
  'ghi_chu' TEXT 
);

COMMENT ON TABLE cap_uy_nhiem_kys IS 'Thông tin về các cấp ủy và nhiệm kỳ hoạt động.'; 
COMMENT ON COLUMN cap_uy_nhiem_kys.'id' IS 'Khóa chính'; 
COMMENT ON COLUMN cap_uy_nhiem_kys.'ten_cap_uy' IS 'Tên cấp ủy (ví dụ: Đảng ủy Trung đoàn X, Chi bộ Đại đội Y)'; 
COMMENT ON COLUMN cap_uy_nhiem_kys.'loai_cap_uy_id' IS 'Loại cấp ủy (tham chiếu ''gia_tri_danh_mucs'' mã ''DMLOAICAPUY'')'; 
COMMENT ON COLUMN cap_uy_nhiem_kys.'don_vi_phu_trach_id' IS 'Đơn vị mà cấp ủy này phụ trách (tham chiếu ''gia_tri_danh_mucs'' mã ''DMDONVI'')'; 
COMMENT ON COLUMN cap_uy_nhiem_kys.'nhiem_ky' IS 'Nhiệm kỳ (ví dụ: 2020--2025)'; 
COMMENT ON COLUMN cap_uy_nhiem_kys.'ngay_bat_dau_nhiem_ky' IS 'Ngày bắt đầu nhiệm kỳ'; 
COMMENT ON COLUMN cap_uy_nhiem_kys.'ngay_ket_thuc_nhiem_ky' IS 'Ngày kết thúc nhiệm kỳ'; 
COMMENT ON COLUMN cap_uy_nhiem_kys.'ghi_chu' IS 'Ghi chú'; 


-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS thanh_vien_cap_uys ( 
  'id' BIGSERIAL PRIMARY KEY,
  'cap_uy_nhiem_ky_id' BIGINT NOT NULL REFERENCES 'cap_uy_nhiem_kys(id)' ON DELETE CASCADE, 
  'dang_vien_id' VARCHAR(20) NOT NULL REFERENCES 'dang_viens(ma_dang_vien)' ON DELETE CASCADE, 
  'chuc_vu_trong_cap_uy_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)', 
  'ngay_bat_dau_giu_chuc_vu' DATE, 
  'ngay_ket_thuc_giu_chuc_vu' DATE, 
  'ghi_chu' TEXT 
);

COMMENT ON TABLE thanh_vien_cap_uys IS 'Danh sách thành viên trong các cấp ủy.'; 
COMMENT ON COLUMN thanh_vien_cap_uys.'id' IS 'Khóa chính'; 
COMMENT ON COLUMN thanh_vien_cap_uys.'cap_uy_nhiem_ky_id' IS 'Liên kết với cấp ủy nhiệm kỳ'; 
COMMENT ON COLUMN thanh_vien_cap_uys.'dang_vien_id' IS 'Liên kết với đảng viên'; 
COMMENT ON COLUMN thanh_vien_cap_uys.'chuc_vu_trong_cap_uy_id' IS 'Chức vụ trong cấp ủy (tham chiếu ''gia_tri_danh_mucs'' mã ''DMCHUCVUCAPUY'')'; 
COMMENT ON COLUMN thanh_vien_cap_uys.'ngay_bat_dau_giu_chuc_vu' IS 'Ngày bắt đầu giữ chức vụ trong cấp ủy'; 
COMMENT ON COLUMN thanh_vien_cap_uys.'ngay_ket_thuc_giu_chuc_vu' IS 'Ngày kết thúc giữ chức vụ'; 
COMMENT ON COLUMN thanh_vien_cap_uys.'ghi_chu' IS 'Ghi chú'; 


-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS ke_hoach_sinh_hoat_dangs ( 
  'id' BIGSERIAL PRIMARY KEY,
  'to_chuc_dang_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)', 
  'ten_ke_hoach' VARCHAR(500) NOT NULL, 
  'nam_ke_hoach' INTEGER, 
  'quy_ke_hoach' INTEGER, 
  'noi_dung_ke_hoach' TEXT, 
  'tap_tin_dinh_kem_id' UUID REFERENCES 'du_lieu_tep_tins(id)', 
);

COMMENT ON TABLE ke_hoach_sinh_hoat_dangs IS 'Kế hoạch sinh hoạt của các tổ chức đảng.'; 
COMMENT ON COLUMN ke_hoach_sinh_hoat_dangs.'id' IS 'Khóa chính'; 
COMMENT ON COLUMN ke_hoach_sinh_hoat_dangs.'to_chuc_dang_id' IS 'Tổ chức đảng lập kế hoạch (tham chiếu ''gia_tri_danh_mucs'' mã ''DMTOCHUCDANG'')'; 
COMMENT ON COLUMN ke_hoach_sinh_hoat_dangs.'ten_ke_hoach' IS 'Tên kế hoạch sinh hoạt'; 
COMMENT ON COLUMN ke_hoach_sinh_hoat_dangs.'nam_ke_hoach' IS 'Năm của kế hoạch'; 
COMMENT ON COLUMN ke_hoach_sinh_hoat_dangs.'quy_ke_hoach' IS 'Quý của kế hoạch (1--4)'; 
COMMENT ON COLUMN ke_hoach_sinh_hoat_dangs.'noi_dung_ke_hoach' IS 'Nội dung chính của kế hoạch'; 
COMMENT ON COLUMN ke_hoach_sinh_hoat_dangs.'tap_tin_dinh_kem_id' IS 'ID Tệp đính kèm kế hoạch'; 

-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS buoi_sinh_hoat_dangs ( 
  'id' BIGSERIAL PRIMARY KEY,
  'ke_hoach_id' BIGINT REFERENCES 'ke_hoach_sinh_hoat_dangs(id)', 
  'to_chuc_dang_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)', 
  'ngay_sinh_hoat' DATE NOT NULL, 
  'dia_diem' VARCHAR(255),
  'chu_tri_id' VARCHAR(20) REFERENCES 'dang_viens(ma_dang_vien)', 
  'thu_ky_id' VARCHAR(20) REFERENCES 'dang_viens(ma_dang_vien)', 
  'noi_dung_buoi_sinh_hoat' TEXT, 
  'so_dang_vien_vang_mat' INTEGER DEFAULT 0, 
  'ly_do_vang_mat' TEXT, 
  'ket_luan' TEXT,
  'tap_tin_bien_ban_id' UUID REFERENCES 'du_lieu_tep_tins(id)' 
);

COMMENT ON TABLE buoi_sinh_hoat_dangs IS 'Thông tin chi tiết về các buổi sinh hoạt đảng.'; 
COMMENT ON COLUMN buoi_sinh_hoat_dangs.'id' IS 'Khóa chính'; 
COMMENT ON COLUMN buoi_sinh_hoat_dangs.'ke_hoach_id' IS 'Liên kết với kế hoạch (nếu có)'; 
COMMENT ON COLUMN buoi_sinh_hoat_dangs.'to_chuc_dang_id' IS 'Tổ chức đảng sinh hoạt (tham chiếu ''gia_tri_danh_mucs'' mã ''DMTOCHUCDANG'')'; 
COMMENT ON COLUMN buoi_sinh_hoat_dangs.'ngay_sinh_hoat' IS 'Ngày diễn ra sinh hoạt'; 
COMMENT ON COLUMN buoi_sinh_hoat_dangs.'dia_diem' IS 'Địa điểm sinh hoạt'; 
COMMENT ON COLUMN buoi_sinh_hoat_dangs.'chu_tri_id' IS 'Đảng viên chủ trì buổi sinh hoạt'; 
COMMENT ON COLUMN buoi_sinh_hoat_dangs.'thu_ky_id' IS 'Đảng viên thư ký buổi sinh hoạt'; 
COMMENT ON COLUMN buoi_sinh_hoat_dangs.'noi_dung_buoi_sinh_hoat ' IS 'Nội dung chính của buổi sinh hoạt'; 
COMMENT ON COLUMN buoi_sinh_hoat_dangs.'so_dang_vien_vang_mat' IS 'Số lượng đảng viên vắng mặt'; 
COMMENT ON COLUMN buoi_sinh_hoat_dangs.'ly_do_vang_mat' IS 'Lý do vắng mặt (nếu có)'; 
COMMENT ON COLUMN buoi_sinh_hoat_dangs.'ket_luan' IS 'Kết luận của buổi sinh hoạt'; 
COMMENT ON COLUMN buoi_sinh_hoat_dangs.'tap_tin_bien_ban_id' IS 'ID Tệp đính kèm biên bản (nếu có)'; 


-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS nghi_quyet_dangs ( 
  'id' BIGSERIAL PRIMARY KEY,
  'buoi_sinh_hoat_id' BIGINT REFERENCES 'buoi_sinh_hoat_dangs(id)', 
  'to_chuc_dang_ban_hanh_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)', 
  'so_nghi_quyet' VARCHAR(50) NOT NULL, 
  'ngay_ban_hanh' DATE NOT NULL, 
  'trich_yeu_noi_dung' TEXT NOT NULL, 
  'tap_tin_dinh_kem_id' UUID REFERENCES 'du_lieu_tep_tins(id)', 
);

COMMENT ON TABLE nghi_quyet_dangs IS 'Lưu trữ các nghị quyết của đảng.'; 
COMMENT ON COLUMN nghi_quyet_dangs.'id' IS 'Khóa chính'; 
COMMENT ON COLUMN nghi_quyet_dangs.'buoi_sinh_hoat_id' IS 'Liên kết với buổi sinh hoạt ra nghị quyết (nếu có)'; 
COMMENT ON COLUMN nghi_quyet_dangs.'to_chuc_dang_ban_hanh_id' IS 'Tổ chức đảng ban hành nghị quyết (tham chiếu ''gia_tri_danh_mucs'' mã ''DMTOCHUCDANG'')'; 
COMMENT ON COLUMN nghi_quyet_dangs.'so_nghi_quyet' IS 'Số hiệu nghị quyết'; 
COMMENT ON COLUMN nghi_quyet_dangs.'ngay_ban_hanh' IS 'Ngày ban hành nghị quyết'; 
COMMENT ON COLUMN nghi_quyet_dangs.'trich_yeu_noi_dung' IS 'Trích yếu nội dung nghị quyết'; 
COMMENT ON COLUMN nghi_quyet_dangs.'tap_tin_dinh_kem_id' IS 'ID Tệp đính kèm toàn văn nghị quyết'; 


-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS ho_so_phat_trien_dang_viens ( 
  'id' BIGSERIAL PRIMARY KEY,
  'quan_nhan_id' VARCHAR(20) NOT NULL UNIQUE REFERENCES 'quan_nhans(so_hieu_quan_nhan)', 
  'ngay_bat_dau_theo_doi' DATE, 
  'nguoi_gioi_thieu_1_id' VARCHAR(20) REFERENCES 'dang_viens(ma_dang_vien)', 
  'nguoi_gioi_thieu_2_id' VARCHAR(20) REFERENCES 'dang_viens(ma_dang_vien)', 
  'ngay_hoc_lop_cam_tinh_dang' DATE, 
  'trang_thai_phat_trien_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)', 
  'ghi_chu' TEXT, 
);

COMMENT ON TABLE ho_so_phat_trien_dang_viens IS 'Hồ sơ theo dõi quá trình phát triển đảng viên mới.'; 
COMMENT ON COLUMN ho_so_phat_trien_dang_viens.'id' IS 'Khóa chính'; 
COMMENT ON COLUMN ho_so_phat_trien_dang_viens.'quan_nhan_id' IS 'Liên kết với quân nhân là đối tượng phát triển Đảng'; 
COMMENT ON COLUMN ho_so_phat_trien_dang_viens.'ngay_bat_dau_theo_doi' IS 'Ngày bắt đầu đưa vào diện theo dõi, bồi dưỡng'; 
COMMENT ON COLUMN ho_so_phat_trien_dang_viens.'nguoi_gioi_thieu_1_id' IS 'Đảng viên chính thức giới thiệu thứ nhất'; 
COMMENT ON COLUMN ho_so_phat_trien_dang_viens.'nguoi_gioi_thieu_2_id' IS 'Đảng viên chính thức giới thiệu thứ hai'; 
COMMENT ON COLUMN ho_so_phat_trien_dang_viens.'ngay_hoc_lop_cam_tinh_dang' IS 'Ngày hoàn thành lớp bồi dưỡng nhận thức về Đảng'; 
COMMENT ON COLUMN ho_so_phat_trien_dang_viens.'trang_thai_phat_trien_id' IS 'Trạng thái phát triển (tham chiếu ''gia_tri_danh_mucs'' mã ''DMTRANGTHAIPTĐ'')'; 
COMMENT ON COLUMN ho_so_phat_trien_dang_viens.'ghi_chu' IS 'Ghi chú quá trình'; 

-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS danh_gia_xep_loai_dang_viens ( 
  'id' BIGSERIAL PRIMARY KEY,
  'dang_vien_id' VARCHAR(20) NOT NULL REFERENCES 'dang_viens(ma_dang_vien)' ON DELETE CASCADE, 
  'nam_danh_gia' INTEGER NOT NULL, 
  'dot_danh_gia_id' BIGINT REFERENCES 'gia_tri_danh_mucs(id)', 
  'noi_dung_tu_danh_gia' TEXT, 
  'danh_gia_cua_chi_bo' TEXT, 
  'xep_loai_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)',
  'ghi_chu' TEXT, 

);

COMMENT ON TABLE danh_gia_xep_loai_dang_viens IS 'Kết quả đánh giá, xếp loại đảng viên hàng năm.'; 
COMMENT ON COLUMN danh_gia_xep_loai_dang_viens.'id' IS 'Khóa chính'; 
COMMENT ON COLUMN danh_gia_xep_loai_dang_viens.'dang_vien_id' IS 'Liên kết với đảng viên'; 
COMMENT ON COLUMN danh_gia_xep_loai_dang_viens.'nam_danh_gia' IS 'Năm đánh giá'; 
COMMENT ON COLUMN danh_gia_xep_loai_dang_viens.'dot_danh_gia_id' IS 'Đợt đánh giá (tham chiếu ''gia_tri_danh_mucs'' mã ''DMDOTDANHGIA'')'; 
COMMENT ON COLUMN danh_gia_xep_loai_dang_viens.'noi_dung_tu_danh_gia' IS 'Nội dung tự đánh giá của đảng viên'; 
COMMENT ON COLUMN danh_gia_xep_loai_dang_viens.'danh_gia_cua_chi_bo' IS 'Ý kiến đánh giá của chi bộ'; 
COMMENT ON COLUMN danh_gia_xep_loai_dang_viens.'xep_loai_id' IS 'Xếp loại chất lượng đảng viên (tham chiếu ''gia_tri_danh_mucs'' mã ''DMXEPLOAIDV'')'; 
COMMENT ON COLUMN danh_gia_xep_loai_dang_viens.'ghi_chu' IS 'Ghi chú'; 
COMMENT ON COLUMN danh_gia_xep_loai_dang_viens.'ngay_tao' IS 'Ngày tạo'; 

-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS danh_gia_xep_loai_to_chuc_dangs ( 
  'id' BIGSERIAL PRIMARY KEY,
  'to_chuc_dang_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)', 
  'nam_danh_gia' INTEGER NOT NULL, 
  'dot_danh_gia_id' BIGINT REFERENCES 'gia_tri_danh_mucs(id)', 
  'noi_dung_tu_danh_gia' TEXT, 
  'danh_gia_cua_cap_tren' TEXT, 
  'xep_loai_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)',
  'ghi_chu' TEXT, 
);

COMMENT ON TABLE danh_gia_xep_loai_to_chuc_dangs IS 'Kết quả đánh giá, xếp loại tổ chức đảng hàng năm.'; 
COMMENT ON COLUMN danh_gia_xep_loai_to_chuc_dangs.'id' IS 'Khóa chính'; 
COMMENT ON COLUMN danh_gia_xep_loai_to_chuc_dangs.'to_chuc_dang_id' IS 'Tổ chức đảng được đánh giá (tham chiếu ''gia_tri_danh_mucs'' mã ''DMTOCHUCDANG'')'; 
COMMENT ON COLUMN danh_gia_xep_loai_to_chuc_dangs.'nam_danh_gia' IS 'Năm đánh giá'; 
COMMENT ON COLUMN danh_gia_xep_loai_to_chuc_dangs.'dot_danh_gia_id' IS 'Đợt đánh giá (tham chiếu ''gia_tri_danh_mucs'' mã ''DMDOTDANHGIA'')'; 
COMMENT ON COLUMN danh_gia_xep_loai_to_chuc_dangs.'noi_dung_tu_danh_gia' IS 'Nội dung tự đánh giá của tổ chức đảng'; 
COMMENT ON COLUMN danh_gia_xep_loai_to_chuc_dangs.'danh_gia_cua_cap_tren' IS 'Ý kiến đánh giá của cấp ủy cấp trên'; 

-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS thu_chi_dang_phis ( 
  'id' BIGSERIAL PRIMARY KEY,
  'dang_vien_id' VARCHAR(20) REFERENCES 'dang_viens(ma_dang_vien)', 
  'to_chuc_dang_id' BIGINT REFERENCES 'gia_tri_danh_mucs(id)', 
  'loai_giao_dich_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)', 
  'ngay_giao_dich' DATE NOT NULL, 
  'so_tien' DECIMAL(18,2) NOT NULL, 
  'noi_dung_giao_dich' TEXT, 
  'thang_dong_dang_phi' VARCHAR(7), 
  'ghi_chu' TEXT, 
);

COMMENT ON TABLE thu_chi_dang_phis IS 'Quản lý thu chi đảng phí.'; 
COMMENT ON COLUMN thu_chi_dang_phis.'id' IS 'Khóa chính'; 
COMMENT ON COLUMN thu_chi_dang_phis.'dang_vien_id' IS 'Đảng viên nộp đảng phí (nếu là khoản thu)'; 
COMMENT ON COLUMN thu_chi_dang_phis.'to_chuc_dang_id' IS 'Tổ chức đảng thu/chi (tham chiếu ''gia_tri_danh_mucs'' mã ''DMTOCHUCDANG'')'; 
COMMENT ON COLUMN thu_chi_dang_phis.'loai_giao_dich_id' IS 'Loại giao dịch (Thu/Chi, tham chiếu ''gia_tri_danh_mucs'' mã ''DMLOAIGDDP'')'; 
COMMENT ON COLUMN thu_chi_dang_phis.'ngay_giao_dich' IS 'Ngày giao dịch'; 
COMMENT ON COLUMN thu_chi_dang_phis.'so_tien' IS 'Số tiền giao dịch'; 
COMMENT ON COLUMN thu_chi_dang_phis.'noi_dung_giao_dich' IS 'Nội dung thu/chi đảng phí'; 
COMMENT ON COLUMN thu_chi_dang_phis.'thang_dong_dang_phi' IS 'Tháng đóng đảng phí (YYYY--MM, nếu là khoản thu của đảng viên)'; 
COMMENT ON COLUMN thu_chi_dang_phis.'ghi_chu' IS 'Ghi chú'; 



-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS ke_hoach_kiem_tra_giam_sat_dangs ( 
  'id' BIGSERIAL PRIMARY KEY,
  'cap_uy_kiem_tra_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)', 
  'ten_ke_hoach' VARCHAR(500) NOT NULL, 
  'nam_ke_hoach' INTEGER, 
  'noi_dung_kiem_tra' TEXT, 
  'doi_tuong_kiem_tra' TEXT, 
  'thoi_gian_thuc_hien' VARCHAR(255), 
  'tap_tin_dinh_kem_id' UUID REFERENCES 'du_lieu_tep_tins(id)', 

);

COMMENT ON TABLE ke_hoach_kiem_tra_giam_sat_dangs IS 'Kế hoạch kiểm tra, giám sát của đảng.'; 
COMMENT ON COLUMN ke_hoach_kiem_tra_giam_sat_dangs.'id' IS 'Khóa chính'; 
COMMENT ON COLUMN ke_hoach_kiem_tra_giam_sat_dangs.'cap_uy_kiem_tra_id' IS 'Cấp ủy thực hiện kiểm tra (tham chiếu ''gia_tri_danh_mucs'' mã ''DMCAPUY'')'; 
COMMENT ON COLUMN ke_hoach_kiem_tra_giam_sat_dangs.'ten_ke_hoach' IS 'Tên kế hoạch kiểm tra, giám sát'; 
COMMENT ON COLUMN ke_hoach_kiem_tra_giam_sat_dangs.'nam_ke_hoach' IS 'Năm thực hiện kế hoạch'; 
COMMENT ON COLUMN ke_hoach_kiem_tra_giam_sat_dangs.'noi_dung_kiem_tra' IS 'Nội dung chính kiểm tra, giám sát'; 
COMMENT ON COLUMN ke_hoach_kiem_tra_giam_sat_dangs.'doi_tuong_kiem_tra' IS 'Đối tượng được kiểm tra, giám sát (tổ chức, cá nhân)'; 
COMMENT ON COLUMN ke_hoach_kiem_tra_giam_sat_dangs.'thoi_gian_thuc_hien' IS 'Thời gian dự kiến thực hiện'; 
COMMENT ON COLUMN ke_hoach_kiem_tra_giam_sat_dangs.'tap_tin_dinh_kem_id' IS 'ID Tệp đính kèm kế hoạch'; 


-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS cuoc_kiem_tra_giam_sat_dangs ( 
  'id' BIGSERIAL PRIMARY KEY,
  'ke_hoach_kiem_tra_id' BIGINT REFERENCES 'ke_hoach_kiem_tra_giam_sat_dangs(id)', 
  'cap_uy_kiem_tra_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)', 
  'doi_tuong_duoc_kiem_tra_dang_vien_id' VARCHAR(20) REFERENCES 'dang_viens(ma_dang_vien)', 
  'doi_tuong_duoc_kiem_tra_to_chuc_id' BIGINT REFERENCES 'gia_tri_danh_mucs(id)', 
  'ngay_bat_dau' DATE, 
  'ngay_ket_thuc' DATE, 
  'noi_dung_kiem_tra' TEXT, 
  'ket_luan_kiem_tra' TEXT, 
  'kien_nghi_sau_xu_ly' TEXT, 
  'tap_tin_ket_luan_dinh_kem_id' UUID REFERENCES 'du_lieu_tep_tins(id)', 
);

COMMENT ON TABLE cuoc_kiem_tra_giam_sat_dangs IS 'Thông tin về các cuộc kiểm tra, giám sát đã thực hiện.'; 
COMMENT ON COLUMN cuoc_kiem_tra_giam_sat_dangs.'id' IS 'Khóa chính'; 
COMMENT ON COLUMN cuoc_kiem_tra_giam_sat_dangs.'ke_hoach_kiem_tra_id' IS 'Liên kết với kế hoạch (nếu có)'; 
COMMENT ON COLUMN cuoc_kiem_tra_giam_sat_dangs.'cap_uy_kiem_tra_id' IS 'Cấp ủy thực hiện kiểm tra (tham chiếu ''gia_tri_danh_mucs'' mã ''DMCAPUY'')'; 
COMMENT ON COLUMN cuoc_kiem_tra_giam_sat_dangs.'doi_tuong_duoc_kiem_tra_dang_vien_id' IS 'Đảng viên được kiểm tra (nếu là cá nhân)'; 
COMMENT ON COLUMN cuoc_kiem_tra_giam_sat_dangs.'doi_tuong_duoc_kiem_tra_to_chuc_id' IS 'Tổ chức đảng được kiểm tra (tham chiếu ''gia_tri_danh_mucs'' mã ''DMTOCHUCDANG'')'; 
COMMENT ON COLUMN cuoc_kiem_tra_giam_sat_dangs.'ngay_bat_dau' IS 'Ngày bắt đầu kiểm tra'; 
COMMENT ON COLUMN cuoc_kiem_tra_giam_sat_dangs.'ngay_ket_thuc' IS 'Ngày kết thúc kiểm tra'; 
COMMENT ON COLUMN cuoc_kiem_tra_giam_sat_dangs.'noi_dung_kiem_tra' IS 'Nội dung đã kiểm tra'; 
COMMENT ON COLUMN cuoc_kiem_tra_giam_sat_dangs.'ket_luan_kiem_tra' IS 'Kết luận sau kiểm tra'; 
COMMENT ON COLUMN cuoc_kiem_tra_giam_sat_dangs.'kien_nghi_sau_xu_ly' IS 'Kiến nghị, biện pháp xử lý sau kiểm tra'; 
COMMENT ON COLUMN cuoc_kiem_tra_giam_sat_dangs.'tap_tin_ket_luan_dinh_kem_id' IS 'ID Tệp đính kèm kết luận kiểm tra'; 

-- -----------------------------------------------------


CREATE TABLE IF NOT EXISTS ke_hoach_giao_duc_chinh_tris ( 
  'id' BIGSERIAL PRIMARY KEY,
  'ten_ke_hoach' VARCHAR(500) NOT NULL, 
  'ma_ke_hoach' VARCHAR(50) UNIQUE, 
  'loai_ke_hoach_giao_duc_chinh_tri_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)', 
  'noi_dung_ke_hoach' TEXT, 
  'don_vi_to_chuc_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)', 
  'thoi_gian_bat_dau_ke_hoach' DATE, 
  'thoi_gian_ket_thuc_ke_hoach' DATE, 
  'nguoi_phu_trach_id' BIGINT REFERENCES 'users(id)', 
  'tap_tin_dinh_kem_id' UUID REFERENCES 'du_lieu_tep_tins(id)', 
  'trang_thai_phe_duyet_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)', 
);

COMMENT ON TABLE ke_hoach_giao_duc_chinh_tris IS 'Kế hoạch Giáo dục Chính trị.'; 
COMMENT ON COLUMN ke_hoach_giao_duc_chinh_tris.'id' IS 'Khóa chính'; 
COMMENT ON COLUMN ke_hoach_giao_duc_chinh_tris.'ten_ke_hoach' IS 'Tên kế hoạch giáo dục chính trị'; 
COMMENT ON COLUMN ke_hoach_giao_duc_chinh_tris.'ma_ke_hoach' IS 'Mã kế hoạch (nếu có)'; 
COMMENT ON COLUMN ke_hoach_giao_duc_chinh_tris.'loai_ke_hoach_giao_duc_chinh_tri_id' IS 'ID Loại kế hoạch GDCT (tham chiếu ''gia_tri_danh_mucs'' mã ''DMLOAIKH_GDCT'')'; 
COMMENT ON COLUMN ke_hoach_giao_duc_chinh_tris.'noi_dung_ke_hoach' IS 'Nội dung chính của kế hoạch'; 
COMMENT ON COLUMN ke_hoach_giao_duc_chinh_tris.'don_vi_to_chuc_id' IS 'ID Đơn vị tổ chức (tham chiếu ''gia_tri_danh_mucs'' mã ''DMDONVI'')'; 
COMMENT ON COLUMN ke_hoach_giao_duc_chinh_tris.'thoi_gian_bat_dau_ke_hoach' IS 'Thời gian bắt đầu kế hoạch'; 
COMMENT ON COLUMN ke_hoach_giao_duc_chinh_tris.'thoi_gian_ket_thuc_ke_hoach' IS 'Thời gian kết thúc kế hoạch'; 
COMMENT ON COLUMN ke_hoach_giao_duc_chinh_tris.'nguoi_phu_trach_id' IS 'Người phụ trách kế hoạch'; 
COMMENT ON COLUMN ke_hoach_giao_duc_chinh_tris.'tap_tin_dinh_kem_id' IS 'ID Tệp đính kèm kế hoạch'; 
COMMENT ON COLUMN ke_hoach_giao_duc_chinh_tris.'trang_thai_phe_duyet_id' IS 'ID Trạng thái phê duyệt (tham chiếu ''gia_tri_danh_mucs'' mã ''DMTRANGTHAIPD'')'; 

-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS hoat_dong_giao_duc_chinh_tris ( 
  'id' BIGSERIAL PRIMARY KEY,
  'ke_hoach_giao_duc_chinh_tri_id' BIGINT REFERENCES 'ke_hoach_giao_duc_chinh_tris(id)', 
  'ten_hoat_dong' VARCHAR(500) NOT NULL, 
  'loai_hoat_dong_id' BIGINT REFERENCES 'gia_tri_danh_mucs(id)', 
  'noi_dung_hoat_dong' TEXT, 
  'don_vi_thuc_hien_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)', 
  'thoi_gian_bat_dau' TIMESTAMP WITH TIME ZONE, 
  'thoi_gian_ket_thuc' TIMESTAMP WITH TIME ZONE, 
  'dia_diem' VARCHAR(255),
  'nguoi_chu_tri_id' BIGINT REFERENCES 'users(id)', 
  'so_luong_tham_gia' INTEGER, 
  'ket_qua_hoat_dong' TEXT, 
  'tap_tin_dinh_kem_id' UUID REFERENCES 'du_lieu_tep_tins(id)', 
);

COMMENT ON TABLE hoat_dong_giao_duc_chinh_tris IS 'Các hoạt động giáo dục chính trị cụ thể đã triển khai.'; 
COMMENT ON COLUMN hoat_dong_giao_duc_chinh_tris.'id' IS 'Khóa chính'; 
COMMENT ON COLUMN hoat_dong_giao_duc_chinh_tris.'ke_hoach_giao_duc_chinh_tri_id' IS 'Liên kết với kế hoạch GDCT (nếu có)'; 
COMMENT ON COLUMN hoat_dong_giao_duc_chinh_tris.'ten_hoat_dong' IS 'Tên hoạt động giáo dục chính trị'; 
COMMENT ON COLUMN hoat_dong_giao_duc_chinh_tris.'loai_hoat_dong_id' IS 'ID Loại hoạt động GDCT (tham chiếu ''gia_tri_danh_mucs'' mã ''DMLOAIHD_GDCT'')'; 
COMMENT ON COLUMN hoat_dong_giao_duc_chinh_tris.'noi_dung_hoat_dong' IS 'Nội dung chi tiết của hoạt động'; 
COMMENT ON COLUMN hoat_dong_giao_duc_chinh_tris.'don_vi_thuc_hien_id' IS 'ID Đơn vị thực hiện (tham chiếu ''gia_tri_danh_mucs'' mã ''DMDONVI'')'; 
COMMENT ON COLUMN hoat_dong_giao_duc_chinh_tris.'thoi_gian_bat_dau' IS 'Thời gian bắt đầu hoạt động'; 
COMMENT ON COLUMN hoat_dong_giao_duc_chinh_tris.'thoi_gian_ket_thuc' IS 'Thời gian kết thúc hoạt động'; 
COMMENT ON COLUMN hoat_dong_giao_duc_chinh_tris.'dia_diem' IS 'Địa điểm tổ chức'; 
COMMENT ON COLUMN hoat_dong_giao_duc_chinh_tris.'nguoi_chu_tri_id' IS 'Người chủ trì hoạt động'; 
COMMENT ON COLUMN hoat_dong_giao_duc_chinh_tris.'so_luong_tham_gia' IS 'Số lượng người tham gia dự kiến/thực tế'; 
COMMENT ON COLUMN hoat_dong_giao_duc_chinh_tris.'ket_qua_hoat_dong' IS 'Kết quả, đánh giá hoạt động'; 
COMMENT ON COLUMN hoat_dong_giao_duc_chinh_tris.'tap_tin_dinh_kem_id' IS 'ID Tệp đính kèm (hình ảnh, tài liệu...)'; 

-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS tham_gia_giao_duc_chinh_tris ( 
  'id' BIGSERIAL PRIMARY KEY,
  'hoat_dong_giao_duc_chinh_tri_id' BIGINT NOT NULL REFERENCES 'hoat_dong_giao_duc_chinh_tris(id)' ON DELETE CASCADE, 
  'quan_nhan_id' VARCHAR(20) NOT NULL REFERENCES 'quan_nhans(so_hieu_quan_nhan)' ON DELETE CASCADE, 
  'thoi_gian_tham_gia' TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP, 
  'vai_tro_tham_gia' VARCHAR(100), 
  'danh_gia' TEXT,
  'ghi_chu' TEXT 
);

COMMENT ON TABLE tham_gia_giao_duc_chinh_tris IS 'Ghi nhận sự tham gia của quân nhân vào các hoạt động GDCT.'; 
COMMENT ON COLUMN tham_gia_giao_duc_chinh_tris.'id' IS 'Khóa chính'; 
COMMENT ON COLUMN tham_gia_giao_duc_chinh_tris.'hoat_dong_giao_duc_chinh_tri_id' IS 'Liên kết với hoạt động GDCT'; 
COMMENT ON COLUMN tham_gia_giao_duc_chinh_tris.'quan_nhan_id' IS 'Liên kết với quân nhân tham gia'; 
COMMENT ON COLUMN tham_gia_giao_duc_chinh_tris.'thoi_gian_tham_gia' IS 'Thời điểm ghi nhận tham gia'; 
COMMENT ON COLUMN tham_gia_giao_duc_chinh_tris.'vai_tro_tham_gia' IS 'Vai trò tham gia (ví dụ: học viên, báo cáo viên)'; 
COMMENT ON COLUMN tham_gia_giao_duc_chinh_tris.'danh_gia' IS 'Đánh giá (nếu có)'; 
COMMENT ON COLUMN tham_gia_giao_duc_chinh_tris.'ghi_chu' IS 'Ghi chú'; 


-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS ket_qua_hoc_tap_chinh_tris ( 
  'id' BIGSERIAL PRIMARY KEY,
  'quan_nhan_id' VARCHAR(20) NOT NULL REFERENCES 'quan_nhans(so_hieu_quan_nhan)' ON DELETE CASCADE, 
  'hoat_dong_giao_duc_chinh_tri_id' BIGINT REFERENCES 'hoat_dong_giao_duc_chinh_tris(id)', 
  'ten_bai_kiem_tra' VARCHAR(255), 
  'ngay_kiem_tra' DATE NOT NULL, 
  'diem_so' DECIMAL(5,2), 
  'xep_loai_id' BIGINT REFERENCES 'gia_tri_danh_mucs(id)',
  'nhan_xet' TEXT, 
  'tap_tin_dinh_kem_id' UUID REFERENCES 'du_lieu_tep_tins(id)', 

);

COMMENT ON TABLE ket_qua_hoc_tap_chinh_tris IS 'Kết quả học tập, kiểm tra nhận thức chính trị.'; 
COMMENT ON COLUMN ket_qua_hoc_tap_chinh_tris.'id' IS 'Khóa chính'; 
COMMENT ON COLUMN ket_qua_hoc_tap_chinh_tris.'quan_nhan_id' IS 'Liên kết với quân nhân'; 
COMMENT ON COLUMN ket_qua_hoc_tap_chinh_tris.'hoat_dong_giao_duc_chinh_tri_id' IS 'Liên kết với hoạt động GDCT (nếu là kết quả của một hoạt động cụ thể)'; 
COMMENT ON COLUMN ket_qua_hoc_tap_chinh_tris.'ten_bai_kiem_tra' IS 'Tên bài kiểm tra/đợt học tập'; 
COMMENT ON COLUMN ket_qua_hoc_tap_chinh_tris.'ngay_kiem_tra' IS 'Ngày kiểm tra/đánh giá'; 
COMMENT ON COLUMN ket_qua_hoc_tap_chinh_tris.'diem_so' IS 'Điểm số (nếu có)'; 
COMMENT ON COLUMN ket_qua_hoc_tap_chinh_tris.'xep_loai_id' IS 'Xếp loại (tham chiếu ''gia_tri_danh_mucs'' mã ''DMXEPLOAIHT'')'; 
COMMENT ON COLUMN ket_qua_hoc_tap_chinh_tris.'nhan_xet' IS 'Nhận xét của cán bộ'; 
COMMENT ON COLUMN ket_qua_hoc_tap_chinh_tris.'tap_tin_dinh_kem_id' IS 'ID Tệp đính kèm bài làm, kết quả'; 


-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS ghi_nhan_tinh_hinh_tu_tuongs ( 
  'id' BIGSERIAL PRIMARY KEY,
  'don_vi_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)',
  'quan_nhan_lien_quan_id' VARCHAR(20) REFERENCES 'quan_nhans(so_hieu_quan_nhan)', 
  'ngay_ghi_nhan' DATE NOT NULL, 
  'noi_dung_tinh_hinh' TEXT NOT NULL, 
  'nguon_goc' TEXT, 
  'muc_do_anh_huong_id' BIGINT REFERENCES 'gia_tri_danh_mucs(id)', 
  'de_xuat_xu_ly' TEXT, 
);

COMMENT ON TABLE ghi_nhan_tinh_hinh_tu_tuongs IS 'Ghi nhận, nắm bắt tình hình tư tưởng trong đơn vị.'; 
COMMENT ON COLUMN ghi_nhan_tinh_hinh_tu_tuongs.'id' IS 'Khóa chính'; 
COMMENT ON COLUMN ghi_nhan_tinh_hinh_tu_tuongs.'don_vi_id' IS 'Đơn vị được ghi nhận (tham chiếu ''gia_tri_danh_mucs'' mã ''DMDONVI'')'; 
COMMENT ON COLUMN ghi_nhan_tinh_hinh_tu_tuongs.'quan_nhan_lien_quan_id' IS 'Quân nhân liên quan (nếu là vấn đề cá nhân)'; 
COMMENT ON COLUMN ghi_nhan_tinh_hinh_tu_tuongs.'ngay_ghi_nhan' IS 'Ngày ghi nhận'; 
COMMENT ON COLUMN ghi_nhan_tinh_hinh_tu_tuongs.'noi_dung_tinh_hinh' IS 'Nội dung tình hình tư tưởng'; 
COMMENT ON COLUMN ghi_nhan_tinh_hinh_tu_tuongs.'nguon_goc' IS 'Nguồn gốc thông tin'; 
COMMENT ON COLUMN ghi_nhan_tinh_hinh_tu_tuongs.'muc_do_anh_huong_id' IS 'Mức độ ảnh hưởng (tham chiếu ''gia_tri_danh_mucs'' mã ''DMMUCDOAH'')'; 
COMMENT ON COLUMN ghi_nhan_tinh_hinh_tu_tuongs.'de_xuat_xu_ly' IS 'Đề xuất hướng xử lý'; 

-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS bien_phap_tac_dong_tu_tuongs ( 
  'id' BIGSERIAL PRIMARY KEY,
  'ghi_nhan_tinh_hinh_tu_tuong_id' BIGINT REFERENCES 'ghi_nhan_tinh_hinh_tu_tuongs(id)' ON DELETE SET NULL, 
  'ten_bien_phap' VARCHAR(500) NOT NULL, 
  'noi_dung_bien_phap' TEXT, 
  'don_vi_thuc_hien_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)', 
  'nguoi_thuc_hien_id' BIGINT REFERENCES 'users(id)', 
  'thoi_gian_bat_dau' DATE, 
  'thoi_gian_ket_thuc' DATE, 
  'ket_qua_danh_gia' TEXT, 
);

COMMENT ON TABLE bien_phap_tac_dong_tu_tuongs IS 'Các biện pháp tác động, giải quyết vấn đề tư tưởng.'; 
COMMENT ON COLUMN bien_phap_tac_dong_tu_tuongs.'id' IS 'Khóa chính'; 
COMMENT ON COLUMN bien_phap_tac_dong_tu_tuongs.'ghi_nhan_tinh_hinh_tu_tuong_id' IS 'Liên kết với ghi nhận tình hình tư tưởng (nếu có)'; 
COMMENT ON COLUMN bien_phap_tac_dong_tu_tuongs.'ten_bien_phap' IS 'Tên biện pháp tác động'; 
COMMENT ON COLUMN bien_phap_tac_dong_tu_tuongs.'noi_dung_bien_phap' IS 'Nội dung chi tiết của biện pháp'; 
COMMENT ON COLUMN bien_phap_tac_dong_tu_tuongs.'don_vi_thuc_hien_id' IS 'Đơn vị thực hiện (tham chiếu ''gia_tri_danh_mucs'' mã ''DMDONVI'')'; 
COMMENT ON COLUMN bien_phap_tac_dong_tu_tuongs.'nguoi_thuc_hien_id' IS 'Người chịu trách nhiệm thực hiện'; 
COMMENT ON COLUMN bien_phap_tac_dong_tu_tuongs.'thoi_gian_bat_dau' IS 'Thời gian bắt đầu thực hiện'; 
COMMENT ON COLUMN bien_phap_tac_dong_tu_tuongs.'thoi_gian_ket_thuc' IS 'Thời gian dự kiến/kết thúc'; 
COMMENT ON COLUMN bien_phap_tac_dong_tu_tuongs.'ket_qua_danh_gia' IS 'Kết quả, đánh giá hiệu quả của biện pháp'; 

-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS ke_hoach_tuyen_truyen_van_hoas ( 
  'id' BIGSERIAL PRIMARY KEY,
  'ten_ke_hoach' VARCHAR(500) NOT NULL, 
  'loai_hinh_hoat_dong_id' BIGINT REFERENCES 'gia_tri_danh_mucs(id)', 
  'don_vi_to_chuc_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)', 
  'thoi_gian_du_kien' VARCHAR(255), 
  'noi_dung_ke_hoach' TEXT, 
  'muc_dich_yeu_cau' TEXT, 
  'kinh_phi_du_kien' DECIMAL(18,2), 
  'nguoi_phu_trach_id' BIGINT REFERENCES 'users(id)', 
  'tap_tin_dinh_kem_id' UUID REFERENCES 'du_lieu_tep_tins(id)', 
);

COMMENT ON TABLE ke_hoach_tuyen_truyen_van_hoas IS 'Kế hoạch công tác tuyên truyền, văn hóa, văn nghệ.'; 
COMMENT ON COLUMN ke_hoach_tuyen_truyen_van_hoas.'id' IS 'Khóa chính'; 
COMMENT ON COLUMN ke_hoach_tuyen_truyen_van_hoas.'ten_ke_hoach' IS 'Tên kế hoạch'; 
COMMENT ON COLUMN ke_hoach_tuyen_truyen_van_hoas.'loai_hinh_hoat_dong_id' IS 'Loại hình hoạt động (tham chiếu ''gia_tri_danh_mucs'' mã ''DMLOAIHINHTTVH'')'; 
COMMENT ON COLUMN ke_hoach_tuyen_truyen_van_hoas.'don_vi_to_chuc_id' IS 'Đơn vị tổ chức (tham chiếu ''gia_tri_danh_mucs'' mã ''DMDONVI'')'; 
COMMENT ON COLUMN ke_hoach_tuyen_truyen_van_hoas.'thoi_gian_du_kien' IS 'Thời gian dự kiến (ví dụ: Quý I/2024, Tháng 5/2024)'; 
COMMENT ON COLUMN ke_hoach_tuyen_truyen_van_hoas.'noi_dung_ke_hoach' IS 'Nội dung chính của kế hoạch'; 
COMMENT ON COLUMN ke_hoach_tuyen_truyen_van_hoas.'muc_dich_yeu_cau' IS 'Mục đích, yêu cầu'; 
COMMENT ON COLUMN ke_hoach_tuyen_truyen_van_hoas.'kinh_phi_du_kien' IS 'Kinh phí dự kiến'; 
COMMENT ON COLUMN ke_hoach_tuyen_truyen_van_hoas.'nguoi_phu_trach_id' IS 'Người phụ trách'; 
COMMENT ON COLUMN ke_hoach_tuyen_truyen_van_hoas.'tap_tin_dinh_kem_id' IS 'ID Tệp đính kèm kế hoạch'; 

-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS hoat_dong_tuyen_truyen_van_hoas ( 
  'id' BIGSERIAL PRIMARY KEY,
  'ke_hoach_tuyen_truyen_van_hoa_id' BIGINT REFERENCES 'ke_hoach_tuyen_truyen_van_hoas(id)', 
  'ten_hoat_dong' VARCHAR(500) NOT NULL, 
  'don_vi_thuc_hien_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)', 
  'thoi_gian_thuc_hien' DATE, 
  'dia_diem' VARCHAR(255),
  'noi_dung_thuc_hien' TEXT, 
  'so_luong_nguoi_tham_gia' INTEGER, 
  'kinh_phi_thuc_te' DECIMAL(18,2), 
  'danh_gia_ket_qua' TEXT, 
  'tap_tin_dinh_kem_id' UUID REFERENCES 'du_lieu_tep_tins(id)', 

);

COMMENT ON TABLE hoat_dong_tuyen_truyen_van_hoas IS 'Các hoạt động tuyên truyền, văn hóa đã thực hiện.'; 
COMMENT ON COLUMN hoat_dong_tuyen_truyen_van_hoas.'id' IS 'Khóa chính'; 
COMMENT ON COLUMN hoat_dong_tuyen_truyen_van_hoas.'ke_hoach_tuyen_truyen_van_hoa_id' IS 'Liên kết với kế hoạch (nếu có)'; 
COMMENT ON COLUMN hoat_dong_tuyen_truyen_van_hoas.'ten_hoat_dong' IS 'Tên hoạt động'; 
COMMENT ON COLUMN hoat_dong_tuyen_truyen_van_hoas.'don_vi_thuc_hien_id' IS 'Đơn vị thực hiện (tham chiếu ''gia_tri_danh_mucs'' mã ''DMDONVI'')'; 
COMMENT ON COLUMN hoat_dong_tuyen_truyen_van_hoas.'thoi_gian_thuc_hien' IS 'Thời gian thực hiện'; 
COMMENT ON COLUMN hoat_dong_tuyen_truyen_van_hoas.'dia_diem' IS 'Địa điểm'; 
COMMENT ON COLUMN hoat_dong_tuyen_truyen_van_hoas.'noi_dung_thuc_hien' IS 'Nội dung đã thực hiện'; 
COMMENT ON COLUMN hoat_dong_tuyen_truyen_van_hoas.'so_luong_nguoi_tham_gia' IS 'Số lượng người tham gia'; 
COMMENT ON COLUMN hoat_dong_tuyen_truyen_van_hoas.'kinh_phi_thuc_te' IS 'Kinh phí thực tế'; 
COMMENT ON COLUMN hoat_dong_tuyen_truyen_van_hoas.'danh_gia_ket_qua' IS 'Đánh giá kết quả, hiệu quả'; 
COMMENT ON COLUMN hoat_dong_tuyen_truyen_van_hoas.'tap_tin_dinh_kem_id' IS 'ID Tệp đính kèm (hình ảnh, video...)'; 

-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS phong_trao_thi_dua_chinh_tris ( 
  'id' BIGSERIAL PRIMARY KEY,
  'ten_phong_trao' VARCHAR(500) NOT NULL, 
  'don_vi_phat_dong_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)', 
  'ngay_phat_dong' DATE, 
  'ngay_ket_thuc' DATE, 
  'noi_dung_phong_trao' TEXT, 
  'chi_tieu_thi_dua' TEXT, 
  'ket_qua_so_ket' TEXT, 
  'ket_qua_tong_ket' TEXT, 
  'tap_tin_dinh_kem_id' UUID REFERENCES 'du_lieu_tep_tins(id)', 

);

COMMENT ON TABLE phong_trao_thi_dua_chinh_tris IS 'Thông tin về các phong trào thi đua trong công tác chính trị.'; 
COMMENT ON COLUMN phong_trao_thi_dua_chinh_tris.'id' IS 'Khóa chính'; 
COMMENT ON COLUMN phong_trao_thi_dua_chinh_tris.'ten_phong_trao' IS 'Tên phong trào thi đua'; 
COMMENT ON COLUMN phong_trao_thi_dua_chinh_tris.'don_vi_phat_dong_id' IS 'Đơn vị phát động (tham chiếu ''gia_tri_danh_mucs'' mã ''DMDONVI'')'; 
COMMENT ON COLUMN phong_trao_thi_dua_chinh_tris.'ngay_phat_dong' IS 'Ngày phát động'; 
COMMENT ON COLUMN phong_trao_thi_dua_chinh_tris.'ngay_ket_thuc' IS 'Ngày kết thúc (dự kiến/thực tế)'; 
COMMENT ON COLUMN phong_trao_thi_dua_chinh_tris.'noi_dung_phong_trao' IS 'Nội dung chính của phong trào'; 
COMMENT ON COLUMN phong_trao_thi_dua_chinh_tris.'chi_tieu_thi_dua' IS 'Chỉ tiêu thi đua'; 
COMMENT ON COLUMN phong_trao_thi_dua_chinh_tris.'ket_qua_so_ket' IS 'Kết quả sơ kết (nếu có)'; 
COMMENT ON COLUMN phong_trao_thi_dua_chinh_tris.'ket_qua_tong_ket' IS 'Kết quả tổng kết'; 
COMMENT ON COLUMN phong_trao_thi_dua_chinh_tris.'tap_tin_dinh_kem_id' IS 'ID Tệp đính kèm liên quan'; 


-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS chinh_sach_hau_phuongs ( 
  'id' BIGSERIAL PRIMARY KEY,
  'ten_chinh_sach' VARCHAR(500) NOT NULL, 
  'loai_chinh_sach_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)', 
  'noi_dung_chinh_sach' TEXT, 
  'doi_tuong_ap_dung' TEXT, 
  'van_ban_phap_ly_id' UUID REFERENCES 'du_lieu_tep_tins(id)', 
  'ngay_hieu_luc' DATE, 
  'ngay_het_hieu_luc' DATE, 
  'ghi_chu' TEXT, 
);

COMMENT ON TABLE chinh_sach_hau_phuongs IS 'Quản lý thông tin về các chính sách hậu phương quân đội.'; 
COMMENT ON COLUMN chinh_sach_hau_phuongs.'id' IS 'Khóa chính'; 
COMMENT ON COLUMN chinh_sach_hau_phuongs.'ten_chinh_sach' IS 'Tên chính sách'; 
COMMENT ON COLUMN chinh_sach_hau_phuongs.'loai_chinh_sach_id' IS 'Loại chính sách (tham chiếu ''gia_tri_danh_mucs'' mã ''DMLOAICS_HP'')'; 
COMMENT ON COLUMN chinh_sach_hau_phuongs.'noi_dung_chinh_sach' IS 'Nội dung chi tiết của chính sách'; 
COMMENT ON COLUMN chinh_sach_hau_phuongs.'doi_tuong_ap_dung' IS 'Đối tượng áp dụng'; 
COMMENT ON COLUMN chinh_sach_hau_phuongs.'van_ban_phap_ly_id' IS 'ID Văn bản pháp lý liên quan'; 
COMMENT ON COLUMN chinh_sach_hau_phuongs.'ngay_hieu_luc' IS 'Ngày chính sách có hiệu lực'; 
COMMENT ON COLUMN chinh_sach_hau_phuongs.'ngay_het_hieu_luc' IS 'Ngày chính sách hết hiệu lực'; 
COMMENT ON COLUMN chinh_sach_hau_phuongs.'ghi_chu' IS 'Ghi chú'; 


-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS doi_tuong_chinh_sach_hau_phuongs ( 
  'id' BIGSERIAL PRIMARY KEY,
  'chinh_sach_hau_phuong_id' BIGINT NOT NULL REFERENCES 'chinh_sach_hau_phuongs(id)' ON DELETE CASCADE, 
  'quan_nhan_id' VARCHAR(20) REFERENCES 'quan_nhans(so_hieu_quan_nhan)', 
  'ho_ten_nguoi_than' VARCHAR(100), 
  'moi_quan_he_voi_quan_nhan_id' BIGINT REFERENCES 'gia_tri_danh_mucs(id)', 
  'dia_chi' VARCHAR(255),
  'so_dien_thoai' VARCHAR(15), 
  'ngay_bat_dau_huong' DATE, 
  'ngay_ket_thuc_huong' DATE, 
  'ghi_chu' TEXT, 
);

COMMENT ON TABLE doi_tuong_chinh_sach_hau_phuongs IS 'Danh sách các đối tượng được hưởng chính sách hậu phương.'; 
COMMENT ON COLUMN doi_tuong_chinh_sach_hau_phuongs.'id' IS 'Khóa chính'; 
COMMENT ON COLUMN doi_tuong_chinh_sach_hau_phuongs.'chinh_sach_hau_phuong_id' IS 'Liên kết với chính sách hậu phương'; 
COMMENT ON COLUMN doi_tuong_chinh_sach_hau_phuongs.'quan_nhan_id' IS 'Quân nhân là đối tượng (nếu chính sách áp dụng cho quân nhân)'; 
COMMENT ON COLUMN doi_tuong_chinh_sach_hau_phuongs.'ho_ten_nguoi_than' IS 'Họ tên người thân (nếu chính sách áp dụng cho thân nhân)'; 
COMMENT ON COLUMN doi_tuong_chinh_sach_hau_phuongs.'moi_quan_he_voi_quan_nhan_id' IS 'Mối quan hệ với quân nhân (tham chiếu ''gia_tri_danh_mucs'' mã ''DMMOIQUANHE'')'; 
COMMENT ON COLUMN doi_tuong_chinh_sach_hau_phuongs.'dia_chi' IS 'Địa chỉ của đối tượng'; 
COMMENT ON COLUMN doi_tuong_chinh_sach_hau_phuongs.'so_dien_thoai' IS 'Số điện thoại'; 
COMMENT ON COLUMN doi_tuong_chinh_sach_hau_phuongs.'ngay_bat_dau_huong' IS 'Ngày bắt đầu hưởng chính sách'; 
COMMENT ON COLUMN doi_tuong_chinh_sach_hau_phuongs.'ngay_ket_thuc_huong' IS 'Ngày kết thúc hưởng chính sách'; 
COMMENT ON COLUMN doi_tuong_chinh_sach_hau_phuongs.'ghi_chu' IS 'Ghi chú'; 


-- -----------------------------------------------------


CREATE TABLE IF NOT EXISTS thong_baos ( 
  'id' BIGSERIAL PRIMARY KEY,
  'nguoi_nhan_id' BIGINT NOT NULL REFERENCES 'users(id)' ON DELETE CASCADE, 
  'nguoi_gui_id' BIGINT REFERENCES 'users(id)' ON DELETE SET NULL, 
  'loai_thong_bao_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)', 
  'tieu_de' VARCHAR(255) NOT NULL, 
  'noi_dung' TEXT NOT NULL,
  'ngay_gui' TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP, 
  'da_doc' BOOLEAN NOT NULL DEFAULT FALSE, 
  'ngay_doc' TIMESTAMP WITH TIME ZONE, 
  'duong_dan_lien_quan' VARCHAR(500), 
  'doi_tuong_lien_quan_id' VARCHAR(100), 
  'bang_doi_tuong_lien_quan' VARCHAR(100) 
);

COMMENT ON TABLE thong_baos IS 'Bảng Thông báo gửi đến người dùng.'; 
COMMENT ON COLUMN thong_baos.'id' IS 'Khóa chính'; 
COMMENT ON COLUMN thong_baos.'nguoi_nhan_id' IS 'ID Người nhận thông báo'; 
COMMENT ON COLUMN thong_baos.'nguoi_gui_id' IS 'ID Người gửi (NULL nếu là thông báo hệ thống)'; 
COMMENT ON COLUMN thong_baos.'loai_thong_bao_id' IS 'ID Loại thông báo (tham chiếu ''gia_tri_danh_mucs'' mã ''DMLOAITB'')'; 
COMMENT ON COLUMN thong_baos.'tieu_de' IS 'Tiêu đề thông báo'; 
COMMENT ON COLUMN thong_baos.'noi_dung' IS 'Nội dung chi tiết của thông báo'; 
COMMENT ON COLUMN thong_baos.'ngay_gui' IS 'Thời điểm gửi thông báo'; 
COMMENT ON COLUMN thong_baos.'da_doc' IS 'Trạng thái đã đọc hay chưa'; 
COMMENT ON COLUMN thong_baos.'ngay_doc' IS 'Thời điểm đọc thông báo'; 
COMMENT ON COLUMN thong_baos.'duong_dan_lien_quan' IS 'Đường dẫn URL liên quan đến nội dung thông báo (nếu có)'; 
COMMENT ON COLUMN thong_baos.'doi_tuong_lien_quan_id' IS 'ID của đối tượng liên quan (ví dụ: ID đề xuất khen thưởng)'; 
COMMENT ON COLUMN thong_baos.'bang_doi_tuong_lien_quan' IS 'Tên bảng của đối tượng liên quan'; 


-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS quy_trinh_phe_duyets ( 
  'id' BIGSERIAL PRIMARY KEY,
  'ma_quy_trinh' VARCHAR(100) UNIQUE NOT NULL, 
  'ten_quy_trinh' VARCHAR(255) NOT NULL, 
  'mo_ta' VARCHAR(500),
  'module_ap_dung' VARCHAR(50), 
  'bang_doi_tuong_muc_tieu' VARCHAR(100) NOT NULL, 
  'con_hieu_luc' BOOLEAN DEFAULT TRUE 
);

COMMENT ON TABLE quy_trinh_phe_duyets IS 'Bảng định nghĩa các loại quy trình phê duyệt.'; 
COMMENT ON COLUMN quy_trinh_phe_duyets.'id' IS 'Khóa chính'; 
COMMENT ON COLUMN quy_trinh_phe_duyets.'ma_quy_trinh' IS 'Mã quy trình phê duyệt (ví dụ: "DEXUAT_KHENTHUONG")'; 
COMMENT ON COLUMN quy_trinh_phe_duyets.'ten_quy_trinh' IS 'Tên quy trình phê duyệt'; 
COMMENT ON COLUMN quy_trinh_phe_duyets.'mo_ta' IS 'Mô tả chi tiết'; 
COMMENT ON COLUMN quy_trinh_phe_duyets.'module_ap_dung' IS 'Module áp dụng quy trình này (ví dụ: "QLQN", "CTD")'; 
COMMENT ON COLUMN quy_trinh_phe_duyets.'bang_doi_tuong_muc_tieu' IS 'Tên bảng của đối tượng cần phê duyệt (ví dụ: ''de_xuat_khen_thuongs'')'; 
COMMENT ON COLUMN quy_trinh_phe_duyets.'con_hieu_luc' IS 'Quy trình còn được sử dụng hay không'; 


-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS yeu_cau_phe_duyets ( 
  'id' BIGSERIAL PRIMARY KEY,
  'quy_trinh_id' BIGINT NOT NULL REFERENCES 'quy_trinh_phe_duyets(id)', 
  'doi_tuong_muc_tieu_id' VARCHAR(255) NOT NULL, 
  'nguoi_de_xuat_id' BIGINT NOT NULL REFERENCES 'users(id)', 
  'don_vi_de_xuat_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)', 
  'ngay_de_xuat' TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP, 
  'trang_thai_phe_duyet_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)', 
  'nguoi_xu_ly_hien_tai_id' BIGINT REFERENCES 'users(id)', 
  'don_vi_xu_ly_hien_tai_id' BIGINT REFERENCES 'gia_tri_danh_mucs(id)', 
  'buoc_phe_duyet_hien_tai' INTEGER, 
  'ngay_cap_nhat_cuoi' TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP, 
  'noi_dung_de_xuat' TEXT 
);

COMMENT ON TABLE yeu_cau_phe_duyets IS 'Bảng lưu trữ thông tin các yêu cầu phê duyệt cụ thể.'; 
COMMENT ON COLUMN yeu_cau_phe_duyets.'id' IS 'Khóa chính'; 
COMMENT ON COLUMN yeu_cau_phe_duyets.'quy_trinh_id' IS 'Liên kết với loại quy trình phê duyệt'; 
COMMENT ON COLUMN yeu_cau_phe_duyets.'doi_tuong_muc_tieu_id' IS 'ID của bản ghi đối tượng cần phê duyệt (tham chiếu đến bảng trong ''bang_doi_tuong_muc_tieu'' của ''quy_trinh_phe_duyets'')'; 
COMMENT ON COLUMN yeu_cau_phe_duyets.'nguoi_de_xuat_id' IS 'Người tạo yêu cầu phê duyệt'; 
COMMENT ON COLUMN yeu_cau_phe_duyets.'don_vi_de_xuat_id' IS 'Đơn vị của người đề xuất (tham chiếu ''gia_tri_danh_mucs'' mã ''DMDONVI'')'; 
COMMENT ON COLUMN yeu_cau_phe_duyets.'ngay_de_xuat' IS 'Ngày tạo yêu cầu'; 
COMMENT ON COLUMN yeu_cau_phe_duyets.'trang_thai_phe_duyet_id' IS 'ID Trạng thái phê duyệt (tham chiếu ''gia_tri_danh_mucs'' mã ''DMTRANGTHAIPD'')'; 
COMMENT ON COLUMN yeu_cau_phe_duyets.'nguoi_xu_ly_hien_tai_id' IS 'Người đang xử lý yêu cầu hiện tại'; 
COMMENT ON COLUMN yeu_cau_phe_duyets.'don_vi_xu_ly_hien_tai_id' IS 'Đơn vị đang xử lý yêu cầu hiện tại (tham chiếu ''gia_tri_danh_mucs'' mã ''DMDONVI'')'; 
COMMENT ON COLUMN yeu_cau_phe_duyets.'buoc_phe_duyet_hien_tai' IS 'Số thứ tự bước phê duyệt hiện tại (tham chiếu ''buoc_phe_duyets.thu_tu_buoc'')'; 
COMMENT ON COLUMN yeu_cau_phe_duyets.'ngay_cap_nhat_cuoi' IS 'Ngày cập nhật trạng thái lần cuối'; 
COMMENT ON COLUMN yeu_cau_phe_duyets.'noi_dung_de_xuat' IS 'Nội dung tóm tắt của đề xuất'; 


-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS buoc_phe_duyets ( 
  'id' BIGSERIAL PRIMARY KEY,
  'quy_trinh_id' BIGINT NOT NULL REFERENCES 'quy_trinh_phe_duyets(id)' ON DELETE CASCADE, 
  'thu_tu_buoc' INTEGER NOT NULL, 
  'ten_buoc' VARCHAR(255) NOT NULL,
  'role_id' BIGINT REFERENCES 'roles(id)', 
  'so_ngay_xu_ly_toi_da' INTEGER, 
  'hanh_dong_khi_den_han' VARCHAR(50), 
  'mo_ta' VARCHAR(500),
  UNIQUE (quy_trinh_id, thu_tu_buoc) 
);

COMMENT ON TABLE buoc_phe_duyets IS 'Bảng định nghĩa các bước cụ thể cho mỗi quy trình phê duyệt.'; 
COMMENT ON COLUMN buoc_phe_duyets.'id' IS 'Khóa chính'; 
COMMENT ON COLUMN buoc_phe_duyets.'quy_trinh_id' IS 'Liên kết với loại quy trình phê duyệt'; 
COMMENT ON COLUMN buoc_phe_duyets.'thu_tu_buoc' IS 'Thứ tự của bước trong quy trình'; 
COMMENT ON COLUMN buoc_phe_duyets.'ten_buoc' IS 'Tên của bước phê duyệt'; 
COMMENT ON COLUMN buoc_phe_duyets.'role_id' IS 'Vai trò người xử lý bước này (nếu có)'; 
COMMENT ON COLUMN buoc_phe_duyets.'so_ngay_xu_ly_toi_da' IS 'Số ngày tối đa cho phép xử lý bước này'; 
COMMENT ON COLUMN buoc_phe_duyets.'hanh_dong_khi_den_han' IS 'Hành động khi đến hạn (ví dụ: "AUTO_REMIND", "AUTO_ESCALATE")'; 
COMMENT ON COLUMN buoc_phe_duyets.'mo_ta' IS 'Mô tả chi tiết về bước này'; 


-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS lich_su_phe_duyets ( 
  'id' BIGSERIAL PRIMARY KEY,
  'yeu_cau_phe_duyet_id' BIGINT NOT NULL REFERENCES 'yeu_cau_phe_duyets(id)' ON DELETE CASCADE, 
  'nguoi_xu_ly_id' BIGINT NOT NULL REFERENCES 'users(id)', 
  'don_vi_xu_ly_id' BIGINT REFERENCES 'gia_tri_danh_mucs(id)', 
  'ngay_xu_ly' TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP, 
  'hanh_dong_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)',
  'trang_thai_truoc_id' BIGINT REFERENCES 'gia_tri_danh_mucs(id)', 
  'trang_thai_sau_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)', 
  'y_kien_xu_ly' TEXT, 
  'tap_tin_dinh_kem_id' UUID REFERENCES 'du_lieu_tep_tins(id)' 
);

COMMENT ON TABLE lich_su_phe_duyets IS 'Bảng Lịch sử Phê duyệt.'; 
COMMENT ON COLUMN lich_su_phe_duyets.'id' IS 'Khóa chính'; 
COMMENT ON COLUMN lich_su_phe_duyets.'yeu_cau_phe_duyet_id' IS 'Liên kết với yêu cầu phê duyệt'; 
COMMENT ON COLUMN lich_su_phe_duyets.'nguoi_xu_ly_id' IS 'Người thực hiện hành động phê duyệt'; 
COMMENT ON COLUMN lich_su_phe_duyets.'don_vi_xu_ly_id' IS 'Đơn vị của người xử lý (tham chiếu ''gia_tri_danh_mucs'' mã ''DMDONVI'')'; 
COMMENT ON COLUMN lich_su_phe_duyets.'ngay_xu_ly' IS 'Thời điểm thực hiện hành động'; 
COMMENT ON COLUMN lich_su_phe_duyets.'hanh_dong_id' IS 'Hành động phê duyệt (Phê duyệt, Từ chối, Y/c bổ sung -- tham chiếu ''gia_tri_danh_mucs'' mã ''DMHANHDONGPD'')'; 
COMMENT ON COLUMN lich_su_phe_duyets.'trang_thai_truoc_id' IS 'Trạng thái trước khi hành động (tham chiếu ''gia_tri_danh_mucs'' mã ''DMTRANGTHAIPD'')'; 
COMMENT ON COLUMN lich_su_phe_duyets.'trang_thai_sau_id' IS 'Trạng thái sau khi hành động (tham chiếu ''gia_tri_danh_mucs'' mã ''DMTRANGTHAIPD'')'; 
COMMENT ON COLUMN lich_su_phe_duyets.'y_kien_xu_ly' IS 'Ý kiến, ghi chú của người xử lý'; 
COMMENT ON COLUMN lich_su_phe_duyets.'tap_tin_dinh_kem_id' IS 'Tệp đính kèm của người xử lý (nếu có)'; 


-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS nhat_ky_he_thongs ( 
  'id' BIGSERIAL PRIMARY KEY,
  'thoi_gian_xay_ra' TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP, 
  'nguoi_dung_id' BIGINT REFERENCES 'users(id)' ON DELETE SET NULL,
  'dia_chi_ip' VARCHAR(45), 
  'don_vi_nguoi_dung_id' BIGINT REFERENCES 'gia_tri_danh_mucs(id)', 
  'module_muc_tieu' VARCHAR(50), 
  'chuc_nang_muc_tieu' VARCHAR(100), 
  'loai_hanh_dong' VARCHAR(100) NOT NULL,
  'ten_bang_muc_tieu' VARCHAR(100), 
  'khoa_chinh_ban_ghi' VARCHAR(255), 
  'mo_ta' VARCHAR(1000), 
  'du_lieu_cu' JSONB, 
  'du_lieu_moi' JSONB, 
  'trang_thai_hanh_dong_id' BIGINT NOT NULL REFERENCES 'gia_tri_danh_mucs(id)',
  'ma_loi' VARCHAR(50), 
  'thong_bao_loi' VARCHAR(500), 
  'is_su_kien_he_thong' BOOLEAN DEFAULT FALSE 
);

COMMENT ON TABLE nhat_ky_he_thongs IS 'Bảng Ghi Log Hệ thống (Audit Log).'; 
COMMENT ON COLUMN nhat_ky_he_thongs.'id' IS 'Khóa chính'; 
COMMENT ON COLUMN nhat_ky_he_thongs.'thoi_gian_xay_ra' IS 'Thời điểm xảy ra sự kiện'; 
COMMENT ON COLUMN nhat_ky_he_thongs.'nguoi_dung_id' IS 'ID Người dùng thực hiện hành động (NULL nếu là sự kiện hệ thống)'; 
COMMENT ON COLUMN nhat_ky_he_thongs.'dia_chi_ip' IS 'Địa chỉ IP của người dùng'; 
COMMENT ON COLUMN nhat_ky_he_thongs.'don_vi_nguoi_dung_id' IS 'Đơn vị của người dùng tại thời điểm thực hiện (tham chiếu ''DMDONVI'')'; 
COMMENT ON COLUMN nhat_ky_he_thongs.'module_muc_tieu' IS 'Module bị tác động (ví dụ: "QLQN", "PhanQuyen")'; 
COMMENT ON COLUMN nhat_ky_he_thongs.'chuc_nang_muc_tieu' IS 'Chức năng bị tác động (ví dụ: "ThemMoiQuanNhan", "CapNhatVaiTro")'; 
COMMENT ON COLUMN nhat_ky_he_thongs.'loai_hanh_dong' IS 'Loại hành động (LOGIN, CREATE, UPDATE, DELETE, VIEW, EXECUTE_WORKFLOW...)'; 
COMMENT ON COLUMN nhat_ky_he_thongs.'ten_bang_muc_tieu' IS 'Tên bảng dữ liệu bị tác động (nếu có)'; 
COMMENT ON COLUMN nhat_ky_he_thongs.'khoa_chinh_ban_ghi' IS 'Khóa chính của bản ghi bị tác động (nếu có)'; 
COMMENT ON COLUMN nhat_ky_he_thongs.'mo_ta' IS 'Mô tả chi tiết hành động'; 
COMMENT ON COLUMN nhat_ky_he_thongs.'du_lieu_cu' IS 'Dữ liệu cũ (trước khi thay đổi, dạng JSON)'; 
COMMENT ON COLUMN nhat_ky_he_thongs.'du_lieu_moi' IS 'Dữ liệu mới (sau khi thay đổi, dạng JSON)'; 
COMMENT ON COLUMN nhat_ky_he_thongs.'trang_thai_hanh_dong_id' IS 'Trạng thái hành động (Thành công, Thất bại -- tham chiếu ''DMTRANGTHAI_AUDIT'')'; 
COMMENT ON COLUMN nhat_ky_he_thongs.'ma_loi' IS 'Mã lỗi (nếu hành động thất bại)'; 
COMMENT ON COLUMN nhat_ky_he_thongs.'thong_bao_loi' IS 'Thông báo lỗi chi tiết (nếu có)'; 
COMMENT ON COLUMN nhat_ky_he_thongs.'is_su_kien_he_thong' IS 'Đánh dấu nếu là sự kiện do hệ thống tự động thực hiện'; 

-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS du_lieu_tep_tins (
  'id' UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  'ten_tap_tin_goc' VARCHAR(255) NOT NULL, 
  'duong_dan_luu_tru' VARCHAR(500) NOT NULL UNIQUE, 
  'kieu_mime' VARCHAR(100), 
  'kich_thuoc_kb' BIGINT, 
  'ngay_tai_len' TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP, 
  'nguoi_tai_len_id' BIGINT NOT NULL REFERENCES 'users(id)', 
  'module_lien_quan' VARCHAR(50), 
  'doi_tuong_lien_quan_id' VARCHAR(100), 
  'bang_doi_tuong_lien_quan' VARCHAR(100), 
  'mo_ta' VARCHAR(500),
  'ma_bam_sha256' VARCHAR(128) 
);

COMMENT ON TABLE du_lieu_tep_tins IS 'Bảng quản lý metadata của các file lưu trữ trên Supabase Storage.'; 
COMMENT ON COLUMN du_lieu_tep_tins.'id' IS 'Khóa chính, tự sinh UUID'; 
COMMENT ON COLUMN du_lieu_tep_tins.'ten_tap_tin_goc' IS 'Tên tệp gốc khi người dùng tải lên'; 
COMMENT ON COLUMN du_lieu_tep_tins.'duong_dan_luu_tru' IS 'Đường dẫn/key của tệp trên Supabase Storage (ví dụ: ''folder/uuid_filename.pdf'')'; 
COMMENT ON COLUMN du_lieu_tep_tins.'kieu_mime' IS 'Kiểu MIME của tệp (ví dụ: ''application/pdf'', ''image/jpeg'')'; 
COMMENT ON COLUMN du_lieu_tep_tins.'kich_thuoc_kb' IS 'Kích thước tệp (tính bằng KB)'; 
COMMENT ON COLUMN du_lieu_tep_tins.'ngay_tai_len' IS 'Thời điểm tệp được tải lên'; 
COMMENT ON COLUMN du_lieu_tep_tins.'nguoi_tai_len_id' IS 'ID Người dùng đã tải tệp lên'; 
COMMENT ON COLUMN du_lieu_tep_tins.'module_lien_quan' IS 'Module mà tệp này liên quan (ví dụ: "QLQN", "CTD")'; 
COMMENT ON COLUMN du_lieu_tep_tins.'doi_tuong_lien_quan_id' IS 'ID của đối tượng mà tệp này đính kèm (ví dụ: ID quyết định, ID hồ sơ)'; 
COMMENT ON COLUMN du_lieu_tep_tins.'bang_doi_tuong_lien_quan' IS 'Tên bảng của đối tượng mà tệp này đính kèm'; 
COMMENT ON COLUMN du_lieu_tep_tins.'mo_ta' IS 'Mô tả thêm về tệp'; 
COMMENT ON COLUMN du_lieu_tep_tins.'ma_bam_sha256' IS 'Mã băm SHA256 của tệp (để kiểm tra tính toàn vẹn, nếu cần)'; 
-- -----------------------------------------------------


