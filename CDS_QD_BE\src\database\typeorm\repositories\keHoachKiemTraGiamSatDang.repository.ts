/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { KeHoachKiemTraGiamSatDangEntity } from '~/database/typeorm/entities/keHoachKiemTraGiamSatDang.entity';

@Injectable()
export class KeHoachKiemTraGiamSatDangRepository extends Repository<KeHoachKiemTraGiamSatDangEntity> {
    constructor(private dataSource: DataSource) {
        super(KeHoachKiemTraGiamSatDangEntity, dataSource.createEntityManager());
    }
}
