Bước 19: Cậ<PERSON> nhật mockDataChiTiet.ts cho "Khen thưởng"
Định nghĩa KhenThuongEntryType:

Thêm dữ liệu mẫu vào mockQuanNhanChiTietData.khenThuong.

TypeScript

// src\views\quan-ly-quan-nhan\chi-tiet\mockDataChiTiet.ts (Thêm/Cập nhật)

export interface KhenThuongEntryType {
id: string; // ID duy nhất cho mỗi dòng
SoQuyetDinh: string;
NgayQuyetDinh: string; // ISO Date string
CapRaQuyetDinhID: string; // Sẽ map sang tên (Bộ Quốc phòng, Quân khu, S<PERSON> đoàn...)
HinhThucKhenThuongID: string; // Sẽ map sang tên (Chiến sĩ thi đua, Bằng khen, Giấy khen...)
LyDoKhenThuong: string; // Hoặc ThanhTichDatDuoc
NamKhenThuong?: number; // Năm được khen thưởng
FileDinhKemURL?: string; // URL tới file quyết định
GhiChu?: string;
}

// Trong mockQuanNhanChiTietData:
// ...
// khenThuong: [
// {
// id: 'kt_001',
// SoQuyetDinh: 'QD101/BQP',
// NgayQuyetDinh: '2016-11-20T00:00:00Z',
// CapRaQuyetDinhID: 'BQP01', // Bộ Quốc phòng
// HinhThucKhenThuongID: 'HTKT_CSTDCS', // Chiến sĩ thi đua cơ sở
// LyDoKhenThuong: 'Hoàn thành xuất sắc nhiệm vụ huấn luyện và sẵn sàng chiến đấu năm 2016.',
// NamKhenThuong: 2016,
// FileDinhKemURL: '/files/khenthuong/qd101_bqp.pdf',
// GhiChu: 'Kèm theo tiền thưởng 1.500.000 VNĐ'
// },
// {
// id: 'kt_002',
// SoQuyetDinh: 'QD55/SD3',
// NgayQuyetDinh: '2018-07-10T00:00:00Z',
// CapRaQuyetDinhID: 'SD3', // Sư đoàn 3
// HinhThucKhenThuongID: 'HTKT_GK', // Giấy khen
// LyDoKhenThuong: 'Đạt thành tích cao trong đợt diễn tập MT-18.',
// NamKhenThuong: 2018,
// FileDinhKemURL: '/files/khenthuong/qd55_sd3.pdf',
// GhiChu: ''
// }
// ] as KhenThuongEntryType[],
// ...
Bước 20: Xây dựng TabPanel cho "Khen thưởng"
Tạo file src\views\quan-ly-quan-nhan\chi-tiet\components\tabs\TabKhenThuong.tsx

Mục đích: Hiển thị danh sách các quyết định khen thưởng, cho phép thêm, sửa, xóa (nếu được phép).

Props:

initialData?: KhenThuongEntryType[]
idQuanNhan: string
State: Tương tự các tab DataGrid trước (listData, openDialog, editingData, openConfirmDelete, deletingId, paginationModel, openDetailDialog, viewingData).

Component Vuexy (MUI & @mui/x-data-grid): Tương tự.

Cấu hình cột cho DataGrid:

SoQuyetDinh
NgayQuyetDinh (định dạng dd/MM/yyyy)
CapRaQuyetDinhID (hiển thị tên)
HinhThucKhenThuongID (hiển thị tên)
LyDoKhenThuong (rút gọn với tooltip)
NamKhenThuong
FileDinhKemURL (Link tải/xem)
GhiChu
Hành động: Xem chi tiết QĐ (mở dialog), Sửa, Xóa.
TypeScript

// src\views\quan-ly-quan-nhan\chi-tiet\components\tabs\TabKhenThuong.tsx
'use client';

import React, { useState, useEffect } from 'react';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import { DataGrid, GridColDef, GridRenderCellParams } from '@mui/x-data-grid';
import IconButton from '@mui/material/IconButton';
import Tooltip from '@mui/material/Tooltip';

import IconPlus from '@tabler/icons-react/dist/esm/icons/IconPlus';
import IconEdit from '@tabler/icons-react/dist/esm/icons/IconEdit';
import IconTrash from '@tabler/icons-react/dist/esm/icons/IconTrash';
import IconEye from '@tabler/icons-react/dist/esm/icons/IconEye'; // For View Details
import IconFileText from '@tabler/icons-react/dist/esm/icons/IconFileText';

import { KhenThuongEntryType } from '../../mockDataChiTiet';
// Giả định có DialogThemSuaKhenThuong, DialogXemChiTietKhenThuong và DialogXacNhanXoaItem
// import DialogThemSuaKhenThuong from './DialogThemSuaKhenThuong';
// import DialogXemChiTietKhenThuong from './DialogXemChiTietKhenThuong';
// import DialogXacNhanXoaItem from '../../../../components/DialogXacNhanXoaItem';

interface TabKhenThuongProps {
initialData?: KhenThuongEntryType[];
idQuanNhan: string;
}

const formatDateDatagridKT = (dateString?: string | null): string => {
if (!dateString) return '';
try {
const date = new Date(dateString);
if (isNaN(date.getTime())) return 'Không hợp lệ';
return `<span class="math-inline">\{String\(date\.getDate\(\)\)\.padStart\(2, '0'\)\}/</span>{String(date.getMonth() + 1).padStart(2, '0')}/${date.getFullYear()}`;
} catch (e) { return 'Không hợp lệ'; }
};
const mapIdToStringKT = (id?: string, type?: string) => id || 'N/A'; // Placeholder

const TabKhenThuong = ({ initialData = [], idQuanNhan }: TabKhenThuongProps) => {
const [listData, setListData] = useState<KhenThuongEntryType[]>(initialData);
const [openAddEditDialog, setOpenAddEditDialog] = useState(false);
const [editingData, setEditingData] = useState<KhenThuongEntryType | null>(null);
const [openDetailDialog, setOpenDetailDialog] = useState(false);
const [viewingData, setViewingData] = useState<KhenThuongEntryType | null>(null);
const [openConfirmDelete, setOpenConfirmDelete] = useState(false);
const [deletingId, setDeletingId] = useState<string | null>(null);
const [paginationModel, setPaginationModel] = useState({ page: 0, pageSize: 5 });

useEffect(() => {
setListData(initialData);
}, [initialData]);

const handleOpenAddDialog = () => {
setEditingData(null);
setOpenAddEditDialog(true);
};

const handleOpenEditDialog = (rowData: KhenThuongEntryType) => {
setEditingData(rowData);
setOpenAddEditDialog(true);
};

const handleOpenDetailDialog = (rowData: KhenThuongEntryType) => {
setViewingData(rowData);
setOpenDetailDialog(true);
}

const handleCloseDialogs = () => {
setOpenAddEditDialog(false);
setOpenDetailDialog(false);
setEditingData(null);
setViewingData(null);
};

const handleSaveData = (savedData: KhenThuongEntryType) => {
console.log('Saving data (Khen thưởng):', savedData, 'for QN ID:', idQuanNhan);
if (editingData) {
setListData(prev => prev.map(item => (item.id === savedData.id ? savedData : item)));
} else {
setListData(prev => [...prev, { ...savedData, id: `new_kt_${Date.now()}` }]);
}
handleCloseDialogs();
};

const handleOpenDeleteDialog = (id: string) => {
setDeletingId(id);
setOpenConfirmDelete(true);
};

const handleCloseConfirmDelete = () => {
setOpenConfirmDelete(false);
setDeletingId(null);
};

const handleConfirmDelete = () => {
if (deletingId) {
console.log('Deleting Khen thưởng ID:', deletingId);
setListData(prev => prev.filter(item => item.id !== deletingId));
handleCloseConfirmDelete();
}
};

const columns: GridColDef[] = [
{ field: 'SoQuyetDinh', headerName: 'Số QĐ', width: 150 },
{
field: 'NgayQuyetDinh',
headerName: 'Ngày QĐ',
width: 120,
valueFormatter: params => formatDateDatagridKT(params.value)
},
{
field: 'HinhThucKhenThuongID',
headerName: 'Hình thức Khen thưởng',
width: 200,
valueGetter: params => mapIdToStringKT(params.value, 'hinhThucKT')
},
{
field: 'LyDoKhenThuong',
headerName: 'Lý do/Thành tích',
width: 300,
renderCell: (params: GridRenderCellParams) => (
<Tooltip title={params.value || ''} placement="top-start">
<Typography noWrap variant="body2" sx={{overflow: 'hidden', textOverflow: 'ellipsis'}}>
{params.value || ''}
</Typography>
</Tooltip>
)
},
{ field: 'NamKhenThuong', headerName: 'Năm', width: 100 },
{
field: 'actions',
headerName: 'Hành động',
width: 180,
sortable: false,
filterable: false,
renderCell: (params: GridRenderCellParams) => (
<Box>
<Tooltip title="Xem Chi tiết QĐ">
<IconButton size="small" onClick={() => handleOpenDetailDialog(params.row as KhenThuongEntryType)}>
<IconEye size={20} />
</IconButton>
</Tooltip>
<Tooltip title="Xem File đính kèm">
<span>
<IconButton
size="small"
href={params.row.FileDinhKemURL || '#'}
target="_blank"
disabled={!params.row.FileDinhKemURL}
onClick={(e) => { if (!params.row.FileDinhKemURL) e.preventDefault(); else console.log('Viewing file:', params.row.FileDinhKemURL);}}
>
<IconFileText size={20} />
</IconButton>
</span>
</Tooltip>
<Tooltip title="Sửa">
<IconButton size="small" onClick={() => handleOpenEditDialog(params.row as KhenThuongEntryType)}>
<IconEdit size={20} />
</IconButton>
</Tooltip>
<Tooltip title="Xóa">
<IconButton size="small" onClick={() => handleOpenDeleteDialog(params.row.id as string)}>
<IconTrash size={20} />
</IconButton>
</Tooltip>
</Box>
)
}
];

return (
<Box>
<Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
<Typography variant="h6" sx={{ color: 'primary.main' }}>
Khen thưởng
</Typography>
<Button
variant="contained"
startIcon={<IconPlus />}
onClick={handleOpenAddDialog} >
Thêm QĐ Khen thưởng
</Button>
</Box>

      <DataGrid
        autoHeight
        rows={listData}
        columns={columns}
        pageSizeOptions={[5, 10, 25]}
        paginationModel={paginationModel}
        onPaginationModelChange={setPaginationModel}
        getRowId={(row) => row.id}
        sx={{
            '& .MuiDataGrid-columnHeaders': { backgroundColor: 'customColors.tableHeaderBg' }
        }}
      />

      {/* {openAddEditDialog && (
        <DialogThemSuaKhenThuong
          open={openAddEditDialog}
          onClose={handleCloseDialogs}
          onSubmit={handleSaveData}
          initialData={editingData}
          idQuanNhan={idQuanNhan} // Có thể không cần nếu QĐ Khen thưởng là độc lập
        />
      )}

      {openDetailDialog && viewingData && (
        <DialogXemChiTietKhenThuong
            open={openDetailDialog}
            onClose={handleCloseDialogs}
            data={viewingData}
        />
      )}

      {openConfirmDelete && (
        <DialogXacNhanXoaItem
          open={openConfirmDelete}
          onClose={handleCloseConfirmDelete}
          onConfirm={handleConfirmDelete}
          itemName="quyết định khen thưởng này"
        />
      )} */}
      {/* Placeholder cho Dialogs */}
      {openAddEditDialog && <Typography sx={{mt: 2, p:2, border: '1px dashed grey'}}>Dialog Thêm/Sửa Khen thưởng (Placeholder - ID Quân nhân: {idQuanNhan}, Data: {JSON.stringify(editingData)})</Typography>}
      {openDetailDialog && viewingData && <Typography sx={{mt: 2, p:2, border: '1px dashed blue'}}>Dialog Xem Chi tiết Khen thưởng (Placeholder - Data: {JSON.stringify(viewingData)})</Typography>}
      {openConfirmDelete && <Typography sx={{mt: 2, p:2, border: '1px dashed red'}}>Dialog Xác nhận Xóa (Placeholder - ID: {deletingId})</Typography>}
    </Box>

);
};

export default TabKhenThuong;
Bước 21: Tạo Component DialogThemSuaKhenThuong.tsx và DialogXemChiTietKhenThuong.tsx (Sơ bộ)
DialogThemSuaKhenThuong.tsx: Tương tự các dialog thêm/sửa trước, với các trường của KhenThuongEntryType.
DialogXemChiTietKhenThuong.tsx: Một dialog chỉ hiển thị thông tin chi tiết của một quyết định khen thưởng (read-only), có thể bao gồm cả nội dung file đính kèm nếu có thể (hoặc link tới file).
TypeScript

// src\views\quan-ly-quan-nhan\chi-tiet\components\tabs\DialogThemSuaKhenThuong.tsx (Sơ bộ)
// 'use client';
// import React, { useState, useEffect } from 'react';
// import Dialog from '@mui/material/Dialog';
// import DialogTitle from '@mui/material/DialogTitle';
// // ... (các imports khác tương tự DialogThemSuaQuaTrinhCongTac)
// import { KhenThuongEntryType } from '../../mockDataChiTiet';

// interface DialogKhenThuongProps {
// open: boolean;
// onClose: () => void;
// onSubmit: (data: KhenThuongEntryType) => void;
// initialData: KhenThuongEntryType | null;
// // idQuanNhan có thể không cần thiết ở đây nếu QĐ khen thưởng là 1 bản ghi riêng, chỉ cần id của QĐ
// }

// const DialogThemSuaKhenThuong = ({ open, onClose, onSubmit, initialData }: DialogKhenThuongProps) => {
// const [formData, setFormData] = useState<Partial<KhenThuongEntryType>>(initialData || {});

// useEffect(() => {
// setFormData(initialData || {});
// }, [initialData, open]);

// const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => { /_ ... _/ };
// // const handleDateChange = (name: string, date: Date | null) => { /_ ... _/ };

// const handleSubmit = () => {
// onSubmit(formData as KhenThuongEntryType); // Cần validate
// };

// return (
// <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
// <DialogTitle>{initialData ? 'Sửa Quyết định Khen thưởng' : 'Thêm Quyết định Khen thưởng'}</DialogTitle>
// <DialogContent>
// <Grid container spacing={3} sx={{mt:1}}>
// <Grid item xs={12} sm={6}>
// <TextField name="SoQuyetDinh" label="Số Quyết định" value={formData.SoQuyetDinh || ''} onChange={handleChange} fullWidth required />
// </Grid>
// {/_ <Grid item xs={12} sm={6}>
// <DatePicker label="Ngày Quyết định" ... />
// </Grid> _/}
// {/_ ... Thêm các trường khác: CapRaQuyetDinhID (Select), HinhThucKhenThuongID (Select), LyDoKhenThuong (TextArea), NamKhenThuong, FileDinhKemURL (Upload/TextField), GhiChu ... _/}
// <Grid item xs={12}>
// <TextField name="LyDoKhenThuong" label="Lý do/Thành tích khen thưởng" value={formData.LyDoKhenThuong || ''} onChange={handleChange} fullWidth multiline rows={3} required/>
// </Grid>
// </Grid>
// </DialogContent>
// <DialogActions>
// <Button onClick={onClose}>Hủy</Button>
// <Button onClick={handleSubmit} variant="contained">Lưu</Button>
// </DialogActions>
// </Dialog>
// );
// };
// export default DialogThemSuaKhenThuong;

// src\views\quan-ly-quan-nhan\chi-tiet\components\tabs\DialogXemChiTietKhenThuong.tsx (Sơ bộ)
// 'use client';
// import React from 'react';
// import Dialog from '@mui/material/Dialog';
// import DialogTitle from '@mui/material/DialogTitle';
// import DialogContent from '@mui/material/DialogContent';
// import Typography from '@mui/material/Typography';
// import Button from '@mui/material/Button';
// import DialogActions from '@mui/material/DialogActions';
// import Link from '@mui/material/Link';
// import Box from '@mui/material/Box';

// import { KhenThuongEntryType } from '../../mockDataChiTiet';

// interface DialogXemChiTietProps {
// open: boolean;
// onClose: () => void;
// data: KhenThuongEntryType;
// }

// const DialogXemChiTietKhenThuong = ({open, onClose, data}: DialogXemChiTietProps) => {
// // Helper để hiển thị
// const renderDetailRow = (label: string, value?: string | number | null) => (
// <Box sx={{ display: 'flex', mb: 1 }}>
// <Typography variant="subtitle2" sx={{ minWidth: '180px', color: 'text.secondary' }}>{label}:</Typography>
// <Typography variant="body2">{value || 'N/A'}</Typography>
// </Box>
// );

// return (
// <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
// <DialogTitle>Chi tiết Quyết định Khen thưởng</DialogTitle>
// <DialogContent dividers>
// {renderDetailRow("Số Quyết định", data.SoQuyetDinh)}
// {renderDetailRow("Ngày Quyết định", formatDateDatagridKT(data.NgayQuyetDinh))}
// {renderDetailRow("Cấp ra Quyết định", mapIdToStringKT(data.CapRaQuyetDinhID, "capQD"))}
// {renderDetailRow("Hình thức Khen thưởng", mapIdToStringKT(data.HinhThucKhenThuongID, "hinhThucKT"))}
// {renderDetailRow("Năm Khen thưởng", data.NamKhenThuong)}
// <Box sx={{ mb: 1 }}>
// <Typography variant="subtitle2" sx={{ color: 'text.secondary' }}>Lý do/Thành tích:</Typography>
// <Typography variant="body2" sx={{whiteSpace: 'pre-line'}}>{data.LyDoKhenThuong || 'N/A'}</Typography>
// </Box>
// {data.FileDinhKemURL && (
// renderDetailRow("File đính kèm", <Link href={data.FileDinhKemURL} target="_blank" rel="noopener noreferrer">Xem File</Link>)
// )}
// <Box sx={{ mt:1, mb: 1 }}>
// <Typography variant="subtitle2" sx={{ color: 'text.secondary' }}>Ghi chú:</Typography>
// <Typography variant="body2" sx={{whiteSpace: 'pre-line'}}>{data.GhiChu || 'N/A'}</Typography>
// </Box>
// </DialogContent>
// <DialogActions>
// <Button onClick={onClose}>Đóng</Button>
// </DialogActions>
// </Dialog>
// )
// }
// export default DialogXemChiTietKhenThuong;
Bước 22: Cập nhật KhuVucTabsChiTiet.tsx để sử dụng TabKhenThuong
Chỉnh sửa file src\views\quan-ly-quan-nhan\chi-tiet\components\KhuVucTabsChiTiet.tsx:

TypeScript

// src\views\quan-ly-quan-nhan\chi-tiet\components\KhuVucTabsChiTiet.tsx
// ... (các imports khác)
import TabKhenThuong from './tabs/TabKhenThuong';
// ... (import các tab khác khi tạo xong)

// interface TabsProps { ... }

const KhuVucTabsChiTiet = ({ quanNhanData, activeTab, handleTabChange }: TabsProps) => {
const tabContentList: { [key: string]: React.ReactNode } = {
'thong-tin-chung': <TabThongTinChung data={quanNhanData.baseInfo} />,
'ly-lich-ca-nhan': <TabLyLichCaNhan data={quanNhanData.lyLichCaNhan} />,
'qua-trinh-cong-tac': <TabQuaTrinhCongTac initialData={quanNhanData.quaTrinhCongTac} idQuanNhan={quanNhanData.baseInfo.SoHieuQuanNhan} />,
'qua-trinh-dao-tao': <TabQuaTrinhDaoTao initialData={quanNhanData.quaTrinhDaoTao} idQuanNhan={quanNhanData.baseInfo.SoHieuQuanNhan} />,
'khen-thuong': <TabKhenThuong initialData={quanNhanData.khenThuong} idQuanNhan={quanNhanData.baseInfo.SoHieuQuanNhan} />,
'ky-luat': <div>Nội dung Tab Kỷ luật</div>,
// ... các tab khác
};

// ... (phần còn lại của component giữ nguyên)
return (
<TabContext value={activeTab}>
<Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
<TabList /_ ...props... _/ >
{/_ ...Tabs... _/}
</TabList>
</Box>
{Object.keys(tabContentList).map(tabValue => (
<TabPanel key={tabValue} value={tabValue} sx={{ p: 0 }}>
<CardContent>
{tabContentList[tabValue]}
</CardContent>
</TabPanel>
))}
</TabContext>
);
};

export default KhuVucTabsChiTiet;
