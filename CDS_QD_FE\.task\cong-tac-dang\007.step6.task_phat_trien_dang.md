# Quy trình xây dựng giao diện trang "Chi tiết Quy trình Phát triển Đảng viên"

---

**Trang: Chi tiết Quy trình Phát triển Đảng viên (`/${locale}/cong-tac-dang/phat-trien-dang-vien/chi-tiet/{idHoSoPhatTrien}`)**

**IV. <PERSON>hu vực "Chi tiết Bước Hiện tại/Đ<PERSON><PERSON><PERSON> chọn"**

**Ghi nhận sau Bước "Kết nạp Đảng" (Tương ứng với việc thực hiện Lễ Kết nạp - Bước 8 chi tiết)**

- **Tên Bước/Mục Thông tin:** `Ghi nhận Tổ chức Lễ Kết nạp` (<PERSON><PERSON><PERSON> có thể không phải là một "bướ<PERSON>" chủ động trên stepper mà là một form/khu vực thông tin được kích hoạt sau khi Bước "Kết nạp <PERSON>" đư<PERSON><PERSON> hoàn thành với kết quả "Đồng ý Kết nạp").
- **Mục đích:** Ghi nhận lại thông tin về buổi Lễ Kết nạp Đảng viên đã được tổ chức trong thực tế cho quần chúng ưu tú, sau khi có Quyết định Kết nạp của cấp ủy có thẩm quyền.
- **Trạng thái Quy trình (`DmTrangThaiPhatTrienDang.ID`) tương ứng:** Sau khi có QĐ Kết nạp, trạng thái có thể là "Chờ Tổ chức Lễ Kết nạp" hoặc "Đã có QĐ Kết nạp - Chờ ghi nhận Lễ". Sau khi ghi nhận Lễ, sẽ chuyển sang "Đảng viên Dự bị".

- **Hành động/Nội dung chính trong mục "Ghi nhận Tổ chức Lễ Kết nạp":**

  1.  **Hiển thị Quyết định Kết nạp:**
      - Số Quyết định Kết nạp: `HoSoPhatTrienDangVien.SoQuyetDinhKetNap` (read-only).
      - Ngày Quyết định Kết nạp: `HoSoPhatTrienDangVien.NgayQuyetDinhKetNap` (read-only).
      - File Quyết định Kết nạp: Liên kết xem/tải (read-only).
  2.  **Thông tin về Lễ Kết nạp:**
      - **Trường chọn ngày/nhập liệu "Ngày Tổ chức Lễ Kết nạp":** (Bắt buộc phải có để xác nhận buổi lễ đã diễn ra).
      - **Trường nhập liệu "Địa điểm Tổ chức Lễ Kết nạp":**
      - **Trường nhập liệu "Thành phần Tham dự chủ yếu":** (Ví dụ: Toàn thể đảng viên Chi bộ X, Đại diện Đảng ủy Y...).
      - **Trường nhập liệu "Nội dung tóm tắt buổi Lễ":** (Các nghi thức chính đã thực hiện).
  3.  **Xác nhận quần chúng đã tuyên thệ:**
      - Checkbox "Xác nhận Đảng viên mới đã tuyên thệ".

- **Danh sách Tài liệu Yêu cầu/Đã nộp/Phát sinh (Fix cứng hoặc tùy chọn cho mục này):**

  1.  **Loại Tài liệu:** "Biên bản Lễ Kết nạp Đảng viên" (nếu có)
      - **Trạng thái:** "Chưa nộp" / "Đã nộp" (ngày nộp).
      - **File đính kèm:** (Nếu đã nộp) Hiển thị tên file với nút "Xem/Tải".
      - **Hành động:** Nút "Nộp Biên bản Lễ Kết nạp" (mở giao diện upload file).
  2.  **Loại Tài liệu:** "Hình ảnh Lễ Kết nạp" (nếu có)
      - **Trạng thái:** "Chưa nộp" / "Đã nộp".
      - **File đính kèm:** (Nếu đã nộp) Hiển thị tên file/ảnh với nút "Xem/Tải".
      - **Hành động:** Nút "Nộp Hình ảnh Lễ Kết nạp".

- **Nút Hành động cho mục "Ghi nhận Tổ chức Lễ Kết nạp" (Dành cho người có trách nhiệm, ví dụ Bí thư Chi bộ):**
  - **Nút "Xác nhận Đã Tổ chức Lễ Kết nạp & Chuyển sang Đảng viên Dự bị":**
    - **Điều kiện hiển thị:** Khi Quyết định Kết nạp đã có, và người dùng nhập ít nhất "Ngày Tổ chức Lễ Kết nạp".
    - **Hành động:**
      - Lưu các thông tin về Lễ Kết nạp (Ngày, Địa điểm, Thành phần...) vào bảng `HoSoPhatTrienDangVien` (cần thêm các trường này) hoặc một bảng riêng ghi nhận sự kiện Lễ Kết nạp nếu cần quản lý chi tiết hơn.
      - Lưu các file tài liệu liên quan (Biên bản, Hình ảnh) vào `TaiLieuPhatTrienDangVien`.
      - **Quan trọng:** Cập nhật `DangVien.NgayVaoDangDuBi` bằng "Ngày Tổ chức Lễ Kết nạp".
      - Cập nhật `HoSoPhatTrienDangVien.TrangThaiQuyTrinhID` sang ID của bước "Đảng viên Dự bị".
      - Cập nhật `HoSoPhatTrienDangVien.NgayCongNhanChinhThucDuKien` (tính 12 tháng kể từ ngày kết nạp dự bị).
      - Tạo một bản ghi mới trong bảng `DangVien` cho đảng viên này với trạng thái "Dự bị", hoặc cập nhật trạng thái nếu bản ghi `DangVien` đã được tạo placeholder trước đó.
      - Hiển thị thông báo thành công: "Đã ghi nhận Lễ Kết nạp cho [Tên Đảng viên]. Đảng viên chính thức vào giai đoạn dự bị từ ngày [Ngày Lễ Kết nạp]."
      - Giao diện tự động chuyển sang hiển thị chi tiết của Bước tiếp theo trên stepper ("Đảng viên dự bị").

---

**II. Bảng Dữ liệu Liên quan Chính cho mục "Ghi nhận Tổ chức Lễ Kết nạp":**

- **Bảng `HoSoPhatTrienDangVien`:**

  - `ID: number`
  - `TrangThaiQuyTrinhID: number` (Sẽ được cập nhật)
  - `SoQuyetDinhKetNap: string` (Hiển thị)
  - `NgayQuyetDinhKetNap: date` (Hiển thị)
  - **(Các trường mới có thể cần thêm để lưu thông tin Lễ Kết nạp):**
    - `NgayToChucLeKetNap: date` (DATE NULL)
    - `DiaDiemLeKetNap: string` (NVARCHAR(255) NULL)
    - `ThanhPhanThamDuLe: string` (NTEXT NULL)
    - `NoiDungTomTatLe: string` (NTEXT NULL)
    - `DaTuyenThe: boolean` (BOOLEAN DEFAULT FALSE)
  - `NgayCongNhanChinhThucDuKien: date` (Sẽ được tính toán và cập nhật)

- **Bảng `DangVien`:** (Bản ghi của đảng viên mới này)

  - `MaDangVien: string` (Có thể được sinh ra ở bước này nếu chưa có)
  - `SoHieuQuanNhan: string` (Lấy từ `HoSoPhatTrienDangVien`)
  - `NgayVaoDangDuBi: date` (Sẽ được cập nhật bằng `NgayToChucLeKetNap`)
  - `TrangThaiDangTichID: number` (Sẽ được cập nhật thành ID của "Dự bị" từ `DmTrangThaiDangTich`)
  - (Các thông tin khác được đồng bộ từ hồ sơ quân nhân hoặc hồ sơ phát triển)

- **Bảng `TaiLieuPhatTrienDangVien`:**

  - `HoSoPhatTrienID: number`
  - `LoaiTaiLieuPhatTrienID: number` (FK đến `DmLoaiTaiLieuPhatTrienDang` - ID của "Biên bản Lễ Kết nạp", "Hình ảnh Lễ Kết nạp")
  - `FileURL: string`
  - `TenTaiLieu: string`
  - `NgayNopTaiLieu: date`

- **Danh mục `DmLoaiTaiLieuPhatTrienDang`:** Cần có các loại tài liệu tương ứng.
- **Danh mục `DmTrangThaiPhatTrienDang`:** Cần có trạng thái "Chờ Tổ chức Lễ Kết nạp" (sau khi có QĐ Kết nạp) và "Đảng viên Dự bị" (sau Lễ Kết nạp).
- **Danh mục `DmTrangThaiDangTich`:** Cần có trạng thái "Dự bị".

---
