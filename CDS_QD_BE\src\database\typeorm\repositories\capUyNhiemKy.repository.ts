/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { CapUyNhiemKyEntity } from '~/database/typeorm/entities/capUyNhiemKy.entity';

@Injectable()
export class CapUyNhiemKyRepository extends Repository<CapUyNhiemKyEntity> {
    constructor(private dataSource: DataSource) {
        super(CapUyNhiemKyEntity, dataSource.createEntityManager());
    }
}
