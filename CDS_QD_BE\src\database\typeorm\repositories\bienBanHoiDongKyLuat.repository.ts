/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { BienBanHoiDongKyLuatEntity } from '~/database/typeorm/entities/bienBanHoiDongKyLuat.entity';

@Injectable()
export class BienBanHoiDongKyLuatRepository extends Repository<BienBanHoiDongKyLuatEntity> {
    constructor(private dataSource: DataSource) {
        super(BienBanHoiDongKyLuatEntity, dataSource.createEntityManager());
    }
}
