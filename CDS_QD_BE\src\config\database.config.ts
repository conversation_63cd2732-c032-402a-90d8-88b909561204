import * as fs from 'fs';

function getDatabaseConfig() {
    let ssl = null;
    if (process.env.DATABASE_SSL == 'enable') {
        ssl = { ca: fs.readFileSync(process.env.DATABASE_CA_FILE).toString() };
    }

    return {
        database: {
            type: process.env.DATABASE_TYPE,
            host: process.env.DATABASE_HOST || 'localhost',
            port: process.env.DATABASE_PORT ? parseInt(process.env.DATABASE_PORT, 10) : process.env.DATABASE_TYPE === 'postgres' ? 5433 : 3306,
            database: process.env.DATABASE_DB_NAME || 'postgres',
            username: process.env.DATABASE_USERNAME || 'postgres',
            password: process.env.DATABASE_PASSWORD || 'postgres',
            cache: true,
            keepConnectionAlive: process.env.DATABASE_KEEPCONNECTIONALIVE ? JSON.parse(process.env.DATABASE_KEEPCONNECTIONALIVE) : false,
            logging: process.env.DATABASE_LOGGING ? JSON.parse(process.env.DATABASE_LOGGING) : false,
            synchronize: process.env.DATABASE_SYNCHRONIZE === 'true',
            ssl: ssl,
            timezone: process.env.DATABASE_TIMEZONE || '+00:00',
        },
    };
}

export default () => {
    return getDatabaseConfig();
};
