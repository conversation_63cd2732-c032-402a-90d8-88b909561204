# Quy trình xây dựng giao diện trang "Xem chi tiết Hồ sơ Quân nhân"

**III. Trang Xem chi tiết Hồ sơ Quân nhân (`/${locale}/quan-ly-quan-nhan/chi-tiet/{idQuanNhan}`)**

- **<PERSON><PERSON><PERSON> (Tiêu đề hiển thị):** `<PERSON> tiết Hồ sơ Quân nhân: [Họ và tên Khai sinh Quân nhân]`
- **Mục đích:** Hiển thị toàn bộ, chi tiết thông tin của một quân nhân cụ thể, bao gồ<PERSON> l<PERSON> l<PERSON>, các quá trình công tác, đ<PERSON><PERSON> tạo, khen thưởng, kỷ luật, sức khỏe, gia đình và các chế độ chính sách liên quan. Giao diện cần rõ ràng, d<PERSON> đọc và cho phép người dùng dễ dàng nắm bắt thông tin.

- **<PERSON><PERSON><PERSON> phần <PERSON><PERSON><PERSON> (Components) và Vị trí:**

  1.  **Breadcrumbs:**

      - **Vị trí:** Phía trên cùng, dưới header chính của layout.
      - **Nội dung:** `Trang chủ / Quản lý Quân nhân / Danh sách Quân nhân / Chi tiết: [Họ và tên Khai sinh Quân nhân]`
      - **Component Vuexy:** `Breadcrumbs`.

  2.  **Khu vực Header Thông tin Tổng quan Quân nhân:**

      - **Vị trí:** Phía trên các tab, là một `Card` nổi bật.
      - **Component Vuexy:** `Card`, `CardContent`, `Avatar`, `Typography`, `Button`, `Menu` (cho các hành động khác).
      - **Nội dung hiển thị:**
        - **Ảnh chân dung (`AnhChanDungURL`):** Hiển thị `Avatar` lớn.
        - **Họ và tên Khai sinh (`HoVaTenKhaiSinh`):** `Typography` (h4 hoặc h5).
        - **Số hiệu Quân nhân (`SoHieuQuanNhan`):** `Typography` (body1 hoặc subtitle1).
        - **Cấp bậc hiện tại (Tên từ `CapBacHienTaiID`):** `Typography` (body1).
        - **Chức vụ hiện tại (Tên từ `ChucVuHienTaiID`):** `Typography` (body1).
        - **Đơn vị hiện tại (Tên từ `DonViID`):** `Typography` (body1).
        - **Trạng thái Hồ sơ (Tên từ `TrangThaiHoSo`):** `CustomChip` với màu sắc tương ứng trạng thái.
        - **Nút "Chỉnh sửa Hồ sơ":** `Button` (variant="contained", color="primary", startIcon={<i className='tabler-edit' />}), điều hướng đến trang chỉnh sửa.
        - **(Tùy chọn) Nút "Hành động khác":** `IconButton` (icon `tabler-dots-vertical`) mở `Menu` dropdown với các tùy chọn như:
          - "In Hồ sơ 02a/SQN" (nếu có chức năng generate report).
          - "Thay đổi Trạng thái Hồ sơ" (mở Dialog chọn trạng thái mới).
          - "Xem Lịch sử Thay đổi" (điều hướng đến trang/tab Audit Log của quân nhân này).

  3.  **Khu vực Tab (Tabs):**

      - **Component Vuexy:** `Tabs` component (`TabContext`, `TabList`, `TabPanel`).
      - **Các Tab và nội dung chi tiết từng Tab:**

        - **Tab 1: "Thông tin Chung"**

          - **Mục đích:** Hiển thị các thông tin cơ bản nhất từ bảng `QuanNhan`.
          - **Layout:** Dạng danh sách key-value hoặc grid 2 cột.
          - **Các trường dữ liệu hiển thị (read-only):**
            - `SoHieuQuanNhan`: Số hiệu Quân nhân
            - `HoVaTenKhaiSinh`: Họ và tên Khai sinh
            - `TenThuongDung`: Tên thường dùng
            - `NgaySinh`: Ngày sinh
            - `GioiTinh`: Giới tính (Hiển thị tên: Nam, Nữ, Khác)
            - `SoCCCD_CMTQD`: Số CCCD/CMT Quân đội
            - `NgayNhapNgu`: Ngày nhập ngũ
            - `DonViID`: Đơn vị hiện tại (Hiển thị tên đơn vị)
            - `CapBacHienTaiID`: Cấp bậc hiện tại (Hiển thị tên cấp bậc)
            - `ChucVuHienTaiID`: Chức vụ hiện tại (Hiển thị tên chức vụ)
            - `TrangThaiHoSo`: Trạng thái Hồ sơ (Hiển thị tên trạng thái)
            - `Email`: Địa chỉ email
            - `SoDienThoai`: Số điện thoại
            - `NgayTao`: Ngày tạo hồ sơ
            - `NguoiTaoID`: Người tạo hồ sơ
            - `NgayCapNhat`: Ngày cập nhật lần cuối
            - `NguoiCapNhatID`: Người cập nhật lần cuối

        - **Tab 2: "Lý lịch Cá nhân"**

          - **Mục đích:** Hiển thị chi tiết thông tin lý lịch từ bảng `LyLichCaNhan`.
          - **Layout:** Dạng danh sách key-value hoặc grid 2 cột, có thể nhóm các trường liên quan.
          - **Các trường dữ liệu hiển thị (read-only):**
            - **Quê quán:**
              - `QueQuan_XaID`: Xã/Phường (Hiển thị tên)
              - `QueQuan_HuyenID`: Quận/Huyện (Hiển thị tên)
              - `QueQuan_TinhID`: Tỉnh/Thành phố (Hiển thị tên)
            - **Nơi ở hiện tại:**
              - `NoiOHienNay_XaID`: Xã/Phường (Hiển thị tên)
              - `NoiOHienNay_HuyenID`: Quận/Huyện (Hiển thị tên)
              - `NoiOHienNay_TinhID`: Tỉnh/Thành phố (Hiển thị tên)
            - `DanTocID`: Dân tộc (Hiển thị tên dân tộc)
            - `TonGiaoID`: Tôn giáo (Hiển thị tên tôn giáo)
            - `ThanhPhanGiaDinhID`: Thành phần gia đình khi nhập ngũ (Hiển thị tên thành phần)
            - `NgheNghiepTruocNhapNguID`: Nghề nghiệp trước khi nhập ngũ (Hiển thị tên nghề nghiệp)
            - `TrinhDoVanHoaPhoThong`: Trình độ văn hóa phổ thông
            - `TrinhDoLyLuanChinhTriID`: Trình độ lý luận chính trị (Hiển thị tên trình độ)
            - `TrinhDoNgoaiNgu`: Trình độ ngoại ngữ
            - `TrinhDoTinHoc`: Trình độ tin học
            - `NangKhieuSoTruong`: Năng khiếu, sở trường
            - `TinhHinhChinhTriLichSuBanThan`: Đặc điểm lịch sử bản thân (hiển thị dạng text dài)
            - `NgayVaoDangChinhThuc`: Ngày vào Đảng chính thức
            - `NgayVaoDoan`: Ngày vào Đoàn TNCS Hồ Chí Minh
            - `SoHoKhau`: Số sổ hộ khẩu
            - `TinhTrangHonNhanID`: Tình trạng hôn nhân (Hiển thị tên tình trạng)
            - `GhiChuLyLich`: Ghi chú thêm về lý lịch

        - **Tab 3: "Quá trình Công tác"**

          - **Mục đích:** Hiển thị lịch sử các giai đoạn công tác của quân nhân.
          - **Nút "Thêm giai đoạn Công tác":** Mở Dialog/Form nhỏ cho phép nhập thông tin giai đoạn mới.
          - **Component:** `DataGrid`.
          - **Các cột hiển thị cho mỗi giai đoạn (từ bảng `QuaTrinhCongTac`):**
            - `ThoiGianBatDau`: Thời gian bắt đầu
            - `ThoiGianKetThuc`: Thời gian kết thúc (Nếu null thì hiển thị "Đến nay")
            - `DonViCongTacID`: Đơn vị công tác (Hiển thị tên đơn vị)
            - `ChucVuDamNhiemID`: Chức vụ đảm nhiệm (Hiển thị tên chức vụ)
            - `CapBacKhiDamNhiemID`: Cấp bậc khi đảm nhiệm (Hiển thị tên cấp bậc)
            - `QuyetDinhSo`: Số quyết định
            - `NgayQuyetDinh`: Ngày quyết định
            - `CoQuanRaQuyetDinhID`: Cơ quan ra quyết định (Hiển thị tên)
            - `LoaiQuyetDinhID`: Loại quyết định (Hiển thị tên)
            - `MoTaCongViecChinh`: Mô tả công việc chính
            - `FileDinhKemURL`: File đính kèm (Link tải/xem)
            - `GhiChu`: Ghi chú
            - **Hành động trên dòng:** Sửa, Xóa (có xác nhận), Xem file.

        - **Tab 4: "Quá trình Đào tạo"**

          - **Mục đích:** Hiển thị lịch sử các khóa đào tạo, bồi dưỡng, văn bằng chứng chỉ.
          - **Nút "Thêm thông tin Đào tạo":** Mở Dialog/Form nhỏ.
          - **Component:** `DataGrid`.
          - **Các cột hiển thị cho mỗi khóa đào tạo (từ bảng `QuaTrinhDaoTao`):**
            - `TenKhoaHoc`: Tên khóa học/Chương trình
            - `HinhThucDaoTaoID`: Hình thức đào tạo (Hiển thị tên)
            - `ChuyenNganhDaoTaoID`: Chuyên ngành đào tạo (Hiển thị tên)
            - `CoSoDaoTaoID`: Cơ sở đào tạo (Hiển thị tên)
            - `QuocGiaDaoTaoID`: Quốc gia đào tạo (Hiển thị tên, nếu có)
            - `ThoiGianBatDau`: Thời gian bắt đầu
            - `ThoiGianKetThuc`: Thời gian kết thúc
            - `VanBangChungChiLoaiID`: Loại văn bằng/chứng chỉ (Hiển thị tên)
            - `SoHieuVanBang`: Số hiệu văn bằng/chứng chỉ
            - `NgayCapVanBang`: Ngày cấp
            - `NoiCapVanBang`: Nơi cấp
            - `XepLoaiTotNghiepID`: Xếp loại tốt nghiệp (Hiển thị tên)
            - `DiemSo`: Điểm số
            - `LuanVanDeTaiTotNghiep`: Tên luận văn/đề tài tốt nghiệp
            - `TrangThaiDaoTao`: Trạng thái đào tạo (Hiển thị tên: Đã hoàn thành, Đang học, Bị đình chỉ)
            - `FileDinhKemURL`: File đính kèm (Link tải/xem)
            - `GhiChu`: Ghi chú
            - **Hành động trên dòng:** Sửa, Xóa, Xem file.

        - **Tab 5: "Khen thưởng"**

          - **Mục đích:** Liệt kê các quyết định khen thưởng quân nhân đã nhận.
          - **Nút "Thêm Quyết định Khen thưởng":** (Nếu cho phép thêm trực tiếp, không qua quy trình đề xuất).
          - **Component:** `DataGrid`.
          - **Các cột hiển thị cho mỗi quyết định (từ bảng `KhenThuong` liên quan đến quân nhân này):**
            - `SoQuyetDinh`: Số quyết định
            - `NgayQuyetDinh`: Ngày quyết định
            - `CapRaQuyetDinhID`: Cấp ra quyết định (Hiển thị tên)
            - `HinhThucKhenThuongID`: Hình thức khen thưởng (Hiển thị tên)
            - `LyDoKhenThuong`: Lý do/Thành tích khen thưởng
            - `NamKhenThuong`: Năm khen thưởng
            - `FileDinhKemURL`: File đính kèm QĐ (Link tải/xem)
            - `GhiChu`: Ghi chú
            - **Hành động trên dòng:** Xem chi tiết QĐ (có thể mở dialog), Sửa (nếu được phép), Xóa (nếu được phép).

        - **Tab 6: "Kỷ luật"**

          - **Mục đích:** Liệt kê các quyết định kỷ luật quân nhân đã nhận.
          - **Nút "Thêm Quyết định Kỷ luật":** (Nếu cho phép thêm trực tiếp, không qua quy trình xử lý vụ việc).
          - **Component:** `DataGrid`.
          - **Các cột hiển thị cho mỗi quyết định (từ bảng `KyLuat` liên quan đến quân nhân này, và có thể link đến `HoSoVuViecKyLuat`):**
            - `HoSoVuViecKyLuatID`: Mã Hồ sơ Vụ việc (Link đến chi tiết vụ việc nếu có)
            - `SoQuyetDinh`: Số quyết định
            - `NgayQuyetDinh`: Ngày quyết định
            - `CapRaQuyetDinhID`: Cấp ra quyết định (Hiển thị tên)
            - `HinhThucKyLuatID`: Hình thức kỷ luật (Hiển thị tên)
            - `LyDoKyLuat`: Lý do/Nội dung vi phạm
            - `ThoiHanKyLuat`: Thời hạn kỷ luật
            - `NgayBatDauThiHanh`: Ngày bắt đầu thi hành
            - `NgayKetThucKyLuatDuKien`: Ngày kết thúc dự kiến
            - `TinhTrangChapHanh`: Tình trạng chấp hành (Hiển thị tên)
            - `FileDinhKemURL`: File đính kèm QĐ (Link tải/xem)
            - `GhiChu`: Ghi chú
            - **Hành động trên dòng:** Xem chi tiết QĐ, Cập nhật Tình trạng chấp hành (mở dialog theo dõi).

        - **Tab 7: "Sức khỏe"**

          - **Mục đích:** Hiển thị lịch sử khám và thông tin sức khỏe.
          - **Nút "Thêm thông tin Khám sức khỏe":** Mở Dialog/Form nhỏ.
          - **Component:** `DataGrid`.
          - **Các cột hiển thị cho mỗi lần khám (từ bảng `ThongTinSucKhoe`):**
            - `NgayKham`: Ngày khám
            - `LoaiKhamID`: Loại khám (Hiển thị tên)
            - `NoiKhamID`: Nơi khám (Hiển thị tên)
            - `ChieuCao`: Chiều cao (cm)
            - `CanNang`: Cân nặng (kg)
            - `NhomMauID`: Nhóm máu (Hiển thị tên)
            - `Mach`: Mạch (lần/phút)
            - `HuyetAp`: Huyết áp (mmHg)
            - `PhanLoaiSucKhoeID`: Phân loại sức khỏe (Hiển thị tên)
            - `KetLuanBacSi`: Kết luận của bác sĩ
            - `BenhLyManTinh`: Bệnh lý mãn tính
            - `TienSuBenh`: Tiền sử bệnh tật
            - `DiUngThuocThucPham`: Dị ứng thuốc/thực phẩm
            - `FileDinhKemURL`: File kết quả khám (Link tải/xem)
            - `GhiChuYBacSi`: Ghi chú của bác sĩ
            - **Hành động trên dòng:** Sửa, Xóa, Xem file.

        - **Tab 8: "Quan hệ Gia đình"**

          - **Mục đích:** Liệt kê thông tin các thân nhân của quân nhân.
          - **Nút "Thêm Thân nhân":** Mở Dialog/Form nhỏ.
          - **Component:** `DataGrid`.
          - **Các cột hiển thị cho mỗi thân nhân (từ bảng `ThanNhan`):**
            - `HoTenThanNhan`: Họ tên thân nhân
            - `NgaySinhThanNhan`: Ngày sinh
            - `MoiQuanHeVoiQuanNhanID`: Mối quan hệ (Hiển thị tên)
            - `NgheNghiepID`: Nghề nghiệp (Hiển thị tên)
            - `NoiO_CongTacHienTai`: Nơi ở/Công tác hiện tại
            - `HoanCanhKinhTe`: Hoàn cảnh kinh tế
            - `TinhHinhChinhTriThanNhan`: Tình hình chính trị (cần kiểm soát hiển thị)
            - `LaDangVien`: Là Đảng viên (Có/Không)
            - `ThamGiaTCCTXH`: Tham gia Tổ chức Chính trị - Xã hội
            - `LichSuChinhTriLuuY`: Vấn đề lịch sử chính trị cần lưu ý (cần kiểm soát hiển thị)
            - `SoDienThoaiLienHe`: Số điện thoại liên hệ
            - `TrangThaiThanNhan`: Trạng thái (Còn sống/Đã mất)
            - `NgayMat`: Ngày mất (nếu đã mất)
            - `GhiChu`: Ghi chú
            - **Hành động trên dòng:** Sửa, Xóa.

        - **Tab 9: "Chế độ Chính sách"**

          - **Mục đích:** Liệt kê các chế độ, chính sách mà quân nhân đang hoặc đã được hưởng.
          - **Nút "Thêm Chế độ/Chính sách":** Mở Dialog/Form nhỏ.
          - **Component:** `DataGrid`.
          - **Các cột hiển thị cho mỗi chế độ (từ bảng `CheDoChinhSachQuanNhan`):**
            - `LoaiCheDoID`: Loại chế độ/chính sách (Hiển thị tên)
            - `NgayBatDauHuong`: Ngày bắt đầu hưởng
            - `NgayKetThucHuong`: Ngày kết thúc hưởng
            - `SoQuyetDinhLienQuan`: Số quyết định liên quan
            - `CoQuanGiaiQuyet`: Cơ quan giải quyết
            - `ChiTietNoiDung`: Chi tiết nội dung thực hiện
            - `SoTienHuong`: Số tiền hưởng (nếu có)
            - `TrangThaiGiaiQuyet`: Trạng thái giải quyết (Hiển thị tên)
            - `NgayGiaiQuyet`: Ngày giải quyết
            - `FileDinhKemURL`: File đính kèm (Link tải/xem)
            - `GhiChu`: Ghi chú
            - **Hành động trên dòng:** Sửa, Xóa, Xem file.

        - **(Tùy chọn) Tab 10: "Lịch sử Thay đổi Hồ sơ"**
          - **Mục đích:** Hiển thị log các thay đổi quan trọng trên hồ sơ của quân nhân này.
          - **Component:** `DataGrid` (lấy từ bảng `AuditLog` đã được lọc theo `DoiTuongIDBiTacDong` là `SoHieuQuanNhan` của quân nhân này và `TenBangBiTacDong` liên quan đến QLQN).
          - **Các cột hiển thị:** Thời gian, Người thực hiện, Hành động, Mô tả thay đổi.
          - Cho phép xem chi tiết thay đổi (Dữ liệu cũ/mới).

---

Như vậy, trang "Xem chi tiết Hồ sơ Quân nhân" sẽ là một trang tổng hợp thông tin rất phong phú, được tổ chức khoa học bằng các Tab để người dùng dễ dàng điều hướng và nắm bắt. Mỗi tab con sẽ quản lý một khía cạnh cụ thể của hồ sơ quân nhân.
