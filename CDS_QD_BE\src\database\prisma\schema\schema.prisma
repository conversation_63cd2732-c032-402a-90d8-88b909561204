generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model accounts {
  created_at   DateTime  @default(now()) @db.Timestamp(6)
  updated_at   DateTime  @default(now()) @db.Timestamp(6)
  deleted_at   DateTime? @db.Timestamp(6)
  id           Int       @id(map: "PK_5a7a02c20412299d198e097a8fe") @default(autoincrement())
  username     String    @db.VarChar(255)
  password     String?   @db.VarChar(255)
  salt         String?   @db.VarChar(255)
  secret_token String?   @db.VarChar(255)
  is_active    Boolean?  @default(true)
  users        users?
}

model medias {
  created_at DateTime         @default(now()) @db.Timestamp(6)
  updated_at DateTime         @default(now()) @db.Timestamp(6)
  deleted_at DateTime?        @db.Timestamp(6)
  id         Int              @id(map: "PK_f27321557a66cd4fae9bc1ed6e7") @default(autoincrement())
  type       medias_type_enum
  name       String           @db.VarChar(255)
  path       String           @db.VarChar(500)
  users      users[]
}

model permissions {
  created_at        DateTime            @default(now()) @db.Timestamp(6)
  updated_at        DateTime            @default(now()) @db.Timestamp(6)
  deleted_at        DateTime?           @db.Timestamp(6)
  id                Int                 @id(map: "PK_920331560282b8bd21bb02290df") @default(autoincrement())
  name              String              @db.VarChar(255)
  action            String              @db.VarChar(255)
  roles_permissions roles_permissions[]

  @@unique([name, action], map: "UQ_permissions_name_action")
}

model query_result_cache {
  id         Int     @id(map: "PK_6a98f758d8bfd010e7e10ffd3d3") @default(autoincrement())
  identifier String? @db.VarChar
  time       BigInt
  duration   Int
  query      String
  result     String

  @@map("query-result-cache")
}

model roles {
  created_at        DateTime            @default(now()) @db.Timestamp(6)
  updated_at        DateTime            @default(now()) @db.Timestamp(6)
  deleted_at        DateTime?           @db.Timestamp(6)
  id                Int                 @id(map: "PK_c1433d71a4838793a49dcad46ab") @default(autoincrement())
  name              String              @db.VarChar(255)
  description       String?             @db.VarChar(255)
  roles_permissions roles_permissions[]
  users             users[]
}

model roles_permissions {
  permission_id Int
  role_id       Int
  permissions   permissions @relation(fields: [permission_id], references: [id], onDelete: NoAction, map: "FK_337aa8dba227a1fe6b73998307b")
  roles         roles       @relation(fields: [role_id], references: [id], onDelete: NoAction, map: "FK_7d2dad9f14eddeb09c256fea719")

  @@id([permission_id, role_id], map: "PK_0cd11f0b35c4d348c6ebb9b36b7")
  @@index([permission_id], map: "IDX_337aa8dba227a1fe6b73998307")
  @@index([role_id], map: "IDX_7d2dad9f14eddeb09c256fea71")
}

model users {
  created_at DateTime          @default(now()) @db.Timestamp(6)
  updated_at DateTime          @default(now()) @db.Timestamp(6)
  deleted_at DateTime?         @db.Timestamp(6)
  id         Int               @id(map: "PK_a3ffb1c0c8416b9fc6f907b7433") @default(autoincrement())
  account_id Int               @unique(map: "REL_17a709b8b6146c491e6615c29d")
  role_id    Int?
  media_id   Int?
  full_name  String?           @db.VarChar(255)
  email      String?           @unique(map: "UQ_97672ac88f789774dd47f7c8be3") @db.VarChar(255)
  area_code  String?           @db.VarChar(5)
  phone      String?           @db.VarChar(15)
  birthday   String?           @db.VarChar(255)
  address    String?           @db.VarChar(500)
  gender     String?           @db.VarChar(10)
  status     users_status_enum @default(ACTIVE)
  accounts   accounts          @relation(fields: [account_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "FK_17a709b8b6146c491e6615c29d7")
  medias     medias?           @relation(fields: [media_id], references: [id], map: "FK_9c08bde4931ab78abb8729714a3")
  roles      roles?            @relation(fields: [role_id], references: [id], onDelete: Restrict, map: "FK_a2cecd1a3531c0b041e29ba46e1")
}

enum medias_type_enum {
  IMAGE
  VIDEO
  AUDIO
  DOCUMENT
  MISC
}

enum users_status_enum {
  ACTIVE
  DISABLED
}
