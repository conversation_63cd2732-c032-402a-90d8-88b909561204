/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { BuocPheDuyetEntity } from '~/database/typeorm/entities/buocPheDuyet.entity';

@Injectable()
export class BuocPheDuyetRepository extends Repository<BuocPheDuyetEntity> {
    constructor(private dataSource: DataSource) {
        super(BuocPheDuyetEntity, dataSource.createEntityManager());
    }
}
