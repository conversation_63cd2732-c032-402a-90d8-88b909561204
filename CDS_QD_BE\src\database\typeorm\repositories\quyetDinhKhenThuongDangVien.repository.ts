/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { QuyetDinhKhenThuongDangVienEntity } from '~/database/typeorm/entities/quyetDinhKhenThuongDangVien.entity';

@Injectable()
export class QuyetDinhKhenThuongDangVienRepository extends Repository<QuyetDinhKhenThuongDangVienEntity> {
    constructor(private dataSource: DataSource) {
        super(QuyetDinhKhenThuongDangVienEntity, dataSource.createEntityManager());
    }
}
