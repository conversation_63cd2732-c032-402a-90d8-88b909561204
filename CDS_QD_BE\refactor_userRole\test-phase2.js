// Test script for Phase 2 - Authentication & Authorization
console.log('🧪 Testing Phase 2 - Authentication & Authorization');

// Mock user data for testing
const mockUserWithSingleRole = {
    id: 1,
    roleId: 2,
    hoTen: 'Test User Single Role',
    role: { id: 2, name: 'Editor' },
    userRoles: []
};

const mockUserWithMultipleRoles = {
    id: 2,
    roleId: 2,
    hoTen: 'Test User Multiple Roles',
    role: { id: 2, name: 'Editor' },
    userRoles: [
        { role: { id: 3, name: 'Viewer' } },
        { role: { id: 4, name: 'Manager' } }
    ]
};

const mockAdminUser = {
    id: 3,
    roleId: 1, // ADMIN role
    hoTen: 'Admin User',
    role: { id: 1, name: 'Admin' },
    userRoles: [
        { role: { id: 2, name: 'Editor' } }
    ]
};

// Test helper functions
function hasAdminRole(user) {
    const USER_ROLE = { ADMIN: 1 };
    
    // Check single role
    if (user.roleId === USER_ROLE.ADMIN) {
        return true;
    }

    // Check multiple roles
    if (user.userRoles && user.userRoles.length > 0) {
        return user.userRoles.some(userRole => userRole.role?.id === USER_ROLE.ADMIN);
    }

    return false;
}

function getAllUserRoleIds(user) {
    const roleIds = [];

    // Add single role if exists
    if (user.roleId) {
        roleIds.push(user.roleId);
    }

    // Add multiple roles if exists
    if (user.userRoles && user.userRoles.length > 0) {
        user.userRoles.forEach(userRole => {
            if (userRole.role?.id) {
                roleIds.push(userRole.role.id);
            }
        });
    }

    // Remove duplicates
    return [...new Set(roleIds)];
}

function getAllUserRoles(user) {
    const roles = [];
    
    // Add single role if exists
    if (user.role) {
        roles.push(user.role);
    }

    // Add multiple roles if exists
    if (user.userRoles && user.userRoles.length > 0) {
        user.userRoles.forEach(userRole => {
            if (userRole.role) {
                roles.push(userRole.role);
            }
        });
    }

    // Remove duplicates based on role ID
    const uniqueRoles = roles.filter((role, index, self) => 
        index === self.findIndex(r => r.id === role.id)
    );

    return uniqueRoles;
}

// Run tests
console.log('\n📋 Test Results:');

// Test 1: Single role user
console.log('\n1. Testing user with single role:');
console.log('   User:', mockUserWithSingleRole.hoTen);
console.log('   Has admin role:', hasAdminRole(mockUserWithSingleRole));
console.log('   All role IDs:', getAllUserRoleIds(mockUserWithSingleRole));
console.log('   All roles:', getAllUserRoles(mockUserWithSingleRole).map(r => r.name));

// Test 2: Multiple roles user
console.log('\n2. Testing user with multiple roles:');
console.log('   User:', mockUserWithMultipleRoles.hoTen);
console.log('   Has admin role:', hasAdminRole(mockUserWithMultipleRoles));
console.log('   All role IDs:', getAllUserRoleIds(mockUserWithMultipleRoles));
console.log('   All roles:', getAllUserRoles(mockUserWithMultipleRoles).map(r => r.name));

// Test 3: Admin user
console.log('\n3. Testing admin user:');
console.log('   User:', mockAdminUser.hoTen);
console.log('   Has admin role:', hasAdminRole(mockAdminUser));
console.log('   All role IDs:', getAllUserRoleIds(mockAdminUser));
console.log('   All roles:', getAllUserRoles(mockAdminUser).map(r => r.name));

// Test 4: Login response structure
console.log('\n4. Testing login response structure:');
const loginResponse = {
    result: true,
    message: 'Login successfully',
    data: {
        id: 123,
        session: 'mock-token',
        expired: Date.now() + 3600000,
        refreshToken: 'mock-refresh-token',
        role: mockUserWithMultipleRoles.role, // Backward compatibility
        roles: getAllUserRoles(mockUserWithMultipleRoles), // New field
    },
};
console.log('   Login response data keys:', Object.keys(loginResponse.data));
console.log('   Single role (backward compatibility):', loginResponse.data.role.name);
console.log('   Multiple roles:', loginResponse.data.roles.map(r => r.name));

console.log('\n✅ Phase 2 logic tests completed successfully!');
console.log('\n📊 Summary:');
console.log('- ✅ hasAdminRole() works with both single and multiple roles');
console.log('- ✅ getAllUserRoleIds() combines single and multiple roles');
console.log('- ✅ getAllUserRoles() removes duplicates correctly');
console.log('- ✅ Login response maintains backward compatibility');
console.log('- ✅ New roles array provides multiple roles support');
