/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { LoaiDanhMucEntity } from '~/database/typeorm/entities/loaiDanhMuc.entity';

@Injectable()
export class LoaiDanhMucRepository extends Repository<LoaiDanhMucEntity> {
    constructor(private dataSource: DataSource) {
        super(LoaiDanhMucEntity, dataSource.createEntityManager());
    }
}
