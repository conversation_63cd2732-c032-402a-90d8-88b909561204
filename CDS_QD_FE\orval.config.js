module.exports = {
  cdsqd: {
    input: {
      target: '../CDS_QD_BE/swagger-spec.json'
      // Hoặc sử dụng URL nếu API đã được triển khai
      // target: 'http://localhost:8080/docs-json',
    },
    output: {
      mode: 'tags-split',
      target: './src/api/generated',
      schemas: './src/api/generated/model',
      client: 'react-query',
      prettier: true,
      override: {
        mutator: {
          path: './src/api/mutator/custom-instance.ts',
          name: 'customInstance'
        },
        query: {
          useQuery: true,
          useInfinite: true,
          useInfiniteQueryParam: 'page',
          options: {
            staleTime: 10000
          }
        },
        mutation: {
          useMutation: true
        },
        // Định nghĩa các kiểu dữ liệu trả về cho từng endpoint
        operations: {
          // Auth endpoints
          authControllerLogin: {
            response: {
              type: 'LoginResponse',
              imports: [
                {
                  from: '../../types',
                  namedImports: ['LoginResponse']
                }
              ]
            }
          },
          authControllerRenewToken: {
            response: {
              type: 'TokenResponse',
              imports: [
                {
                  from: '../../types',
                  namedImports: ['TokenResponse']
                }
              ]
            }
          },
          // User endpoints
          userControllerFindAll: {
            response: {
              type: 'PaginatedResponse<UserResponse>',
              imports: [
                {
                  from: '../../types',
                  namedImports: ['PaginatedResponse', 'UserResponse']
                }
              ]
            }
          },
          userControllerFindOne: {
            response: {
              type: 'UserResponse',
              imports: [
                {
                  from: '../../types',
                  namedImports: ['UserResponse']
                }
              ]
            }
          },
          userControllerCreate: {
            response: {
              type: 'UserResponse',
              imports: [
                {
                  from: '../../types',
                  namedImports: ['UserResponse']
                }
              ]
            }
          },
          userControllerUpdate: {
            response: {
              type: 'UserResponse',
              imports: [
                {
                  from: '../../types',
                  namedImports: ['UserResponse']
                }
              ]
            }
          }
        }
      }
    },
    hooks: {
      afterAllFilesWrite: 'prettier --write'
    }
  }
}
