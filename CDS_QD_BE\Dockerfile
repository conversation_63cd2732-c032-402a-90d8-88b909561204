# DEPENDENCIES
FROM node:18-alpine AS fulldeps

RUN mkdir -p /home/<USER>

WORKDIR /home/<USER>

COPY package*.json ./

COPY yarn*.lock ./

RUN yarn install

# BUILDER
FROM node:18-alpine AS builder

RUN mkdir -p /home/<USER>

WORKDIR /home/<USER>

COPY --from=fulldeps /home/<USER>/node_modules ./node_modules

COPY . .

RUN yarn run build

# RUNNER
FROM node:18-alpine AS runner

RUN mkdir -p /home/<USER>

WORKDIR /home/<USER>

COPY --from=builder /home/<USER>/node_modules ./node_modules
COPY --from=builder /home/<USER>/dist ./dist
COPY --from=builder /home/<USER>/.env ./.env

EXPOSE 8080

CMD ["node", "dist/main"]