{"version": 3, "sources": ["../../src/bootstraps/socket.bootstrap.ts"], "sourcesContent": ["import { INestApplication } from '@nestjs/common';\r\nimport { SocketAdapter } from '~/modules/socket/socket.adapter';\r\n\r\nexport function bootstrapSocket(app: INestApplication): void {\r\n    app.useWebSocketAdapter(new SocketAdapter(app));\r\n}\r\n"], "names": ["bootstrapSocket", "app", "useWebSocketAdapter", "SocketAdapter"], "mappings": "oGAGgBA,yDAAAA,gDAFc,oCAEvB,SAASA,gBAAgBC,GAAqB,EACjDA,IAAIC,mBAAmB,CAAC,IAAIC,4BAAa,CAACF,KAC9C"}