# Kế hoạch Cấu hình Luồng Gọi API cho Frontend (Next.js)

## Mục tiêu

Xây dựng một kiến trúc gọi API chuẩn và nhất quán cho ứng dụng Next.js, tuân theo luồng: React Component -> Custom Hook -> API Service -> Axios Instance (Interceptors) -> BE API. Kế hoạch này tập trung vào việc thiết lập nền tảng vững chắc cho việc tương tác với API, đảm bảo tính dễ bảo trì, mở rộng và thống nhất trong request/response.

## Luồng API Dự kiến

1. **React Component**: Thành phần giao diện người dùng, chịu trách nhiệm hiển thị dữ liệu và kích hoạt các hành động liên quan đến API.
2. **Custom Hook**: Tách biệt logic gọi API và quản lý state liên quan ra khỏi component. Cung cấp một interface rõ ràng cho component sử dụng.
3. **API Service**: Lớp trừu tượng hóa việc gọi các endpoint cụ thể. Mỗi service sẽ tương ứng với một nhóm chức năng hoặc một resource trên BE.
4. **Axios Instance (Interceptors)**: Cấu hình một instance Axios duy nhất với các interceptor để xử lý các tác vụ chung như thêm token xác thực, xử lý lỗi tập trung, logging, v.v.
5. **BE API**: Các API endpoints phía backend.

## Tổng quan về hiện trạng codebase

Qua việc phân tích codebase CDS_QD_FE hiện tại, chúng ta đã xác định:

1. **Cấu trúc thư mục**:

   - Đã có thư mục `src/api/` (hiện đang trống)
   - Đã có thư mục `src/hooks/` với cấu trúc phân chia theo modules (shared/, table/, modules/, filter/)

2. **Thư viện đã cài đặt**:

   - Đã cài đặt `@tanstack/react-query` phiên bản ^5.76.1
   - Chưa cài đặt rõ ràng `axios` trong package.json
   - Chưa cài đặt `@tanstack/react-query-devtools`

3. **Provider và cấu hình**:
   - Có file `src/components/Providers.tsx` quản lý các providers hiện tại
   - Chưa thiết lập `QueryClientProvider` trong hệ thống Providers

## Phase 1: Cài đặt và Cấu hình Ban đầu

### Task 1.1: Cài đặt Thư viện

- **Subtask 1.1.1**: Cài đặt `axios`.
  - Lệnh: `cd CDS_QD_FE ; pnpm add axios`
- **Subtask 1.1.2**: Xác nhận phiên bản `@tanstack/react-query` (hiện tại: `^5.76.1` - đã cài đặt).
- **Subtask 1.1.3**: Cài đặt `@tanstack/react-query-devtools` cho môi trường development.
  - Lệnh: `cd CDS_QD_FE ; pnpm add @tanstack/react-query-devtools -D`

### Task 1.2: Cấu hình `QueryClientProvider`

- **Subtask 1.2.1**: Cập nhật file `src/components/Providers.tsx` để tích hợp QueryClientProvider.
- **Subtask 1.2.2**: Khởi tạo và cấu hình QueryClient với các defaultOptions phù hợp cho SSR và Next.js App Router.
- **Subtask 1.2.3**: Thêm `ReactQueryDevtools` cho môi trường development.

  ```tsx
  // src/components/Providers.tsx
  import * as React from "react"
  import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
  import { ReactQueryDevtools } from "@tanstack/react-query-devtools"
  import type { ChildrenType, Direction } from "@core/types"

  // Context Imports
  import { NextAuthProvider } from "@/contexts/nextAuthProvider"
  import { VerticalNavProvider } from "@menu/contexts/verticalNavContext"
  import { SettingsProvider } from "@core/contexts/settingsContext"
  import ThemeProvider from "@components/theme"

  // Config Imports
  import themeConfig from "@configs/themeConfig"

  // Styled Component Imports
  import AppReactToastify from "@/libs/styles/AppReactToastify"

  // Util Imports
  import {
    getDemoName,
    getMode,
    getSettingsFromCookie,
    getSystemMode
  } from "@core/utils/serverHelpers"

  type Props = ChildrenType & {
    direction: Direction
  }

  const Providers = (props: Props) => {
    // Props
    const { children, direction } = props

    // Vars
    const mode = getMode()
    const settingsCookie = getSettingsFromCookie()
    const demoName = getDemoName()
    const systemMode = getSystemMode()

    // Tạo queryClient với useState để đảm bảo mỗi request có một instance riêng
    const [queryClient] = React.useState(
      () =>
        new QueryClient({
          defaultOptions: {
            queries: {
              staleTime: 60 * 1000, // 1 phút
              refetchOnWindowFocus: false
            }
          }
        })
    )

    return (
      <QueryClientProvider client={queryClient}>
        <NextAuthProvider basePath={process.env.NEXTAUTH_BASEPATH}>
          <VerticalNavProvider>
            <SettingsProvider
              settingsCookie={settingsCookie}
              mode={mode}
              demoName={demoName}
            >
              <ThemeProvider direction={direction} systemMode={systemMode}>
                {children}
                <AppReactToastify
                  position={themeConfig.toastPosition}
                  hideProgressBar
                />
                {process.env.NODE_ENV !== "production" && (
                  <ReactQueryDevtools initialIsOpen={false} />
                )}
              </ThemeProvider>
            </SettingsProvider>
          </VerticalNavProvider>
        </NextAuthProvider>
      </QueryClientProvider>
    )
  }

  export default Providers
  ```

### Task 1.3: Thiết lập Cấu trúc Thư mục

- **Subtask 1.3.1**: Tạo cấu trúc thư mục cơ bản cho API, phân chia theo modules:
  ```
  src/
  |-- api/
  |   |-- index.ts                          # Khởi tạo và cấu hình Axios instance
  |   |-- services/                         # Chứa các API services theo module
  |   |   |-- auth/                         # Auth services
  |   |   |   |-- auth.service.ts           # Service cho xác thực
  |   |   |   |-- auth.types.ts             # Types cho auth API
  |   |   |
  |   |   |-- common/                       # Common services
  |   |   |   |-- dropdown.service.ts       # Các API dùng chung như dropdown
  |   |   |
  |   |   |-- quan-ly-quan-nhan/            # Module Quản lý Quân nhân
  |   |   |   |-- quan-nhan.service.ts      # Service cho quân nhân
  |   |   |   |-- quan-nhan.types.ts        # Types
  |   |   |   |-- don-vi.service.ts         # Service cho đơn vị
  |   |   |
  |   |   |-- cong-tac-dang/                # Module Công tác Đảng
  |   |       |-- dang-vien.service.ts
  |   |       |-- dang-vien.types.ts
  |   |
  |   |-- interceptors/                    # Chứa các interceptors
  |       |-- auth.interceptor.ts          # Xử lý authentication
  |       |-- error.interceptor.ts         # Xử lý lỗi
  |
  |-- hooks/                               # Đã có sẵn
      |-- shared/                          # Shared hooks
      |   |-- api/                         # API hooks dùng chung
      |       |-- useAuthApi.ts            # Hook xác thực
      |       |-- useDropdownApi.ts        # Hook dropdown
      |
      |-- modules/                         # Các module nghiệp vụ
          |-- quan-ly-quan-nhan/
          |   |-- api/                     # API hooks cho Quân nhân
          |       |-- useQuanNhanApi.ts    # Hook quân nhân
          |       |-- useDonViApi.ts       # Hook đơn vị
          |
          |-- cong-tac-dang/
              |-- api/                     # API hooks cho Đảng
                  |-- useDangVienApi.ts    # Hook đảng viên
  ```
- **Subtask 1.3.2**: Thống nhất quy ước đặt tên file và folder:
  - Sử dụng kebab-case cho tên file và folder (đã áp dụng trong dự án)
  - Sử dụng camelCase cho tên hàm và biến
  - Sử dụng PascalCase cho tên interface, type, và object service

## Phase 2: Cấu hình Axios và Interceptors

### Task 2.1: Tạo Axios Instance

- **Subtask 2.1.1**: Tạo file `src/api/index.ts` và triển khai Axios instance.

  ```typescript
  // src/api/index.ts
  import axios from "axios"

  const apiClient = axios.create({
    baseURL: process.env.NEXT_PUBLIC_API_URL,
    timeout: 10000,
    headers: {
      "Content-Type": "application/json"
    }
  })

  export default apiClient
  ```

### Task 2.2: Cấu hình Interceptors

- **Subtask 2.2.1**: Tạo file `src/api/interceptors/auth.interceptor.ts` để xử lý authentication.

  ```typescript
  // src/api/interceptors/auth.interceptor.ts
  import { AxiosRequestConfig } from "axios"

  export const authInterceptor = async (
    config: AxiosRequestConfig
  ): Promise<AxiosRequestConfig> => {
    try {
      // Lấy token từ localStorage hoặc cookie
      const token = localStorage.getItem("accessToken")

      if (token) {
        config.headers = config.headers || {}
        config.headers.Authorization = `Bearer ${token}`
      }
    } catch (error) {
      console.error("Error adding auth token:", error)
    }

    return config
  }
  ```

- **Subtask 2.2.2**: Tạo file `src/api/interceptors/error.interceptor.ts` để xử lý lỗi.

  ```typescript
  // src/api/interceptors/error.interceptor.ts
  import { AxiosError } from "axios"
  import { toast } from "react-toastify"

  export const errorInterceptor = (error: AxiosError) => {
    const message = error.response?.data?.message || "Đã xảy ra lỗi"

    // Xử lý các mã lỗi cụ thể
    if (error.response?.status === 401) {
      // Xử lý lỗi xác thực - có thể redirect về trang login
      // window.location.href = "/login"
    }

    toast.error(message)
    return Promise.reject(error)
  }
  ```

- **Subtask 2.2.3**: Cập nhật file `src/api/index.ts` để áp dụng các interceptors.

  ```typescript
  // src/api/index.ts (cập nhật)
  import axios from "axios"
  import { authInterceptor } from "./interceptors/auth.interceptor"
  import { errorInterceptor } from "./interceptors/error.interceptor"

  const apiClient = axios.create({
    baseURL: process.env.NEXT_PUBLIC_API_URL,
    timeout: 10000,
    headers: {
      "Content-Type": "application/json"
    }
  })

  // Thêm interceptors
  apiClient.interceptors.request.use(authInterceptor)
  apiClient.interceptors.response.use(
    (response) => response.data, // Trả về data trực tiếp
    errorInterceptor
  )

  export default apiClient
  ```

## Phase 3: Cấu hình và Xây dựng hệ thống bảo vệ route

### Task 3.1: Cấu hình và Xây dựng hệ thống bảo vệ route

- **Subtask 3.1.1**: Cấu hình và Xây dựng hệ thống bảo vệ route

  ```typescript
  // src/api/index.ts (cập nhật)
  import axios from "axios"
  import { authInterceptor } from "./interceptors/auth.interceptor"
  import { errorInterceptor } from "./interceptors/error.interceptor"

  const apiClient = axios.create({
    baseURL: process.env.NEXT_PUBLIC_API_URL,
    timeout: 10000,
    headers: {
      "Content-Type": "application/json"
    }
  })

  // Thêm interceptors
  apiClient.interceptors.request.use(authInterceptor)
  apiClient.interceptors.response.use(
    (response) => response.data, // Trả về data trực tiếp
    errorInterceptor
  )

  export default apiClient
  ```

- **Subtask 3.1.2**: Cấu hình và Xây dựng hệ thống bảo vệ route

  ```typescript
  export const config = {
    matcher: ["/((?!api|_next/static|_next/image|favicon.ico|assets).*)"]
  }
  ```

- **Subtask 3.4.3**: Cập nhật các component sử dụng NextAuth

  - Cập nhật UserDropdown.tsx để sử dụng useAuth hook thay vì useSession

    ```typescript
    // src/components/layout/shared/UserDropdown.tsx (cập nhật)
    "use client"

    // React Imports
    import { useRef, useState } from "react"
    import type { MouseEvent } from "react"

    // Next Imports
    import { useParams, useRouter } from "next/navigation"

    // MUI Imports
    import { styled } from "@mui/material/styles"
    import Badge from "@mui/material/Badge"
    import Avatar from "@mui/material/Avatar"
    import Popper from "@mui/material/Popper"
    import Fade from "@mui/material/Fade"
    import Paper from "@mui/material/Paper"
    import ClickAwayListener from "@mui/material/ClickAwayListener"
    import MenuList from "@mui/material/MenuList"
    import Typography from "@mui/material/Typography"
    import Divider from "@mui/material/Divider"
    import MenuItem from "@mui/material/MenuItem"
    import Button from "@mui/material/Button"

    // Hook Imports
    import { useAuth } from "@/contexts/authContext"
    import { useAuthApi } from "@/hooks/shared/api/useAuthApi"
    import { useSettings } from "@core/hooks/useSettings"

    // Type Imports
    import type { Locale } from "@configs/i18n"

    // Util Imports
    import { getLocalizedUrl } from "@/utils/i18n"

    // Styled component for badge content
    const BadgeContentSpan = styled("span")({
      width: 8,
      height: 8,
      borderRadius: "50%",
      cursor: "pointer",
      backgroundColor: "var(--mui-palette-success-main)",
      boxShadow: "0 0 0 2px var(--mui-palette-background-paper)"
    })

    const UserDropdown = () => {
      // States
      const [open, setOpen] = useState(false)

      // Refs
      const anchorRef = useRef<HTMLDivElement>(null)

      // Hooks
      const router = useRouter()
      const { user, authToken, logout: clearAuthData } = useAuth()
      const { lang: locale } = useParams()
      const { settings } = useSettings()
      const { logout: logoutApi } = useAuthApi(locale as Locale)

      const handleDropdownOpen = () => {
        !open ? setOpen(true) : setOpen(false)
      }

      const handleDropdownClose = (
        event?: MouseEvent<HTMLLIElement> | (MouseEvent | TouchEvent),
        url?: string
      ) => {
        if (url) {
          router.push(getLocalizedUrl(url, locale as Locale))
        }

        if (
          anchorRef.current &&
          anchorRef.current.contains(event?.target as HTMLElement)
        ) {
          return
        }

        setOpen(false)
      }

      const handleUserLogout = async () => {
        try {
          // Gọi API đăng xuất
          if (authToken) {
            await logoutApi({ session: authToken })
          }

          // Xóa dữ liệu đăng nhập từ context
          clearAuthData()

          // Chuyển hướng đến trang đăng nhập
          router.push(getLocalizedUrl("/login", locale as Locale))
        } catch (error) {
          console.error(error)
        }
      }

      return (
        <>
          <Badge
            ref={anchorRef}
            overlap="circular"
            badgeContent={<BadgeContentSpan onClick={handleDropdownOpen} />}
            anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
            className="mis-2"
          >
            <Avatar
              ref={anchorRef}
              alt={user?.fullName || ""}
              src="/path-to-default-avatar.png"
              onClick={handleDropdownOpen}
              className="cursor-pointer bs-[38px] is-[38px]"
            />
          </Badge>
          {/* Phần còn lại của component không thay đổi */}
        </>
      )
    }

    export default UserDropdown
    ```

  - Cập nhật các component khác sử dụng session

### Task 3.5: Xây dựng hệ thống bảo vệ route

- **Subtask 3.5.1**: Tạo file `src/utils/auth.ts` để xử lý các hàm tiện ích cho auth

  ```typescript
  // src/utils/auth.ts
  import { jwtDecode } from "jwt-decode"
  import { AuthTokenPayload } from "@/api/services/auth/auth.types"

  export const isTokenExpired = (token: string): boolean => {
    try {
      const decoded = jwtDecode<AuthTokenPayload>(token)
      return decoded.exp < Date.now() / 1000
    } catch {
      return true
    }
  }

  // Đồng bộ cookie và localStorage để sử dụng trong cả client-side và server-side
  export const syncTokenToCookie = (token: string | null): void => {
    if (typeof document === "undefined") return

    if (token) {
      document.cookie = `authToken=${token}; path=/; max-age=${
        60 * 60 * 24 * 7
      }; SameSite=Strict` // 7 ngày
    } else {
      document.cookie = "authToken=; path=/; max-age=0"
    }
  }

  export const getAuthRedirectUrl = (redirectPath?: string): string => {
    if (!redirectPath) return "/"

    try {
      // Kiểm tra xem redirectPath có phải là URL hợp lệ không
      const url = new URL(redirectPath, window.location.origin)
      // Chỉ cho phép redirect trong cùng domain
      if (url.origin === window.location.origin) {
        return url.pathname + url.search
      }
    } catch (e) {
      // Nếu không phải URL hợp lệ, xem như là pathname
      if (redirectPath.startsWith("/")) {
        return redirectPath
      }
    }

    return "/"
  }
  ```

- **Subtask 3.5.2**: Tạo file `src/components/auth/RequireAuth.tsx` để bảo vệ route của component

  ```typescript
  // src/components/auth/RequireAuth.tsx
  "use client"

  import { useEffect } from "react"
  import { useRouter, usePathname, useSearchParams } from "next/navigation"
  import { useAuth } from "@/contexts/authContext"
  import { getLocalizedUrl } from "@/utils/i18n"
  import type { Locale } from "@configs/i18n"
  import type { ReactNode } from "react"

  interface RequireAuthProps {
    children: ReactNode
    locale: Locale
  }

  /**
   * HOC để bảo vệ các route yêu cầu xác thực
   * Nếu người dùng chưa đăng nhập, chuyển hướng đến trang login
   */
  export const RequireAuth = ({ children, locale }: RequireAuthProps) => {
    const { isAuthenticated } = useAuth()
    const router = useRouter()
    const pathname = usePathname()

    useEffect(() => {
      if (!isAuthenticated) {
        const searchParams = new URLSearchParams()
        searchParams.append("redirectTo", pathname)
        router.push(
          getLocalizedUrl(`/login?${searchParams.toString()}`, locale)
        )
      }
    }, [isAuthenticated, router, pathname, locale])

    if (!isAuthenticated) {
      return null
    }

    return <>{children}</>
  }

  interface RequireGuestProps {
    children: ReactNode
    locale: Locale
  }

  /**
   * HOC để bảo vệ các route chỉ dành cho khách
   * Nếu người dùng đã đăng nhập, chuyển hướng đến trang home
   */
  export const RequireGuest = ({ children, locale }: RequireGuestProps) => {
    const { isAuthenticated } = useAuth()
    const router = useRouter()
    const searchParams = useSearchParams()

    useEffect(() => {
      if (isAuthenticated) {
        const redirectTo = searchParams.get("redirectTo") || "/"
        router.push(getLocalizedUrl(redirectTo, locale))
      }
    }, [isAuthenticated, router, searchParams, locale])

    if (isAuthenticated) {
      return null
    }

    return <>{children}</>
  }
  ```

- **Subtask 3.5.3**: Tạo file `src/app/[lang]/login/page.tsx` để sử dụng RequireGuest

  ```typescript
  // src/app/[lang]/login/page.tsx
  import { RequireGuest } from "@/components/auth/RequireAuth"
  import LoginForm from "@/components/auth/LoginForm"
  import { Locale } from "@configs/i18n"

  interface LoginPageProps {
    params: {
      lang: Locale
    }
  }

  const LoginPage = ({ params }: LoginPageProps) => {
    const { lang } = params

    return (
      <RequireGuest locale={lang}>
        <LoginForm locale={lang} />
      </RequireGuest>
    )
  }

  export default LoginPage
  ```

### Task 3.6: Tích hợp và kiểm thử

- **Subtask 3.6.1**: Tạo file `src/components/auth/LoginForm.tsx` để tích hợp với form đăng nhập hiện tại

  ```typescript
  // src/components/auth/LoginForm.tsx
  "use client"

  import { useState } from "react"
  import { useSearchParams } from "next/navigation"
  import { useAuthApi } from "@/hooks/shared/api/useAuthApi"
  import { Locale } from "@configs/i18n"
  import { getAuthRedirectUrl } from "@/utils/auth"

  // MUI Imports - tùy vào thiết kế UI hiện tại
  import TextField from "@mui/material/TextField"
  import Button from "@mui/material/Button"
  import Card from "@mui/material/Card"
  import CardContent from "@mui/material/CardContent"
  import Typography from "@mui/material/Typography"
  import Box from "@mui/material/Box"
  import CircularProgress from "@mui/material/CircularProgress"

  interface LoginFormProps {
    locale: Locale
  }

  const LoginForm = ({ locale }: LoginFormProps) => {
    const [username, setUsername] = useState("")
    const [password, setPassword] = useState("")
    const [error, setError] = useState<string | null>(null)

    const searchParams = useSearchParams()
    const { login, isLoginLoading } = useAuthApi(locale)

    const handleSubmit = async (e: React.FormEvent) => {
      e.preventDefault()
      setError(null)

      if (!username.trim() || !password.trim()) {
        setError("Vui lòng nhập đầy đủ thông tin")
        return
      }

      try {
        login(
          { username, password },
          {
            onSuccess: () => {
              // Đã xử lý redirect trong hook useAuthApi
            },
            onError: (err: any) => {
              setError(err?.message || "Đăng nhập thất bại")
            }
          }
        )
      } catch (error) {
        setError("Đăng nhập thất bại, vui lòng thử lại")
      }
    }

    return (
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          minHeight: "100vh"
        }}
      >
        <Card sx={{ maxWidth: 400, width: "100%" }}>
          <CardContent>
            <Typography
              variant="h5"
              component="h1"
              sx={{ mb: 3, textAlign: "center" }}
            >
              Đăng nhập
            </Typography>

            <form onSubmit={handleSubmit}>
              <TextField
                label="Tên đăng nhập"
                variant="outlined"
                fullWidth
                margin="normal"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
              />

              <TextField
                label="Mật khẩu"
                type="password"
                variant="outlined"
                fullWidth
                margin="normal"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />

              {error && (
                <Typography color="error" variant="body2" sx={{ mt: 2 }}>
                  {error}
                </Typography>
              )}

              <Button
                type="submit"
                variant="contained"
                color="primary"
                fullWidth
                sx={{ mt: 3 }}
                disabled={isLoginLoading}
              >
                {isLoginLoading ? <CircularProgress size={24} /> : "Đăng nhập"}
              </Button>
            </form>
          </CardContent>
        </Card>
      </Box>
    )
  }

  export default LoginForm
  ```

- **Subtask 3.6.2**: Kiểm thử luồng đăng nhập, đăng xuất, và refresh token

  - Kiểm thử đăng nhập với tài khoản hợp lệ và không hợp lệ
  - Kiểm thử bảo vệ route với RequireAuth và RequireGuest
  - Kiểm thử đăng xuất và xóa token
  - Kiểm thử refresh token khi token hết hạn
  - Kiểm thử đồng bộ cookie và localStorage

## Phase 4: Tối ưu hóa và Theo dõi Hiệu suất

### Task 4.1: Tối ưu hóa hiệu suất auth

- **Subtask 4.1.1**: Cấu hình caching token và thông tin người dùng

  - Sử dụng React Query để caching kết quả API
  - Tối ưu số lần fetch thông tin người dùng

- **Subtask 4.1.2**: Giảm thiểu việc gọi API không cần thiết
  - Kiểm tra token hết hạn trước khi gọi API
  - Cân nhắc prefetching dữ liệu sau khi đăng nhập

### Task 4.2: Thêm tính năng bảo mật

- **Subtask 4.2.1**: Thêm hỗ trợ 2FA (Xác thực hai yếu tố) nếu BE hỗ trợ

  - Tạo interface và hook để tương tác với API 2FA
  - Thiết kế UI cho nhập mã xác thực

- **Subtask 4.2.2**: Cải thiện an ninh token
  - Cấu hình thời gian sống hợp lý cho token
  - Cân nhắc lưu trữ refresh token trong HTTP-only cookie

### Task 4.3: Theo dõi và cải thiện

- **Subtask 4.3.1**: Theo dõi hiệu suất auth

  - Giám sát số lần refresh token
  - Theo dõi thời gian phản hồi API auth

- **Subtask 4.3.2**: Phân tích và cải thiện
  - Xác định các điểm nghẽn trong luồng auth
  - Tối ưu hóa cơ chế caching và quản lý state
