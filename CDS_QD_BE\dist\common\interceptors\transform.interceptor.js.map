{"version": 3, "sources": ["../../../src/common/interceptors/transform.interceptor.ts"], "sourcesContent": ["import { Injectable } from '@nestjs/common';\nimport { map } from 'rxjs/operators';\n\n@Injectable()\nexport class TransformInterceptor {\n    intercept(context, next) {\n        return next.handle().pipe(\n            map((data: object) => {\n                if (!data) {\n                    return {\n                        result: false,\n                        message: '[Interceptor] No data returned',\n                        data: null,\n                        pagination: undefined,\n                    };\n                }\n\n                const res = data['data'] || data;\n                return {\n                    result: !!res,\n                    message: res ? data['message'] || 'Success' : data['message'] || 'Fail',\n                    data: res,\n                    pagination: data['pagination'] || undefined,\n                };\n            }),\n        );\n    }\n}\n"], "names": ["TransformInterceptor", "intercept", "context", "next", "handle", "pipe", "map", "data", "result", "message", "pagination", "undefined", "res"], "mappings": "oGAIaA,8DAAAA,8CAJc,2CACP,ocAGb,IAAA,AAAMA,qBAAN,MAAMA,qBACTC,UAAUC,OAAO,CAAEC,IAAI,CAAE,CACrB,OAAOA,KAAKC,MAAM,GAAGC,IAAI,CACrBC,GAAAA,cAAG,EAAC,AAACC,OACD,GAAI,CAACA,KAAM,CACP,MAAO,CACHC,OAAQ,MACRC,QAAS,iCACTF,KAAM,KACNG,WAAYC,SAChB,CACJ,CAEA,MAAMC,IAAML,IAAI,CAAC,OAAO,EAAIA,KAC5B,MAAO,CACHC,OAAQ,CAAC,CAACI,IACVH,QAASG,IAAML,IAAI,CAAC,UAAU,EAAI,UAAYA,IAAI,CAAC,UAAU,EAAI,OACjEA,KAAMK,IACNF,WAAYH,IAAI,CAAC,aAAa,EAAII,SACtC,CACJ,GAER,CACJ"}