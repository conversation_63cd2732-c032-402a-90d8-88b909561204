{"version": 3, "sources": ["../../../../src/database/typeorm/entities/user.entity.ts"], "sourcesContent": ["import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany, OneToOne, PrimaryGeneratedColumn, Relation } from 'typeorm';\nimport { AbstractEntity } from '~/database/typeorm/entities/abstract.entity';\nimport { AccountEntity } from '~/database/typeorm/entities/account.entity';\nimport { GiaTriDanhMucEntity } from '~/database/typeorm/entities/giaTriDanhMuc.entity';\nimport { DuLieuTepTinEntity } from '~/database/typeorm/entities/duLieuTepTin.entity';\nimport { QuanNhanEntity } from '~/database/typeorm/entities/quanNhan.entity';\nimport { RoleEntity } from './role.entity';\nimport { UsersRoleEntity } from './usersRole.entity';\nimport { USER_STATUS } from '~/common/enums/enum';\n\n@Entity({ name: 'users' })\nexport class UserEntity extends AbstractEntity {\n    @PrimaryGeneratedColumn('increment', { name: 'id', type: 'int', unsigned: true })\n    id: number;\n\n    @Column({ name: 'account_id', type: 'bigint', nullable: false })\n    accountId: number;\n\n    @Column({ name: 'role_id', type: 'int', unsigned: true, nullable: true })\n    roleId: number;\n\n    @Column({ name: 'avatar_id', type: 'uuid', nullable: true })\n    avatarId: string;\n\n    @Column({ name: 'ho_ten', type: 'varchar', length: 100, nullable: false })\n    hoTen: string;\n\n    @Column({ name: 'don_vi_id', type: 'bigint', nullable: true })\n    donViId: number;\n\n    @Column({ name: 'email', type: 'varchar', length: 100, nullable: true, unique: true })\n    email: string;\n\n    @Column({ name: 'so_dien_thoai', type: 'varchar', length: 15, nullable: true })\n    soDienThoai: string;\n\n    @Column({ name: 'quan_nhan_id', type: 'varchar', length: 20, nullable: true, unique: true })\n    quanNhanId: string;\n\n    // @Column({ name: 'trang_thai_dang_nhap_id', type: 'bigint', nullable: false })\n    // trangThaiDangNhapId: number;\n\n    @Column({ type: 'enum', enum: USER_STATUS, default: USER_STATUS.ACTIVE })\n    status: USER_STATUS;\n\n    @Column({ name: 'lan_dang_nhap_cuoi', type: 'timestamp with time zone', nullable: true })\n    lanDangNhapCuoi: Date;\n\n    @Column({ name: 'so_lan_dang_nhap_sai', type: 'integer', default: 0 })\n    soLanDangNhapSai: number;\n\n    @Column({ name: 'thoi_gian_khoa_tai_khoan', type: 'timestamp with time zone', nullable: true })\n    thoiGianKhoaTaiKhoan: Date;\n\n    @Column({ name: 'yeu_cau_doi_mat_khau', type: 'boolean', default: false })\n    yeuCauDoiMatKhau: boolean;\n\n    /* RELATION */\n    @OneToOne(() => AccountEntity, { createForeignKeyConstraints: false })\n    @JoinColumn({ name: 'account_id', referencedColumnName: 'id' })\n    account: Relation<AccountEntity>;\n\n    @OneToOne(() => DuLieuTepTinEntity, { createForeignKeyConstraints: false })\n    @JoinColumn({ name: 'avatar_id', referencedColumnName: 'id' })\n    avatar?: Relation<DuLieuTepTinEntity>;\n\n    @ManyToOne(() => GiaTriDanhMucEntity, { createForeignKeyConstraints: false })\n    @JoinColumn({ name: 'don_vi_id', referencedColumnName: 'id' })\n    donVi: Relation<GiaTriDanhMucEntity>;\n\n    @OneToOne(() => QuanNhanEntity, { createForeignKeyConstraints: false })\n    @JoinColumn({ name: 'quan_nhan_id', referencedColumnName: 'soHieuQuanNhan' })\n    quanNhan: Relation<QuanNhanEntity>;\n\n    // @ManyToOne(() => GiaTriDanhMucEntity, { createForeignKeyConstraints: false })\n    // @JoinColumn({ name: 'trang_thai_dang_nhap_id', referencedColumnName: 'id' })\n    // trangThaiDangNhap: Relation<GiaTriDanhMucEntity>;\n\n    /* RELATION */\n    @ManyToOne(() => RoleEntity, (role: RoleEntity) => role.id, {\n        onDelete: 'RESTRICT',\n        onUpdate: 'CASCADE',\n        createForeignKeyConstraints: false,\n    })\n    @JoinColumn({ name: 'role_id', referencedColumnName: 'id' })\n    role: Relation<RoleEntity>;\n\n    // Many-to-many relationship with roles through users_roles table\n    @OneToMany(() => UsersRoleEntity, (userRole) => userRole.user, {\n        cascade: true,\n        createForeignKeyConstraints: false,\n    })\n    userRoles: Relation<UsersRoleEntity[]>;\n}\n"], "names": ["UserEntity", "AbstractEntity", "name", "type", "unsigned", "nullable", "length", "unique", "enum", "USER_STATUS", "default", "ACTIVE", "AccountEntity", "createForeignKeyConstraints", "referencedColumnName", "DuLieuTepTinEntity", "GiaTriDanhMucEntity", "QuanNhanEntity", "RoleEntity", "role", "id", "onDelete", "onUpdate", "UsersRoleEntity", "userRole", "user", "cascade"], "mappings": "oGAWaA,oDAAAA,qCAXgG,yCAC9E,kDACD,uDACM,4DACD,uDACJ,+CACJ,gDACK,0CACJ,2kBAGrB,IAAA,AAAMA,WAAN,MAAMA,mBAAmBC,8BAAc,CAkF9C,iEAjF2CC,KAAM,KAAMC,KAAM,MAAOC,SAAU,gHAGhEF,KAAM,aAAcC,KAAM,SAAUE,SAAU,wHAG9CH,KAAM,UAAWC,KAAM,MAAOC,SAAU,KAAMC,SAAU,oHAGxDH,KAAM,YAAaC,KAAM,OAAQE,SAAU,sHAG3CH,KAAM,SAAUC,KAAM,UAAWG,OAAQ,IAAKD,SAAU,oHAGxDH,KAAM,YAAaC,KAAM,SAAUE,SAAU,qHAG7CH,KAAM,QAASC,KAAM,UAAWG,OAAQ,IAAKD,SAAU,KAAME,OAAQ,mHAGrEL,KAAM,gBAAiBC,KAAM,UAAWG,OAAQ,GAAID,SAAU,yHAG9DH,KAAM,eAAgBC,KAAM,UAAWG,OAAQ,GAAID,SAAU,KAAME,OAAQ,wHAM3EJ,KAAM,OAAQK,KAAMC,iBAAW,CAAEC,QAASD,iBAAW,CAACE,MAAM,yKAG5DT,KAAM,qBAAsBC,KAAM,2BAA4BE,SAAU,4JAGxEH,KAAM,uBAAwBC,KAAM,UAAWO,QAAS,2HAGxDR,KAAM,2BAA4BC,KAAM,2BAA4BE,SAAU,iKAG9EH,KAAM,uBAAwBC,KAAM,UAAWO,QAAS,qIAIlDE,4BAAa,EAAIC,4BAA6B,iCAChDX,KAAM,aAAcY,qBAAsB,mLAGxCC,sCAAkB,EAAIF,4BAA6B,iCACrDX,KAAM,YAAaY,qBAAsB,mLAGtCE,wCAAmB,EAAIH,4BAA6B,iCACvDX,KAAM,YAAaY,qBAAsB,iLAGvCG,8BAAc,EAAIJ,4BAA6B,iCACjDX,KAAM,eAAgBY,qBAAsB,iMAQzCI,sBAAU,CAAGC,MAAqBA,KAAKC,EAAE,EACtDC,SAAU,WACVC,SAAU,UACVT,4BAA6B,iCAEnBX,KAAM,UAAWY,qBAAsB,iLAIpCS,gCAAe,CAAGC,UAAaA,SAASC,IAAI,EACzDC,QAAS,KACTb,4BAA6B,4LAhF3BX,KAAM"}