"use strict";Object.defineProperty(exports,"__esModule",{value:true});Object.defineProperty(exports,"bootstrapExceptions",{enumerable:true,get:function(){return bootstrapExceptions}});const _exceptionfilter=require("../common/filters/exception.filter");const _typeormfilter=require("../common/filters/typeorm.filter");function bootstrapExceptions(app){app.useGlobalFilters(new _exceptionfilter.AllExceptionsFilter,new _typeormfilter.TypeOrmFilter)}
//# sourceMappingURL=exceptions.bootstrap.js.map