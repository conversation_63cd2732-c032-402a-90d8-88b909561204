/* eslint-disable @typescript-eslint/no-var-requires */
const fs = require('fs');
const path = require('path');

// Đường dẫn đến thư mục entities và repositories
const entitiesDir = path.join(__dirname, '..', 'src', 'database', 'typeorm', 'entities');
const repositoriesDir = path.join(__dirname, '..', 'src', 'database', 'typeorm', 'repositories');

// Các cặp tên file cần đổi (từ -> đến)
const fileRenames = [
    // Entities
    {
        from: path.join(entitiesDir, 'loaidanhmuc.entity.ts'),
        to: path.join(entitiesDir, 'loaiDanhMuc.entity.ts'),
    },
    {
        from: path.join(entitiesDir, 'giatridanhmuc.entity.ts'),
        to: path.join(entitiesDir, 'giaTriDanhMuc.entity.ts'),
    },
    // Repositories
    {
        from: path.join(repositoriesDir, 'loaidanhmuc.repository.ts'),
        to: path.join(repositoriesDir, 'loaiDanhMuc.repository.ts'),
    },
    {
        from: path.join(repositoriesDir, 'giatridanhmuc.repository.ts'),
        to: path.join(repositoriesDir, 'giaTriDanhMuc.repository.ts'),
    },
];

// Thực hiện đổi tên
fileRenames.forEach(({ from, to }) => {
    if (fs.existsSync(from)) {
        try {
            fs.renameSync(from, to);
            console.log(`Đã đổi tên file: ${path.basename(from)} -> ${path.basename(to)}`);
        } catch (error) {
            console.error(`Lỗi khi đổi tên file ${path.basename(from)}: ${error.message}`);
        }
    } else {
        console.log(`File không tồn tại: ${path.basename(from)}`);
    }
});

console.log('Hoàn tất đổi tên file!');
