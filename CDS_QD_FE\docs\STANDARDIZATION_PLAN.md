# Kế hoạch Chuẩn hóa Components và Hooks

## Giới thiệu

Tài liệu này trình bày kế hoạch chuẩn hóa toàn diện cho components, hooks, và utility functions được sử dụng trong dự án, nhằm đảm bảo tính nhất quán và tránh lặp lại code.

## 1. UI Components

### 1.1. DataTable và các thành phần liên quan

| Component | Mô tả | Đường dẫn |
|-----------|-------|-----------|
| `DataTable` | Bảng dữ liệu chung xây dựng trên TanStack Table | `src/components/shared/DataTable/DataTable.tsx` |
| `DataTableToolbar` | Thanh công cụ với tìm kiếm và các nút hành động | `src/components/shared/DataTable/DataTableToolbar.tsx` |
| `DataTableFilter` | Component lọc dữ liệu | `src/components/shared/DataTable/DataTableFilter.tsx` |
| `TablePagination` | Component phân trang thống nhất | `src/components/shared/DataTable/TablePagination.tsx` |
| `ColumnSelectorMenu` | Menu chọn cột hiển thị | `src/components/shared/DataTable/ColumnSelectorMenu.tsx` |
| `RowActions` | Các hành động cho từng dòng trong bảng | `src/components/shared/DataTable/RowActions.tsx` |

#### Best Practices cho DataTable:

1. **Tách biệt data và columns**:
   ```tsx
   const columns = useMemo(() => [...], [dependencies])
   ```

2. **Ổn định references**:
   ```tsx
   // Tạo stable reference cho columns và data
   const columns = useMemo(() => [...], [])
   const [data, setData] = useState(() => [...])
   ```

3. **Tách UI và Data Logic**:
   - Sử dụng hooks riêng biệt để quản lý dữ liệu và trạng thái bảng
   - Component DataTable thuần túy chỉ xử lý render

### 1.2. Filter Components

| Component | Mô tả | Đường dẫn |
|-----------|-------|----------|
| `FilterCard` | Component bao bọc chứa bộ lọc | `src/components/shared/Filter/FilterCard.tsx` |
| `FilterPanel` | Panel chứa nhiều bộ lọc khác nhau | `src/components/shared/Filter/FilterPanel.tsx` |
| `DateRangeFilter` | Bộ lọc theo khoảng thời gian | `src/components/shared/Filter/DateRangeFilter.tsx` |
| `SelectFilter` | Bộ lọc chọn từ danh sách | `src/components/shared/Filter/SelectFilter.tsx` |
| `TextFilter` | Bộ lọc theo văn bản | `src/components/shared/Filter/TextFilter.tsx` |
| `TreeViewFilter` | Bộ lọc dạng cây | `src/components/shared/Filter/TreeViewFilter.tsx` |
| `FilterActions` | Nút hành động cho bộ lọc | `src/components/shared/Filter/FilterActions.tsx` |

#### Best Practices cho Filter:

1. **Quản lý trạng thái bộ lọc**:
   ```tsx
   const [filters, setFilters] = useState(initialFilters)
   
   // Xử lý áp dụng bộ lọc
   const handleApplyFilters = useCallback(() => {
     onApplyFilters(filters)
   }, [filters, onApplyFilters])
   ```

2. **Tái sử dụng các giá trị lọc**:
   ```tsx
   // Hỗ trợ giá trị mặc định và giá trị hiện tại
   const FilterPanel = ({
     defaultValues,
     values,
     onChange,
     onApply,
     onReset
   }) => { ... }
   ```

3. **Sử dụng debounce cho bộ lọc text**:
   ```tsx
   const handleFilterChange = useDebounce((value) => {
     onChange(value)
   }, 300)
   ```

### 1.3. Dialog Components

| Component | Mô tả | Đường dẫn |
|-----------|-------|----------|
| `DialogBase` | Dialog cơ sở với layout chung | `src/components/dialogs/custom/DialogBase.tsx` |
| `DialogForm` | Dialog chứa form nhập liệu | `src/components/dialogs/custom/DialogForm.tsx` |
| `DialogConfirm` | Dialog xác nhận hành động | `src/components/dialogs/custom/DialogConfirm.tsx` |
| `DialogAlert` | Dialog thông báo | `src/components/dialogs/custom/DialogAlert.tsx` |
| `DialogThemSuaItem` | Dialog tổng quát cho thêm/sửa dữ liệu | `src/components/dialogs/custom/DialogThemSuaItem.tsx` |
| `DialogDelete` | Dialog xác nhận xóa | `src/components/dialogs/custom/DialogDelete.tsx` |
| `DialogCloseButton` | Nút đóng dialog | `src/components/dialogs/DialogCloseButton.tsx` |

#### Best Practices cho Dialog:

1. **Quản lý trạng thái mở/đóng**:
   ```tsx
   const [open, setOpen] = useState(false)
   
   const handleOpen = useCallback(() => setOpen(true), [])
   const handleClose = useCallback(() => setOpen(false), [])
   ```

2. **Xử lý form trong dialog**:
   ```tsx
   const DialogForm = ({ open, onClose, onSubmit, initialData, validationSchema, children }) => {
     const form = useForm({
       defaultValues: initialData,
       resolver: zodResolver(validationSchema)
     })
     
     const handleSubmit = form.handleSubmit((data) => {
       onSubmit(data)
       onClose()
     })
     
     return (
       <DialogBase open={open} onClose={onClose} title="Form Dialog">
         <form onSubmit={handleSubmit}>
           {typeof children === 'function' ? children(form) : children}
           <DialogActions>
             <Button onClick={onClose}>Hủy</Button>
             <Button type="submit" variant="contained">Xác nhận</Button>
           </DialogActions>
         </form>
       </DialogBase>
     )
   }
   ```

3. **Tách biệt logic và UI**:
   - Sử dụng hook để quản lý trạng thái dialog
   - Component dialog chỉ xử lý phần render

### 1.4. Document Components

| Component | Mô tả | Đường dẫn |
|-----------|-------|----------|
| `DocumentList` | Danh sách tài liệu đính kèm | `src/components/shared/Document/DocumentList.tsx` |
| `DocumentItem` | Item hiển thị thông tin tài liệu | `src/components/shared/Document/DocumentItem.tsx` |
| `DocumentUploader` | Component tải lên tài liệu | `src/components/shared/Document/DocumentUploader.tsx` |
| `DocumentViewer` | Component xem tài liệu | `src/components/shared/Document/DocumentViewer.tsx` |
| `FileTypeIcon` | Icon hiển thị loại tài liệu | `src/components/shared/Document/FileTypeIcon.tsx` |

#### Best Practices cho Document Components:

1. **Xử lý tải lên nhiều file**:
   ```tsx
   const DocumentUploader = ({ onUpload, maxFiles = 5, maxSize = 5 }) => {
     const handleFilesSelected = (files) => {
       // Kiểm tra kích thước, loại file
       const validFiles = [...files].filter(file => {
         return file.size <= maxSize * 1024 * 1024
       })
       
       onUpload(validFiles)
     }
     
     return (
       <Dropzone
         onDrop={handleFilesSelected}
         maxFiles={maxFiles}
         validator={customValidator}
       >
         {({getRootProps, getInputProps}) => (
           <div {...getRootProps()}>
             <input {...getInputProps()} />
             <p>Kéo thả file hoặc click để chọn</p>
           </div>
         )}
       </Dropzone>
     )
   }
   ```

2. **Render danh sách tài liệu**:
   ```tsx
   const DocumentList = ({ documents, onView, onDelete }) => {
     return (
       <List>
         {documents.map(doc => (
           <DocumentItem
             key={doc.id}
             document={doc}
             onView={() => onView(doc)}
             onDelete={() => onDelete(doc.id)}
           />
         ))}
       </List>
     )
   }
   ```

### 1.5. TreeView Components

| Component | Mô tả | Đường dẫn |
|-----------|-------|----------|
| `CustomTreeView` | Component hiển thị dữ liệu dạng cây | `src/components/shared/TreeView/CustomTreeView.tsx` |
| `TreeItem` | Item trong cây | `src/components/shared/TreeView/TreeItem.tsx` |
| `TreeItemLabel` | Label cho TreeItem | `src/components/shared/TreeView/TreeItemLabel.tsx` |
| `TreeViewContextMenu` | Menu context cho TreeView | `src/components/shared/TreeView/TreeViewContextMenu.tsx` |

#### Best Practices cho TreeView:

1. **Xử lý dữ liệu đệ quy**:
   ```tsx
   const renderTree = (nodes) => (
     nodes.map((node) => (
       <TreeItem
         key={node.id}
         nodeId={node.id}
         label={<TreeItemLabel node={node} />}
       >
         {Array.isArray(node.children) && node.children.length > 0
           ? renderTree(node.children)
           : null}
       </TreeItem>
     ))
   )
   ```

2. **Quản lý trạng thái mở rộng**:
   ```tsx
   const [expanded, setExpanded] = useState([])
   
   const handleToggle = (event, nodeIds) => {
     setExpanded(nodeIds)
   }
   ```

3. **Xử lý chọn node**:
   ```tsx
   const [selected, setSelected] = useState('')
   
   const handleSelect = (event, nodeId) => {
     setSelected(nodeId)
     onNodeSelect(nodeId)
   }
   ```

## 2. Custom Hooks

### 2.1. DataTable Hooks

| Hook | Mô tả | Đường dẫn |
|------|-------|----------|
| `useDataTable` | Hook tổng hợp cho quản lý DataTable | `src/hooks/table/useDataTable.ts` |
| `usePagination` | Quản lý phân trang | `src/hooks/table/usePagination.ts` |
| `useSorting` | Quản lý sắp xếp | `src/hooks/table/useSorting.ts` |
| `useRowSelection` | Quản lý chọn dòng | `src/hooks/table/useRowSelection.ts` |
| `useTableFilter` | Quản lý lọc dữ liệu | `src/hooks/table/useTableFilter.ts` |

#### Best Practices cho DataTable Hooks:

1. **Tách biệt các concern**:
   ```tsx
   // useDataTable.ts - Hook tổng hợp
   export function useDataTable<T>({
     data,
     columns,
     initialState,
     onStateChange
   }: DataTableOptions<T>) {
     // Sử dụng các hooks riêng biệt
     const sorting = useSorting(initialState?.sorting)
     const pagination = usePagination(initialState?.pagination)
     const rowSelection = useRowSelection(initialState?.rowSelection)
     const filter = useTableFilter(initialState?.filter)
     
     // Kết hợp state
     useEffect(() => {
       if (onStateChange) {
         onStateChange({
           sorting: sorting.state,
           pagination: pagination.state,
           rowSelection: rowSelection.state,
           filter: filter.state
         })
       }
     }, [sorting.state, pagination.state, rowSelection.state, filter.state])
     
     // Trả về các hooks và helpers
     return {
       sorting,
       pagination,
       rowSelection,
       filter,
       // Các helpers khác...
     }
   }
   ```

2. **Controlled vs Uncontrolled**:
   ```tsx
   // usePagination.ts
   export function usePagination({
     pageIndex: controlledPageIndex,
     pageSize: controlledPageSize,
     onPageChange,
     onPageSizeChange,
     ...options
   }: PaginationOptions = {}) {
     // Gửi component có thể controlled hoặc uncontrolled
     const [pageIndex, setPageIndex] = useState(controlledPageIndex ?? 0)
     const [pageSize, setPageSize] = useState(controlledPageSize ?? 10)
     
     // Đồng bộ state nếu có controlled values
     useEffect(() => {
       if (controlledPageIndex !== undefined) {
         setPageIndex(controlledPageIndex)
       }
     }, [controlledPageIndex])
     
     useEffect(() => {
       if (controlledPageSize !== undefined) {
         setPageSize(controlledPageSize)
       }
     }, [controlledPageSize])
     
     // Xử lý thay đổi
     const handlePageChange = useCallback((newPage: number) => {
       setPageIndex(newPage)
       if (onPageChange) {
         onPageChange(newPage)
       }
     }, [onPageChange])
     
     // Trả về state và handlers
     return {
       state: { pageIndex, pageSize },
       setPageIndex: handlePageChange,
       setPageSize: /* tương tự */,
       // Các helpers khác...
     }
   }
   ```

### 2.2. Dialog Hooks

| Hook | Mô tả | Đường dẫn |
|------|-------|----------|
| `useDialog` | Quản lý trạng thái dialog đơn giản | `src/hooks/dialog/useDialog.ts` |
| `useDialogWithData` | Quản lý dialog với dữ liệu | `src/hooks/dialog/useDialogWithData.ts` |
| `useConfirmDialog` | Quản lý dialog xác nhận | `src/hooks/dialog/useConfirmDialog.ts` |
| `useFormDialog` | Quản lý dialog chứa form | `src/hooks/dialog/useFormDialog.ts` |

#### Best Practices cho Dialog Hooks:

1. **useDialog cơ bản**:
   ```tsx
   export function useDialog(initialOpen = false) {
     const [open, setOpen] = useState(initialOpen)
     
     const handleOpen = useCallback(() => setOpen(true), [])
     const handleClose = useCallback(() => setOpen(false), [])
     const handleToggle = useCallback(() => setOpen(prev => !prev), [])
     
     return {
       open,
       setOpen,
       handleOpen,
       handleClose,
       handleToggle
     }
   }
   ```

2. **useDialogWithData cho thêm/sửa**:
   ```tsx
   export function useDialogWithData<T>(initialOpen = false) {
     const [open, setOpen] = useState(initialOpen)
     const [data, setData] = useState<T | null>(null)
     
     const handleOpen = useCallback((item?: T) => {
       setData(item ?? null)
       setOpen(true)
     }, [])
     
     const handleClose = useCallback(() => {
       setOpen(false)
       // Xóa dữ liệu sau khi đóng
       setTimeout(() => {
         setData(null)
       }, 300) // Delay để animation hoàn tất
     }, [])
     
     return {
       open,
       data,
       isEditMode: data !== null,
       handleOpen,
       handleClose
     }
   }
   ```

### 2.3. Document Hooks

| Hook | Mô tả | Đường dẫn |
|------|-------|----------|
| `useDocumentList` | Quản lý danh sách tài liệu | `src/hooks/document/useDocumentList.ts` |
| `useFileUpload` | Quản lý tải lên file | `src/hooks/document/useFileUpload.ts` |
| `useDocumentViewer` | Quản lý xem tài liệu | `src/hooks/document/useDocumentViewer.ts` |

#### Best Practices cho Document Hooks:

1. **useFileUpload**:
   ```tsx
   export function useFileUpload({
     maxFiles = 5,
     maxSize = 5, // MB
     acceptedTypes = ['*/*'],
     onUploaded
   }: FileUploadOptions) {
     const [files, setFiles] = useState<File[]>([])
     const [uploading, setUploading] = useState(false)
     const [progress, setProgress] = useState(0)
     const [error, setError] = useState<string | null>(null)
     
     const validateFile = useCallback((file: File) => {
       // Kiểm tra kích thước
       if (file.size > maxSize * 1024 * 1024) {
         return `File ${file.name} vượt quá kích thước cho phép (${maxSize}MB)`
       }
       
       // Kiểm tra loại file
       if (acceptedTypes[0] !== '*/*') {
         const fileType = file.type
         if (!acceptedTypes.some(type => fileType.match(type))) {
           return `File ${file.name} không đúng định dạng`
         }
       }
       
       return null
     }, [maxSize, acceptedTypes])
     
     const handleFilesSelected = useCallback((selectedFiles: File[]) => {
       setError(null)
       
       // Kiểm tra số lượng
       if (selectedFiles.length > maxFiles) {
         setError(`Chỉ được chọn tối đa ${maxFiles} files`)
         return
       }
       
       // Kiểm tra từng file
       const errors: string[] = []
       const validFiles = selectedFiles.filter(file => {
         const error = validateFile(file)
         if (error) {
           errors.push(error)
           return false
         }
         return true
       })
       
       if (errors.length > 0) {
         setError(errors.join('\n'))
       }
       
       setFiles(validFiles)
     }, [maxFiles, validateFile])
     
     const upload = useCallback(async () => {
       if (files.length === 0) return
       
       setUploading(true)
       setProgress(0)
       
       try {
         // Giả lập upload logic
         // Trong thực tế, sử dụng FormData và axios
         
         // Mô phỏng tiến trình
         let currentProgress = 0
         const interval = setInterval(() => {
           currentProgress += 10
           setProgress(currentProgress)
           if (currentProgress >= 100) {
             clearInterval(interval)
             
             // Giả lập kết quả upload thành công
             const uploadedFiles = files.map(file => ({
               id: Math.random().toString(36).substr(2, 9),
               name: file.name,
               size: file.size,
               type: file.type,
               url: URL.createObjectURL(file)
             }))
             
             if (onUploaded) {
               onUploaded(uploadedFiles)
             }
             
             setUploading(false)
             setFiles([])
           }
         }, 300)
       } catch (err) {
         setError('Lỗi khi tải lên files')
         setUploading(false)
       }
     }, [files, onUploaded])
     
     return {
       files,
       uploading,
       progress,
       error,
       handleFilesSelected,
       upload,
       clearFiles: () => setFiles([]),
       clearError: () => setError(null)
     }
   }
   ```

### 2.4. TreeView Hooks

| Hook | Mô tả | Đường dẫn |
|------|-------|----------|
| `useTreeView` | Quản lý trạng thái và logic của TreeView | `src/hooks/treeview/useTreeView.ts` |
| `useTreeNode` | Quản lý trạng thái và logic của node trong cây | `src/hooks/treeview/useTreeNode.ts` |

#### Best Practices cho TreeView Hooks:

1. **useTreeView cơ bản**:
   ```tsx
   export function useTreeView<T>({
     data,
     getNodeId,
     getNodeChildren,
     initialExpanded = [],
     initialSelected = null
   }: TreeViewOptions<T>) {
     const [expanded, setExpanded] = useState<string[]>(initialExpanded)
     const [selected, setSelected] = useState<string | null>(initialSelected)
     
     // Expand/collapse functions
     const handleToggle = useCallback((nodeId: string) => {
       setExpanded(prev => {
         const isExpanded = prev.includes(nodeId)
         return isExpanded
           ? prev.filter(id => id !== nodeId)
           : [...prev, nodeId]
       })
     }, [])
     
     const handleExpandAll = useCallback(() => {
       const getAllNodeIds = (nodes: T[]): string[] => {
         let ids: string[] = []
         nodes.forEach(node => {
           const nodeId = getNodeId(node)
           ids.push(nodeId)
           
           const children = getNodeChildren(node)
           if (children && children.length > 0) {
             ids = [...ids, ...getAllNodeIds(children)]
           }
         })
         return ids
       }
       
       setExpanded(getAllNodeIds(data))
     }, [data, getNodeId, getNodeChildren])
     
     const handleCollapseAll = useCallback(() => {
       setExpanded([])
     }, [])
     
     // Selection functions
     const handleSelect = useCallback((nodeId: string) => {
       setSelected(prev => prev === nodeId ? null : nodeId)
     }, [])
     
     const getSelectedNode = useCallback(() => {
       if (!selected) return null
       
       const findNode = (nodes: T[]): T | null => {
         for (const node of nodes) {
           if (getNodeId(node) === selected) {
             return node
           }
           
           const children = getNodeChildren(node)
           if (children && children.length > 0) {
             const foundInChildren = findNode(children)
             if (foundInChildren) return foundInChildren
           }
         }
         
         return null
       }
       
       return findNode(data)
     }, [data, selected, getNodeId, getNodeChildren])
     
     return {
       // State
       expanded,
       selected,
       
       // Actions
       handleToggle,
       handleExpandAll,
       handleCollapseAll,
       handleSelect,
       
       // Helpers
       isExpanded: (nodeId: string) => expanded.includes(nodeId),
       isSelected: (nodeId: string) => selected === nodeId,
       getSelectedNode
     }
   }
   ```

## 3. Utility Functions

### 3.1. Format Utilities

| Function | Mô tả | Đường dẫn |
|----------|-------|----------|
| `formatDate` | Định dạng ngày tháng | `src/utils/format/formatDate.ts` |
| `formatCurrency` | Định dạng tiền tệ | `src/utils/format/formatCurrency.ts` |
| `formatNumber` | Định dạng số | `src/utils/format/formatNumber.ts` |
| `formatFileSize` | Định dạng kích thước tệp | `src/utils/format/formatFileSize.ts` |

#### Best Practices cho Format Utilities:

1. **formatDate**:
   ```tsx
   export function formatDate(
     date: Date | string | number | null | undefined,
     format: string = 'dd/MM/yyyy',
     locale: string = 'vi-VN'
   ): string {
     if (!date) return ''
     
     try {
       const dateObj = typeof date === 'object' ? date : new Date(date)
       
       // Kiểm tra date hợp lệ
       if (isNaN(dateObj.getTime())) {
         return ''
       }
       
       // Sử dụng date-fns hoặc Intl.DateTimeFormat
       return new Intl.DateTimeFormat(locale, {
         year: 'numeric',
         month: '2-digit',
         day: '2-digit'
       }).format(dateObj)
     } catch (error) {
       console.error('Error formatting date:', error)
       return ''
     }
   }
   ```

2. **formatCurrency**:
   ```tsx
   export function formatCurrency(
     value: number | string | null | undefined,
     locale: string = 'vi-VN',
     currency: string = 'VND'
   ): string {
     if (value === null || value === undefined || value === '') return ''
     
     try {
       const numValue = typeof value === 'string' ? parseFloat(value) : value
       
       // Kiểm tra số hợp lệ
       if (isNaN(numValue)) {
         return ''
       }
       
       return new Intl.NumberFormat(locale, {
         style: 'currency',
         currency,
         minimumFractionDigits: 0,
         maximumFractionDigits: 2
       }).format(numValue)
     } catch (error) {
       console.error('Error formatting currency:', error)
       return ''
     }
   }
   ```

3. **formatNumber**:
   ```tsx
   export function formatNumber(
     value: number | string | null | undefined,
     options: {
       locale?: string,
       minimumFractionDigits?: number,
       maximumFractionDigits?: number
     } = {}
   ): string {
     if (value === null || value === undefined || value === '') return ''
     
     try {
       const numValue = typeof value === 'string' ? parseFloat(value) : value
       
       // Kiểm tra số hợp lệ
       if (isNaN(numValue)) {
         return ''
       }
       
       const {
         locale = 'vi-VN',
         minimumFractionDigits = 0,
         maximumFractionDigits = 2
       } = options
       
       return new Intl.NumberFormat(locale, {
         minimumFractionDigits,
         maximumFractionDigits
       }).format(numValue)
     } catch (error) {
       console.error('Error formatting number:', error)
       return ''
     }
   }
   ```

4. **formatFileSize**:
   ```tsx
   export function formatFileSize(
     bytes: number | null | undefined,
     decimals: number = 2
   ): string {
     if (bytes === null || bytes === undefined || bytes === 0) return '0 Bytes'
     
     const k = 1024
     const dm = decimals < 0 ? 0 : decimals
     const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
     
     const i = Math.floor(Math.log(bytes) / Math.log(k))
     
     return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]
   }
   ```

### 3.2. File Utilities

| Function | Mô tả | Đường dẫn |
|----------|-------|----------|
| `getFileExtension` | Lấy phần mở rộng của tệp | `src/utils/file/getFileExtension.ts` |
| `getFileIcon` | Lấy icon cho loại tệp | `src/utils/file/getFileIcon.ts` |
| `downloadFile` | Tải xuống tệp | `src/utils/file/downloadFile.ts` |
| `readFile` | Đọc tệp | `src/utils/file/readFile.ts` |

#### Best Practices cho File Utilities:

1. **getFileExtension**:
   ```tsx
   export function getFileExtension(filename: string): string {
     if (!filename) return ''
     
     // Tránh lỗi với dấu chấm trong tên file
     const parts = filename.split('.')
     
     if (parts.length === 1) return ''
     
     return parts[parts.length - 1].toLowerCase()
   }
   ```

2. **getFileIcon**:
   ```tsx
   import {
     Description as TextIcon,
     PictureAsPdf as PdfIcon,
     Image as ImageIcon,
     VideoLibrary as VideoIcon,
     AudioFile as AudioIcon,
     Archive as ArchiveIcon,
     Code as CodeIcon,
     InsertDriveFile as DefaultIcon
   } from '@mui/icons-material'
   
   export function getFileIcon(filename: string): React.ElementType {
     const ext = getFileExtension(filename)
     
     switch (ext) {
       // Text files
       case 'txt':
       case 'doc':
       case 'docx':
       case 'rtf':
       case 'odt':
         return TextIcon
       
       // PDF
       case 'pdf':
         return PdfIcon
       
       // Images
       case 'jpg':
       case 'jpeg':
       case 'png':
       case 'gif':
       case 'bmp':
       case 'svg':
       case 'webp':
         return ImageIcon
       
       // Videos
       case 'mp4':
       case 'avi':
       case 'mov':
       case 'wmv':
       case 'mkv':
       case 'webm':
         return VideoIcon
       
       // Audio
       case 'mp3':
       case 'wav':
       case 'ogg':
       case 'm4a':
       case 'flac':
         return AudioIcon
       
       // Archives
       case 'zip':
       case 'rar':
       case '7z':
       case 'tar':
       case 'gz':
         return ArchiveIcon
       
       // Code
       case 'js':
       case 'jsx':
       case 'ts':
       case 'tsx':
       case 'html':
       case 'css':
       case 'json':
       case 'xml':
       case 'py':
       case 'java':
       case 'php':
         return CodeIcon
       
       default:
         return DefaultIcon
     }
   }
   ```

3. **downloadFile**:
   ```tsx
   export function downloadFile(url: string, filename?: string): void {
     try {
       const link = document.createElement('a')
       link.href = url
       
       // Xử lý tên file
       if (filename) {
         link.download = filename
       } else {
         // Lấy tên file từ URL nếu không có tên
         const urlParts = url.split('/')
         link.download = urlParts[urlParts.length - 1].split('?')[0]
       }
       
       // Thêm vào DOM và click
       document.body.appendChild(link)
       link.click()
       
       // Xóa element
       document.body.removeChild(link)
     } catch (error) {
       console.error('Error downloading file:', error)
     }
   }
   ```

### 3.3. Array Utilities

| Function | Mô tả | Đường dẫn |
|----------|-------|----------|
| `sortArray` | Sắp xếp mảng | `src/utils/array/sortArray.ts` |
| `filterArray` | Lọc mảng | `src/utils/array/filterArray.ts` |
| `groupArray` | Nhóm mảng | `src/utils/array/groupArray.ts` |
| `paginateArray` | Phân trang mảng | `src/utils/array/paginateArray.ts` |

#### Best Practices cho Array Utilities:

1. **sortArray**:
   ```tsx
   export function sortArray<T>({
     data,
     key,
     direction = 'asc',
     primer = (v: any) => v
   }: {
     data: T[],
     key: keyof T,
     direction?: 'asc' | 'desc',
     primer?: (value: any) => any
   }): T[] {
     if (!data || !Array.isArray(data) || data.length === 0) return []
     
     const sortedData = [...data].sort((a, b) => {
       const valA = primer(a[key])
       const valB = primer(b[key])
       
       // Xử lý giá trị null/undefined
       if (valA === null || valA === undefined) return direction === 'asc' ? -1 : 1
       if (valB === null || valB === undefined) return direction === 'asc' ? 1 : -1
       
       // So sánh chuỗi không phân biệt chữ hoa/thường
       if (typeof valA === 'string' && typeof valB === 'string') {
         return direction === 'asc'
           ? valA.localeCompare(valB, 'vi')
           : valB.localeCompare(valA, 'vi')
       }
       
       // So sánh các loại khác
       return direction === 'asc' ? (valA > valB ? 1 : -1) : (valA > valB ? -1 : 1)
     })
     
     return sortedData
   }
   ```

2. **filterArray**:
   ```tsx
   export function filterArray<T>({
     data,
     filters,
     caseSensitive = false
   }: {
     data: T[],
     filters: { key: keyof T, value: any }[],
     caseSensitive?: boolean
   }): T[] {
     if (!data || !Array.isArray(data) || data.length === 0) return []
     if (!filters || !Array.isArray(filters) || filters.length === 0) return data
     
     return data.filter(item => {
       return filters.every(filter => {
         const itemValue = item[filter.key]
         const filterValue = filter.value
         
         // Nếu itemValue hoặc filterValue là null/undefined, không phù hợp
         if (itemValue === null || itemValue === undefined || filterValue === null || filterValue === undefined) {
           return false
         }
         
         // Kiểm tra chuỗi
         if (typeof itemValue === 'string' && typeof filterValue === 'string') {
           return caseSensitive
             ? itemValue.includes(filterValue)
             : itemValue.toLowerCase().includes(filterValue.toLowerCase())
         }
         
         // Kiểm tra số
         if (typeof itemValue === 'number' && typeof filterValue === 'number') {
           return itemValue === filterValue
         }
         
         // Kiểm tra boolean
         if (typeof itemValue === 'boolean' && typeof filterValue === 'boolean') {
           return itemValue === filterValue
         }
         
         // Kiểm tra ngày tháng
         if (itemValue instanceof Date && filterValue instanceof Date) {
           return itemValue.getTime() === filterValue.getTime()
         }
         
         return false
       })
     })
   }
   ```

3. **groupArray**:
   ```tsx
   export function groupArray<T>({
     data,
     key
   }: {
     data: T[],
     key: keyof T
   }): Record<string, T[]> {
     if (!data || !Array.isArray(data) || data.length === 0) return {}
     
     return data.reduce((acc, item) => {
       const groupValue = String(item[key] || 'undefined')
       
       if (!acc[groupValue]) {
         acc[groupValue] = []
       }
       
       acc[groupValue].push(item)
       
       return acc
     }, {} as Record<string, T[]>)
   }
   ```

4. **paginateArray**:
   ```tsx
   export function paginateArray<T>({
     data,
     pageIndex = 0,
     pageSize = 10
   }: {
     data: T[],
     pageIndex?: number,
     pageSize?: number
   }): {
     paginatedData: T[],
     totalPages: number,
     totalItems: number
   } {
     if (!data || !Array.isArray(data)) {
       return { paginatedData: [], totalPages: 0, totalItems: 0 }
     }
     
     const totalItems = data.length
     const totalPages = Math.ceil(totalItems / pageSize)
     
     // Đảm bảo pageIndex hợp lệ
     const safePageIndex = Math.max(0, Math.min(pageIndex, totalPages - 1))
     
     // Nếu không có dữ liệu hoặc trang không hợp lệ
     if (totalItems === 0 || totalPages === 0) {
       return { paginatedData: [], totalPages: 0, totalItems }
     }
     
     const startIndex = safePageIndex * pageSize
     const endIndex = startIndex + pageSize
     
     const paginatedData = data.slice(startIndex, endIndex)
     
     return {
       paginatedData,
       totalPages,
       totalItems
     }
   }
   ```

## 4. Kế hoạch triển khai

### 4.1. Thứ tự triển khai

1. **Phần cơ sở** (Tuần 1-2)
   - DataTable và các thành phần liên quan
   - Filter Components
   - Utility Functions (Format, File, Array)

2. **Phần chức năng** (Tuần 3-4)
   - Dialog Components
   - Document Components
   - TreeView Components

3. **Phần hooks** (Tuần 5-6)
   - DataTable Hooks
   - Dialog Hooks
   - Document Hooks
   - TreeView Hooks

### 4.2. Cấu trúc thư mục đề xuất

```
src/
├── components/
│   ├── shared/
│   │   ├── DataTable/           # DataTable và các thành phần liên quan
│   │   ├── Document/            # Document components
│   │   ├── Filter/              # Filter components
│   │   ├── TreeView/            # TreeView components
│   │   └── Status/              # Status components
│   └── dialogs/
│       ├── index.ts
│       ├── DialogCloseButton.tsx
│       └── custom/              # Custom dialogs
├── hooks/
│   ├── table/               # Hooks cho DataTable
│   ├── dialog/              # Hooks cho Dialog
│   ├── document/            # Hooks cho Document
│   └── treeview/            # Hooks cho TreeView
└── utils/
    ├── format/              # Định dạng dữ liệu
    ├── file/                # Xử lý file
    └── array/               # Xử lý mảng
```

## Functional Components

### Filter Components

| Component         | Mô tả                               | Đường dẫn                                          |
| ----------------- | ----------------------------------- | -------------------------------------------------- |
| `FilterCard`      | Component lọc dữ liệu chung         | `src/components/shared/Filter/FilterCard.tsx`      |
| `DateRangeFilter` | Component lọc theo khoảng thời gian | `src/components/shared/Filter/DateRangeFilter.tsx` |
| `SelectFilter`    | Component lọc theo select           | `src/components/shared/Filter/SelectFilter.tsx`    |
| `SearchFilter`    | Component tìm kiếm                  | `src/components/shared/Filter/SearchFilter.tsx`    |

### Layout Components

| Component        | Mô tả                                      | Đường dẫn                                         |
| `useDialogWithData` | Quản lý dialog với dữ liệu (thêm/sửa)   | `src/hooks/shared/useDialogWithData.ts` |
| `useConfirmDialog`  | Quản lý dialog xác nhận                 | `src/hooks/shared/useConfirmDialog.ts`  |

### Form Hooks

| Hook                | Mô tả                                     | Đường dẫn                               |
| ------------------- | ----------------------------------------- | --------------------------------------- |
| `useForm`           | Quản lý trạng thái và validation của form | `src/hooks/shared/useForm.ts`           |
| `useFormWithSubmit` | Quản lý form với xử lý submit             | `src/hooks/shared/useFormWithSubmit.ts` |
| `useFileUpload`     | Quản lý tải lên tệp                       | `src/hooks/shared/useFileUpload.ts`     |

### Document Hooks

| Hook                | Mô tả                      | Đường dẫn                               |
| ------------------- | -------------------------- | --------------------------------------- |
| `useDocumentList`   | Quản lý danh sách tài liệu | `src/hooks/shared/useDocumentList.ts`   |
| `useFileUpload`     | Quản lý tải lên tệp        | `src/hooks/shared/useFileUpload.ts`     |
| `useDocumentViewer` | Quản lý xem tài liệu       | `src/hooks/shared/useDocumentViewer.ts` |

## Utility Functions

### Format Utilities

| Function         | Mô tả                    | Đường dẫn                            |
| ---------------- | ------------------------ | ------------------------------------ |
| `formatDate`     | Định dạng ngày tháng     | `src/utils/format/formatDate.ts`     |
| `formatCurrency` | Định dạng tiền tệ        | `src/utils/format/formatCurrency.ts` |
| `formatNumber`   | Định dạng số             | `src/utils/format/formatNumber.ts`   |
| `formatFileSize` | Định dạng kích thước tệp | `src/utils/format/formatFileSize.ts` |

### Validation Utilities

| Function           | Mô tả                    | Đường dẫn                                  |
| ------------------ | ------------------------ | ------------------------------------------ |
| `validateRequired` | Kiểm tra trường bắt buộc | `src/utils/validation/validateRequired.ts` |
| `validateEmail`    | Kiểm tra email           | `src/utils/validation/validateEmail.ts`    |
| `validatePhone`    | Kiểm tra số điện thoại   | `src/utils/validation/validatePhone.ts`    |
| `validateDate`     | Kiểm tra ngày tháng      | `src/utils/validation/validateDate.ts`     |
| `validateFileType` | Kiểm tra loại tệp        | `src/utils/validation/validateFileType.ts` |
| `validateFileSize` | Kiểm tra kích thước tệp  | `src/utils/validation/validateFileSize.ts` |

### File Utilities

| Function           | Mô tả                    | Đường dẫn                            |
| ------------------ | ------------------------ | ------------------------------------ |
| `getFileExtension` | Lấy phần mở rộng của tệp | `src/utils/file/getFileExtension.ts` |
| `getFileIcon`      | Lấy icon cho loại tệp    | `src/utils/file/getFileIcon.ts`      |
| `downloadFile`     | Tải xuống tệp            | `src/utils/file/downloadFile.ts`     |
| `readFile`         | Đọc tệp                  | `src/utils/file/readFile.ts`         |

### Array Utilities

| Function        | Mô tả           | Đường dẫn                          |
| --------------- | --------------- | ---------------------------------- |
| `sortArray`     | Sắp xếp mảng    | `src/utils/array/sortArray.ts`     |
| `filterArray`   | Lọc mảng        | `src/utils/array/filterArray.ts`   |
| `groupArray`    | Nhóm mảng       | `src/utils/array/groupArray.ts`    |
| `paginateArray` | Phân trang mảng | `src/utils/array/paginateArray.ts` |

## Cấu trúc thư mục đề xuất

```
src/
├── components/
│   ├── shared/
│   │   ├── DataTable/           # DataTable và các thành phần liên quan
│   │   ├── Document/            # Document components
│   │   ├── Form/                # Form components
│   │   ├── Card/                # Card components
│   │   ├── Filter/              # Filter components
│   │   ├── Status/              # Status components
│   │   ├── Action/              # Action components
│   │   ├── Tab/                 # Tab components
│   │   └── Layout/              # Layout components
│   └── dialogs/
│       ├── index.ts
│       ├── DialogCloseButton.tsx
│       └── custom/              # Custom dialogs
├── hooks/
│   └── shared/                  # Shared hooks
├── utils/
│   ├── format/                  # Format utilities
│   ├── validation/              # Validation utilities
│   ├── file/                    # File utilities
│   └── array/                   # Array utilities
└── types/                       # Type definitions
```

### TreeView Hooks

| Hook          | Mô tả                                          | Đường dẫn                         |
| ------------- | ---------------------------------------------- | --------------------------------- |
| `useTreeView` | Quản lý trạng thái và logic của TreeView       | `src/hooks/shared/useTreeView.ts` |
| `useTreeNode` | Quản lý trạng thái và logic của node trong cây | `src/hooks/shared/useTreeNode.ts` |

### Autocomplete Hooks

| Hook                   | Mô tả                                        | Đường dẫn                                  |
| ---------------------- | -------------------------------------------- | ------------------------------------------ |
| `useAutocomplete`      | Quản lý trạng thái và logic của Autocomplete | `src/hooks/shared/useAutocomplete.ts`      |
| `useAsyncAutocomplete` | Quản lý Autocomplete với dữ liệu bất đồng bộ | `src/hooks/shared/useAsyncAutocomplete.ts` |

## Ưu tiên triển khai

1. DataTable và TablePagination (ưu tiên cao)
2. Dialog components (ưu tiên cao)
3. Document components (ưu tiên cao)
4. TreeView components (ưu tiên cao)
5. Autocomplete components (ưu tiên cao)
6. Form components (ưu tiên trung bình)
7. Custom hooks (ưu tiên trung bình)
8. Card và các components khác (ưu tiên thấp)
9. Utility functions (ưu tiên thấp)
