/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { QuaTrinhDaoTaoEntity } from '~/database/typeorm/entities/quaTrinhDaoTao.entity';

@Injectable()
export class QuaTrinhDaoTaoRepository extends Repository<QuaTrinhDaoTaoEntity> {
    constructor(private dataSource: DataSource) {
        super(QuaTrinhDaoTaoEntity, dataSource.createEntityManager());
    }
}
