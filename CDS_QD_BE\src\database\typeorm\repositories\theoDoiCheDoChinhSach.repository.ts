/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { TheoDoiCheDoChinhSachEntity } from '~/database/typeorm/entities/theoDoiCheDoChinhSach.entity';

@Injectable()
export class TheoDoiCheDoChinhSachRepository extends Repository<TheoDoiCheDoChinhSachEntity> {
    constructor(private dataSource: DataSource) {
        super(TheoDoiCheDoChinhSachEntity, dataSource.createEntityManager());
    }
}
