version: '3.3'
services:
    redis:
        image: 'bitnami/redis:latest'
        container_name: redis
        environment:
            - REDIS_PASSWORD=${REDIS_PASSWORD}
            - ENABLE_OVERCOMMIT_MEMORY=true
            - REDIS_AOF_ENABLED=no
        networks:
            - myNetwork
        ports:
            - '6379:6379'
        volumes:
            - redis_data:/bitnami/redis/data
        restart: always

    db:
        image: mysql:8.0
        container_name: db
        restart: always
        environment:
            MYSQL_ROOT_PASSWORD: ${DATABASE_PASSWORD}
            MYSQL_DATABASE: ${DATABASE_DB_NAME}
        ports:
            - '3306:3306'
        volumes:
            - db_data:/var/lib/mysql
        networks:
            - myNetwork

    project:
        build:
            context: .
            dockerfile: Dockerfile
        image: ${APP_NAME}
        container_name: ${APP_NAME}
        networks:
            - myNetwork
        ports:
            - '8080:8080'
        volumes:
            - './public:/home/<USER>/public'
        restart: always
        depends_on:
            - redis
            - db
        environment:
            - DATABASE_HOST=db
            - REDIS_HOST=redis
            - REDIS_PORT=6379
            - REDIS_PASSWORD=${REDIS_PASSWORD}
            - REDIS_PREFIX=${REDIS_PREFIX}

networks:
    myNetwork:
        driver: bridge

volumes:
    redis_data:
    db_data:
