/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 *  Swagger
 * The  API documents
 * OpenAPI spec version: 1.0
 */
import { useInfiniteQuery, useMutation, useQuery } from '@tanstack/react-query'
import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseInfiniteQueryResult,
  DefinedUseQueryResult,
  InfiniteData,
  MutationFunction,
  QueryClient,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseInfiniteQueryOptions,
  UseInfiniteQueryResult,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult
} from '@tanstack/react-query'

import type { CreateUserDto, UpdateUserDto, UserControllerFindAllParams } from '.././model'

import { customInstance } from '../../mutator/custom-instance'
import type { ApiResponse } from '../../types'

export const userControllerCreate = (createUserDto: CreateUserDto, signal?: AbortSignal) => {
  return customInstance<ApiResponse<void>>({
    url: `/user`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: createUserDto,
    signal
  })
}

export const getUserControllerCreateMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof userControllerCreate>>,
    TError,
    { data: CreateUserDto },
    TContext
  >
}): UseMutationOptions<Awaited<ReturnType<typeof userControllerCreate>>, TError, { data: CreateUserDto }, TContext> => {
  const mutationKey = ['userControllerCreate']
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } }

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof userControllerCreate>>,
    { data: CreateUserDto }
  > = props => {
    const { data } = props ?? {}

    return userControllerCreate(data)
  }

  return { mutationFn, ...mutationOptions }
}

export type UserControllerCreateMutationResult = NonNullable<Awaited<ReturnType<typeof userControllerCreate>>>
export type UserControllerCreateMutationBody = CreateUserDto
export type UserControllerCreateMutationError = unknown

export const useUserControllerCreate = <TError = unknown, TContext = unknown>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof userControllerCreate>>,
      TError,
      { data: CreateUserDto },
      TContext
    >
  },
  queryClient?: QueryClient
): UseMutationResult<Awaited<ReturnType<typeof userControllerCreate>>, TError, { data: CreateUserDto }, TContext> => {
  const mutationOptions = getUserControllerCreateMutationOptions(options)

  return useMutation(mutationOptions, queryClient)
}
export const userControllerFindAll = (params?: UserControllerFindAllParams, signal?: AbortSignal) => {
  return customInstance<ApiResponse<void>>({ url: `/user`, method: 'GET', params, signal })
}

export const getUserControllerFindAllQueryKey = (params?: UserControllerFindAllParams) => {
  return [`/user`, ...(params ? [params] : [])] as const
}

export const getUserControllerFindAllInfiniteQueryOptions = <
  TData = InfiniteData<Awaited<ReturnType<typeof userControllerFindAll>>, UserControllerFindAllParams['page']>,
  TError = unknown
>(
  params?: UserControllerFindAllParams,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof userControllerFindAll>>,
        TError,
        TData,
        Awaited<ReturnType<typeof userControllerFindAll>>,
        QueryKey,
        UserControllerFindAllParams['page']
      >
    >
  }
) => {
  const { query: queryOptions } = options ?? {}

  const queryKey = queryOptions?.queryKey ?? getUserControllerFindAllQueryKey(params)

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof userControllerFindAll>>,
    QueryKey,
    UserControllerFindAllParams['page']
  > = ({ signal, pageParam }) => userControllerFindAll({ ...params, page: pageParam || params?.['page'] }, signal)

  return { queryKey, queryFn, staleTime: 10000, ...queryOptions } as UseInfiniteQueryOptions<
    Awaited<ReturnType<typeof userControllerFindAll>>,
    TError,
    TData,
    Awaited<ReturnType<typeof userControllerFindAll>>,
    QueryKey,
    UserControllerFindAllParams['page']
  > & { queryKey: DataTag<QueryKey, TData, TError> }
}

export type UserControllerFindAllInfiniteQueryResult = NonNullable<Awaited<ReturnType<typeof userControllerFindAll>>>
export type UserControllerFindAllInfiniteQueryError = unknown

export function useUserControllerFindAllInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof userControllerFindAll>>, UserControllerFindAllParams['page']>,
  TError = unknown
>(
  params: undefined | UserControllerFindAllParams,
  options: {
    query: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof userControllerFindAll>>,
        TError,
        TData,
        Awaited<ReturnType<typeof userControllerFindAll>>,
        QueryKey,
        UserControllerFindAllParams['page']
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof userControllerFindAll>>,
          TError,
          Awaited<ReturnType<typeof userControllerFindAll>>,
          QueryKey
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): DefinedUseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useUserControllerFindAllInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof userControllerFindAll>>, UserControllerFindAllParams['page']>,
  TError = unknown
>(
  params?: UserControllerFindAllParams,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof userControllerFindAll>>,
        TError,
        TData,
        Awaited<ReturnType<typeof userControllerFindAll>>,
        QueryKey,
        UserControllerFindAllParams['page']
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof userControllerFindAll>>,
          TError,
          Awaited<ReturnType<typeof userControllerFindAll>>,
          QueryKey
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useUserControllerFindAllInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof userControllerFindAll>>, UserControllerFindAllParams['page']>,
  TError = unknown
>(
  params?: UserControllerFindAllParams,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof userControllerFindAll>>,
        TError,
        TData,
        Awaited<ReturnType<typeof userControllerFindAll>>,
        QueryKey,
        UserControllerFindAllParams['page']
      >
    >
  },
  queryClient?: QueryClient
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }

export function useUserControllerFindAllInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof userControllerFindAll>>, UserControllerFindAllParams['page']>,
  TError = unknown
>(
  params?: UserControllerFindAllParams,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof userControllerFindAll>>,
        TError,
        TData,
        Awaited<ReturnType<typeof userControllerFindAll>>,
        QueryKey,
        UserControllerFindAllParams['page']
      >
    >
  },
  queryClient?: QueryClient
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
  const queryOptions = getUserControllerFindAllInfiniteQueryOptions(params, options)

  const query = useInfiniteQuery(queryOptions, queryClient) as UseInfiniteQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>
  }

  query.queryKey = queryOptions.queryKey

  return query
}

export const getUserControllerFindAllQueryOptions = <
  TData = Awaited<ReturnType<typeof userControllerFindAll>>,
  TError = unknown
>(
  params?: UserControllerFindAllParams,
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof userControllerFindAll>>, TError, TData>> }
) => {
  const { query: queryOptions } = options ?? {}

  const queryKey = queryOptions?.queryKey ?? getUserControllerFindAllQueryKey(params)

  const queryFn: QueryFunction<Awaited<ReturnType<typeof userControllerFindAll>>> = ({ signal }) =>
    userControllerFindAll(params, signal)

  return { queryKey, queryFn, staleTime: 10000, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof userControllerFindAll>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> }
}

export type UserControllerFindAllQueryResult = NonNullable<Awaited<ReturnType<typeof userControllerFindAll>>>
export type UserControllerFindAllQueryError = unknown

export function useUserControllerFindAll<TData = Awaited<ReturnType<typeof userControllerFindAll>>, TError = unknown>(
  params: undefined | UserControllerFindAllParams,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof userControllerFindAll>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof userControllerFindAll>>,
          TError,
          Awaited<ReturnType<typeof userControllerFindAll>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useUserControllerFindAll<TData = Awaited<ReturnType<typeof userControllerFindAll>>, TError = unknown>(
  params?: UserControllerFindAllParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof userControllerFindAll>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof userControllerFindAll>>,
          TError,
          Awaited<ReturnType<typeof userControllerFindAll>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useUserControllerFindAll<TData = Awaited<ReturnType<typeof userControllerFindAll>>, TError = unknown>(
  params?: UserControllerFindAllParams,
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof userControllerFindAll>>, TError, TData>> },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }

export function useUserControllerFindAll<TData = Awaited<ReturnType<typeof userControllerFindAll>>, TError = unknown>(
  params?: UserControllerFindAllParams,
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof userControllerFindAll>>, TError, TData>> },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
  const queryOptions = getUserControllerFindAllQueryOptions(params, options)

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>
  }

  query.queryKey = queryOptions.queryKey

  return query
}

export const userControllerFindOne = (id: string, signal?: AbortSignal) => {
  return customInstance<ApiResponse<ApiResponse<void>>>({ url: `/user/${id}`, method: 'GET', signal })
}

export const getUserControllerFindOneQueryKey = (id: string) => {
  return [`/user/${id}`] as const
}

export const getUserControllerFindOneInfiniteQueryOptions = <
  TData = InfiniteData<Awaited<ReturnType<typeof userControllerFindOne>>>,
  TError = unknown
>(
  id: string,
  options?: {
    query?: Partial<UseInfiniteQueryOptions<Awaited<ReturnType<typeof userControllerFindOne>>, TError, TData>>
  }
) => {
  const { query: queryOptions } = options ?? {}

  const queryKey = queryOptions?.queryKey ?? getUserControllerFindOneQueryKey(id)

  const queryFn: QueryFunction<Awaited<ReturnType<typeof userControllerFindOne>>> = ({ signal }) =>
    userControllerFindOne(id, signal)

  return { queryKey, queryFn, enabled: !!id, staleTime: 10000, ...queryOptions } as UseInfiniteQueryOptions<
    Awaited<ReturnType<typeof userControllerFindOne>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> }
}

export type UserControllerFindOneInfiniteQueryResult = NonNullable<Awaited<ReturnType<typeof userControllerFindOne>>>
export type UserControllerFindOneInfiniteQueryError = unknown

export function useUserControllerFindOneInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof userControllerFindOne>>>,
  TError = unknown
>(
  id: string,
  options: {
    query: Partial<UseInfiniteQueryOptions<Awaited<ReturnType<typeof userControllerFindOne>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof userControllerFindOne>>,
          TError,
          Awaited<ReturnType<typeof userControllerFindOne>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): DefinedUseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useUserControllerFindOneInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof userControllerFindOne>>>,
  TError = unknown
>(
  id: string,
  options?: {
    query?: Partial<UseInfiniteQueryOptions<Awaited<ReturnType<typeof userControllerFindOne>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof userControllerFindOne>>,
          TError,
          Awaited<ReturnType<typeof userControllerFindOne>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useUserControllerFindOneInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof userControllerFindOne>>>,
  TError = unknown
>(
  id: string,
  options?: {
    query?: Partial<UseInfiniteQueryOptions<Awaited<ReturnType<typeof userControllerFindOne>>, TError, TData>>
  },
  queryClient?: QueryClient
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }

export function useUserControllerFindOneInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof userControllerFindOne>>>,
  TError = unknown
>(
  id: string,
  options?: {
    query?: Partial<UseInfiniteQueryOptions<Awaited<ReturnType<typeof userControllerFindOne>>, TError, TData>>
  },
  queryClient?: QueryClient
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
  const queryOptions = getUserControllerFindOneInfiniteQueryOptions(id, options)

  const query = useInfiniteQuery(queryOptions, queryClient) as UseInfiniteQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>
  }

  query.queryKey = queryOptions.queryKey

  return query
}

export const getUserControllerFindOneQueryOptions = <
  TData = Awaited<ReturnType<typeof userControllerFindOne>>,
  TError = unknown
>(
  id: string,
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof userControllerFindOne>>, TError, TData>> }
) => {
  const { query: queryOptions } = options ?? {}

  const queryKey = queryOptions?.queryKey ?? getUserControllerFindOneQueryKey(id)

  const queryFn: QueryFunction<Awaited<ReturnType<typeof userControllerFindOne>>> = ({ signal }) =>
    userControllerFindOne(id, signal)

  return { queryKey, queryFn, enabled: !!id, staleTime: 10000, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof userControllerFindOne>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> }
}

export type UserControllerFindOneQueryResult = NonNullable<Awaited<ReturnType<typeof userControllerFindOne>>>
export type UserControllerFindOneQueryError = unknown

export function useUserControllerFindOne<TData = Awaited<ReturnType<typeof userControllerFindOne>>, TError = unknown>(
  id: string,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof userControllerFindOne>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof userControllerFindOne>>,
          TError,
          Awaited<ReturnType<typeof userControllerFindOne>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useUserControllerFindOne<TData = Awaited<ReturnType<typeof userControllerFindOne>>, TError = unknown>(
  id: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof userControllerFindOne>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof userControllerFindOne>>,
          TError,
          Awaited<ReturnType<typeof userControllerFindOne>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useUserControllerFindOne<TData = Awaited<ReturnType<typeof userControllerFindOne>>, TError = unknown>(
  id: string,
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof userControllerFindOne>>, TError, TData>> },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }

export function useUserControllerFindOne<TData = Awaited<ReturnType<typeof userControllerFindOne>>, TError = unknown>(
  id: string,
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof userControllerFindOne>>, TError, TData>> },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
  const queryOptions = getUserControllerFindOneQueryOptions(id, options)

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>
  }

  query.queryKey = queryOptions.queryKey

  return query
}

export const userControllerUpdate = (id: string, updateUserDto: UpdateUserDto) => {
  return customInstance<ApiResponse<ApiResponse<void>>>({
    url: `/user/${id}`,
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json' },
    data: updateUserDto
  })
}

export const getUserControllerUpdateMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof userControllerUpdate>>,
    TError,
    { id: string; data: UpdateUserDto },
    TContext
  >
}): UseMutationOptions<
  Awaited<ReturnType<typeof userControllerUpdate>>,
  TError,
  { id: string; data: UpdateUserDto },
  TContext
> => {
  const mutationKey = ['userControllerUpdate']
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } }

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof userControllerUpdate>>,
    { id: string; data: UpdateUserDto }
  > = props => {
    const { id, data } = props ?? {}

    return userControllerUpdate(id, data)
  }

  return { mutationFn, ...mutationOptions }
}

export type UserControllerUpdateMutationResult = NonNullable<Awaited<ReturnType<typeof userControllerUpdate>>>
export type UserControllerUpdateMutationBody = UpdateUserDto
export type UserControllerUpdateMutationError = unknown

export const useUserControllerUpdate = <TError = unknown, TContext = unknown>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof userControllerUpdate>>,
      TError,
      { id: string; data: UpdateUserDto },
      TContext
    >
  },
  queryClient?: QueryClient
): UseMutationResult<
  Awaited<ReturnType<typeof userControllerUpdate>>,
  TError,
  { id: string; data: UpdateUserDto },
  TContext
> => {
  const mutationOptions = getUserControllerUpdateMutationOptions(options)

  return useMutation(mutationOptions, queryClient)
}
export const userControllerRemove = (id: string) => {
  return customInstance<ApiResponse<void>>({ url: `/user/${id}`, method: 'DELETE' })
}

export const getUserControllerRemoveMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<Awaited<ReturnType<typeof userControllerRemove>>, TError, { id: string }, TContext>
}): UseMutationOptions<Awaited<ReturnType<typeof userControllerRemove>>, TError, { id: string }, TContext> => {
  const mutationKey = ['userControllerRemove']
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } }

  const mutationFn: MutationFunction<Awaited<ReturnType<typeof userControllerRemove>>, { id: string }> = props => {
    const { id } = props ?? {}

    return userControllerRemove(id)
  }

  return { mutationFn, ...mutationOptions }
}

export type UserControllerRemoveMutationResult = NonNullable<Awaited<ReturnType<typeof userControllerRemove>>>

export type UserControllerRemoveMutationError = unknown

export const useUserControllerRemove = <TError = unknown, TContext = unknown>(
  options?: {
    mutation?: UseMutationOptions<Awaited<ReturnType<typeof userControllerRemove>>, TError, { id: string }, TContext>
  },
  queryClient?: QueryClient
): UseMutationResult<Awaited<ReturnType<typeof userControllerRemove>>, TError, { id: string }, TContext> => {
  const mutationOptions = getUserControllerRemoveMutationOptions(options)

  return useMutation(mutationOptions, queryClient)
}
