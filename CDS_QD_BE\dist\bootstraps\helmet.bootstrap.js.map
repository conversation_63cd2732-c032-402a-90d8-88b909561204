{"version": 3, "sources": ["../../src/bootstraps/helmet.bootstrap.ts"], "sourcesContent": ["import { INestApplication } from '@nestjs/common';\r\nimport helmet from 'helmet';\r\n\r\nexport function bootstrapHelmet(app: INestApplication): void {\r\n    if (process.env.NODE_ENV === 'production') {\r\n        app.use(helmet());\r\n    }\r\n}\r\n"], "names": ["bootstrapHelmet", "app", "process", "env", "NODE_ENV", "use", "helmet"], "mappings": "oGAGgBA,yDAAAA,+EAFG,+FAEZ,SAASA,gBAAgBC,GAAqB,EACjD,GAAIC,QAAQC,GAAG,CAACC,QAAQ,GAAK,aAAc,CACvCH,IAAII,GAAG,CAACC,GAAAA,eAAM,IAClB,CACJ"}