# Hướng dẫn sử dụng UI Components

## 1. Tổng quan
- Sử dụng Material-UI (MUI) làm thư viện UI chính
- <PERSON><PERSON> thủ Material Design 3
- Hỗ trợ theme tùy chỉnh và dark mode

## 2. Cài đặt

```bash
pnpm add @mui/material @emotion/react @emotion/styled @mui/icons-material
```

## 3. Cấu hình Theme

```typescript
// src/theme/index.ts
import { createTheme } from '@mui/material/styles';

export const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
      light: '#42a5f5',
      dark: '#1565c0',
      contrastText: '#fff',
    },
    secondary: {
      main: '#9c27b0',
      light: '#ba68c8',
      dark: '#7b1fa2',
      contrastText: '#fff',
    },
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    h1: { fontSize: '2.5rem', fontWeight: 500 },
    h2: { fontSize: '2rem', fontWeight: 500 },
    h3: { fontSize: '1.75rem', fontWeight: 500 },
    h4: { fontSize: '1.5rem', fontWeight: 500 },
    h5: { fontSize: '1.25rem', fontWeight: 500 },
    h6: { fontSize: '1rem', fontWeight: 500 },
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          borderRadius: 8,
        },
      },
    },
  },
});
```

## 4. Component cơ bản

### 4.1. Button

```tsx
import { Button, Stack } from '@mui/material';
import SaveIcon from '@mui/icons-material/Save';

export function ButtonExample() {
  return (
    <Stack direction="row" spacing={2}>
      <Button variant="contained">Primary</Button>
      <Button variant="outlined">Secondary</Button>
      <Button variant="text">Text</Button>
      <Button 
        variant="contained" 
        color="success"
        startIcon={<SaveIcon />}
      >
        Lưu
      </Button>
    </Stack>
  );
}
```

### 4.2. Form Controls

```tsx
import { 
  TextField, 
  Checkbox, 
  FormControlLabel,
  RadioGroup,
  Radio,
  Switch,
  Slider,
  Select,
  MenuItem,
  InputLabel,
  FormControl,
  FormHelperText,
} from '@mui/material';

export function FormExample() {
  return (
    <Stack spacing={3} sx={{ maxWidth: 400 }}>
      <TextField 
        label="Họ và tên" 
        variant="outlined" 
        fullWidth 
        required
        helperText="Nhập họ và tên đầy đủ"
      />
      
      <FormControl fullWidth>
        <InputLabel>Vai trò</InputLabel>
        <Select label="Vai trò" defaultValue="user">
          <MenuItem value="admin">Quản trị viên</MenuItem>
          <MenuItem value="user">Người dùng</MenuItem>
          <MenuItem value="guest">Khách</MenuItem>
        </Select>
      </FormControl>
      
      <FormControlLabel 
        control={<Checkbox defaultChecked />} 
        label="Đồng ý điều khoản" 
      />
      
      <FormControlLabel
        control={<Switch defaultChecked />}
        label="Kích hoạt"
      />
      
      <Slider
        defaultValue={30}
        valueLabelDisplay="auto"
        step={10}
        marks
        min={10}
        max={110}
      />
    </Stack>
  );
}
```

## 5. Data Display

### 5.1. Data Grid

```tsx
import { DataGrid, GridColDef } from '@mui/x-data-grid';

const columns: GridColDef[] = [
  { field: 'id', headerName: 'ID', width: 70 },
  { field: 'name', headerName: 'Họ tên', flex: 1 },
  { field: 'email', headerName: 'Email', flex: 1.5 },
  {
    field: 'status',
    headerName: 'Trạng thái',
    width: 130,
    renderCell: (params) => (
      <Chip 
        label={params.value} 
        color={params.value === 'active' ? 'success' : 'default'}
        size="small"
      />
    ),
  },
];

const rows = [
  { id: 1, name: 'Nguyễn Văn A', email: '<EMAIL>', status: 'active' },
  { id: 2, name: 'Trần Thị B', email: '<EMAIL>', status: 'inactive' },
];

export function DataGridExample() {
  return (
    <div style={{ height: 400, width: '100%' }}>
      <DataGrid
        rows={rows}
        columns={columns}
        pageSize={5}
        rowsPerPageOptions={[5]}
        checkboxSelection
        disableSelectionOnClick
      />
    </div>
  );
}
```

## 6. Feedback Components

### 6.1. Alert

```tsx
import { Alert, AlertTitle, Snackbar } from '@mui/material';
import { useState } from 'react';

export function AlertExample() {
  const [open, setOpen] = useState(true);

  return (
    <Snackbar 
      open={open} 
      autoHideDuration={6000} 
      onClose={() => setOpen(false)}
      anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
    >
      <Alert 
        onClose={() => setOpen(false)} 
        severity="success"
        variant="filled"
        sx={{ width: '100%' }}
      >
        <AlertTitle>Thành công</AlertTitle>
        Dữ liệu đã được lưu thành công!
      </Alert>
    </Snackbar>
  );
}
```

## 7. Navigation

### 7.1. Tabs

```tsx
import { Tab, Tabs, Box, Typography } from '@mui/material';
import { useState } from 'react';

function TabPanel({ children, value, index, ...other }: any) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`tabpanel-${index}`}
      aria-labelledby={`tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          <Typography>{children}</Typography>
        </Box>
      )}
    </div>
  );
}

export function TabsExample() {
  const [value, setValue] = useState(0);

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };

  return (
    <Box sx={{ width: '100%' }}>
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={value} onChange={handleChange}>
          <Tab label="Thông tin chung" />
          <Tab label="Chi tiết" />
          <Tab label="Lịch sử" />
        </Tabs>
      </Box>
      <TabPanel value={value} index={0}>
        Nội dung thông tin chung
      </TabPanel>
      <TabPanel value={value} index={1}>
        Nội dung chi tiết
      </TabPanel>
      <TabPanel value={value} index={2}>
        Lịch sử thay đổi
      </TabPanel>
    </Box>
  );
}
```

## 8. Tối ưu hiệu năng

### 8.1. Memoize components

```tsx
import { memo } from 'react';
import { Button } from '@mui/material';

// Sử dụng memo để tránh re-render không cần thiết
export const PrimaryButton = memo(({ children, ...props }: any) => (
  <Button variant="contained" {...props}>
    {children}
  </Button>
));
```

## 9. Best Practices
- Sử dụng `sx` prop cho style đơn giản
- Tạo styled components cho style phức tạp
- Tái sử dụng theme variables
- Sử dụng Material-UI Icons thay vì icon tùy chỉnh khi có thể
- Tạo các component wrapper cho các pattern sử dụng nhiều lần
