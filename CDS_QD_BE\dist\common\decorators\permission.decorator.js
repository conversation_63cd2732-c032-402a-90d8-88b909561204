"use strict";Object.defineProperty(exports,"__esModule",{value:true});function _export(target,all){for(var name in all)Object.defineProperty(target,name,{enumerable:true,get:Object.getOwnPropertyDescriptor(all,name).get})}_export(exports,{get PERMISSION_KEY(){return PERMISSION_KEY},get Permission(){return Permission}});const _common=require("@nestjs/common");const PERMISSION_KEY="permission";const Permission=(...args)=>(0,_common.SetMetadata)(PERMISSION_KEY,args);
//# sourceMappingURL=permission.decorator.js.map