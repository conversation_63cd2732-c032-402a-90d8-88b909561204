"use strict";Object.defineProperty(exports,"__esModule",{value:true});Object.defineProperty(exports,"UsersRoleEntity",{enumerable:true,get:function(){return UsersRoleEntity}});const _typeorm=require("typeorm");const _userentity=require("./user.entity");const _roleentity=require("./role.entity");function _ts_decorate(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect==="object"&&typeof Reflect.decorate==="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)if(d=decorators[i])r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r;return c>3&&r&&Object.defineProperty(target,key,r),r}function _ts_metadata(k,v){if(typeof Reflect==="object"&&typeof Reflect.metadata==="function")return Reflect.metadata(k,v)}let UsersRoleEntity=class UsersRoleEntity{};_ts_decorate([(0,_typeorm.PrimaryColumn)({name:"user_id",type:"bigint"}),_ts_metadata("design:type",Number)],UsersRoleEntity.prototype,"userId",void 0);_ts_decorate([(0,_typeorm.PrimaryColumn)({name:"role_id",type:"bigint"}),_ts_metadata("design:type",Number)],UsersRoleEntity.prototype,"roleId",void 0);_ts_decorate([(0,_typeorm.ManyToOne)(()=>_userentity.UserEntity,user=>user.userRoles,{onDelete:"CASCADE",onUpdate:"CASCADE",createForeignKeyConstraints:false}),(0,_typeorm.JoinColumn)({name:"user_id",referencedColumnName:"id"}),_ts_metadata("design:type",typeof _typeorm.Relation==="undefined"?Object:_typeorm.Relation)],UsersRoleEntity.prototype,"user",void 0);_ts_decorate([(0,_typeorm.ManyToOne)(()=>_roleentity.RoleEntity,role=>role.userRoles,{onDelete:"CASCADE",onUpdate:"CASCADE",createForeignKeyConstraints:false}),(0,_typeorm.JoinColumn)({name:"role_id",referencedColumnName:"id"}),_ts_metadata("design:type",typeof _typeorm.Relation==="undefined"?Object:_typeorm.Relation)],UsersRoleEntity.prototype,"role",void 0);UsersRoleEntity=_ts_decorate([(0,_typeorm.Entity)({name:"users_roles"})],UsersRoleEntity);
//# sourceMappingURL=usersRole.entity.js.map