/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { DoiTuongChinhSachHauPhuongEntity } from '~/database/typeorm/entities/doiTuongChinhSachHauPhuong.entity';

@Injectable()
export class DoiTuongChinhSachHauPhuongRepository extends Repository<DoiTuongChinhSachHauPhuongEntity> {
    constructor(private dataSource: DataSource) {
        super(DoiTuongChinhSachHauPhuongEntity, dataSource.createEntityManager());
    }
}
