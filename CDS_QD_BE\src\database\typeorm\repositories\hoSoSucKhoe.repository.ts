/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { HoSoSucKhoeEntity } from '~/database/typeorm/entities/hoSoSucKhoe.entity';

@Injectable()
export class HoSoSucKhoeRepository extends Repository<HoSoSucKhoeEntity> {
    constructor(private dataSource: DataSource) {
        super(HoSoSucKhoeEntity, dataSource.createEntityManager());
    }
}
