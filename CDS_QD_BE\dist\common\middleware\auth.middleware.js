"use strict";Object.defineProperty(exports,"__esModule",{value:true});Object.defineProperty(exports,"AuthMiddleware",{enumerable:true,get:function(){return AuthMiddleware}});const _common=require("@nestjs/common");const _services=require("../../shared/services");const _tokenservice=require("../../shared/services/token.service");function _ts_decorate(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect==="object"&&typeof Reflect.decorate==="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)if(d=decorators[i])r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r;return c>3&&r&&Object.defineProperty(target,key,r),r}function _ts_metadata(k,v){if(typeof Reflect==="object"&&typeof Reflect.metadata==="function")return Reflect.metadata(k,v)}let AuthMiddleware=class AuthMiddleware{async use(req,res,next){try{if(!req.headers["authorization"]&&!req.headers["passcode"]){console.log("LOG:: AuthMiddleware - Missing authorization header");throw new _common.UnauthorizedException("Request Forbidden")}const authHeader=req.headers["authorization"];let authToken=authHeader;if(authHeader?.startsWith("Bearer ")){authToken=authHeader.substring(7);console.log("LOG:: AuthMiddleware - Bearer token detected, token length:",authToken?.length||0)}const authData=await this.tokenService.verifyAuthToken({authToken:authToken});if(!authData?.id||!authData?.user?.id){console.log("LOG:: AuthMiddleware - Invalid token or user data not found");throw new _common.UnauthorizedException("Error: Invalid token data")}req.headers["_accountId"]=authData.id;req.headers["_userId"]=authData.user.id.toString();req["user"]=authData.user;console.log("LOG:: AuthMiddleware - Token verified successfully, user ID:",authData.user.id);next()}catch(err){console.log("LOG:: AuthMiddleware Error:",err.message);throw new _common.UnauthorizedException("Error: Request Forbidden [Token Invalid]")}}constructor(tokenService,utilService){this.tokenService=tokenService;this.utilService=utilService}};AuthMiddleware=_ts_decorate([(0,_common.Injectable)(),_ts_metadata("design:type",Function),_ts_metadata("design:paramtypes",[typeof _tokenservice.TokenService==="undefined"?Object:_tokenservice.TokenService,typeof _services.UtilService==="undefined"?Object:_services.UtilService])],AuthMiddleware);
//# sourceMappingURL=auth.middleware.js.map