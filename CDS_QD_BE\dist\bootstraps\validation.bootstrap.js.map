{"version": 3, "sources": ["../../src/bootstraps/validation.bootstrap.ts"], "sourcesContent": ["import { BadRequestException, INestApplication, ValidationPipe } from '@nestjs/common';\r\nimport { ValidationError, useContainer } from 'class-validator';\r\nimport { AppModule } from '~/app.module';\r\n\r\nexport function bootstrapValidation(app: INestApplication): void {\r\n    // https://github.com/typestack/class-validator#using-service-container\r\n    useContainer(app.select(AppModule), { fallbackOnErrors: true });\r\n\r\n    app.useGlobalPipes(\r\n        new ValidationPipe({\r\n            whitelist: true,\r\n            transform: true,\r\n            // transformOptions: {\r\n            //     // strategy: 'exposeAll',\r\n            //     excludeExtraneousValues: true,\r\n            //     enableImplicitConversion: true,\r\n            // },\r\n            // stopAtFirstError: false,\r\n            // forbidUnknownValues: true,\r\n            // disableErrorMessages: false,\r\n            exceptionFactory: (validationErrors: ValidationError[] = []) => {\r\n                return new BadRequestException(\r\n                    validationErrors.map((error) => ({\r\n                        field: error.property,\r\n                        error: Object.values(error.constraints).join(', '),\r\n                    })),\r\n                );\r\n            },\r\n            // validationError: { target: true, value: true },\r\n        }),\r\n    );\r\n}\r\n"], "names": ["bootstrapValidation", "app", "useContainer", "select", "AppModule", "fallbackOnErrors", "useGlobalPipes", "ValidationPipe", "whitelist", "transform", "exceptionFactory", "validationErrors", "BadRequestException", "map", "error", "field", "property", "Object", "values", "constraints", "join"], "mappings": "oGAIgBA,6DAAAA,6CAJsD,gDACxB,4CACpB,iBAEnB,SAASA,oBAAoBC,GAAqB,EAErDC,GAAAA,4BAAY,EAACD,IAAIE,MAAM,CAACC,oBAAS,EAAG,CAAEC,iBAAkB,IAAK,GAE7DJ,IAAIK,cAAc,CACd,IAAIC,sBAAc,CAAC,CACfC,UAAW,KACXC,UAAW,KASXC,iBAAkB,CAACC,iBAAsC,EAAE,IACvD,OAAO,IAAIC,2BAAmB,CAC1BD,iBAAiBE,GAAG,CAAC,AAACC,OAAW,CAAA,CAC7BC,MAAOD,MAAME,QAAQ,CACrBF,MAAOG,OAAOC,MAAM,CAACJ,MAAMK,WAAW,EAAEC,IAAI,CAAC,KACjD,CAAA,GAER,CAEJ,GAER"}