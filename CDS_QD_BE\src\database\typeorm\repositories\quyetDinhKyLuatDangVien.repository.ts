/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { QuyetDinhKyLuatDangVienEntity } from '~/database/typeorm/entities/quyetDinhKyLuatDangVien.entity';

@Injectable()
export class QuyetDinhKyLuatDangVienRepository extends Repository<QuyetDinhKyLuatDangVienEntity> {
    constructor(private dataSource: DataSource) {
        super(QuyetDinhKyLuatDangVienEntity, dataSource.createEntityManager());
    }
}
