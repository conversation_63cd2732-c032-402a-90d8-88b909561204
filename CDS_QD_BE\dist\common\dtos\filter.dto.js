"use strict";Object.defineProperty(exports,"__esModule",{value:true});Object.defineProperty(exports,"FilterDto",{enumerable:true,get:function(){return FilterDto}});const _swagger=require("@nestjs/swagger");const _classtransformer=require("class-transformer");const _classvalidator=require("class-validator");function _ts_decorate(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect==="object"&&typeof Reflect.decorate==="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)if(d=decorators[i])r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r;return c>3&&r&&Object.defineProperty(target,key,r),r}function _ts_metadata(k,v){if(typeof Reflect==="object"&&typeof Reflect.metadata==="function")return Reflect.metadata(k,v)}let FilterDto=class FilterDto{};_ts_decorate([(0,_swagger.ApiProperty)({required:false,type:Number,default:1}),(0,_classvalidator.IsOptional)(),(0,_classtransformer.Transform)(({value})=>parseInt(value)),_ts_metadata("design:type",Number)],FilterDto.prototype,"page",void 0);_ts_decorate([(0,_swagger.ApiProperty)({required:false,type:Number,default:10}),(0,_classvalidator.IsOptional)(),(0,_classtransformer.Transform)(({value})=>parseInt(value)),_ts_metadata("design:type",Number)],FilterDto.prototype,"perPage",void 0);_ts_decorate([(0,_swagger.ApiProperty)({required:false,default:"id.ASC"}),(0,_classvalidator.IsOptional)(),_ts_metadata("design:type",String)],FilterDto.prototype,"sortBy",void 0);_ts_decorate([(0,_swagger.ApiProperty)({required:false}),(0,_classvalidator.IsOptional)(),_ts_metadata("design:type",String)],FilterDto.prototype,"search",void 0);_ts_decorate([(0,_swagger.ApiProperty)({required:false}),(0,_classvalidator.IsOptional)(),_ts_metadata("design:type",String)],FilterDto.prototype,"type",void 0);
//# sourceMappingURL=filter.dto.js.map