# Ví dụ sử dụng API client đư<PERSON><PERSON> tạo tự động

## Sử dụng hooks để lấy danh sách người dùng

```tsx
// src/pages/user/list.tsx
'use client'

import { useUserList } from '@/hooks/modules/user';
import { Button, CircularProgress, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow } from '@mui/material';

export default function UserListPage() {
  const { userList, pagination, isLoading, error, refresh } = useUserList({
    page: 1,
    perPage: 10
  });

  if (isLoading) return <CircularProgress />;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <div>
      <h1>Danh sách người dùng</h1>
      <Button variant="contained" onClick={refresh}>Làm mới</Button>
      
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>ID</TableCell>
              <TableCell>Họ tên</TableCell>
              <TableCell>Email</TableCell>
              <TableCell>Số điện thoại</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {userList.map((user) => (
              <TableRow key={user.id}>
                <TableCell>{user.id}</TableCell>
                <TableCell>{user.fullName}</TableCell>
                <TableCell>{user.email}</TableCell>
                <TableCell>{user.phone}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
      
      <div>
        <p>Tổng số: {pagination?.total}</p>
        <p>Trang: {pagination?.page}/{pagination?.totalPages}</p>
      </div>
    </div>
  );
}
```

## Sử dụng hooks để xem chi tiết người dùng

```tsx
// src/pages/user/[id].tsx
'use client'

import { useUserDetail } from '@/hooks/modules/user';
import { useParams } from 'next/navigation';
import { Button, Card, CardContent, CircularProgress, Typography } from '@mui/material';

export default function UserDetailPage() {
  const params = useParams();
  const userId = params.id as string;
  
  const { user, isLoading, error, refresh } = useUserDetail(userId);

  if (isLoading) return <CircularProgress />;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <div>
      <h1>Chi tiết người dùng</h1>
      <Button variant="contained" onClick={refresh}>Làm mới</Button>
      
      <Card>
        <CardContent>
          <Typography variant="h5">{user.fullName}</Typography>
          <Typography>Email: {user.email}</Typography>
          <Typography>Số điện thoại: {user.phone}</Typography>
          <Typography>Địa chỉ: {user.address}</Typography>
          <Typography>Ngày sinh: {new Date(user.birthday).toLocaleDateString()}</Typography>
          <Typography>Giới tính: {user.gender}</Typography>
        </CardContent>
      </Card>
    </div>
  );
}
```

## Sử dụng hooks để tạo người dùng mới

```tsx
// src/pages/user/create.tsx
'use client'

import { useCreateUser } from '@/hooks/modules/user';
import { useState } from 'react';
import { Button, CircularProgress, TextField, Grid, Alert } from '@mui/material';
import { useRouter } from 'next/navigation';

export default function CreateUserPage() {
  const router = useRouter();
  const { createUser, isLoading, error, isSuccess } = useCreateUser();
  
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    email: '',
    roleId: 1,
    departmentId: 1,
    fullName: '',
    areaCode: '84',
    phone: '',
    address: '',
    birthday: '',
    gender: 'male'
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    createUser(formData);
  };

  if (isSuccess) {
    router.push('/user/list');
    return null;
  }

  return (
    <div>
      <h1>Tạo người dùng mới</h1>
      
      {error && <Alert severity="error">{error.message}</Alert>}
      
      <form onSubmit={handleSubmit}>
        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Tên đăng nhập"
              name="username"
              value={formData.username}
              onChange={handleChange}
              required
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Mật khẩu"
              name="password"
              type="password"
              value={formData.password}
              onChange={handleChange}
              required
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Email"
              name="email"
              type="email"
              value={formData.email}
              onChange={handleChange}
              required
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Họ tên"
              name="fullName"
              value={formData.fullName}
              onChange={handleChange}
              required
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Số điện thoại"
              name="phone"
              value={formData.phone}
              onChange={handleChange}
              required
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Địa chỉ"
              name="address"
              value={formData.address}
              onChange={handleChange}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Ngày sinh"
              name="birthday"
              type="date"
              value={formData.birthday}
              onChange={handleChange}
              InputLabelProps={{ shrink: true }}
            />
          </Grid>
          <Grid item xs={12}>
            <Button
              type="submit"
              variant="contained"
              disabled={isLoading}
            >
              {isLoading ? <CircularProgress size={24} /> : 'Tạo người dùng'}
            </Button>
          </Grid>
        </Grid>
      </form>
    </div>
  );
}
```
