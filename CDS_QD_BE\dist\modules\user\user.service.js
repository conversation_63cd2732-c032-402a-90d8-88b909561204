"use strict";Object.defineProperty(exports,"__esModule",{value:true});Object.defineProperty(exports,"UserService",{enumerable:true,get:function(){return UserService}});const _common=require("@nestjs/common");const _databaseservice=require("../../database/typeorm/database.service");const _mediaservice=require("../media/media.service");const _services=require("../../shared/services");function _ts_decorate(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect==="object"&&typeof Reflect.decorate==="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)if(d=decorators[i])r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r;return c>3&&r&&Object.defineProperty(target,key,r),r}function _ts_metadata(k,v){if(typeof Reflect==="object"&&typeof Reflect.metadata==="function")return Reflect.metadata(k,v)}let UserService=class UserService{async create(createUserDto){const{username,password,...rest}=createUserDto;const{salt,hash}=this.tokenService.hashPassword(createUserDto.password);const account=await this.database.account.save(this.database.account.create({username:createUserDto.username,password:hash,salt}));if(!account){throw new _common.HttpException("Cannot create account",400)}const user=await this.database.user.save(this.database.user.create({...rest,accountId:account.id}));if(!user){throw new _common.HttpException("Cannot create user",400)}return{data:{account,user}}}async findAll(queries){const{builder,take,pagination}=this.utilService.getQueryBuilderAndPagination(this.database.user,queries);if(!this.utilService.isEmpty(queries.search)){builder.andWhere("(entity.fullName ILIKE :search OR entity.email ILIKE :search)",{search:`%${queries.search}%`})}builder.leftJoinAndSelect("entity.role","role");builder.leftJoinAndSelect("entity.avatar","avatar");builder.select(["entity","role.id","role.name","avatar.id"]);const[result,total]=await builder.getManyAndCount();const totalPages=Math.ceil(total/take);return{data:result,pagination:{...pagination,totalRecords:total,totalPages:totalPages}}}findOne(id){return this.database.user.findOneUserWithAllRelationsById(id)}async update(id,updateUserDto){const{username,password,...rest}=updateUserDto;const user=await this.database.user.findOneBy({id});if(!user){throw new _common.HttpException("Không tìm thấy người dùng",404)}if(password){const{salt,hash}=this.tokenService.hashPassword(updateUserDto.password);this.database.account.update({id:user.accountId},{password:hash,salt})}return await this.database.user.update({id},rest)}async remove(id){const user=await this.database.user.findOneBy({id});if(!user){throw new _common.HttpException("Không tìm thấy người dùng",404)}await this.database.user.delete({id});await this.database.account.delete({id:user.accountId});if(user.avatar?.id){await this.mediaService.remove(user.avatar.id)}return true}constructor(tokenService,utilService,mediaService,database){this.tokenService=tokenService;this.utilService=utilService;this.mediaService=mediaService;this.database=database}};UserService=_ts_decorate([(0,_common.Injectable)(),_ts_metadata("design:type",Function),_ts_metadata("design:paramtypes",[typeof _services.TokenService==="undefined"?Object:_services.TokenService,typeof _services.UtilService==="undefined"?Object:_services.UtilService,typeof _mediaservice.MediaService==="undefined"?Object:_mediaservice.MediaService,typeof _databaseservice.DatabaseService==="undefined"?Object:_databaseservice.DatabaseService])],UserService);
//# sourceMappingURL=user.service.js.map