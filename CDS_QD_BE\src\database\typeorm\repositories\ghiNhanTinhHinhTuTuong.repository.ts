/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { GhiNhanTinhHinhTuTuongEntity } from '~/database/typeorm/entities/ghiNhanTinhHinhTuTuong.entity';

@Injectable()
export class GhiNhanTinhHinhTuTuongRepository extends Repository<GhiNhanTinhHinhTuTuongEntity> {
    constructor(private dataSource: DataSource) {
        super(GhiNhanTinhHinhTuTuongEntity, dataSource.createEntityManager());
    }
}
