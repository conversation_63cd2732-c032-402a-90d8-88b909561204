/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { BienBanHoiDongKyLuatDangEntity } from '~/database/typeorm/entities/bienBanHoiDongKyLuatDang.entity';

@Injectable()
export class BienBanHoiDongKyLuatDangRepository extends Repository<BienBanHoiDongKyLuatDangEntity> {
    constructor(private dataSource: DataSource) {
        super(BienBanHoiDongKyLuatDangEntity, dataSource.createEntityManager());
    }
}
