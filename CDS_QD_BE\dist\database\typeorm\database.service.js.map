{"version": 3, "sources": ["../../../src/database/typeorm/database.service.ts"], "sourcesContent": ["import { Injectable } from '@nestjs/common';\nimport { CACHE_TIME } from '~/common/enums/enum';\nimport { AccountRepository } from '~/database/typeorm/repositories/account.repository';\nimport { DepartmentRepository } from '~/database/typeorm/repositories/department.repository';\nimport { MediaRepository } from '~/database/typeorm/repositories/media.repository';\nimport { PermissionRepository } from '~/database/typeorm/repositories/permission.repository';\nimport { ProviderRepository } from '~/database/typeorm/repositories/provider.repository';\nimport { RoleRepository } from '~/database/typeorm/repositories/role.repository';\nimport { UserRepository } from '~/database/typeorm/repositories/user.repository';\nimport { UserLogRepository } from '~/database/typeorm/repositories/userLog.repository';\nimport { WarehouseRepository } from '~/database/typeorm/repositories/warehouse.repository';\nimport { WarehouseTypeRepository } from '~/database/typeorm/repositories/warehouseType.repository';\nimport { CacheService } from '~/shared/services/cache.service';\nimport { LoaiDanhMucRepository } from '~/database/typeorm/repositories/loaiDanhMuc.repository';\nimport { GiaTriDanhMucRepository } from '~/database/typeorm/repositories/giaTriDanhMuc.repository';\nimport { ThamSoHeThongRepository } from '~/database/typeorm/repositories/thamSoHeThong.repository';\n\nimport { QuanNhanRepository } from '~/database/typeorm/repositories/quanNhan.repository';\nimport { LyLichCanNhanRepository } from '~/database/typeorm/repositories/lyLichCanNhan.repository';\nimport { QuaTrinhCongTacRepository } from '~/database/typeorm/repositories/quaTrinhCongTac.repository';\nimport { QuaTrinhDaoTaoRepository } from '~/database/typeorm/repositories/quaTrinhDaoTao.repository';\nimport { DeXuatKhenThuongRepository } from '~/database/typeorm/repositories/deXuatKhenThuong.repository';\nimport { QuyetDinhKhenThuongRepository } from '~/database/typeorm/repositories/quyetDinhKhenThuong.repository';\nimport { HoSoVuViecKyLuatRepository } from '~/database/typeorm/repositories/hoSoVuViecKyLuat.repository';\nimport { BienBanHoiDongKyLuatRepository } from '~/database/typeorm/repositories/bienBanHoiDongKyLuat.repository';\nimport { TaiLieuVuViecKyLuatRepository } from '~/database/typeorm/repositories/taiLieuVuViecKyLuat.repository';\nimport { QuyetDinhKyLuatRepository } from '~/database/typeorm/repositories/quyetDinhKyLuat.repository';\nimport { HoSoSucKhoeRepository } from '~/database/typeorm/repositories/hoSoSucKhoe.repository';\nimport { QuanHeGiaDinhRepository } from '~/database/typeorm/repositories/quanHeGiaDinh.repository';\nimport { TheoDoiCheDoChinhSachRepository } from '~/database/typeorm/repositories/theoDoiCheDoChinhSach.repository';\nimport { DangVienRepository } from '~/database/typeorm/repositories/dangVien.repository';\nimport { HoSoDangVienRepository } from '~/database/typeorm/repositories/hoSoDangVien.repository';\nimport { DeXuatKhenThuongDangVienRepository } from '~/database/typeorm/repositories/deXuatKhenThuongDangVien.repository';\nimport { QuyetDinhKhenThuongDangVienRepository } from '~/database/typeorm/repositories/quyetDinhKhenThuongDangVien.repository';\nimport { BanKiemDiemDangVienRepository } from '~/database/typeorm/repositories/banKiemDiemDangVien.repository';\nimport { HoSoVuViecKyLuatDangVienRepository } from '~/database/typeorm/repositories/hoSoVuViecKyLuatDangVien.repository';\nimport { BienBanHoiDongKyLuatDangRepository } from '~/database/typeorm/repositories/bienBanHoiDongKyLuatDang.repository';\nimport { QuyetDinhKyLuatDangVienRepository } from '~/database/typeorm/repositories/quyetDinhKyLuatDangVien.repository';\nimport { CapUyNhiemKyRepository } from '~/database/typeorm/repositories/capUyNhiemKy.repository';\nimport { ThanhVienCapUyRepository } from '~/database/typeorm/repositories/thanhVienCapUy.repository';\nimport { KeHoachSinhHoatDangRepository } from '~/database/typeorm/repositories/keHoachSinhHoatDang.repository';\nimport { BuoiSinhHoatDangRepository } from '~/database/typeorm/repositories/buoiSinhHoatDang.repository';\nimport { NghiQuyetDangRepository } from '~/database/typeorm/repositories/nghiQuyetDang.repository';\nimport { HoSoPhatTrienDangVienRepository } from '~/database/typeorm/repositories/hoSoPhatTrienDangVien.repository';\nimport { DanhGiaXepLoaiDangVienRepository } from '~/database/typeorm/repositories/danhGiaXepLoaiDangVien.repository';\nimport { DanhGiaXepLoaiToChucDangRepository } from '~/database/typeorm/repositories/danhGiaXepLoaiToChucDang.repository';\nimport { ThuChiDangPhiRepository } from '~/database/typeorm/repositories/thuChiDangPhi.repository';\nimport { KeHoachKiemTraGiamSatDangRepository } from '~/database/typeorm/repositories/keHoachKiemTraGiamSatDang.repository';\nimport { CuocKiemTraGiamSatDangRepository } from '~/database/typeorm/repositories/cuocKiemTraGiamSatDang.repository';\nimport { KeHoachGiaoDucChinhTriRepository } from '~/database/typeorm/repositories/keHoachGiaoDucChinhTri.repository';\nimport { HoatDongGiaoDucChinhTriRepository } from '~/database/typeorm/repositories/hoatDongGiaoDucChinhTri.repository';\nimport { ThamGiaGiaoDucChinhTriRepository } from '~/database/typeorm/repositories/thamGiaGiaoDucChinhTri.repository';\nimport { KetQuaHocTapChinhTriRepository } from '~/database/typeorm/repositories/ketQuaHocTapChinhTri.repository';\nimport { GhiNhanTinhHinhTuTuongRepository } from '~/database/typeorm/repositories/ghiNhanTinhHinhTuTuong.repository';\nimport { BienPhapTacDongTuTuongRepository } from '~/database/typeorm/repositories/bienPhapTacDongTuTuong.repository';\nimport { KeHoachTuyenTruyenVanHoaRepository } from '~/database/typeorm/repositories/keHoachTuyenTruyenVanHoa.repository';\nimport { HoatDongTuyenTruyenVanHoaRepository } from '~/database/typeorm/repositories/hoatDongTuyenTruyenVanHoa.repository';\nimport { PhongTraoThiDuaChinhTriRepository } from '~/database/typeorm/repositories/phongTraoThiDuaChinhTri.repository';\nimport { ChinhSachHauPhuongRepository } from '~/database/typeorm/repositories/chinhSachHauPhuong.repository';\nimport { DoiTuongChinhSachHauPhuongRepository } from '~/database/typeorm/repositories/doiTuongChinhSachHauPhuong.repository';\nimport { ThongBaoRepository } from '~/database/typeorm/repositories/thongBao.repository';\nimport { QuyTrinhPheDuyetRepository } from '~/database/typeorm/repositories/quyTrinhPheDuyet.repository';\nimport { YeuCauPheDuyetRepository } from '~/database/typeorm/repositories/yeuCauPheDuyet.repository';\nimport { BuocPheDuyetRepository } from '~/database/typeorm/repositories/buocPheDuyet.repository';\nimport { LichSuPheDuyetRepository } from '~/database/typeorm/repositories/lichSuPheDuyet.repository';\nimport { NhatKyHeThongRepository } from '~/database/typeorm/repositories/nhatKyHeThong.repository';\nimport { DuLieuTepTinRepository } from '~/database/typeorm/repositories/duLieuTepTin.repository';\nimport { UsersRoleRepository } from '~/database/typeorm/repositories/usersRole.repository';\n\n@Injectable()\nexport class DatabaseService {\n    constructor(\n        public readonly department: DepartmentRepository,\n        public readonly user: UserRepository,\n        public readonly account: AccountRepository,\n        public readonly media: MediaRepository,\n        public readonly permission: PermissionRepository,\n        public readonly role: RoleRepository,\n        public readonly userLog: UserLogRepository,\n        public readonly warehouse: WarehouseRepository,\n        public readonly warehouseType: WarehouseTypeRepository,\n        public readonly provider: ProviderRepository,\n        private readonly cacheService: CacheService,\n        public readonly loaiDanhMuc: LoaiDanhMucRepository,\n        public readonly giaTriDanhMuc: GiaTriDanhMucRepository,\n        public readonly thamSoHeThong: ThamSoHeThongRepository,\n        public readonly quanNhan: QuanNhanRepository,\n        public readonly lyLichCanNhan: LyLichCanNhanRepository,\n        public readonly quaTrinhCongTac: QuaTrinhCongTacRepository,\n        public readonly quaTrinhDaoTao: QuaTrinhDaoTaoRepository,\n        public readonly deXuatKhenThuong: DeXuatKhenThuongRepository,\n        public readonly quyetDinhKhenThuong: QuyetDinhKhenThuongRepository,\n        public readonly hoSoVuViecKyLuat: HoSoVuViecKyLuatRepository,\n        public readonly bienBanHoiDongKyLuat: BienBanHoiDongKyLuatRepository,\n        public readonly taiLieuVuViecKyLuat: TaiLieuVuViecKyLuatRepository,\n        public readonly quyetDinhKyLuat: QuyetDinhKyLuatRepository,\n        public readonly hoSoSucKhoe: HoSoSucKhoeRepository,\n        public readonly quanHeGiaDinh: QuanHeGiaDinhRepository,\n        public readonly theoDoiCheDoChinhSach: TheoDoiCheDoChinhSachRepository,\n        public readonly dangVien: DangVienRepository,\n        public readonly hoSoDangVien: HoSoDangVienRepository,\n        public readonly deXuatKhenThuongDangVien: DeXuatKhenThuongDangVienRepository,\n        public readonly quyetDinhKhenThuongDangVien: QuyetDinhKhenThuongDangVienRepository,\n        public readonly banKiemDiemDangVien: BanKiemDiemDangVienRepository,\n        public readonly hoSoVuViecKyLuatDangVien: HoSoVuViecKyLuatDangVienRepository,\n        public readonly bienBanHoiDongKyLuatDang: BienBanHoiDongKyLuatDangRepository,\n        public readonly quyetDinhKyLuatDangVien: QuyetDinhKyLuatDangVienRepository,\n        public readonly capUyNhiemKy: CapUyNhiemKyRepository,\n        public readonly thanhVienCapUy: ThanhVienCapUyRepository,\n        public readonly keHoachSinhHoatDang: KeHoachSinhHoatDangRepository,\n        public readonly buoiSinhHoatDang: BuoiSinhHoatDangRepository,\n        public readonly nghiQuyetDang: NghiQuyetDangRepository,\n        public readonly hoSoPhatTrienDangVien: HoSoPhatTrienDangVienRepository,\n        public readonly danhGiaXepLoaiDangVien: DanhGiaXepLoaiDangVienRepository,\n        public readonly danhGiaXepLoaiToChucDang: DanhGiaXepLoaiToChucDangRepository,\n        public readonly thuChiDangPhi: ThuChiDangPhiRepository,\n        public readonly keHoachKiemTraGiamSatDang: KeHoachKiemTraGiamSatDangRepository,\n        public readonly cuocKiemTraGiamSatDang: CuocKiemTraGiamSatDangRepository,\n        public readonly keHoachGiaoDucChinhTri: KeHoachGiaoDucChinhTriRepository,\n        public readonly hoatDongGiaoDucChinhTri: HoatDongGiaoDucChinhTriRepository,\n        public readonly thamGiaGiaoDucChinhTri: ThamGiaGiaoDucChinhTriRepository,\n        public readonly ketQuaHocTapChinhTri: KetQuaHocTapChinhTriRepository,\n        public readonly ghiNhanTinhHinhTuTuong: GhiNhanTinhHinhTuTuongRepository,\n        public readonly bienPhapTacDongTuTuong: BienPhapTacDongTuTuongRepository,\n        public readonly keHoachTuyenTruyenVanHoa: KeHoachTuyenTruyenVanHoaRepository,\n        public readonly hoatDongTuyenTruyenVanHoa: HoatDongTuyenTruyenVanHoaRepository,\n        public readonly phongTraoThiDuaChinhTri: PhongTraoThiDuaChinhTriRepository,\n        public readonly chinhSachHauPhuong: ChinhSachHauPhuongRepository,\n        public readonly doiTuongChinhSachHauPhuong: DoiTuongChinhSachHauPhuongRepository,\n        public readonly thongBao: ThongBaoRepository,\n        public readonly quyTrinhPheDuyet: QuyTrinhPheDuyetRepository,\n        public readonly yeuCauPheDuyet: YeuCauPheDuyetRepository,\n        public readonly buocPheDuyet: BuocPheDuyetRepository,\n        public readonly lichSuPheDuyet: LichSuPheDuyetRepository,\n        public readonly nhatKyHeThong: NhatKyHeThongRepository,\n        public readonly duLieuTepTin: DuLieuTepTinRepository,\n        public readonly usersRole: UsersRoleRepository,\n    ) {\n        // load all departments to cache\n        // this.loadDepartmentsToCache();\n        // this.loadPermissionsByRoleToCache();\n    }\n\n    private loadDepartmentsToCache() {\n        this.department.find().then((departments) => {\n            departments.forEach((department) => {\n                this.cacheService.setJson(`department:${department.id}`, department, CACHE_TIME.ONE_MONTH);\n            });\n        });\n    }\n\n    private loadPermissionsByRoleToCache() {\n        this.role.find({ relations: ['permissions'] }).then((roles) => {\n            roles.forEach((role) => {\n                this.cacheService.setJson(\n                    `permissions:${role.id}`,\n                    role.permissions.map((p) => p.action),\n                    CACHE_TIME.ONE_MONTH,\n                );\n            });\n        });\n    }\n}\n"], "names": ["DatabaseService", "loadDepartmentsToCache", "department", "find", "then", "departments", "for<PERSON>ach", "cacheService", "<PERSON><PERSON><PERSON>", "id", "CACHE_TIME", "ONE_MONTH", "loadPermissionsByRoleToCache", "role", "relations", "roles", "permissions", "map", "p", "action", "constructor", "user", "account", "media", "permission", "userLog", "warehouse", "warehouseType", "provider", "loaiDanhMuc", "giaTriDanhMuc", "thamSoHeThong", "quan<PERSON>han", "lyLichCanNhan", "quaTrinhCongTac", "quaTrin<PERSON><PERSON><PERSON><PERSON><PERSON>", "deXuatKhen<PERSON><PERSON><PERSON>", "quyet<PERSON><PERSON><PERSON><PERSON><PERSON>", "hoSoVuViecKyLuat", "bienBanHoiDongKyLuat", "taiLieuVuViecKyLuat", "quyetDinhKyLuat", "ho<PERSON>o<PERSON><PERSON><PERSON><PERSON>", "quanHeGiaDinh", "theoDoiCheDoChinhSach", "dang<PERSON><PERSON>", "hoSoDangVien", "deXuatKhenThuongDangVien", "quyetDinhKhenThuongDangVien", "banKiemDiemDangVien", "hoSoVuViecKyLuatDangVien", "bienBanHoiDongKyLuatDang", "quyetDinhKyLuatDangVien", "capUyNhiemKy", "thanhVienCapUy", "keHoachSinhHoatDang", "buoiSinhHoatDang", "nghiQuyetDang", "hoSoPhatTrienDangVien", "danhGiaXepLoaiDangVien", "danhGiaXepLoaiToChucDang", "thuChiDangPhi", "keHoachKiemTraGiamSatDang", "cuocKiemTraGiamSatDang", "keHoachGiaoDucChinhTri", "hoatDongGiaoDucChinhTri", "thamGiaGiaoDucChinhTri", "ketQuaHocTapChinhTri", "ghiNhanTinhHinhTuTuong", "bienPhapTacDongTuTuong", "keHoachTuyenTruyenVanHoa", "hoatDongTuyenTruyenVanHoa", "phongTraoThiDuaChinhTri", "chinhSachHauPhuong", "doiTuongChinhSachHauPhuong", "<PERSON><PERSON><PERSON><PERSON>", "quyTrinhPheDuyet", "yeuCauPheDuyet", "buocPheD<PERSON><PERSON>", "lichSuPheDuyet", "nhatKyHeThong", "duLieuTepTin", "usersRole"], "mappings": "oGAsEaA,yDAAAA,yCAtEc,sCACA,4DACO,yEACG,uEACL,uEACK,0EACF,oEACJ,gEACA,mEACG,wEACE,8EACI,uEACX,4EACS,gFACE,kFACA,6EAEL,6EACK,oFACE,qFACD,sFACE,2FACG,2FACH,4FACI,+FACD,0FACJ,kFACJ,gFACE,0FACQ,qFACb,4EACI,4FACY,2GACG,sGACR,mGACK,wGACA,uGACD,2FACX,kFACE,yFACK,2FACH,qFACH,0FACQ,mGACC,sGACE,6FACX,8FACY,uGACH,oGACA,qGACC,qGACD,kGACF,kGACE,oGACA,sGACE,yGACC,wGACF,iGACL,oGACQ,0FAClB,gFACQ,sFACF,kFACF,kFACE,mFACD,iFACD,6EACH,olBAG7B,IAAA,AAAMA,gBAAN,MAAMA,gBAyET,AAAQC,wBAAyB,CAC7B,IAAI,CAACC,UAAU,CAACC,IAAI,GAAGC,IAAI,CAAC,AAACC,cACzBA,YAAYC,OAAO,CAAC,AAACJ,aACjB,IAAI,CAACK,YAAY,CAACC,OAAO,CAAC,CAAC,WAAW,EAAEN,WAAWO,EAAE,CAAC,CAAC,CAAEP,WAAYQ,gBAAU,CAACC,SAAS,CAC7F,EACJ,EACJ,CAEA,AAAQC,8BAA+B,CACnC,IAAI,CAACC,IAAI,CAACV,IAAI,CAAC,CAAEW,UAAW,CAAC,cAAc,AAAC,GAAGV,IAAI,CAAC,AAACW,QACjDA,MAAMT,OAAO,CAAC,AAACO,OACX,IAAI,CAACN,YAAY,CAACC,OAAO,CACrB,CAAC,YAAY,EAAEK,KAAKJ,EAAE,CAAC,CAAC,CACxBI,KAAKG,WAAW,CAACC,GAAG,CAAC,AAACC,GAAMA,EAAEC,MAAM,EACpCT,gBAAU,CAACC,SAAS,CAE5B,EACJ,EACJ,CA1FAS,YACI,AAAgBlB,UAAgC,CAChD,AAAgBmB,IAAoB,CACpC,AAAgBC,OAA0B,CAC1C,AAAgBC,KAAsB,CACtC,AAAgBC,UAAgC,CAChD,AAAgBX,IAAoB,CACpC,AAAgBY,OAA0B,CAC1C,AAAgBC,SAA8B,CAC9C,AAAgBC,aAAsC,CACtD,AAAgBC,QAA4B,CAC5C,AAAiBrB,YAA0B,CAC3C,AAAgBsB,WAAkC,CAClD,AAAgBC,aAAsC,CACtD,AAAgBC,aAAsC,CACtD,AAAgBC,QAA4B,CAC5C,AAAgBC,aAAsC,CACtD,AAAgBC,eAA0C,CAC1D,AAAgBC,cAAwC,CACxD,AAAgBC,gBAA4C,CAC5D,AAAgBC,mBAAkD,CAClE,AAAgBC,gBAA4C,CAC5D,AAAgBC,oBAAoD,CACpE,AAAgBC,mBAAkD,CAClE,AAAgBC,eAA0C,CAC1D,AAAgBC,WAAkC,CAClD,AAAgBC,aAAsC,CACtD,AAAgBC,qBAAsD,CACtE,AAAgBC,QAA4B,CAC5C,AAAgBC,YAAoC,CACpD,AAAgBC,wBAA4D,CAC5E,AAAgBC,2BAAkE,CAClF,AAAgBC,mBAAkD,CAClE,AAAgBC,wBAA4D,CAC5E,AAAgBC,wBAA4D,CAC5E,AAAgBC,uBAA0D,CAC1E,AAAgBC,YAAoC,CACpD,AAAgBC,cAAwC,CACxD,AAAgBC,mBAAkD,CAClE,AAAgBC,gBAA4C,CAC5D,AAAgBC,aAAsC,CACtD,AAAgBC,qBAAsD,CACtE,AAAgBC,sBAAwD,CACxE,AAAgBC,wBAA4D,CAC5E,AAAgBC,aAAsC,CACtD,AAAgBC,yBAA8D,CAC9E,AAAgBC,sBAAwD,CACxE,AAAgBC,sBAAwD,CACxE,AAAgBC,uBAA0D,CAC1E,AAAgBC,sBAAwD,CACxE,AAAgBC,oBAAoD,CACpE,AAAgBC,sBAAwD,CACxE,AAAgBC,sBAAwD,CACxE,AAAgBC,wBAA4D,CAC5E,AAAgBC,yBAA8D,CAC9E,AAAgBC,uBAA0D,CAC1E,AAAgBC,kBAAgD,CAChE,AAAgBC,0BAAgE,CAChF,AAAgBC,QAA4B,CAC5C,AAAgBC,gBAA4C,CAC5D,AAAgBC,cAAwC,CACxD,AAAgBC,YAAoC,CACpD,AAAgBC,cAAwC,CACxD,AAAgBC,aAAsC,CACtD,AAAgBC,YAAoC,CACpD,AAAgBC,SAA8B,CAChD,MAjEkBhF,WAAAA,gBACAmB,KAAAA,UACAC,QAAAA,aACAC,MAAAA,WACAC,WAAAA,gBACAX,KAAAA,UACAY,QAAAA,aACAC,UAAAA,eACAC,cAAAA,mBACAC,SAAAA,cACCrB,aAAAA,kBACDsB,YAAAA,iBACAC,cAAAA,mBACAC,cAAAA,mBACAC,SAAAA,cACAC,cAAAA,mBACAC,gBAAAA,qBACAC,eAAAA,oBACAC,iBAAAA,sBACAC,oBAAAA,yBACAC,iBAAAA,sBACAC,qBAAAA,0BACAC,oBAAAA,yBACAC,gBAAAA,qBACAC,YAAAA,iBACAC,cAAAA,mBACAC,sBAAAA,2BACAC,SAAAA,cACAC,aAAAA,kBACAC,yBAAAA,8BACAC,4BAAAA,iCACAC,oBAAAA,yBACAC,yBAAAA,8BACAC,yBAAAA,8BACAC,wBAAAA,6BACAC,aAAAA,kBACAC,eAAAA,oBACAC,oBAAAA,yBACAC,iBAAAA,sBACAC,cAAAA,mBACAC,sBAAAA,2BACAC,uBAAAA,4BACAC,yBAAAA,8BACAC,cAAAA,mBACAC,0BAAAA,+BACAC,uBAAAA,4BACAC,uBAAAA,4BACAC,wBAAAA,6BACAC,uBAAAA,4BACAC,qBAAAA,0BACAC,uBAAAA,4BACAC,uBAAAA,4BACAC,yBAAAA,8BACAC,0BAAAA,+BACAC,wBAAAA,6BACAC,mBAAAA,wBACAC,2BAAAA,gCACAC,SAAAA,cACAC,iBAAAA,sBACAC,eAAAA,oBACAC,aAAAA,kBACAC,eAAAA,oBACAC,cAAAA,mBACAC,aAAAA,kBACAC,UAAAA,SAKpB,CAqBJ"}