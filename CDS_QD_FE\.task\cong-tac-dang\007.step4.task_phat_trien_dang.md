# Quy trình xây dựng giao diện trang "Chi tiết Quy trình Phát triển Đảng viên"

---

**Trang: Chi tiết Quy trình Phát triển Đảng viên (`/${locale}/cong-tac-dang/phat-trien-dang-vien/chi-tiet/{idHoSoPhatTrien}`)**

**IV. <PERSON>hu vực "Chi tiết Bước Hiện tại/Đ<PERSON><PERSON><PERSON> chọn"**

**Bước 4: Đảng viên Chính thức Giới thiệu**

- **Tên Bước Hiển thị trên Stepper Chính:** `Giới thiệu vào Đảng`
- **Mục đích của Bước:** Quần chúng ưu tú (sau khi đã học lớp nhận thức) cần được ít nhất hai đảng viên chính thức đang cùng sinh hoạt trong một tổ chức cơ sở đảng (hoặc một đảng viên chính thức nếu người vào Đảng đang công tác trong lực lượng vũ trang hoặc ở những nơi chưa có đảng viên, chưa có chi bộ) giới thiệu vào Đảng. Bước này bao gồm việc chuẩn bị và nộp các giấy giới thiệu.
- **Trạng thái Quy trình (`DmTrangThaiPhatTrienDang.ID`) tương ứng:** Ví dụ ID cho "Chờ Giấy Giới thiệu" hoặc "Đang Thu thập Giấy Giới thiệu".

- **Hành động/Nội dung chính trong Bước 4:**

  1.  **Thông tin Đảng viên Giới thiệu:**
      - **Hiển thị/Cập nhật Đảng viên Hướng dẫn/Giới thiệu 1:**
        - Hiển thị tên Đảng viên từ `DangVien.HoVaTen` (liên kết qua `HoSoPhatTrienDangVien.NguoiHuongDan1ID`). _Trường này có thể đã được nhập từ Bước 1, ở bước này có thể cho phép xác nhận hoặc cập nhật nếu cần._
      - **Hiển thị/Cập nhật Đảng viên Hướng dẫn/Giới thiệu 2:**
        - Hiển thị tên Đảng viên từ `DangVien.HoVaTen` (liên kết qua `HoSoPhatTrienDangVien.NguoiHuongDan2ID`). _Tương tự như người hướng dẫn 1._
      - _(Lưu ý: Theo quy định, người giới thiệu phải là đảng viên chính thức và cùng công tác với người được giới thiệu ít nhất một năm. Hệ thống có thể cần kiểm tra điều kiện này nếu có đủ dữ liệu)._
  2.  **Ghi nhận việc hoàn thành thu thập các giấy giới thiệu:**
      - Có thể có các checkbox hoặc trạng thái cho từng giấy giới thiệu để theo dõi việc đã nộp.

- **Danh sách Tài liệu Yêu cầu/Đã nộp/Phát sinh (Fix cứng cho Bước 4):**

  1.  **Loại Tài liệu:** "Đơn xin vào Đảng (của quần chúng ưu tú)"
      - **Trạng thái:** "Chưa nộp" / "Đã nộp" (ngày nộp). _Tài liệu này có thể đã được yêu cầu từ các bước trước, ở đây là để rà soát và đảm bảo có._
      - **File đính kèm:** (Nếu đã nộp) Hiển thị tên file với nút "Xem/Tải".
      - **Hành động:** (Nếu chưa nộp và người dùng có quyền) Nút "Nộp Đơn xin vào Đảng".
  2.  **Loại Tài liệu:** "Lý lịch của người xin vào Đảng (Mẫu 2-KNĐ)"
      - **Trạng thái:** "Chưa nộp" / "Đã nộp" (ngày nộp). _Tương tự như Đơn xin vào Đảng._
      - **File đính kèm:** (Nếu đã nộp) Hiển thị tên file với nút "Xem/Tải".
      - **Hành động:** (Nếu chưa nộp và người dùng có quyền) Nút "Nộp Lý lịch".
  3.  **Loại Tài liệu:** "Giấy giới thiệu của Đảng viên chính thức thứ nhất"
      - **Trạng thái:** "Chưa nộp" / "Đã nộp" (ngày nộp).
      - **File đính kèm:** (Nếu đã nộp) Hiển thị tên file với nút "Xem/Tải".
      - **Hành động:** (Cho người quản lý hồ sơ) Nút "Nộp Giấy giới thiệu thứ nhất" (mở giao diện upload file, tự động gán `LoaiTaiLieuPhatTrienID`).
  4.  **Loại Tài liệu:** "Giấy giới thiệu của Đảng viên chính thức thứ hai" (nếu quy định yêu cầu 2 người)
      - **Trạng thái:** "Chưa nộp" / "Đã nộp" (ngày nộp).
      - **File đính kèm:** (Nếu đã nộp) Hiển thị tên file với nút "Xem/Tải".
      - **Hành động:** (Cho người quản lý hồ sơ) Nút "Nộp Giấy giới thiệu thứ hai".
  5.  **(Tùy chọn, nếu cần) Loại Tài liệu:** "Nghị quyết giới thiệu của tổ chức Đoàn Thanh niên hoặc Công đoàn cơ sở" (nếu người vào Đảng là đoàn viên thanh niên hoặc đoàn viên công đoàn).
      - **Trạng thái:** "Chưa nộp" / "Đã nộp" (ngày nộp).
      - **File đính kèm:** (Nếu đã nộp) Hiển thị tên file với nút "Xem/Tải".
      - **Hành động:** Nút "Nộp Nghị quyết giới thiệu (Đoàn/Công đoàn)".

- **Nút Hành động cho Bước 4 (Tùy theo vai trò người dùng và trạng thái hiện tại của hồ sơ):**
  - **Nút "Lưu thông tin Người giới thiệu & Trạng thái Tài liệu":**
    - **Hành động:** Lưu lại thông tin người giới thiệu (nếu có thay đổi) và trạng thái nộp các tài liệu.
    - **Logic:** Cập nhật bản ghi `HoSoPhatTrienDangVien` và các bản ghi `TaiLieuPhatTrienDangVien` tương ứng.
  - **Nút "Hoàn thành Thu thập Giấy giới thiệu & Chuyển Chi bộ Xem xét":**
    - **Điều kiện hiển thị:** Khi tất cả các tài liệu bắt buộc của Bước 4 (Đơn, Lý lịch, Giấy giới thiệu) đã được nộp.
    - **Hành động:**
      - Lưu lại thông tin (nếu có thay đổi chưa lưu).
      - Cập nhật `HoSoPhatTrienDangVien.TrangThaiQuyTrinhID` sang ID của bước "Chi bộ Xem xét, Đề nghị Kết nạp".
      - Hiển thị thông báo thành công.
      - Giao diện tự động chuyển sang hiển thị chi tiết của Bước 5.
      - (Có thể có logic gửi thông báo cho Bí thư Chi bộ về việc có hồ sơ cần xem xét).
  - **Nút "Yêu cầu Bổ sung/Làm lại Giấy tờ":**
    - **Điều kiện hiển thị:** Nếu các giấy tờ nộp lên có vấn đề.
    - **Hành động:** Mở dialog yêu cầu nhập nội dung cần bổ sung/làm lại, có thể giữ nguyên trạng thái "Chờ Giấy Giới thiệu" hoặc chuyển sang một trạng thái "Chờ Bổ sung Giấy tờ", thông báo cho người liên quan.

---

**II. Bảng Dữ liệu Liên quan Chính cho Bước 4:**

- **Bảng `HoSoPhatTrienDangVien`:**

  - `ID: number`
  - `TrangThaiQuyTrinhID: number` (Sẽ được cập nhật khi chuyển bước)
  - `NguoiHuongDan1ID: string` (Được xem là người giới thiệu thứ nhất)
  - `NguoiHuongDan2ID: string` (Được xem là người giới thiệu thứ hai)
  - (Các trường khác đã có)

- **Bảng `TaiLieuPhatTrienDangVien`:**

  - `HoSoPhatTrienID: number`
  - `LoaiTaiLieuPhatTrienID: number` (FK đến `DmLoaiTaiLieuPhatTrienDang` - ID của "Đơn xin vào Đảng", "Lý lịch của người xin vào Đảng", "Giấy giới thiệu của Đảng viên chính thức thứ nhất", "Giấy giới thiệu của Đảng viên chính thức thứ hai", "Nghị quyết giới thiệu của tổ chức Đoàn/Công đoàn")
  - `FileURL: string`
  - `TenTaiLieu: string`
  - `NgayNopTaiLieu: date`

- **Bảng `DangVien`:** (Để lấy thông tin người giới thiệu)

  - `MaDangVien: string`
  - `HoVaTen: string`
  - (Cần kiểm tra điều kiện là đảng viên chính thức và thời gian công tác nếu hệ thống có đủ dữ liệu).

- **Danh mục `DmLoaiTaiLieuPhatTrienDang`:** Cần có các loại tài liệu tương ứng với bước này.
- **Danh mục `DmTrangThaiPhatTrienDang`:** Cần có các trạng thái như "Chờ Giấy Giới thiệu", "Đang Thu thập Giấy Giới thiệu", "Đã đủ Giấy giới thiệu".

---
