# Quy trình xây dựng giao diện trang "Cuộ<PERSON> tra, <PERSON><PERSON><PERSON><PERSON> (KTGS)"

---

#### A. Chức năng trang `DanhSachCuocKTGSPage`

1.  **Chứ<PERSON> năng "Hiển thị Danh sách Các cuộc KTGS"**

    - **Tổng quan:** Trang hiển thị một bảng liệt kê các cuộc KTGS.
    - **Chi tiết:**
      - Thông tin được trình bày dưới dạng bảng, mỗi hàng là một cuộc KTGS.
      - **<PERSON><PERSON><PERSON> cột thông tin cho mỗi cuộc KTGS:** "Tên Cuộc KTGS", "Kế hoạch liên quan (nếu có)", "Trưởng đoàn", "Đối tượng KTGS (Đảng viên/Tổ chức Đảng)", "Thời gian thực hiện (Từ-Đến)", "<PERSON>r<PERSON><PERSON> thái Cuộc KTGS".
      - Hỗ tr<PERSON>, <PERSON><PERSON><PERSON> (<PERSON><PERSON>, Trưởng đ<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ng thái).
      - Phân trang.

2.  **Chức năng "Tạo Cuộc KTGS mới"**

    - **Tổng quan:** Cho phép người dùng (có quyền) khởi tạo một cuộc KTGS mới.
    - **Chi tiết:**
      - Nhấn nút "Tạo Cuộc KTGS mới".
      - Mở giao diện (form/dialog) để nhập thông tin ban đầu:
        - `KeHoachID`: (Tùy chọn) Lựa chọn Kế hoạch KTGS liên quan (từ danh sách kế hoạch đã duyệt).
        - `TenCuocKiemTra`: Tên/Nội dung chính của cuộc KTGS (bắt buộc).
        - `SoQuyetDinhThanhLapDoan`: Số Quyết định thành lập đoàn KTGS (nếu có).
        - `FileQuyetDinhThanhLapDoanURL`: Tải lên file Quyết định thành lập đoàn.
        - `NgayThanhLapDoan`: Ngày thành lập đoàn.
        - `TruongDoanID`: Lựa chọn Trưởng đoàn (là Đảng viên).
        - `DanhSachThanhVienDoan_Text`: Nhập danh sách thành viên đoàn (dạng text hoặc có thể là chọn nhiều Đảng viên).
        - `DoiTuongKiemTraDangVienID`: (Nếu đối tượng là Đảng viên) Lựa chọn Đảng viên bị KTGS.
        - `DoiTuongKiemTraToChucDangID`: (Nếu đối tượng là Tổ chức Đảng) Lựa chọn Tổ chức Đảng bị KTGS.
        - `NoiDungKiemTraGiamSat`: Nội dung kiểm tra, giám sát chi tiết (trường văn bản).
        - `ThoiGianThucHienTu`: Ngày bắt đầu thực hiện.
        - `ThoiGianThucHienDen`: Ngày dự kiến kết thúc.
        - `TrangThaiCuocKiemTraID`: Trạng thái ban đầu (ví dụ: Chuẩn bị, Đang tiến hành) - từ `DmTrangThaiCuocKiemTra`.
      - Nút "Lưu Cuộc KTGS".

3.  **Chức năng "Xem chi tiết Cuộc KTGS" (hành động trên dòng)**
    - **Tổng quan:** Điều hướng người dùng đến trang chi tiết của cuộc KTGS đã chọn.
    - **Chi tiết:** Khi nhấn "Xem chi tiết", hệ thống chuyển đến trang `/${locale}/cong-tac-dang/kiem-tra-giam-sat/chi-tiet-cuoc-ktgs/{idCuocKTGS}`.

---

#### B. Trang Chi tiết Cuộc Kiểm tra, Giám sát (`/.../chi-tiet-cuoc-ktgs/{idCuocKTGS}`)

- **Tên Trang:** `Chi tiết Cuộc Kiểm tra, Giám sát: [Tên Cuộc KTGS]`
- **Mục đích:** Hiển thị toàn bộ thông tin, diễn biến, tài liệu, kết luận và theo dõi sau KTGS của một cuộc KTGS cụ thể.
- **Bố cục:** Thông tin tổng quan ở trên, và các khu vực/tab chi tiết bên dưới.
- **Các khu vực/tab và chức năng chi tiết:**

  1.  **Khu vực "Thông tin Chung Cuộc KTGS":**

      - Hiển thị các thông tin đã nhập khi tạo/sửa cuộc KTGS (Tên, Kế hoạch liên quan, QĐ thành lập đoàn, Ngày thành lập, Trưởng đoàn, Danh sách TV, Đối tượng, Nội dung KTGS, Thời gian, Trạng thái).
      - Nút "Sửa thông tin Cuộc KTGS" (nếu được phép).

  2.  **Khu vực/Tab "Đoàn Kiểm tra, Giám sát":**

      - Hiển thị chi tiết `TruongDoanID` (Tên, Chức vụ Đảng, Đơn vị).
      - Hiển thị `DanhSachThanhVienDoan_Text` (Có thể cho phép định dạng hoặc quản lý danh sách thành viên chi tiết hơn nếu cần).

  3.  **Khu vực/Tab "Đối tượng Kiểm tra, Giám sát":**

      - Hiển thị chi tiết `DoiTuongKiemTraDangVienID` (Tên, Chức vụ Đảng, Đơn vị) hoặc `DoiTuongKiemTraToChucDangID` (Tên TCD, Cấp, Người đứng đầu).

  4.  **Khu vực/Tab "Tiến độ và Tài liệu Thu thập":**

      - **Ghi nhận Tiến độ:** Có thể có các mốc tiến độ hoặc trường văn bản để cập nhật tình hình thực hiện.
      - **Quản lý Tài liệu Thu thập (từ bảng `TaiLieuKiemTraGiamSat`):**
        - Hiển thị danh sách các tài liệu đã thu thập/lập trong quá trình KTGS (Tên tài liệu, Loại tài liệu, Ngày phát hành/thu thập, File đính kèm).
        - Nút "Thêm Tài liệu mới" (mở form/dialog upload file và nhập thông tin: `TenTaiLieu`, `LoaiTaiLieuKiemTraID`, `FileURL`, `NgayPhatHanhHoacThuThap`, `MoTaNgan`).
        - Hành động trên dòng tài liệu: Xem/Tải file, Sửa metadata, Xóa.

  5.  **Khu vực/Tab "Kết luận Kiểm tra, Giám sát":**

      - **Nhập/Hiển thị Thông tin Kết luận:**
        - `FileBaoCaoKetQuaURL`: Tải lên/Xem file Báo cáo Kết quả KTGS.
        - `FileKetLuanChinhThucURL`: Tải lên/Xem file Kết luận Chính thức của Đoàn/Cấp có thẩm quyền.
        - `NgayBanHanhKetLuan`: Ngày ban hành Kết luận.
        - Nội dung tóm tắt kết luận (nếu có trường riêng).
      - Cập nhật `TrangThaiCuocKiemTraID` thành "Đã có Kết luận" hoặc tương đương.

  6.  **Khu vực/Tab "Theo dõi Thực hiện Kết luận" (từ bảng `TheoDoiThucHienKetLuanKTGS`):**

      - **Mục đích:** Theo dõi việc khắc phục, sửa chữa của đối tượng được KTGS sau khi có kết luận.
      - **Hiển thị danh sách các nội dung cần thực hiện và tiến độ:**
        - **Các cột:** "Nội dung Kết luận/Kiến nghị cần thực hiện", "Đối tượng thực hiện (ĐV/TCĐ)", "Thời hạn Hoàn thành", "Ngày Hoàn thành Thực tế", "Kết quả Thực hiện", "File Minh chứng", "Trạng thái Thực hiện".
        - Nút "Thêm Nội dung Theo dõi mới" (nếu kết luận có nhiều điểm cần theo dõi riêng).
        - Hành động trên dòng: "Cập nhật Tiến độ/Kết quả" (mở form/dialog cho phép nhập `NgayHoanThanhThucTe`, `KetQuaThucHien`, upload `FileMinhChungThucHienURL`, cập nhật `TrangThaiThucHienID`).

  7.  **Nút hành động chung của Cuộc KTGS:** "Lưu thay đổi", "Kết thúc Cuộc KTGS" (cập nhật trạng thái).

---

#### C. Bảng dữ liệu được sử dụng trên các trang Kiểm tra, Giám sát

- **Bảng `KeHoachKiemTraGiamSat` (InspectionSupervisionPlans)** (Đã liệt kê ở mục I.B)

- **Bảng `CuocKiemTraGiamSat` (InspectionsSupervisions)**

  - `ID: number` (BIGINT, PK, AUTO_INCREMENT)
  - `KeHoachID: number` (BIGINT, FK NULL REFERENCES KeHoachKiemTraGiamSat(ID)) - Kế hoạch liên quan
  - `TenCuocKiemTra: string` (NVARCHAR(500) NOT NULL) - Tên/Nội dung cuộc KTGS
  - `SoQuyetDinhThanhLapDoan: string` (VARCHAR(50) NULL)
  - `FileQuyetDinhThanhLapDoanURL: string` (VARCHAR(255) NULL)
  - `NgayThanhLapDoan: date` (DATE NULL)
  - `TruongDoanID: string` (VARCHAR(20), FK NULL REFERENCES DangVien(MaDangVien)) - Trưởng đoàn
  - `DanhSachThanhVienDoan_Text: string` (NTEXT) - Danh sách thành viên (dạng text hoặc có thể tách bảng nếu cần quản lý chi tiết)
  - `DoiTuongKiemTraDangVienID: string` (VARCHAR(20), FK NULL REFERENCES DangVien(MaDangVien)) - Đối tượng là Đảng viên
  - `DoiTuongKiemTraToChucDangID: number` (BIGINT, FK NULL REFERENCES ToChucDang(ID)) - Đối tượng là Tổ chức Đảng
  - `NoiDungKiemTraGiamSat: string` (NTEXT NOT NULL) - Nội dung KTGS chi tiết
  - `ThoiGianThucHienTu: date` (DATE)
  - `ThoiGianThucHienDen: date` (DATE)
  - `FileBaoCaoKetQuaURL: string` (VARCHAR(255) NULL)
  - `FileKetLuanChinhThucURL: string` (VARCHAR(255) NULL)
  - `NgayBanHanhKetLuan: date` (DATE NULL)
  - `TrangThaiCuocKiemTraID: number` (INT, FK REFERENCES DmTrangThaiCuocKiemTra(ID)) - Trạng thái cuộc KTGS
  - `GhiChu: string` (NTEXT)

- **Bảng `TaiLieuKiemTraGiamSat` (InspectionSupervisionDocuments)**

  - `ID: number` (BIGINT, PK, AUTO_INCREMENT)
  - `CuocKiemTraGiamSatID: number` (BIGINT, NOT NULL, FK REFERENCES CuocKiemTraGiamSat(ID))
  - `TenTaiLieu: string` (NVARCHAR(255) NOT NULL)
  - `LoaiTaiLieuKiemTraID: number` (INT NOT NULL, FK REFERENCES DmLoaiTaiLieuKiemTra(ID))
  - `FileURL: string` (VARCHAR(255) NOT NULL) (Cần mã hóa nếu nhạy cảm)
  - `NgayPhatHanhHoacThuThap: date` (DATE NULL)
  - `MoTaNgan: string` (NTEXT)

- **Bảng `TheoDoiThucHienKetLuanKTGS` (PostInspectionActionTracking)**

  - `ID: number` (BIGINT, PK, AUTO_INCREMENT)
  - `CuocKiemTraGiamSatID: number` (BIGINT, NOT NULL, FK REFERENCES CuocKiemTraGiamSat(ID))
  - `NoiDungCanThucHien: string` (NVARCHAR(1000) NOT NULL) - Nội dung kết luận/kiến nghị cần đối tượng thực hiện
  - `DoiTuongThucHienDangVienID: string` (VARCHAR(20), FK NULL REFERENCES DangVien(MaDangVien))
  - `DoiTuongThucHienToChucDangID: number` (BIGINT, FK NULL REFERENCES ToChucDang(ID))
  - `ThoiHanHoanThanh: date` (DATE NULL)
  - `NgayHoanThanhThucTe: date` (DATE NULL)
  - `KetQuaThucHien: string` (NTEXT)
  - `FileMinhChungThucHienURL: string` (VARCHAR(255) NULL)
  - `TrangThaiThucHienID: number` (INT, FK REFERENCES DmTrangThaiThucHienKL(ID)) - Trạng thái thực hiện kết luận

- **Bảng `DangVien` (PartyMembers)** (Để chọn Trưởng đoàn, Thành viên, Đối tượng KTGS là Đảng viên)

  - `MaDangVien: string`
  - `HoVaTen: string`

- **Bảng `ToChucDang` (PartyOrganizations)** (Để chọn Đối tượng KTGS là Tổ chức Đảng)

  - `ID: number`
  - `TenToChucDang: string`

- **Danh mục `DmLoaiKeHoachKTGS`** (Đã liệt kê)
- **Danh mục `DmTrangThaiKeHoach`** (Đã liệt kê)
- **Danh mục `DmTrangThaiCuocKiemTra`**
  - `ID: number` (PK)
  - `TenTrangThai: string` (Ví dụ: Chuẩn bị, Đang tiến hành, Chờ Kết luận, Đã có Kết luận, Đang theo dõi thực hiện KL, Hoàn thành)
- **Danh mục `DmLoaiTaiLieuKiemTra`**
  - `ID: number` (PK)
  - `TenLoaiTaiLieu: string` (Ví dụ: Báo cáo của Đối tượng, Biên bản làm việc, Tài liệu minh chứng, Báo cáo kết quả của Đoàn, Kết luận chính thức)
- **Danh mục `DmTrangThaiThucHienKL`** (Kết luận)
  - `ID: number` (PK)
  - `TenTrangThai: string` (Ví dụ: Chưa thực hiện, Đang thực hiện, Đã hoàn thành, Chậm tiến độ)

---

#### D. Liên kết với page khác

1.  **Trang Chi tiết Hồ sơ Đảng viên / Tổ chức Đảng:**
    - Nếu một Đảng viên hoặc Tổ chức Đảng là đối tượng của một cuộc KTGS, thông tin về cuộc KTGS đó (và kết luận) có thể được hiển thị trong hồ sơ chi tiết của họ.
2.  **Module Kỷ luật Đảng:**
    - Nếu kết quả KTGS dẫn đến việc phải xem xét kỷ luật, thông tin từ cuộc KTGS (ví dụ `VuViecKiemTraID` trong bảng `KyLuatDang`) sẽ liên kết đến đây.

---
