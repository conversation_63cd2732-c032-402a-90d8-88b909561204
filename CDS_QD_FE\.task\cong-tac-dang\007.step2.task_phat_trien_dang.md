# Quy trình xây dựng giao diện trang "Chi tiết Quy trình Phát triển Đảng viên"

**Hãy tiếp tục với Bước 2 của <PERSON><PERSON>: "Thẩm tra lý lịch"**

- **Tên Bước Hiển thị trên Stepper Chính:** `Thẩm tra lý lịch`
- **Mục đích của Bước:** Chi bộ/tổ chức Đảng được giao nhiệm vụ tiến hành thẩm tra, xác minh các thông tin trong lý lịch của quần chúng ưu tú đã được tạo nguồ<PERSON>, đ<PERSON><PERSON> bảo tính chính xác và đúng đắn.
- **Trạng thá<PERSON>uy trình (`DmTrangThaiPhatTrienDang.ID`) tương ứng:** <PERSON><PERSON>ụ <PERSON> cho "Đ<PERSON> Thẩm tra Lý lịch" hoặc "Chờ Kết quả Thẩm tra".

- **<PERSON><PERSON>nh động/Nội dung chính trong Bước 2:**

  1.  **<PERSON>em lại thông tin Lý lịch đã khai (do quần chúng cung cấp hoặc từ hồ sơ quân nhân):**
      - Hiển thị các thông tin lý lịch đã có (từ Bước 1 hoặc từ tài liệu "Lý lịch người xin vào Đảng" nếu đã nộp).
  2.  **Tiến hành thẩm tra, xác minh:**
      - Đây là nghiệp vụ offline, nhưng hệ thống cần nơi để ghi nhận kết quả.
      - **Trường nhập liệu "Nội dung/Kết quả Thẩm tra Lý lịch":** Người được giao nhiệm vụ thẩm tra sẽ nhập tóm tắt quá trình và kết quả thẩm tra (ví dụ: các thông tin đã được xác minh, các điểm cần lưu ý, kết luận về lý lịch có đảm bảo hay không).
      - **Trường nhập liệu "Đề xuất của người/bộ phận thẩm tra":** (Ví dụ: "Đề nghị tiếp tục quy trình", "Đề nghị làm rõ thêm thông tin X, Y", "Lý lịch có vấn đề, đề nghị xem xét dừng").
  3.  **Đính kèm các Văn bản/Minh chứng Thẩm tra (nếu có):**
      - Cho phép upload các file liên quan đến quá trình thẩm tra (ví dụ: biên bản làm việc với địa phương, cơ quan cũ...).

- **Danh sách Tài liệu Yêu cầu/Đã nộp/Phát sinh (Fix cứng hoặc tùy chọn cho Bước 2):**

  1.  **Loại Tài liệu:** "Lý lịch của người xin vào Đảng (Mẫu 2-KNĐ hoặc tương đương)" (Đây là tài liệu quan trọng nhất để thẩm tra)
      - **Trạng thái:** "Chưa nộp" / "Đã nộp" (ngày nộp). _Tài liệu này có thể đã được yêu cầu từ Bước 1, hoặc là yêu cầu chính của Bước 2._
      - **File đính kèm:** (Nếu đã nộp) Hiển thị tên file với nút "Xem/Tải".
      - **Hành động:** (Nếu chưa nộp và người dùng có quyền) Nút "Nộp Lý lịch".
  2.  **Loại Tài liệu:** "Báo cáo/Kết luận Thẩm tra Lý lịch" (Do người thẩm tra lập và nộp)
      - **Trạng thái:** "Chưa nộp" / "Đã nộp" (ngày nộp).
      - **File đính kèm:** (Nếu đã nộp) Hiển thị tên file với nút "Xem/Tải".
      - **Hành động:** (Cho người được giao thẩm tra) Nút "Nộp Báo cáo Thẩm tra".
  3.  **(Tùy chọn) Loại Tài liệu:** "Các Văn bản Xác minh Khác"
      - Cho phép upload nhiều file minh chứng trong quá trình thẩm tra.

- **Nút Hành động cho Bước 2 (Tùy theo vai trò người dùng và kết quả thẩm tra):**
  - **Nút "Lưu Kết quả Thẩm tra":**
    - **Hành động:** Lưu lại các trường "Nội dung/Kết quả Thẩm tra Lý lịch", "Đề xuất của người/bộ phận thẩm tra".
  - **Nút "Hoàn thành Thẩm tra & Chuyển sang Bồi dưỡng Nhận thức":**
    - **Điều kiện hiển thị:** Khi kết quả thẩm tra là tích cực và các tài liệu cần thiết đã được nộp.
    - **Hành động:**
      - Lưu lại thông tin (nếu có thay đổi).
      - Cập nhật `HoSoPhatTrienDangVien.TrangThaiQuyTrinhID` sang ID của bước "Học nhận thức về Đảng".
      - Hiển thị thông báo thành công.
      - Giao diện tự động chuyển sang hiển thị chi tiết của Bước 3.
  - **Nút "Yêu cầu Làm rõ/Bổ sung Lý lịch":**
    - **Điều kiện hiển thị:** Nếu kết quả thẩm tra cho thấy cần làm rõ thêm.
    - **Hành động:** Mở dialog yêu cầu nhập nội dung cần làm rõ, có thể trả quy trình về trạng thái "Chờ Bổ sung Lý lịch" hoặc một trạng thái tương tự, thông báo cho người tạo hồ sơ/quần chúng.
  - **Nút "Đề nghị Dừng Quy trình (Do vấn đề Lý lịch)":**
    - **Điều kiện hiển thị:** Nếu kết quả thẩm tra phát hiện vấn đề nghiêm trọng.
    - **Hành động:** Mở dialog yêu cầu nhập lý do, cập nhật trạng thái hồ sơ thành "Tạm dừng/Đã trả lại (Lý do lý lịch)", thông báo cho các bên liên quan.

---

**Bảng Dữ liệu Liên quan Chính cho Bước 2 (Ngoài các bảng đã nêu ở Bước 1):**

- **Bảng `HoSoPhatTrienDangVien`:** (Cập nhật các trường liên quan đến kết quả thẩm tra nếu có, và `TrangThaiQuyTrinhID`)
- **Bảng `TaiLieuPhatTrienDangVien`:**

  - Lưu các tài liệu như "Lý lịch của người xin vào Đảng", "Báo cáo/Kết luận Thẩm tra Lý lịch", "Các Văn bản Xác minh Khác". Các trường cần thiết:
    - `HoSoPhatTrienID: number`
    - `LoaiTaiLieuPhatTrienID: number` (FK đến `DmLoaiTaiLieuPhatTrienDang` - ID của "Lý lịch người xin vào Đảng", "Báo cáo thẩm tra"...)
    - `FileURL: string`
    - `TenTaiLieu: string`
    - `NgayNopTaiLieu: date`

- **Danh mục `DmLoaiTaiLieuPhatTrienDang`:** Cần có các loại tài liệu tương ứng với bước này.
- **Danh mục `DmTrangThaiPhatTrienDang`:** Cần có các trạng thái như "Đang Thẩm tra Lý lịch", "Chờ Kết quả Thẩm tra", "Cần Bổ sung Lý lịch (sau thẩm tra)".

---
