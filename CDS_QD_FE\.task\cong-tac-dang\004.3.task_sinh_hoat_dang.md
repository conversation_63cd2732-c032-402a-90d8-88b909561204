# Quy trình xây dựng giao diện trang "Quản lý <PERSON>hị quyết Đảng"

---

**Trang: Quản lý Nghị quyết Đảng (`/${locale}/cong-tac-dang/sinh-hoat-dang/nghi-quyet`)**

- **<PERSON><PERSON><PERSON> (Tiêu đề hiển thị):** `<PERSON>h sách Nghị quyết <PERSON>ả<PERSON>` (cho trang danh sách) và `Ban hành Nghị quyết Đảng mới` / `Chỉnh sửa Thông tin Nghị quyết: [Số Nghị quyết]` (cho trang/giao diện tạo/sửa).
- **<PERSON><PERSON><PERSON> đích chính của Trang/Các Trang:** Cho phép người dùng có thẩm quyền quản lý vòng đời của các <PERSON>h<PERSON> quyết <PERSON>, từ việ<PERSON> ban hành, l<PERSON><PERSON> tr<PERSON>, tra c<PERSON><PERSON>, đến vi<PERSON>c cập nhật thông tin mô tả (metadata) và quản lý file nội dung.

---

#### I. <PERSON><PERSON><PERSON> năng trang `DanhSachNghiQuyetDangPage` (Trang Danh sách Nghị quyết Đảng)

1.  **Chức năng "Hiển thị Danh sách Nghị quyết"**

    - **Tổng quan:** Trang chính hiển thị một bảng liệt kê các nghị quyết đã được ban hành, cho phép người dùng xem tổng quan và truy cập vào từng nghị quyết cụ thể.
    - **Chi tiết:**
      - Khi người dùng truy cập trang, hệ thống sẽ truy vấn và hiển thị danh sách các nghị quyết Đảng dựa trên quyền hạn và phạm vi quản lý của người dùng.
      - Thông tin mỗi nghị quyết được trình bày trên một hàng của bảng.
      - Nếu có nhiều nghị quyết, hệ thống sẽ hỗ trợ phân trang để người dùng dễ dàng duyệt qua.

2.  **Chức năng "Tìm kiếm và Lọc Nghị quyết"**

    - **Tổng quan:** Cung cấp các công cụ để người dùng có thể nhanh chóng tìm kiếm hoặc lọc danh sách nghị quyết theo các tiêu chí khác nhau.
    - **Chi tiết:**
      - **Tìm kiếm theo từ khóa:** Một ô nhập liệu cho phép người dùng nhập Số hiệu Nghị quyết hoặc các từ khóa trong Trích yếu Nội dung để tìm kiếm.
      - **Lọc theo "Tổ chức Đảng Ban hành":** Một trường lựa chọn (có thể là danh sách thả xuống hoặc dạng cây nếu tổ chức Đảng có nhiều cấp) cho phép chọn một hoặc nhiều Tổ chức Đảng đã ban hành nghị quyết. Dữ liệu lấy từ bảng `ToChucDang`.
      - **Lọc theo "Ngày Ban hành":** Hai ô chọn ngày ("Từ ngày" và "Đến ngày") để lọc các nghị quyết được ban hành trong một khoảng thời gian nhất định.
      - **Lọc theo "Độ mật":** Một trường lựa chọn các cấp độ mật (ví dụ: Tuyệt mật, Tối mật, Mật, Phổ biến). Dữ liệu lấy từ danh mục `DmDoMat`.
      - **(Tùy chọn) Lọc theo "Buổi Sinh hoạt liên quan":** Nếu hệ thống cho phép liên kết nghị quyết với buổi sinh hoạt, có thể có bộ lọc để chọn các buổi sinh hoạt đã diễn ra.
      - **Nút "Áp dụng" / "Tìm kiếm":** Thực thi các tiêu chí tìm kiếm và lọc đã chọn.
      - **Nút "Đặt lại" / "Xóa bộ lọc":** Xóa các tiêu chí đang áp dụng và hiển thị lại danh sách đầy đủ.

3.  **Chức năng "Ban hành Nghị quyết mới"**

    - **Tổng quan:** Cung cấp một nút bấm để người dùng (có quyền) bắt đầu quy trình tạo một nghị quyết Đảng mới.
    - **Chi tiết:**
      - Trên trang danh sách, có một nút "Ban hành Nghị quyết mới".
      - Khi nhấn nút này, hệ thống sẽ mở ra một giao diện (có thể là một trang riêng hoặc một cửa sổ/dialog lớn) cho phép người dùng nhập thông tin và tải lên file nghị quyết.

4.  **Chức năng "Xem chi tiết Thông tin Nghị quyết" (hành động trên dòng)**

    - **Tổng quan:** Cho phép người dùng xem các thông tin mô tả (metadata) của một nghị quyết cụ thể từ danh sách.
    - **Chi tiết:**
      - Mỗi hàng trong bảng danh sách nghị quyết sẽ có một hành động "Xem chi tiết" (nút bấm hoặc biểu tượng).
      - Khi nhấn vào, một cửa sổ/dialog hoặc một vùng hiển thị sẽ xuất hiện, trình bày các thông tin như Số hiệu, Ngày ban hành, Trích yếu, Tổ chức ban hành, Độ mật, Phạm vi phổ biến. Chức năng này chủ yếu để xem nhanh metadata.

5.  **Chức năng "Xem/Tải file Nội dung Nghị quyết" (hành động trên dòng)**

    - **Tổng quan:** Cho phép người dùng truy cập vào nội dung đầy đủ của file nghị quyết.
    - **Chi tiết:**
      - Mỗi hàng trong bảng danh sách sẽ có hành động "Xem file" và/hoặc "Tải file" (nút bấm hoặc biểu tượng).
      - **"Xem file":** Nếu file là định dạng có thể xem trực tiếp trên trình duyệt (ví dụ PDF), hệ thống sẽ mở file trong một tab mới hoặc một trình xem tích hợp.
      - **"Tải file":** Hệ thống sẽ cho phép người dùng tải file nghị quyết về máy.
      - **Kiểm soát quyền truy cập:** Cả hai hành động này phải kiểm tra quyền của người dùng dựa trên `DoMatID` của nghị quyết. Nếu file được mã hóa, hệ thống cần giải mã trước khi cho xem/tải.

6.  **Chức năng "Sửa Thông tin (Metadata) Nghị quyết" (hành động trên dòng)**

    - **Tổng quan:** Cho phép người dùng (có quyền) chỉnh sửa các thông tin mô tả của một nghị quyết đã ban hành. Lưu ý: việc này thường không bao gồm sửa nội dung file nghị quyết gốc mà là cập nhật các thông tin đi kèm hoặc tải lên một phiên bản file mới nếu quy trình cho phép.
    - **Chi tiết:**
      - Mỗi hàng trong bảng danh sách sẽ có hành động "Sửa" (nút bấm hoặc biểu tượng).
      - Khi nhấn vào, hệ thống sẽ mở giao diện tạo/sửa nghị quyết, với các trường thông tin được điền sẵn dữ liệu của nghị quyết đang chọn. Người dùng có thể thay đổi Trích yếu, Phạm vi phổ biến, Độ mật (nếu được phép), hoặc tải lên file nghị quyết phiên bản mới.

7.  **Chức năng "Xóa Nghị quyết" (hành động trên dòng)**
    - **Tổng quan:** Cho phép người dùng (có quyền hạn rất cao) xóa một nghị quyết khỏi hệ thống.
    - **Chi tiết:**
      - Mỗi hàng sẽ có hành động "Xóa" (nút bấm hoặc biểu tượng).
      - Cần có một hộp thoại xác nhận rõ ràng về hậu quả của việc xóa nghị quyết.
      - Việc xóa có thể là "xóa mềm" (đánh dấu là không còn hiệu lực, không hiển thị trong danh sách mặc định) thay vì "xóa cứng" khỏi cơ sở dữ liệu, để đảm bảo khả năng truy vết và phục hồi nếu cần.

---

#### II. Chức năng Trang/Dialog Tạo mới hoặc Sửa thông tin Nghị quyết Đảng

- **Mục đích:** Cung cấp một form集 trung để người dùng nhập các thông tin cần thiết khi ban hành một nghị quyết mới, hoặc chỉnh sửa thông tin của một nghị quyết đã có.

1.  **Chức năng "Nhập thông tin Nghị quyết"**

    - **Tổng quan:** Người dùng điền các thông tin mô tả và nội dung cho nghị quyết.
    - **Chi tiết - Các trường thông tin trên form:**
      - **Số hiệu Nghị quyết (`SoNghiQuyet: string`):** Ô nhập liệu văn bản. Bắt buộc. Có thể có kiểm tra định dạng hoặc tính duy nhất (trong phạm vi tổ chức ban hành & năm).
      - **Ngày Ban hành (`NgayBanHanh: date`):** Ô chọn ngày. Bắt buộc.
      - **Trích yếu Nội dung (`TrichYeuNoiDung: string`):** Ô nhập liệu văn bản nhiều dòng (textarea) hoặc trình soạn thảo văn bản đơn giản. Bắt buộc.
      - **Tổ chức Đảng Ban hành (`ToChucDangBanHanhID: number`):** Trường lựa chọn từ danh sách các Tổ chức Đảng mà người dùng có quyền đại diện ban hành. Bắt buộc.
      - **Buổi Sinh hoạt liên quan (nếu có) (`BuoiSinhHoatID: number`):** Trường lựa chọn (tùy chọn) từ danh sách các buổi sinh hoạt đã kết thúc của Tổ chức Đảng đã chọn ở trên.
      - **Độ mật (`DoMatID: number`):** Trường lựa chọn từ danh sách các cấp độ mật (Tuyệt mật, Tối mật, Mật, Phổ biến). Bắt buộc.
      - **Phạm vi Phổ biến (`PhamViPhoBien: string`):** Ô nhập liệu văn bản nhiều dòng (textarea) để mô tả phạm vi nghị quyết này được phổ biến đến đâu.
      - **Tải lên File Nghị quyết (`FileNghiQuyetURL: file`):** Một trường cho phép người dùng chọn và tải lên file văn bản chứa nội dung đầy đủ của nghị quyết (thường là PDF, DOCX).
        - Khi tạo mới: Bắt buộc phải có file.
        - Khi sửa: Hiển thị tên file hiện tại (nếu có), cho phép tải lên file mới để thay thế. Có thể có tùy chọn xóa file hiện tại (nếu logic cho phép nghị quyết không có file, nhưng thường là không).
        - Hệ thống cần xử lý việc lưu trữ file an toàn, có thể mã hóa dựa trên `DoMatID`.

2.  **Chức năng "Lưu và Ban hành / Lưu thay đổi"**

    - **Tổng quan:** Nút bấm để xác nhận việc tạo mới hoặc cập nhật thông tin nghị quyết.
    - **Chi tiết:**
      - Trước khi lưu, hệ thống thực hiện kiểm tra tính hợp lệ của tất cả các trường đã nhập (ví dụ: các trường bắt buộc không được trống, định dạng ngày tháng, file đã được tải lên khi tạo mới).
      - Nếu hợp lệ:
        - Khi tạo mới: Lưu thông tin nghị quyết vào bảng `NghiQuyetDang`, lưu file đã tải lên vào hệ thống lưu trữ file.
        - Khi sửa: Cập nhật các trường thông tin trong bảng `NghiQuyetDang`, nếu có file mới thì thay thế file cũ.
        - Ghi nhận hành động vào nhật ký hệ thống (Audit Log).
        - Hiển thị thông báo thành công và có thể điều hướng người dùng về lại trang danh sách nghị quyết.
      - Nếu không hợp lệ: Hiển thị thông báo lỗi chi tiết ngay trên form.

3.  **Chức năng "Hủy bỏ"**
    - **Tổng quan:** Nút bấm để hủy bỏ thao tác tạo mới hoặc sửa đổi.
    - **Chi tiết:**
      - Khi nhấn nút này, các thông tin đã nhập sẽ không được lưu.
      - Điều hướng người dùng về lại trang danh sách nghị quyết.
      - Có thể có hộp thoại xác nhận nếu người dùng đã nhập liệu.

---

#### III. Bảng dữ liệu được sử dụng trên các trang liên quan đến Nghị quyết Đảng

- **Bảng `NghiQuyetDang` (PartyResolutions)**

  - `ID: number` (BIGINT, PK, AUTO_INCREMENT)
  - `SoNghiQuyet: string` (VARCHAR(50), NOT NULL, UNIQUE)
  - `NgayBanHanh: date` (DATE, NOT NULL)
  - `TrichYeuNoiDung: string` (NVARCHAR(1000), NOT NULL)
  - `BuoiSinhHoatID: number` (BIGINT, FK NULL REFERENCES BuoiSinhHoatDang(ID))
  - `ToChucDangBanHanhID: number` (BIGINT, NOT NULL, FK REFERENCES ToChucDang(ID))
  - `FileNghiQuyetURL: string` (VARCHAR(255), NOT NULL)
  - `DoMatID: number` (INT, FK REFERENCES DmDoMat(ID))
  - `PhamViPhoBien: string` (NTEXT)

- **Bảng `ToChucDang` (PartyOrganizations)** (Dùng cho việc chọn Tổ chức Đảng ban hành và lọc)

  - `ID: number` (BIGINT, PK)
  - `TenToChucDang: string` (NVARCHAR(255), NOT NULL)
  - (Các trường khác để hỗ trợ hiển thị dạng cây nếu cần)

- **Bảng `BuoiSinhHoatDang` (PartyMeetings)** (Dùng cho việc liên kết Nghị quyết với một buổi sinh hoạt cụ thể)

  - `ID: number` (BIGINT, PK)
  - `NoiDungChinhDuKien: string` (NTEXT) (Hoặc một trường khác để định danh buổi họp, ví dụ `TenKeHoach` từ `KeHoachSinhHoatDang` nếu buổi họp được tạo từ kế hoạch)
  - `ThoiGianBatDau: datetime`
  - `ToChucDangID: number` (Để lọc buổi họp theo tổ chức Đảng)

- **Danh mục `DmDoMat`** (Dùng cho việc chọn và hiển thị Độ mật)
  - `ID: number` (PK)
  - `TenDoMat: string` (Ví dụ: Tuyệt mật, Tối mật, Mật, Phổ biến)

---

#### IV. Liên kết với page khác

1.  **Trang Chi tiết Buổi Sinh hoạt Đảng:**
    - Từ trang chi tiết một buổi sinh hoạt, có thể có chức năng "Ban hành Nghị quyết" liên quan đến buổi sinh hoạt đó, điều này sẽ mở giao diện tạo Nghị quyết với trường `BuoiSinhHoatID` được điền sẵn.
    - Trong chi tiết buổi sinh hoạt cũng có thể hiển thị danh sách các nghị quyết đã được ban hành từ buổi sinh hoạt đó, mỗi nghị quyết là một liên kết đến chức năng "Xem chi tiết Thông tin Nghị quyết" hoặc "Xem/Tải file Nội dung Nghị quyết".
2.  **Thư viện Văn kiện Đảng / Tài liệu Chính trị (các module khác):**
    - Sau khi một Nghị quyết Đảng được ban hành, nó có thể được xem xét để đưa vào thư viện tài liệu chung của Đảng hoặc thư viện tài liệu chính trị (nếu nội dung phù hợp). Điều này có thể là một quy trình thủ công hoặc bán tự động, và cần cơ chế phân quyền truy cập dựa trên `DoMatID` của nghị quyết.

---
