import apiClient from '../index'
import type { ApiResponse, PaginatedResponse } from '../types'

export const customInstance = <T>({
  url,
  method,
  params,
  data,
  headers,
  signal
}: {
  url: string
  method: 'get' | 'post' | 'put' | 'delete' | 'patch' | 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  params?: any
  data?: any
  headers?: any
  signal?: AbortSignal
}): Promise<T> => {
  // Chuyển đổi method về chữ thường để phù hợp với axios
  const normalizedMethod = method.toLowerCase() as 'get' | 'post' | 'put' | 'delete' | 'patch'

  return apiClient({
    url,
    method: normalizedMethod,
    params,
    data,
    headers,
    signal
  }) as Promise<T>
}

// Helper functions để tạo các kiểu dữ liệu cụ thể
export const createApiResponse = <T>(data: T): ApiResponse<T> => {
  return { data }
}

export const createPaginatedResponse = <T>(
  data: T[],
  pagination: PaginatedResponse<T>['pagination']
): PaginatedResponse<T> => {
  return { data, pagination }
}
