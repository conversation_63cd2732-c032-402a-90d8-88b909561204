/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { ThamGiaGiaoDucChinhTriEntity } from '~/database/typeorm/entities/thamGiaGiaoDucChinhTri.entity';

@Injectable()
export class ThamGiaGiaoDucChinhTriRepository extends Repository<ThamGiaGiaoDucChinhTriEntity> {
    constructor(private dataSource: DataSource) {
        super(ThamGiaGiaoDucChinhTriEntity, dataSource.createEntityManager());
    }
}
