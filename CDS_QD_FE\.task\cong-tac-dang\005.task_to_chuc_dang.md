Tuyệt vời! Chúng ta sẽ đi vào chi tiết mô tả cho **Trang Quản lý Tổ chức Đảng**.

---

**Trang: Quản lý Tổ chức <PERSON> (`/${locale}/cong-tac-dang/to-chuc-dang`)**

- **<PERSON><PERSON><PERSON> (Tiêu đề hiển thị):** `<PERSON>uản lý Cơ cấu Tổ chức Đảng`
- **<PERSON><PERSON><PERSON> đích chính của Trang:** Cho phép người dùng (có quyề<PERSON>) xem, tạo mới, cập nhật thông tin của các Tổ chức <PERSON> (Chi bộ, Đảng bộ cơ sở, <PERSON><PERSON><PERSON> bộ cấp trên cơ sở...) và quản lý thông tin cấp ủy của từng tổ chức theo nhiệm kỳ.

---

#### I. <PERSON><PERSON><PERSON> năng trang `QuanLyToChucDangPage`

1.  **<PERSON><PERSON><PERSON> năng "Hiển thị Cây Cơ cấu Tổ chức Đảng"**

    - **Tổng quan:** <PERSON><PERSON> hiển thị một cấu trúc dạng cây trực quan hóa hệ thống các Tổ chức Đảng, từ cấp cao nhất xuống các cấp thấp hơn.
    - **Chi tiết:**
      - Khi tải trang, hệ thống truy vấn và xây dựng cây cơ cấu Tổ chức Đảng dựa trên mối quan hệ cha-con (trường `ToChucDangCapTrenID`).
      - Mỗi nút trên cây đại diện cho một Tổ chức Đảng, hiển thị tên của Tổ chức Đảng đó (`TenToChucDang`).
      - Người dùng có thể mở rộng (expand) hoặc thu gọn (collapse) các nút cha để xem các Tổ chức Đảng cấp dưới.
      - Khi người dùng chọn (nhấn vào) một nút Tổ chức Đảng trên cây, thông tin chi tiết của Tổ chức Đảng đó và khu vực quản lý cấp ủy sẽ được hiển thị ở một khu vực riêng trên trang.

2.  **Chức năng "Hiển thị Thông tin Chi tiết của một Tổ chức Đảng" (Khi chọn một nút trên cây)**

    - **Tổng quan:** Khi một Tổ chức Đảng được chọn từ cây cơ cấu, một khu vực trên trang sẽ hiển thị các thông tin chi tiết liên quan đến Tổ chức Đảng đó.
    - **Chi tiết - Các thông tin hiển thị (read-only):**
      - `MaToChucDang`: Mã Tổ chức Đảng.
      - `TenToChucDang`: Tên Tổ chức Đảng.
      - `CapToChucDangID`: Cấp Tổ chức Đảng (Hiển thị tên từ danh mục `DmCapToChucDang`).
      - `NgayThanhLap`: Ngày thành lập.
      - `DiaChiDonViChuQuan`: Địa chỉ/Đơn vị chủ quản.
      - `ToChucDangCapTrenID`: Tổ chức Đảng cấp trên trực thuộc (Hiển thị tên).
      - `SoLuongDangVien`: Số lượng đảng viên hiện tại (có thể được tính toán tự động từ số đảng viên đang sinh hoạt tại Tổ chức Đảng này và các Tổ chức Đảng cấp dưới trực thuộc, hoặc là một trường được cập nhật định kỳ).
      - `TrangThaiHoatDongID`: Trạng thái hoạt động (Hiển thị tên từ danh mục `DmTrangThaiHoatDongTCD` - ví dụ: Đang hoạt động, Giải thể, Sáp nhập).
      - `NgayGiaiTheSapNhap`: Ngày giải thể/sáp nhập (nếu có).
      - `GhiChu`: Ghi chú thêm.
    - **Nút "Sửa thông tin Tổ chức Đảng":**
      - **Chức năng:** Mở ra một giao diện (thường là một cửa sổ/dialog) cho phép chỉnh sửa các thông tin trên của Tổ chức Đảng đang chọn (nếu người dùng có quyền).
      - **Form sửa bao gồm các trường:** Tương tự như các trường hiển thị ở trên, trừ `MaToChucDang` (thường không đổi) và `SoLuongDangVien` (thường không nhập tay). `ToChucDangCapTrenID` có thể cho phép thay đổi nếu có nghiệp vụ điều chuyển.
      - Nút "Lưu thay đổi" và "Hủy".

3.  **Chức năng "Quản lý Cấp ủy theo Nhiệm kỳ" (Khi chọn một nút trên cây và hiển thị thông tin chi tiết)**

    - **Tổng quan:** Trong khu vực thông tin chi tiết của Tổ chức Đảng đã chọn, có một phần riêng để quản lý các nhiệm kỳ cấp ủy và danh sách cấp ủy viên của từng nhiệm kỳ.
    - **Chi tiết:**
      - **Hiển thị danh sách các Nhiệm kỳ Cấp ủy (từ bảng `CapUyNhiemKy` liên quan đến `ToChucDangID` đang chọn):**
        - Thông tin hiển thị cho mỗi nhiệm kỳ: `NhiemKyBatDau` (ví dụ: "2020-2025"), `NhiemKyKetThucDuKien`, `NgayBauCu`, liên kết xem/tải `FileQuyetDinhChuanYKQBauCuURL`.
        - Hành động trên mỗi nhiệm kỳ: "Xem/Sửa Cấp ủy viên", "Sửa thông tin Nhiệm kỳ", "Xóa Nhiệm kỳ" (cần kiểm soát chặt chẽ).
      - **Nút "Thêm Nhiệm kỳ Cấp ủy mới":**
        - **Chức năng:** Mở giao diện (cửa sổ/dialog) để tạo một nhiệm kỳ cấp ủy mới cho Tổ chức Đảng đang chọn.
        - **Form tạo mới Nhiệm kỳ bao gồm các trường:**
          - `ToChucDangID` (ẩn, lấy từ TCD đang chọn).
          - `NhiemKyBatDau`: Nhập chuỗi định dạng nhiệm kỳ (ví dụ: "2025-2030").
          - `NhiemKyKetThucDuKien`: Chọn ngày dự kiến kết thúc nhiệm kỳ.
          - `NgayBauCu`: Chọn ngày bầu cử cấp ủy.
          - `FileQuyetDinhChuanYKQBauCuURL`: Cho phép tải lên file Quyết định chuẩn y kết quả bầu cử.
        - Nút "Lưu Nhiệm kỳ" và "Hủy".
      - **Khi chọn một Nhiệm kỳ cụ thể từ danh sách nhiệm kỳ (hoặc mặc định hiển thị nhiệm kỳ hiện tại):**
        - **Hiển thị danh sách Cấp ủy viên của Nhiệm kỳ đó (từ bảng `ThanhVienCapUy` liên quan đến `CapUyNhiemKyID` đang chọn):**
          - Thông tin hiển thị cho mỗi cấp ủy viên: `DangVienID` (Hiển thị Họ tên Đảng viên), `ChucVuTrongCapUyID` (Hiển thị Tên Chức vụ trong cấp ủy từ `DmChucVuTrongCapUy`), `NgayBatDauDamNhiem`, `NgayKetThucDamNhiem`, `IsConTrongCapUy` (Còn trong cấp ủy: Có/Không).
          - Hành động trên mỗi cấp ủy viên: "Sửa thông tin", "Xóa khỏi Cấp ủy".
        - **Nút "Thêm Cấp ủy viên mới" (vào nhiệm kỳ đang chọn):**
          - **Chức năng:** Mở giao diện (cửa sổ/dialog) để thêm một đảng viên vào cấp ủy của nhiệm kỳ hiện tại.
          - **Form thêm Cấp ủy viên bao gồm các trường:**
            - `CapUyNhiemKyID` (ẩn, lấy từ nhiệm kỳ đang chọn).
            - `DangVienID`: Lựa chọn Đảng viên (từ danh sách đảng viên thuộc Tổ chức Đảng đó hoặc phạm vi rộng hơn tùy quy định).
            - `ChucVuTrongCapUyID`: Lựa chọn Chức vụ trong cấp ủy (Bí thư, Phó Bí thư, Ủy viên...) từ danh mục `DmChucVuTrongCapUy`.
            - `NgayBatDauDamNhiem`: Chọn ngày bắt đầu đảm nhiệm.
            - `NgayKetThucDamNhiem`: (Tùy chọn) Chọn ngày kết thúc đảm nhiệm.
            - `IsConTrongCapUy`: (Mặc định là TRUE - Có).
          - Nút "Lưu Cấp ủy viên" và "Hủy".

4.  **Chức năng "Thêm Tổ chức Đảng mới"**

    - **Tổng quan:** Cho phép người dùng (có quyền) tạo một Tổ chức Đảng mới trong cơ cấu.
    - **Chi tiết:**
      - Trên giao diện quản lý cây, có một nút "Thêm Tổ chức Đảng mới".
      - Khi nhấn nút, mở giao diện (cửa sổ/dialog) để nhập thông tin.
      - **Form tạo mới Tổ chức Đảng bao gồm các trường:**
        - `MaToChucDang`: Mã Tổ chức Đảng (có thể tự sinh hoặc yêu cầu nhập, cần unique).
        - `TenToChucDang`: Tên Tổ chức Đảng (bắt buộc).
        - `CapToChucDangID`: Lựa chọn Cấp Tổ chức Đảng (bắt buộc) từ danh mục `DmCapToChucDang`.
        - `NgayThanhLap`: Chọn Ngày thành lập.
        - `DiaChiDonViChuQuan`: Nhập địa chỉ/đơn vị chủ quản.
        - `ToChucDangCapTrenID`: Lựa chọn Tổ chức Đảng cấp trên trực thuộc (từ cây cơ cấu hiện tại, hoặc để trống nếu là cấp cao nhất người dùng được quản lý).
        - `TrangThaiHoatDongID`: Lựa chọn Trạng thái hoạt động (thường mặc định là "Đang hoạt động") từ danh mục `DmTrangThaiHoatDongTCD`.
        - `GhiChu`: Ghi chú thêm.
      - Nút "Lưu Tổ chức Đảng" và "Hủy".
      - Sau khi lưu, cây cơ cấu Tổ chức Đảng trên giao diện sẽ được cập nhật.

5.  **Chức năng "Sáp nhập/Giải thể Tổ chức Đảng" (Chức năng phức tạp, tùy chọn)**
    - **Tổng quan:** Cung cấp công cụ để thực hiện các nghiệp vụ sáp nhập hoặc giải thể Tổ chức Đảng. Đây là những nghiệp vụ cần quy trình chặt chẽ và phân quyền cao.
    - **Chi tiết (ở mức ý tưởng):**
      - **Sáp nhập:** Chọn Tổ chức Đảng nguồn và Tổ chức Đảng đích. Hệ thống cần xử lý việc chuyển đảng viên, cập nhật lại cơ cấu, lịch sử.
      - **Giải thể:** Chọn Tổ chức Đảng cần giải thể. Cần có lý do, quyết định giải thể. Hệ thống cần xử lý việc chuyển đảng viên (nếu có), cập nhật trạng thái Tổ chức Đảng thành "Đã giải thể" và `NgayGiaiTheSapNhap`.
      - Các chức năng này thường không đơn giản là một form mà là một quy trình gồm nhiều bước, có thể cần các trang hoặc luồng xử lý riêng.

---

#### II. Bảng dữ liệu được sử dụng trên trang `QuanLyToChucDangPage`

- **Bảng `ToChucDang` (PartyOrganizations)**

  - `ID: number` (BIGINT, PK, AUTO_INCREMENT)
  - `MaToChucDang: string` (VARCHAR(50), UNIQUE)
  - `TenToChucDang: string` (NVARCHAR(255), NOT NULL)
  - `CapToChucDangID: number` (INT, NOT NULL, FK REFERENCES DmCapToChucDang(ID))
  - `NgayThanhLap: date` (DATE)
  - `DiaChiDonViChuQuan: string` (NVARCHAR(500))
  - `ToChucDangCapTrenID: number` (BIGINT, FK NULL REFERENCES ToChucDang(ID))
  - `SoLuongDangVien: number` (INT, DEFAULT 0)
  - `TrangThaiHoatDongID: number` (INT, FK REFERENCES DmTrangThaiHoatDongTCD(ID))
  - `NgayGiaiTheSapNhap: date` (DATE NULL)
  - `GhiChu: string` (NTEXT)

- **Bảng `CapUyNhiemKy` (PartyCommitteeTerms)**

  - `ID: number` (BIGINT, PK, AUTO_INCREMENT)
  - `ToChucDangID: number` (BIGINT, NOT NULL, FK REFERENCES ToChucDang(ID))
  - `NhiemKyBatDau: string` (VARCHAR(20)) (Ví dụ: "2020-2025")
  - `NhiemKyKetThucDuKien: date` (DATE NULL)
  - `NgayBauCu: date` (DATE)
  - `FileQuyetDinhChuanYKQBauCuURL: string` (VARCHAR(255) NULL)

- **Bảng `ThanhVienCapUy` (PartyCommitteeMembers)**

  - `ID: number` (BIGINT, PK, AUTO_INCREMENT)
  - `CapUyNhiemKyID: number` (BIGINT, NOT NULL, FK REFERENCES CapUyNhiemKy(ID))
  - `DangVienID: string` (VARCHAR(20), NOT NULL, FK REFERENCES DangVien(MaDangVien))
  - `ChucVuTrongCapUyID: number` (INT, NOT NULL, FK REFERENCES DmChucVuTrongCapUy(ID))
  - `NgayBatDauDamNhiem: date` (DATE)
  - `NgayKetThucDamNhiem: date` (DATE NULL)
  - `IsConTrongCapUy: boolean` (BOOLEAN, DEFAULT TRUE)

- **Bảng `DangVien` (PartyMembers)** (Để chọn và hiển thị tên Đảng viên trong Cấp ủy)

  - `MaDangVien: string` (PK)
  - `HoVaTen: string`

- **Danh mục `DmCapToChucDang`**

  - `ID: number` (PK)
  - `TenCapToChuc: string` (Ví dụ: Chi bộ, Đảng bộ Cơ sở, Đảng bộ Cấp trên CS...)

- **Danh mục `DmTrangThaiHoatDongTCD`** (Tổ chức Đảng)

  - `ID: number` (PK)
  - `TenTrangThai: string` (Ví dụ: Đang hoạt động, Giải thể, Sáp nhập)

- **Danh mục `DmChucVuTrongCapUy`**
  - `ID: number` (PK)
  - `TenChucVu: string` (Ví dụ: Bí thư, Phó Bí thư, Ủy viên Ban Thường vụ, Ủy viên...)

---

#### III. Liên kết với page khác

1.  **Trang Danh sách Đảng viên:**
    - Thông tin Tổ chức Đảng sinh hoạt của Đảng viên được lấy từ đây.
    - Bộ lọc Đảng viên có thể dựa trên cơ cấu Tổ chức Đảng.
2.  **Trang Kế hoạch Sinh hoạt Đảng / Buổi Sinh hoạt Đảng / Nghị quyết Đảng:**
    - Các kế hoạch, buổi sinh hoạt, nghị quyết đều phải gắn với một Tổ chức Đảng cụ thể.
3.  **Trang Quản lý Hồ sơ Phát triển Đảng viên:**
    - Tổ chức Đảng đề nghị kết nạp được chọn từ danh sách này.

---

Mô tả này đã chi tiết hóa các chức năng và dữ liệu cho Trang Quản lý Tổ chức Đảng. Trang này đóng vai trò xương sống trong việc quản lý cơ cấu và hoạt động của các cấp Đảng trong hệ thống.
