# Workflow Refactor Tách Logic và UI từ A đến Z

Đây là workflow chi tiết để thực hiện việc refactor các trang và chức năng trong project, tập trung vào việc tách biệt logic xử lý và UI component, áp dụng best practices và reusable components.

## 1. Phân tích và Lên kế hoạch

### Bước 1: Hiểu rõ cấu trúc hiện tại

1. **Phân tích cấu trúc file**

   - Xác định các component chính trong page
   - Phân tích props và state sử dụng trong component
   - Liệt kê các sự kiện và data flow

2. **Xác định phần logic cần tách biệt**

   - State management (useState, useReducer)
   - API calls và data fetching
   - Event handlers và business logic
   - Side effects (useEffect)

3. **<PERSON>á<PERSON> định UI có thể tái sử dụng**
   - Custom tables → Shared DataTable
   - Filter components → Shared Filters
   - Form elements → Shared Form components
   - Dialogs và modals → Shared Dialog components

### Bước 2: <PERSON><PERSON><PERSON><PERSON> cứu các pattern đã chuẩn hóa

1. **Tìm mẫu tham chiếu trong project**

   - Xác định module tương tự đã được refactor (ví dụ: module đảng viên)
   - Phân tích cấu trúc hook và component đã tách biệt
   - Hiểu rõ convention đặt tên và file structure

2. **Xác định các thành phần shared**

   - DataTable, FilterPanel, TextFilter, SelectFilter, DateFilter
   - RowActions component và các action chuẩn
   - Dialog components
   - Các utility functions

3. **Đảm bảo style nhất quán**
   - Kiểm tra các MUI components sử dụng
   - Kiểm tra typography và hiển thị dữ liệu
   - Kiểm tra các icon components

## 2. Tạo Custom Hook cho Quản lý Trạng thái

### Bước 3: Thiết kế cấu trúc hook

1. **Tạo folder structure**

   ```
   /hooks
     /modules
       /<module-name>          # Ví dụ: /quan-ly-quan-nhan
         /index.ts
         /<function-name>      # Ví dụ: /danh-sach
           /index.ts
           /use<Function>Table.ts  # Ví dụ: /useQuanNhanTable.ts
   ```

2. **Định nghĩa các interface**

   ```typescript
   // State interface
   export interface ModuleTableState {
     rows: DataType[]
     loading: boolean
     pageSize: number
     page: number
     filters: FilterValues
     total: number
     sortModel: SortItem[]
   }

   // Filter interface
   export interface FilterValues {
     searchTerm: string
     // Other filter fields
   }

   // API response interface
   export interface APIResponse {
     data: DataType[]
     total: number
     // Other response fields
   }
   ```

3. **Xác định các dependency cần thiết**
   ```typescript
   import { useState, useEffect, useCallback } from 'react'
   import { APIService } from '@/services/api'
   import type { SortItem } from '@/components/shared/DataTable/types'
   ```

### Bước 4: Triển khai hook cơ bản

1. **Khởi tạo state**

   ```typescript
   export const useModuleTable = () => {
     // Initialize state with default values
     const [state, setState] = useState<ModuleTableState>({
       rows: [],
       loading: false,
       pageSize: 10,
       page: 0,
       filters: {
         searchTerm: '',
         // Other filter defaults
       },
       total: 0,
       sortModel: []
     })

     // State update helpers
     const updateState = (newState: Partial<ModuleTableState>) => {
       setState(prev => ({ ...prev, ...newState }))
     }
   ```

2. **Triển khai các phương thức xử lý dữ liệu cơ bản**

   ```typescript
   // Filter handlers
   const handleFilterChange = (name: string, value: any) => {
     updateState({
       filters: { ...state.filters, [name]: value }
     })
   }

   // Reset filters
   const handleResetFilters = () => {
     updateState({
       filters: {
         searchTerm: ''
         // Reset other filters
       },
       page: 0 // Reset page when filters change
     })

     // Optionally fetch data immediately
     fetchData()
   }

   // Apply filters
   const handleApplyFilters = () => {
     updateState({ page: 0 }) // Reset page when applying filters
     fetchData()
   }
   ```

3. **Triển khai pagination và sorting**

   ```typescript
   // Page change handler
   const handlePageChange = (newPage: number) => {
     updateState({ page: newPage })
     fetchData()
   }

   // Page size change handler
   const handlePageSizeChange = (newPageSize: number) => {
     updateState({ pageSize: newPageSize, page: 0 })
     fetchData()
   }

   // Sort model change handler
   const handleSortModelChange = (sortModel: SortItem[]) => {
     updateState({ sortModel })
     fetchData()
   }
   ```

### Bước 5: Bổ sung logic nghiệp vụ

1. **Fetch data function**

   ```typescript
   const fetchData = useCallback(async () => {
     try {
       updateState({ loading: true })

       // Build query params from state
       const params = {
         page: state.page,
         limit: state.pageSize,
         search: state.filters.searchTerm
         // Other filter params
       }

       // Add sorting if available
       if (state.sortModel.length > 0) {
         params.sort = state.sortModel[0].field
         params.order = state.sortModel[0].sort
       }

       // Call API
       const response = await APIService.getList(params)

       // Update state with response
       updateState({
         rows: response.data,
         total: response.total,
         loading: false
       })
     } catch (error) {
       console.error('Error fetching data:', error)
       updateState({ loading: false })
     }
   }, [state.page, state.pageSize, state.filters, state.sortModel])
   ```

2. **Initialize data on first load**

   ```typescript
   // Fetch initial data when component mounts
   useEffect(() => {
     fetchData()
   }, []) // Empty dependency array for first load only
   ```

3. **Xử lý các action khác**

   ```typescript
   // Export data
   const handleExport = async () => {
     try {
       updateState({ loading: true })

       // Call export API with current filters
       await APIService.exportData({
         filters: state.filters,
         sort: state.sortModel
       })

       updateState({ loading: false })
     } catch (error) {
       console.error('Error exporting data:', error)
       updateState({ loading: false })
     }
   }

   // Delete record
   const handleDelete = async (id: number) => {
     try {
       await APIService.deleteRecord(id)
       // Refresh data
       fetchData()
     } catch (error) {
       console.error('Error deleting record:', error)
     }
   }
   ```

4. **Return hook state và methods**
   ```typescript
   return {
     // States
     ...state,

     // Methods
     handleFilterChange,
     handleApplyFilters,
     handleResetFilters,
     handlePageChange,
     handlePageSizeChange,
     handleSortModelChange,
     handleExport,
     handleDelete,
     fetchData
   }
   ```

## 3. Refactor UI Components

### Bước 6: Tách phần Table Data

1. **Định nghĩa cấu trúc column**

   ```typescript
   import { createColumnHelper } from '@tanstack/react-table'

   const columnHelper = createColumnHelper<DataType>()

   const columns = useMemo(() => {
     return [
       columnHelper.accessor('id', {
         header: 'ID',
         cell: ({ row }) => <Typography>{row.original.id}</Typography>
       }),

       columnHelper.accessor('name', {
         header: 'Tên',
         cell: ({ row }) => (
           <div className='flex items-center gap-4'>
             {getAvatar({ avatar: row.original.avatar, name: row.original.name })}
             <Typography color='text.primary' className='font-medium'>
               {row.original.name}
             </Typography>
           </div>
         )
       }),

       // Các columns khác...

       // Actions column
       {
         id: 'actions',
         header: 'Hành động',
         cell: ({ row }: any) => (
           <RowActions
             row={row}
             maxVisibleActions={2}
             actions={[
               {
                 icon: <i className='tabler-eye text-[22px]' />,
                 label: 'Xem chi tiết',
                 onClick: (original) => onViewDetails(original.id),
                 tooltip: 'Xem chi tiết'
               },
               // Các actions khác...
             ]}
           />
         ),
         enableSorting: false
       }
     ]
   }, [onViewDetails, onEdit, onDelete]) // Thêm các dependencies
   ```

2. **Sử dụng shared DataTable component**
   ```tsx
   <DataTableComponent
     data={rows}
     columns={columns}
     initialPageSize={pageSize}
     rowsPerPageOptions={rowsPerPageOptions}
     onPageChange={onPageChange}
     onPageSizeChange={onPageSizeChange}
     showCheckboxSelection={true}
     toolbar={{
       exportButton: {
         show: true,
         onClick: onExport
       },
       additionalContent: (
         <Button variant='contained' startIcon={<i className='tabler-plus' />} onClick={onAdd}>
           Thêm mới
         </Button>
       )
     }}
     enableSorting={true}
     onSort={onSortModelChange}
     className='mt-6'
   />
   ```

### Bước 7: Tách phần Filter

1. **Sử dụng shared FilterPanel**

   ```tsx
   <FilterPanel
     title='Bộ lọc'
     defaultValues={{
       searchTerm: ''
       // Default values for other filters
     }}
     values={filters}
     onChange={newValues => {
       // Set toàn bộ giá trị mới
       Object.keys(newValues).forEach(key => {
         onFilterChange(key, newValues[key as keyof typeof newValues])
       })
     }}
     onApply={handleApplyFilters}
     onReset={handleResetFilters}
   >
     {renderFilterForm}
   </FilterPanel>
   ```

2. **Triển khai các filter component**
   ```tsx
   const renderFilterForm = () => (
     <>
       <Grid container spacing={4} sx={{ mx: 0, px: 0 }}>
         <Grid item xs={12}>
           <TextFilter
             name='searchTerm'
             value={filters.searchTerm}
             onChange={handleFilterChange}
             placeholder='Tìm kiếm theo tên, mã...'
             startAdornment={<i className='tabler-search text-xl' />}
             fullWidth
           />
         </Grid>

         <Grid item xs={12} sm={6} md={3}>
           <SelectFilter
             name='category'
             label='Loại'
             value={filters.category}
             onChange={handleFilterChange}
             options={[
               { value: '', label: 'Tất cả' },
               ...categoryList.map(item => ({ value: item.value, label: item.label }))
             ]}
             fullWidth
           />
         </Grid>

         {/* Các filter khác... */}
       </Grid>
     </>
   )
   ```

### Bước 8: Chuẩn hóa Row Actions

1. **Sử dụng RowActions component**
   ```tsx
   <RowActions
     row={row}
     maxVisibleActions={2}
     actions={[
       {
         icon: <i className='tabler-eye text-[22px]' />,
         label: 'Xem chi tiết',
         onClick: original => onViewDetails(original.id),
         tooltip: 'Xem chi tiết'
       },
       {
         icon: <i className='tabler-edit text-[22px]' />,
         label: 'Chỉnh sửa',
         onClick: original => onEdit(original.id),
         tooltip: 'Chỉnh sửa'
       },
       {
         icon: <i className='tabler-refresh text-[22px]' />,
         label: 'Thay đổi trạng thái',
         onClick: original => onOpenChangeStatusDialog(original.id),
         tooltip: 'Thay đổi trạng thái'
       },
       {
         icon: <i className='tabler-trash text-[22px]' />,
         label: 'Xóa',
         onClick: original => onOpenDeleteDialog(original.id),
         color: 'error',
         tooltip: 'Xóa'
       }
     ]}
   />
   ```

## 4. Kết nối và Tối ưu hóa

### Bước 9: Kết nối hook với UI component

1. **Refactor Page Component**

   ```tsx
   // page.tsx
   'use client'

   import { useModuleTable } from '@/hooks/modules/module-name/function-name/useModuleTable'
   import { useRouter } from 'next/navigation'
   import ModuleTableComponent from './BangDuLieu'

   const ModulePage = () => {
     const router = useRouter()
     const tableState = useModuleTable()

     // Navigation methods (không đưa vào hook)
     const handleViewDetails = (id: number) => {
       router.push(`/module-path/chi-tiet/${id}`)
     }

     const handleEdit = (id: number) => {
       router.push(`/module-path/chinh-sua/${id}`)
     }

     const handleAdd = () => {
       router.push('/module-path/them-moi')
     }

     // Dialog methods
     const handleOpenDeleteDialog = (id: number) => {
       // Dialog logic
     }

     const handleOpenChangeStatusDialog = (id: number) => {
       // Dialog logic
     }

     return (
       <ModuleTableComponent
         rows={tableState.rows}
         filters={tableState.filters}
         loading={tableState.loading}
         pageSize={tableState.pageSize}
         page={tableState.page}
         total={tableState.total}
         onFilterChange={tableState.handleFilterChange}
         onApplyFilters={tableState.handleApplyFilters}
         onResetFilters={tableState.handleResetFilters}
         onPageChange={tableState.handlePageChange}
         onPageSizeChange={tableState.handlePageSizeChange}
         onSortModelChange={tableState.handleSortModelChange}
         onExport={tableState.handleExport}
         onViewDetails={handleViewDetails}
         onEdit={handleEdit}
         onAdd={handleAdd}
         onOpenDeleteDialog={handleOpenDeleteDialog}
         onOpenChangeStatusDialog={handleOpenChangeStatusDialog}
       />
     )
   }

   export default ModulePage
   ```

### Bước 10: Tách biệt Navigation Logic

1. **Giữ routing logic ở main component**

   ```typescript
   // Để trong page component, không đưa vào hook
   const handleViewDetails = (id: number) => {
     router.push(`/module-path/chi-tiet/${id}`)
   }

   const handleEdit = (id: number) => {
     router.push(`/module-path/chinh-sua/${id}`)
   }

   const handleAdd = () => {
     router.push('/module-path/them-moi')
   }
   ```

2. **Tách biệt dialog logic**

   ```typescript
   // Dialog state có thể đặt trong page hoặc hook tùy context
   const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
   const [selectedId, setSelectedId] = useState<number | null>(null)

   const handleOpenDeleteDialog = (id: number) => {
     setSelectedId(id)
     setDeleteDialogOpen(true)
   }

   const handleCloseDeleteDialog = () => {
     setDeleteDialogOpen(false)
     setSelectedId(null)
   }

   const handleConfirmDelete = () => {
     if (selectedId !== null) {
       tableState.handleDelete(selectedId)
       handleCloseDeleteDialog()
     }
   }
   ```

### Bước 11: Tối ưu hóa Performance

1. **Sử dụng useMemo và useCallback**

   ```typescript
   // Memoize complex calculations
   const filteredData = useMemo(() => {
     // Complex filtering logic
     return data.filter(...)
   }, [data, filters])

   // Memoize handlers
   const handleAction = useCallback((id: number) => {
     // Action handling logic
   }, [dependency1, dependency2])
   ```

2. **Memoize components để tránh render không cần thiết**

   ```tsx
   // Memoize child component
   const MemoizedChildComponent = memo(ChildComponent)

   // Use memoized component in parent
   return (
     <div>
       <MemoizedChildComponent data={data} onAction={handleAction} />
     </div>
   )
   ```

3. **Tối ưu dependency arrays trong các hook**
   ```typescript
   // Tránh re-run effect khi không cần thiết
   useEffect(() => {
     // Effect logic
   }, [depsToWatch]) // Chỉ các deps thực sự cần thiết
   ```

## 5. Kiểm thử và Hoàn thiện

### Bước 12: Kiểm tra tính năng

1. **Kiểm tra data flow**

   - Kiểm tra fetch initial data
   - Kiểm tra pagination
   - Kiểm tra sorting
   - Kiểm tra filtering

2. **Kiểm tra tất cả actions**

   - Add/Edit/Delete functionality
   - Export functionality
   - Status changes
   - Error handling

3. **Kiểm tra UI responsiveness**
   - Desktop view
   - Tablet view
   - Mobile view

### Bước 13: Refactor cấu trúc thư mục

1. **Đảm bảo file structure chuẩn**

   ```
   /hooks
     /modules
       /<module-name>
         /index.ts               # Export all hooks
         /<function-name>
           /index.ts             # Export specific hook
           /use<Function>Table.ts
   /views
     /<module-name>
       /<function-name>
         /page.tsx               # Main page
         /BangDuLieu.tsx         # Data table component
         /DialogComponents.tsx    # Dialog components if needed
   ```

2. **Cập nhật imports**

   ```typescript
   // Fix import paths
   import { useModuleTable } from '@/hooks/modules/module-name/function-name'

   // Export from index.ts
   export * from './useModuleTable'
   ```

### Bước 14: Tài liệu hóa

1. **Viết comments cho các hàm quan trọng**

   ```typescript
   /**
    * Fetches data based on current pagination, filters and sorting
    * @param forceRefresh If true, ignores cache and fetches fresh data
    * @returns Promise that resolves when data is fetched
    */
   const fetchData = useCallback(
     async (forceRefresh = false) => {
       // Implementation
     },
     [deps]
   )
   ```

2. **Cập nhật interface và type definition**
   ```typescript
   /**
    * Configuration for column rendering
    */
   export interface ColumnConfig<T> {
     field: keyof T
     header: string
     renderCell?: (row: T) => React.ReactNode
     sortable?: boolean
     width?: number | string
   }
   ```

## Ví dụ thực tế: Refactor trang Danh sách quân nhân

### 1. Tạo cấu trúc hook:

```typescript
// hooks/modules/quan-ly-quan-nhan/danh-sach/useQuanNhanTable.ts
import { useState, useEffect, useCallback } from 'react'
import type { SortItem } from '@/components/shared/DataTable/types'
import type { QuanNhanType } from '@/views/quan-ly-quan-nhan/danh-sach/mockData'

export interface FilterValues {
  searchTerm: string
  donVi: string
  capBac: string
  chucVu: string
  trangThai: string
}

export interface QuanNhanTableState {
  rows: QuanNhanType[]
  loading: boolean
  pageSize: number
  page: number
  filters: FilterValues
  total: number
  sortModel: SortItem[]
}

export const useQuanNhanTable = () => {
  // State
  const [state, setState] = useState<QuanNhanTableState>({
    rows: [],
    loading: false,
    pageSize: 10,
    page: 0,
    filters: {
      searchTerm: '',
      donVi: '',
      capBac: '',
      chucVu: '',
      trangThai: ''
    },
    total: 0,
    sortModel: []
  })

  // Helper to update state
  const updateState = (newState: Partial<QuanNhanTableState>) => {
    setState(prev => ({ ...prev, ...newState }))
  }

  // Filter handlers
  const handleFilterChange = (name: string, value: any) => {
    updateState({
      filters: { ...state.filters, [name]: value }
    })
  }

  // Apply filters
  const handleApplyFilters = () => {
    updateState({ page: 0 })
    fetchData()
  }

  // Reset filters
  const handleResetFilters = () => {
    updateState({
      filters: {
        searchTerm: '',
        donVi: '',
        capBac: '',
        chucVu: '',
        trangThai: ''
      },
      page: 0
    })
    fetchData()
  }

  // Pagination handlers
  const handlePageChange = (newPage: number) => {
    updateState({ page: newPage })
    fetchData()
  }

  const handlePageSizeChange = (newPageSize: number) => {
    updateState({ pageSize: newPageSize, page: 0 })
    fetchData()
  }

  // Sort handler
  const handleSortModelChange = (sortModel: SortItem[]) => {
    updateState({ sortModel })
    fetchData()
  }

  // Export handler
  const handleExport = () => {
    // Implementation
    console.log('Exporting data with filters:', state.filters)
  }

  // Fetch data
  const fetchData = useCallback(async () => {
    try {
      updateState({ loading: true })

      // Simulate API call
      setTimeout(() => {
        // Mock data fetching
        const mockData = [] // Get mock data

        updateState({
          rows: mockData,
          total: mockData.length,
          loading: false
        })
      }, 500)
    } catch (error) {
      console.error('Error fetching data:', error)
      updateState({ loading: false })
    }
  }, [state.page, state.pageSize, state.filters, state.sortModel])

  // Initial data load
  useEffect(() => {
    fetchData()
  }, [])

  return {
    // States
    ...state,

    // Methods
    handleFilterChange,
    handleApplyFilters,
    handleResetFilters,
    handlePageChange,
    handlePageSizeChange,
    handleSortModelChange,
    handleExport,
    fetchData
  }
}

// hooks/modules/quan-ly-quan-nhan/danh-sach/index.ts
export * from './useQuanNhanTable'

// hooks/modules/quan-ly-quan-nhan/index.ts
export * from './danh-sach'
```

### 2. Refactor UI Components:

```tsx
// views/quan-ly-quan-nhan/danh-sach/page.tsx
'use client'

import { useRouter } from 'next/navigation'
import { useQuanNhanTable } from '@/hooks/modules/quan-ly-quan-nhan/danh-sach'
import BangDuLieu from './BangDuLieu'

const DanhSachQuanNhanPage = () => {
  const router = useRouter()
  const tableState = useQuanNhanTable()

  // Navigation methods
  const handleViewDetails = (id: number) => {
    router.push(`/quan-ly-quan-nhan/chi-tiet/${id}`)
  }

  const handleEdit = (id: number) => {
    router.push(`/quan-ly-quan-nhan/chinh-sua/${id}`)
  }

  const handleAdd = () => {
    router.push('/quan-ly-quan-nhan/them-moi')
  }

  // Dialog methods
  const handleOpenChangeStatusDialog = (id: number) => {
    // Implement dialog logic
  }

  const handleOpenDeleteDialog = (id: number) => {
    // Implement dialog logic
  }

  return (
    <BangDuLieu
      rows={tableState.rows}
      filters={tableState.filters}
      onFilterChange={tableState.handleFilterChange}
      onApplyFilters={tableState.handleApplyFilters}
      onResetFilters={tableState.handleResetFilters}
      pageSize={tableState.pageSize}
      onPageChange={tableState.handlePageChange}
      onPageSizeChange={tableState.handlePageSizeChange}
      onSortModelChange={tableState.handleSortModelChange}
      onExport={tableState.handleExport}
      onViewDetails={handleViewDetails}
      onEdit={handleEdit}
      onAdd={handleAdd}
      onOpenChangeStatusDialog={handleOpenChangeStatusDialog}
      onOpenDeleteDialog={handleOpenDeleteDialog}
    />
  )
}

export default DanhSachQuanNhanPage
```

## Lưu ý quan trọng

1. **Phạm vi trách nhiệm**

   - Hook chỉ quản lý state và business logic
   - Component page quản lý routing và dialog
   - UI components chỉ render dữ liệu và xử lý event

2. **Performance optimization**

   - Sử dụng đúng dependency array để tránh re-render không cần thiết
   - Memoize các computed values và callbacks
   - Tránh inline function trong render trừ khi cần thiết

3. **Error handling**

   - Xử lý lỗi API một cách thống nhất
   - Hiển thị thông báo lỗi phù hợp
   - Logging lỗi đầy đủ để debug

4. **Tương thích với server components (Next.js)**

   - Tách biệt client và server components rõ ràng
   - Sử dụng 'use client' directive khi cần
   - Xử lý data fetching phù hợp với Next.js appDir

5. **Testing considerations**
   - Hooks nên được viết để dễ dàng unit test
   - UI components nên tách biệt để dễ test render
   - Các side effects nên được mock khi test

Tuân thủ workflow này sẽ giúp việc refactor các trang và chức năng trở nên có cấu trúc, nhất quán và dễ bảo trì, đồng thời cải thiện hiệu suất ứng dụng và trải nghiệm phát triển.
