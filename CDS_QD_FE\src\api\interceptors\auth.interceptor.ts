// src/api/interceptors/auth.interceptor.ts
import type { InternalAxiosRequestConfig } from 'axios'

export const authInterceptor = async (config: InternalAxiosRequestConfig): Promise<InternalAxiosRequestConfig> => {
  try {
    // Lấy token từ localStorage hoặc cookie
    const token = localStorage.getItem('accessToken')

    if (token) {
      config.headers = config.headers || {}
      config.headers.Authorization = `Bearer ${token}`
    }
  } catch (error) {
    console.error('Error adding auth token:', error)
  }

  return config
}
