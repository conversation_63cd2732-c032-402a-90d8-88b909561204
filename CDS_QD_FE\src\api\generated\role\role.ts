/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 *  Swagger
 * The  API documents
 * OpenAPI spec version: 1.0
 */
import { useInfiniteQuery, useMutation, useQuery } from '@tanstack/react-query'
import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseInfiniteQueryResult,
  DefinedUseQueryResult,
  InfiniteData,
  MutationFunction,
  QueryClient,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseInfiniteQueryOptions,
  UseInfiniteQueryResult,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult
} from '@tanstack/react-query'

import type { CreateRoleDto, RoleControllerFindAllParams, UpdateRoleDto } from '.././model'

import { customInstance } from '../../mutator/custom-instance'
import type { ApiResponse } from '../../types'

export const roleControllerCreate = (createRoleDto: CreateRoleDto, signal?: AbortSignal) => {
  return customInstance<ApiResponse<void>>({
    url: `/role`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: createRoleDto,
    signal
  })
}

export const getRoleControllerCreateMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof roleControllerCreate>>,
    TError,
    { data: CreateRoleDto },
    TContext
  >
}): UseMutationOptions<Awaited<ReturnType<typeof roleControllerCreate>>, TError, { data: CreateRoleDto }, TContext> => {
  const mutationKey = ['roleControllerCreate']
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } }

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof roleControllerCreate>>,
    { data: CreateRoleDto }
  > = props => {
    const { data } = props ?? {}

    return roleControllerCreate(data)
  }

  return { mutationFn, ...mutationOptions }
}

export type RoleControllerCreateMutationResult = NonNullable<Awaited<ReturnType<typeof roleControllerCreate>>>
export type RoleControllerCreateMutationBody = CreateRoleDto
export type RoleControllerCreateMutationError = unknown

export const useRoleControllerCreate = <TError = unknown, TContext = unknown>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof roleControllerCreate>>,
      TError,
      { data: CreateRoleDto },
      TContext
    >
  },
  queryClient?: QueryClient
): UseMutationResult<Awaited<ReturnType<typeof roleControllerCreate>>, TError, { data: CreateRoleDto }, TContext> => {
  const mutationOptions = getRoleControllerCreateMutationOptions(options)

  return useMutation(mutationOptions, queryClient)
}
export const roleControllerFindAll = (params?: RoleControllerFindAllParams, signal?: AbortSignal) => {
  return customInstance<ApiResponse<void>>({ url: `/role`, method: 'GET', params, signal })
}

export const getRoleControllerFindAllQueryKey = (params?: RoleControllerFindAllParams) => {
  return [`/role`, ...(params ? [params] : [])] as const
}

export const getRoleControllerFindAllInfiniteQueryOptions = <
  TData = InfiniteData<Awaited<ReturnType<typeof roleControllerFindAll>>, RoleControllerFindAllParams['page']>,
  TError = unknown
>(
  params?: RoleControllerFindAllParams,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof roleControllerFindAll>>,
        TError,
        TData,
        Awaited<ReturnType<typeof roleControllerFindAll>>,
        QueryKey,
        RoleControllerFindAllParams['page']
      >
    >
  }
) => {
  const { query: queryOptions } = options ?? {}

  const queryKey = queryOptions?.queryKey ?? getRoleControllerFindAllQueryKey(params)

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof roleControllerFindAll>>,
    QueryKey,
    RoleControllerFindAllParams['page']
  > = ({ signal, pageParam }) => roleControllerFindAll({ ...params, page: pageParam || params?.['page'] }, signal)

  return { queryKey, queryFn, staleTime: 10000, ...queryOptions } as UseInfiniteQueryOptions<
    Awaited<ReturnType<typeof roleControllerFindAll>>,
    TError,
    TData,
    Awaited<ReturnType<typeof roleControllerFindAll>>,
    QueryKey,
    RoleControllerFindAllParams['page']
  > & { queryKey: DataTag<QueryKey, TData, TError> }
}

export type RoleControllerFindAllInfiniteQueryResult = NonNullable<Awaited<ReturnType<typeof roleControllerFindAll>>>
export type RoleControllerFindAllInfiniteQueryError = unknown

export function useRoleControllerFindAllInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof roleControllerFindAll>>, RoleControllerFindAllParams['page']>,
  TError = unknown
>(
  params: undefined | RoleControllerFindAllParams,
  options: {
    query: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof roleControllerFindAll>>,
        TError,
        TData,
        Awaited<ReturnType<typeof roleControllerFindAll>>,
        QueryKey,
        RoleControllerFindAllParams['page']
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof roleControllerFindAll>>,
          TError,
          Awaited<ReturnType<typeof roleControllerFindAll>>,
          QueryKey
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): DefinedUseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useRoleControllerFindAllInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof roleControllerFindAll>>, RoleControllerFindAllParams['page']>,
  TError = unknown
>(
  params?: RoleControllerFindAllParams,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof roleControllerFindAll>>,
        TError,
        TData,
        Awaited<ReturnType<typeof roleControllerFindAll>>,
        QueryKey,
        RoleControllerFindAllParams['page']
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof roleControllerFindAll>>,
          TError,
          Awaited<ReturnType<typeof roleControllerFindAll>>,
          QueryKey
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useRoleControllerFindAllInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof roleControllerFindAll>>, RoleControllerFindAllParams['page']>,
  TError = unknown
>(
  params?: RoleControllerFindAllParams,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof roleControllerFindAll>>,
        TError,
        TData,
        Awaited<ReturnType<typeof roleControllerFindAll>>,
        QueryKey,
        RoleControllerFindAllParams['page']
      >
    >
  },
  queryClient?: QueryClient
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }

export function useRoleControllerFindAllInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof roleControllerFindAll>>, RoleControllerFindAllParams['page']>,
  TError = unknown
>(
  params?: RoleControllerFindAllParams,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof roleControllerFindAll>>,
        TError,
        TData,
        Awaited<ReturnType<typeof roleControllerFindAll>>,
        QueryKey,
        RoleControllerFindAllParams['page']
      >
    >
  },
  queryClient?: QueryClient
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
  const queryOptions = getRoleControllerFindAllInfiniteQueryOptions(params, options)

  const query = useInfiniteQuery(queryOptions, queryClient) as UseInfiniteQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>
  }

  query.queryKey = queryOptions.queryKey

  return query
}

export const getRoleControllerFindAllQueryOptions = <
  TData = Awaited<ReturnType<typeof roleControllerFindAll>>,
  TError = unknown
>(
  params?: RoleControllerFindAllParams,
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof roleControllerFindAll>>, TError, TData>> }
) => {
  const { query: queryOptions } = options ?? {}

  const queryKey = queryOptions?.queryKey ?? getRoleControllerFindAllQueryKey(params)

  const queryFn: QueryFunction<Awaited<ReturnType<typeof roleControllerFindAll>>> = ({ signal }) =>
    roleControllerFindAll(params, signal)

  return { queryKey, queryFn, staleTime: 10000, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof roleControllerFindAll>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> }
}

export type RoleControllerFindAllQueryResult = NonNullable<Awaited<ReturnType<typeof roleControllerFindAll>>>
export type RoleControllerFindAllQueryError = unknown

export function useRoleControllerFindAll<TData = Awaited<ReturnType<typeof roleControllerFindAll>>, TError = unknown>(
  params: undefined | RoleControllerFindAllParams,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof roleControllerFindAll>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof roleControllerFindAll>>,
          TError,
          Awaited<ReturnType<typeof roleControllerFindAll>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useRoleControllerFindAll<TData = Awaited<ReturnType<typeof roleControllerFindAll>>, TError = unknown>(
  params?: RoleControllerFindAllParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof roleControllerFindAll>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof roleControllerFindAll>>,
          TError,
          Awaited<ReturnType<typeof roleControllerFindAll>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useRoleControllerFindAll<TData = Awaited<ReturnType<typeof roleControllerFindAll>>, TError = unknown>(
  params?: RoleControllerFindAllParams,
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof roleControllerFindAll>>, TError, TData>> },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }

export function useRoleControllerFindAll<TData = Awaited<ReturnType<typeof roleControllerFindAll>>, TError = unknown>(
  params?: RoleControllerFindAllParams,
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof roleControllerFindAll>>, TError, TData>> },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
  const queryOptions = getRoleControllerFindAllQueryOptions(params, options)

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>
  }

  query.queryKey = queryOptions.queryKey

  return query
}

export const roleControllerFindOne = (id: string, signal?: AbortSignal) => {
  return customInstance<ApiResponse<ApiResponse<void>>>({ url: `/role/${id}`, method: 'GET', signal })
}

export const getRoleControllerFindOneQueryKey = (id: string) => {
  return [`/role/${id}`] as const
}

export const getRoleControllerFindOneInfiniteQueryOptions = <
  TData = InfiniteData<Awaited<ReturnType<typeof roleControllerFindOne>>>,
  TError = unknown
>(
  id: string,
  options?: {
    query?: Partial<UseInfiniteQueryOptions<Awaited<ReturnType<typeof roleControllerFindOne>>, TError, TData>>
  }
) => {
  const { query: queryOptions } = options ?? {}

  const queryKey = queryOptions?.queryKey ?? getRoleControllerFindOneQueryKey(id)

  const queryFn: QueryFunction<Awaited<ReturnType<typeof roleControllerFindOne>>> = ({ signal }) =>
    roleControllerFindOne(id, signal)

  return { queryKey, queryFn, enabled: !!id, staleTime: 10000, ...queryOptions } as UseInfiniteQueryOptions<
    Awaited<ReturnType<typeof roleControllerFindOne>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> }
}

export type RoleControllerFindOneInfiniteQueryResult = NonNullable<Awaited<ReturnType<typeof roleControllerFindOne>>>
export type RoleControllerFindOneInfiniteQueryError = unknown

export function useRoleControllerFindOneInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof roleControllerFindOne>>>,
  TError = unknown
>(
  id: string,
  options: {
    query: Partial<UseInfiniteQueryOptions<Awaited<ReturnType<typeof roleControllerFindOne>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof roleControllerFindOne>>,
          TError,
          Awaited<ReturnType<typeof roleControllerFindOne>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): DefinedUseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useRoleControllerFindOneInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof roleControllerFindOne>>>,
  TError = unknown
>(
  id: string,
  options?: {
    query?: Partial<UseInfiniteQueryOptions<Awaited<ReturnType<typeof roleControllerFindOne>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof roleControllerFindOne>>,
          TError,
          Awaited<ReturnType<typeof roleControllerFindOne>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useRoleControllerFindOneInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof roleControllerFindOne>>>,
  TError = unknown
>(
  id: string,
  options?: {
    query?: Partial<UseInfiniteQueryOptions<Awaited<ReturnType<typeof roleControllerFindOne>>, TError, TData>>
  },
  queryClient?: QueryClient
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }

export function useRoleControllerFindOneInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof roleControllerFindOne>>>,
  TError = unknown
>(
  id: string,
  options?: {
    query?: Partial<UseInfiniteQueryOptions<Awaited<ReturnType<typeof roleControllerFindOne>>, TError, TData>>
  },
  queryClient?: QueryClient
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
  const queryOptions = getRoleControllerFindOneInfiniteQueryOptions(id, options)

  const query = useInfiniteQuery(queryOptions, queryClient) as UseInfiniteQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>
  }

  query.queryKey = queryOptions.queryKey

  return query
}

export const getRoleControllerFindOneQueryOptions = <
  TData = Awaited<ReturnType<typeof roleControllerFindOne>>,
  TError = unknown
>(
  id: string,
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof roleControllerFindOne>>, TError, TData>> }
) => {
  const { query: queryOptions } = options ?? {}

  const queryKey = queryOptions?.queryKey ?? getRoleControllerFindOneQueryKey(id)

  const queryFn: QueryFunction<Awaited<ReturnType<typeof roleControllerFindOne>>> = ({ signal }) =>
    roleControllerFindOne(id, signal)

  return { queryKey, queryFn, enabled: !!id, staleTime: 10000, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof roleControllerFindOne>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> }
}

export type RoleControllerFindOneQueryResult = NonNullable<Awaited<ReturnType<typeof roleControllerFindOne>>>
export type RoleControllerFindOneQueryError = unknown

export function useRoleControllerFindOne<TData = Awaited<ReturnType<typeof roleControllerFindOne>>, TError = unknown>(
  id: string,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof roleControllerFindOne>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof roleControllerFindOne>>,
          TError,
          Awaited<ReturnType<typeof roleControllerFindOne>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useRoleControllerFindOne<TData = Awaited<ReturnType<typeof roleControllerFindOne>>, TError = unknown>(
  id: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof roleControllerFindOne>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof roleControllerFindOne>>,
          TError,
          Awaited<ReturnType<typeof roleControllerFindOne>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useRoleControllerFindOne<TData = Awaited<ReturnType<typeof roleControllerFindOne>>, TError = unknown>(
  id: string,
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof roleControllerFindOne>>, TError, TData>> },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }

export function useRoleControllerFindOne<TData = Awaited<ReturnType<typeof roleControllerFindOne>>, TError = unknown>(
  id: string,
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof roleControllerFindOne>>, TError, TData>> },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
  const queryOptions = getRoleControllerFindOneQueryOptions(id, options)

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>
  }

  query.queryKey = queryOptions.queryKey

  return query
}

export const roleControllerUpdate = (id: string, updateRoleDto: UpdateRoleDto) => {
  return customInstance<ApiResponse<ApiResponse<void>>>({
    url: `/role/${id}`,
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json' },
    data: updateRoleDto
  })
}

export const getRoleControllerUpdateMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof roleControllerUpdate>>,
    TError,
    { id: string; data: UpdateRoleDto },
    TContext
  >
}): UseMutationOptions<
  Awaited<ReturnType<typeof roleControllerUpdate>>,
  TError,
  { id: string; data: UpdateRoleDto },
  TContext
> => {
  const mutationKey = ['roleControllerUpdate']
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } }

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof roleControllerUpdate>>,
    { id: string; data: UpdateRoleDto }
  > = props => {
    const { id, data } = props ?? {}

    return roleControllerUpdate(id, data)
  }

  return { mutationFn, ...mutationOptions }
}

export type RoleControllerUpdateMutationResult = NonNullable<Awaited<ReturnType<typeof roleControllerUpdate>>>
export type RoleControllerUpdateMutationBody = UpdateRoleDto
export type RoleControllerUpdateMutationError = unknown

export const useRoleControllerUpdate = <TError = unknown, TContext = unknown>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof roleControllerUpdate>>,
      TError,
      { id: string; data: UpdateRoleDto },
      TContext
    >
  },
  queryClient?: QueryClient
): UseMutationResult<
  Awaited<ReturnType<typeof roleControllerUpdate>>,
  TError,
  { id: string; data: UpdateRoleDto },
  TContext
> => {
  const mutationOptions = getRoleControllerUpdateMutationOptions(options)

  return useMutation(mutationOptions, queryClient)
}
export const roleControllerRemove = (id: string) => {
  return customInstance<ApiResponse<void>>({ url: `/role/${id}`, method: 'DELETE' })
}

export const getRoleControllerRemoveMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<Awaited<ReturnType<typeof roleControllerRemove>>, TError, { id: string }, TContext>
}): UseMutationOptions<Awaited<ReturnType<typeof roleControllerRemove>>, TError, { id: string }, TContext> => {
  const mutationKey = ['roleControllerRemove']
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } }

  const mutationFn: MutationFunction<Awaited<ReturnType<typeof roleControllerRemove>>, { id: string }> = props => {
    const { id } = props ?? {}

    return roleControllerRemove(id)
  }

  return { mutationFn, ...mutationOptions }
}

export type RoleControllerRemoveMutationResult = NonNullable<Awaited<ReturnType<typeof roleControllerRemove>>>

export type RoleControllerRemoveMutationError = unknown

export const useRoleControllerRemove = <TError = unknown, TContext = unknown>(
  options?: {
    mutation?: UseMutationOptions<Awaited<ReturnType<typeof roleControllerRemove>>, TError, { id: string }, TContext>
  },
  queryClient?: QueryClient
): UseMutationResult<Awaited<ReturnType<typeof roleControllerRemove>>, TError, { id: string }, TContext> => {
  const mutationOptions = getRoleControllerRemoveMutationOptions(options)

  return useMutation(mutationOptions, queryClient)
}
