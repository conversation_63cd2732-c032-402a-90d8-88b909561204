/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { RolesPermissionEntity } from '~/database/typeorm/entities/rolesPermission.entity';

@Injectable()
export class RolesPermissionRepository extends Repository<RolesPermissionEntity> {
    constructor(private dataSource: DataSource) {
        super(RolesPermissionEntity, dataSource.createEntityManager());
    }
}
