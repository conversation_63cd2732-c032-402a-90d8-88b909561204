Bước 27: C<PERSON><PERSON> nhật mockDataChiTiet.ts cho "Sức khỏe"
Định nghĩa ThongTinSucKhoeEntryType và các type liên quan (ví dụ: PhanLoaiSucKhoeType):

Thêm dữ liệu mẫu vào mockQuanNhanChiTietData.sucKhoe.

TypeScript

// src\views\quan-ly-quan-nhan\chi-tiet\mockDataChiTiet.ts (Thêm/Cập nhật)

export type PhanLoaiSucKhoeType = 'Loại 1' | 'Loại 2' | 'Loại 3' | 'Loại 4' | 'Loại 5' | 'Không phân loại';

// (Không cần object riêng cho màu sắc trừ khi có yêu cầu đặc biệt)

export interface ThongTinSucKhoeEntryType {
id: string; // ID duy nhất cho mỗi lần khám
<PERSON>ham: string; // ISO Date string
LoaiKhamID: string; // Sẽ map sang tên (<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> c<PERSON>...)
NoiKhamID: string; // Sẽ map sang tên (Bệnh viện Quân y 108, Trung tâm Y tế dự phòng...)
ChieuCao?: number; // cm
CanNang?: number; // kg
NhomMauID?: string; // Sẽ map sang tên (A, B, AB, O, Rh+, Rh-)
Mach?: number; // lần/phút
HuyetAp?: string; // mmHg (ví dụ "120/80")
PhanLoaiSucKhoeID?: PhanLoaiSucKhoeType; // Sẽ map sang tên (Loại 1, Loại 2...)
KetLuanBacSi?: string;
BenhLyManTinh?: string; // Liệt kê các bệnh, cách nhau bằng dấu phẩy hoặc xuống dòng
TienSuBenh?: string; // Tiền sử bệnh tật của bản thân và gia đình (nếu cần)
DiUngThuocThucPham?: string; // Liệt kê các dị ứng
FileDinhKemURL?: string; // URL tới file kết quả khám, xét nghiệm
GhiChuYBacSi?: string;
}

// Trong mockQuanNhanChiTietData:
// ...
// sucKhoe: [
// {
// id: 'sk_001',
// NgayKham: '2023-06-15T00:00:00Z',
// LoaiKhamID: 'LK_DK', // Khám định kỳ
// NoiKhamID: 'BVQY103', // Bệnh viện Quân y 103
// ChieuCao: 172,
// CanNang: 68,
// NhomMauID: 'NM_A+', // A+
// Mach: 75,
// HuyetAp: '120/80',
// PhanLoaiSucKhoeID: 'Loại 1',
// KetLuanBacSi: 'Sức khỏe tốt, đủ điều kiện công tác.',
// BenhLyManTinh: 'Không có',
// TienSuBenh: 'Không có tiền sử bệnh lý đặc biệt.',
// DiUngThuocThucPham: 'Không',
// FileDinhKemURL: '/files/suckhoe/ksk_20230615.pdf',
// GhiChuYBacSi: 'Nên duy trì chế độ ăn uống, luyện tập hợp lý.'
// },
// {
// id: 'sk_002',
// NgayKham: '2024-01-20T00:00:00Z',
// LoaiKhamID: 'LK_CK', // Khám chuyên khoa (ví dụ: Mắt)
// NoiKhamID: 'BVQY108_MAT', // Khoa Mắt, Bệnh viện Quân y 108
// ChieuCao: 172, // Có thể không cần nhập lại nếu không thay đổi
// CanNang: 68.5,
// PhanLoaiSucKhoeID: 'Loại 1', // Giữ nguyên hoặc cập nhật theo kết quả khám chuyên khoa
// KetLuanBacSi: 'Thị lực tốt, không phát hiện bệnh lý về mắt.',
// BenhLyManTinh: 'Không có',
// FileDinhKemURL: '/files/suckhoe/khammat_20240120.pdf',
// GhiChuYBacSi: 'Tái khám sau 1 năm.'
// }
// ] as ThongTinSucKhoeEntryType[],
// ...
Bước 28: Xây dựng TabPanel cho "Sức khỏe"
Tạo file src\views\quan-ly-quan-nhan\chi-tiet\components\tabs\TabSucKhoe.tsx

Mục đích: Hiển thị lịch sử khám sức khỏe, cho phép thêm, sửa, xóa.

Props:

initialData?: ThongTinSucKhoeEntryType[]
idQuanNhan: string
State: Tương tự các tab DataGrid trước.

Component Vuexy (MUI & @mui/x-data-grid): Tương tự.

Cấu hình cột cho DataGrid (chọn lọc các cột chính, các cột khác có thể xem ở chi tiết):

NgayKham (định dạng dd/MM/yyyy)
LoaiKhamID (hiển thị tên)
NoiKhamID (hiển thị tên)
PhanLoaiSucKhoeID (hiển thị tên)
KetLuanBacSi (rút gọn với tooltip)
FileDinhKemURL (Link tải/xem)
Hành động: Sửa, Xóa, Xem file (và có thể có nút "Xem chi tiết" để xem đầy đủ các chỉ số nếu không hiển thị hết trên grid).
TypeScript

// src\views\quan-ly-quan-nhan\chi-tiet\components\tabs\TabSucKhoe.tsx
'use client';

import React, { useState, useEffect } from 'react';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import { DataGrid, GridColDef, GridRenderCellParams } from '@mui/x-data-grid';
import IconButton from '@mui/material/IconButton';
import Tooltip from '@mui/material/Tooltip';

import IconPlus from '@tabler/icons-react/dist/esm/icons/IconPlus';
import IconEdit from '@tabler/icons-react/dist/esm/icons/IconEdit';
import IconTrash from '@tabler/icons-react/dist/esm/icons/IconTrash';
import IconFileText from '@tabler/icons-react/dist/esm/icons/IconFileText';
import IconEye from '@tabler/icons-react/dist/esm/icons/IconEye'; // For View Details

import { ThongTinSucKhoeEntryType } from '../../mockDataChiTiet';
// Giả định có DialogThemSuaSucKhoe, DialogXemChiTietSucKhoe và DialogXacNhanXoaItem
// import DialogThemSuaSucKhoe from './DialogThemSuaSucKhoe';
// import DialogXemChiTietSucKhoe from './DialogXemChiTietSucKhoe';
// import DialogXacNhanXoaItem from '../../../../components/DialogXacNhanXoaItem';

interface TabSucKhoeProps {
initialData?: ThongTinSucKhoeEntryType[];
idQuanNhan: string;
}

const formatDateDatagridSK = (dateString?: string | null): string => {
if (!dateString) return '';
// ... (implementation from previous tabs)
try {
const date = new Date(dateString);
if (isNaN(date.getTime())) return 'Không hợp lệ';
return `<span class="math-inline">\{String\(date\.getDate\(\)\)\.padStart\(2, '0'\)\}/</span>{String(date.getMonth() + 1).padStart(2, '0')}/${date.getFullYear()}`;
} catch (e) { return 'Không hợp lệ'; }
};
const mapIdToStringSK = (id?: string, type?: string) => id || 'N/A'; // Placeholder

const TabSucKhoe = ({ initialData = [], idQuanNhan }: TabSucKhoeProps) => {
const [listData, setListData] = useState<ThongTinSucKhoeEntryType[]>(initialData);
const [openAddEditDialog, setOpenAddEditDialog] = useState(false);
const [editingData, setEditingData] = useState<ThongTinSucKhoeEntryType | null>(null);
const [openDetailDialog, setOpenDetailDialog] = useState(false);
const [viewingData, setViewingData] = useState<ThongTinSucKhoeEntryType | null>(null);
const [openConfirmDelete, setOpenConfirmDelete] = useState(false);
const [deletingId, setDeletingId] = useState<string | null>(null);
const [paginationModel, setPaginationModel] = useState({ page: 0, pageSize: 5 });

useEffect(() => {
setListData(initialData);
}, [initialData]);

const handleOpenAddDialog = () => { /_ ... _/ setEditingData(null); setOpenAddEditDialog(true); };
const handleOpenEditDialog = (rowData: ThongTinSucKhoeEntryType) => { /_ ... _/ setEditingData(rowData); setOpenAddEditDialog(true); };
const handleOpenDetailDialog = (rowData: ThongTinSucKhoeEntryType) => { /_ ... _/ setViewingData(rowData); setOpenDetailDialog(true); };

const handleCloseDialogs = () => {
setOpenAddEditDialog(false);
setOpenDetailDialog(false);
setEditingData(null);
setViewingData(null);
};

const handleSaveData = (savedData: ThongTinSucKhoeEntryType) => {
console.log('Saving data (Sức khỏe):', savedData, 'for QN ID:', idQuanNhan);
if (editingData) {
setListData(prev => prev.map(item => (item.id === savedData.id ? savedData : item)));
} else {
setListData(prev => [...prev, { ...savedData, id: `new_sk_${Date.now()}` }]);
}
handleCloseDialogs();
};

const handleOpenDeleteDialog = (id: string) => { /_ ... _/ setDeletingId(id); setOpenConfirmDelete(true); };
const handleCloseConfirmDelete = () => { /_ ... _/ setOpenConfirmDelete(false); setDeletingId(null); };
const handleConfirmDelete = () => {
if (deletingId) {
console.log('Deleting Sức khỏe ID:', deletingId);
setListData(prev => prev.filter(item => item.id !== deletingId));
handleCloseConfirmDelete();
}
};

const columns: GridColDef[] = [
{
field: 'NgayKham',
headerName: 'Ngày khám',
width: 120,
valueFormatter: params => formatDateDatagridSK(params.value)
},
{
field: 'LoaiKhamID',
headerName: 'Loại khám',
width: 180,
valueGetter: params => mapIdToStringSK(params.value, 'loaiKham')
},
{
field: 'NoiKhamID',
headerName: 'Nơi khám',
width: 200,
valueGetter: params => mapIdToStringSK(params.value, 'noiKham')
},
{
field: 'PhanLoaiSucKhoeID',
headerName: 'Phân loại SK',
width: 150,
},
{
field: 'KetLuanBacSi',
headerName: 'Kết luận Bác sĩ',
width: 300,
renderCell: (params: GridRenderCellParams) => (
<Tooltip title={params.value || ''} placement="top-start">
<Typography noWrap variant="body2" sx={{overflow: 'hidden', textOverflow: 'ellipsis'}}>
{params.value || ''}
</Typography>
</Tooltip>
)
},
{
field: 'actions',
headerName: 'Hành động',
width: 180,
sortable: false,
filterable: false,
renderCell: (params: GridRenderCellParams) => (
<Box>
<Tooltip title="Xem Chi tiết">
<IconButton size="small" onClick={() => handleOpenDetailDialog(params.row as ThongTinSucKhoeEntryType)}>
<IconEye size={20} />
</IconButton>
</Tooltip>
<Tooltip title="Xem File đính kèm">
<span>
<IconButton
size="small"
href={params.row.FileDinhKemURL || '#'}
target="_blank"
disabled={!params.row.FileDinhKemURL}
onClick={(e) => { if (!params.row.FileDinhKemURL) e.preventDefault(); else console.log('Viewing file:', params.row.FileDinhKemURL);}}
>
<IconFileText size={20} />
</IconButton>
</span>
</Tooltip>
<Tooltip title="Sửa">
<IconButton size="small" onClick={() => handleOpenEditDialog(params.row as ThongTinSucKhoeEntryType)}>
<IconEdit size={20} />
</IconButton>
</Tooltip>
<Tooltip title="Xóa">
<IconButton size="small" onClick={() => handleOpenDeleteDialog(params.row.id as string)}>
<IconTrash size={20} />
</IconButton>
</Tooltip>
</Box>
)
}
];

return (
<Box>
<Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
<Typography variant="h6" sx={{ color: 'primary.main' }}>
Thông tin Sức khỏe
</Typography>
<Button
variant="contained"
startIcon={<IconPlus />}
onClick={handleOpenAddDialog} >
Thêm thông tin Khám
</Button>
</Box>

      <DataGrid
        autoHeight
        rows={listData}
        columns={columns}
        pageSizeOptions={[5, 10, 25]}
        paginationModel={paginationModel}
        onPaginationModelChange={setPaginationModel}
        getRowId={(row) => row.id}
        sx={{
            '& .MuiDataGrid-columnHeaders': { backgroundColor: 'customColors.tableHeaderBg' }
        }}
      />

      {/* Placeholders for Dialogs */}
      {openAddEditDialog && <Typography sx={{mt: 2, p:2, border: '1px dashed grey'}}>Dialog Thêm/Sửa Thông tin Sức khỏe (Placeholder - Data: {JSON.stringify(editingData)})</Typography>}
      {openDetailDialog && viewingData && <Typography sx={{mt: 2, p:2, border: '1px dashed blue'}}>Dialog Xem Chi tiết Sức khỏe (Placeholder - Data: {JSON.stringify(viewingData)})</Typography>}
      {openConfirmDelete && <Typography sx={{mt: 2, p:2, border: '1px dashed red'}}>Dialog Xác nhận Xóa (Placeholder - ID: {deletingId})</Typography>}
    </Box>

);
};

export default TabSucKhoe;
Bước 29: Tạo Component Dialogs cho "Sức khỏe" (Sơ bộ)
DialogThemSuaSucKhoe.tsx: Form để nhập/sửa thông tin một lần khám sức khỏe. Sẽ bao gồm các TextField cho chỉ số (Chiều cao, Cân nặng, Mạch, Huyết áp - có thể cần validate kiểu số), Select cho các ID (Loại khám, Nơi khám, Nhóm máu, Phân loại SK), DatePicker cho Ngày khám.

DialogXemChiTietSucKhoe.tsx: Hiển thị tất cả các trường của ThongTinSucKhoeEntryType một cách chi tiết, bao gồm cả các trường không hiển thị trên DataGrid.

TypeScript

// src\views\quan-ly-quan-nhan\chi-tiet\components\tabs\DialogThemSuaSucKhoe.tsx (Sơ bộ)
// 'use client';
// import React, { useState, useEffect } from 'react';
// import Dialog from '@mui/material/Dialog';
// import DialogTitle from '@mui/material/DialogTitle';
// // ... (các imports khác)
// import { ThongTinSucKhoeEntryType, PhanLoaiSucKhoeType } from '../../mockDataChiTiet';
// import Grid from '@mui/material/Grid';
// import TextField from '@mui/material/TextField';
// import FormControl from '@mui/material/FormControl';
// import InputLabel from '@mui/material/InputLabel';
// import Select, { SelectChangeEvent } from '@mui/material/Select';
// import MenuItem from '@mui/material/MenuItem';

// interface DialogSucKhoeProps {
// open: boolean;
// onClose: () => void;
// onSubmit: (data: ThongTinSucKhoeEntryType) => void;
// initialData: ThongTinSucKhoeEntryType | null;
// }

// const DialogThemSuaSucKhoe = ({ open, onClose, onSubmit, initialData }: DialogSucKhoeProps) => {
// const [formData, setFormData] = useState<Partial<ThongTinSucKhoeEntryType>>(initialData || {});

// useEffect(() => {
// setFormData(initialData || {});
// }, [initialData, open]);

// const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | SelectChangeEvent<string>) => { /_ ... _/ };
// // const handleDateChange = (name: string, date: Date | null) => { /_ ... _/ };

// const handleSubmit = () => {
// onSubmit(formData as ThongTinSucKhoeEntryType); // Cần validate
// };

// const phanLoaiSKOptions: PhanLoaiSucKhoeType[] = ['Loại 1', 'Loại 2', 'Loại 3', 'Loại 4', 'Loại 5', 'Không phân loại'];

// return (
// <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
// <DialogTitle>{initialData ? 'Sửa Thông tin Sức khỏe' : 'Thêm Thông tin Khám sức khỏe'}</DialogTitle>
// <DialogContent>
// <Grid container spacing={3} sx={{mt:1}}>
// {/_ <Grid item xs={12} sm={6}>
// <DatePicker label="Ngày khám" ... required />
// </Grid> _/}
// <Grid item xs={12} sm={6}>
// <TextField name="NgayKham" label="Ngày khám (YYYY-MM-DD)" type="date" InputLabelProps={{ shrink: true }} value={formData.NgayKham?.substring(0,10) || ''} onChange={handleChange} fullWidth required />
// </Grid>
// <Grid item xs={12} sm={6}>
// <TextField name="LoaiKhamID" label="ID Loại khám" value={formData.LoaiKhamID || ''} onChange={handleChange} fullWidth required />
// </Grid>
// <Grid item xs={12} sm={6}>
// <TextField name="NoiKhamID" label="ID Nơi khám" value={formData.NoiKhamID || ''} onChange={handleChange} fullWidth required />
// </Grid>
// <Grid item xs={6} sm={3}>
// <TextField name="ChieuCao" label="Chiều cao (cm)" type="number" value={formData.ChieuCao || ''} onChange={handleChange} fullWidth />
// </Grid>
// <Grid item xs={6} sm={3}>
// <TextField name="CanNang" label="Cân nặng (kg)" type="number" value={formData.CanNang || ''} onChange={handleChange} fullWidth />
// </Grid>
// <Grid item xs={12} sm={6}>
// <TextField name="NhomMauID" label="ID Nhóm máu" value={formData.NhomMauID || ''} onChange={handleChange} fullWidth />
// </Grid>
// <Grid item xs={6} sm={3}>
// <TextField name="Mach" label="Mạch (lần/phút)" type="number" value={formData.Mach || ''} onChange={handleChange} fullWidth />
// </Grid>
// <Grid item xs={6} sm={3}>
// <TextField name="HuyetAp" label="Huyết áp (mmHg)" value={formData.HuyetAp || ''} onChange={handleChange} fullWidth />
// </Grid>
// <Grid item xs={12} sm={6}>
// <FormControl fullWidth>
// <InputLabel id="phan-loai-sk-label">Phân loại Sức khỏe</InputLabel>
// <Select
// labelId="phan-loai-sk-label"
// name="PhanLoaiSucKhoeID"
// value={formData.PhanLoaiSucKhoeID || ''}
// label="Phân loại Sức khỏe"
// onChange={handleChange as any} // Cast as SelectChangeEvent might be needed for name
// >
// {phanLoaiSKOptions.map((option) => (
// <MenuItem key={option} value={option}>{option}</MenuItem>
// ))}
// </Select>
// </FormControl>
// </Grid>
// <Grid item xs={12}>
// <TextField name="KetLuanBacSi" label="Kết luận của Bác sĩ" value={formData.KetLuanBacSi || ''} onChange={handleChange} fullWidth multiline rows={3}/>
// </Grid>
// {/_ ... Thêm các trường: BenhLyManTinh, TienSuBenh, DiUngThuocThucPham, FileDinhKemURL, GhiChuYBacSi ... _/}
// </Grid>
// </DialogContent>
// <DialogActions>
// <Button onClick={onClose}>Hủy</Button>
// <Button onClick={handleSubmit} variant="contained">Lưu</Button>
// </DialogActions>
// </Dialog>
// );
// };
// export default DialogThemSuaSucKhoe;
Bước 30: Cập nhật KhuVucTabsChiTiet.tsx để sử dụng TabSucKhoe
Chỉnh sửa file src\views\quan-ly-quan-nhan\chi-tiet\components\KhuVucTabsChiTiet.tsx:

TypeScript

// src\views\quan-ly-quan-nhan\chi-tiet\components\KhuVucTabsChiTiet.tsx
// ... (các imports khác)
import TabSucKhoe from './tabs/TabSucKhoe';
// ... (import các tab khác khi tạo xong)

// interface TabsProps { ... }

const KhuVucTabsChiTiet = ({ quanNhanData, activeTab, handleTabChange }: TabsProps) => {
const tabContentList: { [key: string]: React.ReactNode } = {
'thong-tin-chung': <TabThongTinChung data={quanNhanData.baseInfo} />,
'ly-lich-ca-nhan': <TabLyLichCaNhan data={quanNhanData.lyLichCaNhan} />,
'qua-trinh-cong-tac': <TabQuaTrinhCongTac initialData={quanNhanData.quaTrinhCongTac} idQuanNhan={quanNhanData.baseInfo.SoHieuQuanNhan} />,
'qua-trinh-dao-tao': <TabQuaTrinhDaoTao initialData={quanNhanData.quaTrinhDaoTao} idQuanNhan={quanNhanData.baseInfo.SoHieuQuanNhan} />,
'khen-thuong': <TabKhenThuong initialData={quanNhanData.khenThuong} idQuanNhan={quanNhanData.baseInfo.SoHieuQuanNhan} />,
'ky-luat': <TabKyLuat initialData={quanNhanData.kyLuat} idQuanNhan={quanNhanData.baseInfo.SoHieuQuanNhan} />,
'suc-khoe': <TabSucKhoe initialData={quanNhanData.sucKhoe} idQuanNhan={quanNhanData.baseInfo.SoHieuQuanNhan} />,
'quan-he-gia-dinh': <div>Nội dung Tab Quan hệ Gia đình</div>,
// ... các tab khác
};

// ... (phần còn lại của component giữ nguyên)
return (
<TabContext value={activeTab}>
<Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
<TabList /_ ...props... _/ >
{/_ ...Tabs... _/}
</TabList>
</Box>
{Object.keys(tabContentList).map(tabValue => (
<TabPanel key={tabValue} value={tabValue} sx={{ p: 0 }}>
<CardContent>
{tabContentList[tabValue]}
</CardContent>
</TabPanel>
))}
</TabContext>
);
};

export default KhuVucTabsChiTiet;
