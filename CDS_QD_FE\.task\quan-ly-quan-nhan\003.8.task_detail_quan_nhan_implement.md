Bước 31: C<PERSON><PERSON> nhật mockDataChiTiet.ts cho "Quan hệ <PERSON>ia đình"
Định nghĩa ThanNhanEntryType và các type liên quan (ví dụ: TrangThaiThanNhanType):

Thêm dữ liệu mẫu vào mockQuanNhanChiTietData.thanNhan.

TypeScript

// src\views\quan-ly-quan-nhan\chi-tiet\mockDataChiTiet.ts (Thêm/Cập nhật)

export type TrangThaiThanNhanType = 'Còn sống' | 'Đã mất';

export interface ThanNhanEntryType {
id: string; // ID duy nhất cho mỗi thân nhân
HoTenThanNhan: string;
NgaySinhThanNhan?: string; // ISO Date string
MoiQuanHeVoiQuanNhanID: string; // Sẽ map sang tên (Bố, Mẹ, Vợ, Chồng, Con, Anh, Chị, Em...)
NgheNghiepID?: string; // Sẽ map sang tên
NoiO_CongTacHienTai?: string; // Địa chỉ hoặc nơi công tác
HoanCanhKinhTe?: string; // Mô tả ngắn gọn
TinhHinhChinhTriThanNhan?: string; // Thông tin nhạy cảm, cần kiểm soát hiển thị
LaDangVien?: boolean; // True/False
ThamGiaTCCTXH?: string; // Tên các tổ chức tham gia
LichSuChinhTriLuuY?: string; // Thông tin nhạy cảm, cần kiểm soát hiển thị
SoDienThoaiLienHe?: string;
TrangThaiThanNhan: TrangThaiThanNhanType;
NgayMat?: string; // ISO Date string, chỉ có nếu TrangThaiThanNhan là 'Đã mất'
GhiChu?: string;
}

// Trong mockQuanNhanChiTietData:
// ...
// thanNhan: [
// {
// id: 'tn_001',
// HoTenThanNhan: 'Nguyễn Thị B',
// NgaySinhThanNhan: '1965-03-10T00:00:00Z',
// MoiQuanHeVoiQuanNhanID: 'MQH_ME', // Mẹ đẻ
// NgheNghiepID: 'NN_HUU', // Hưu trí
// NoiO_CongTacHienTai: 'Số 10, Đường ABC, Quận XYZ, TP HCM',
// HoanCanhKinhTe: 'Ổn định',
// TinhHinhChinhTriThanNhan: 'Chấp hành tốt chủ trương chính sách.', // Ví dụ
// LaDangVien: true,
// ThamGiaTCCTXH: 'Hội Phụ nữ phường',
// LichSuChinhTriLuuY: 'Không có', // Ví dụ
// SoDienThoaiLienHe: '0912345678',
// TrangThaiThanNhan: 'Còn sống',
// GhiChu: 'Sức khỏe tốt.'
// },
// {
// id: 'tn_002',
// HoTenThanNhan: 'Trần Văn C',
// NgaySinhThanNhan: '1993-11-25T00:00:00Z',
// MoiQuanHeVoiQuanNhanID: 'MQH_VO', // Vợ
// NgheNghiepID: 'NN_GV', // Giáo viên
// NoiO_CongTacHienTai: 'Trường THCS XYZ, Quận 1, TP HCM',
// HoanCanhKinhTe: 'Khá',
// TinhHinhChinhTriThanNhan: 'Tốt',
// LaDangVien: false,
// ThamGiaTCCTXH: 'Công đoàn trường',
// SoDienThoaiLienHe: '0987654321',
// TrangThaiThanNhan: 'Còn sống',
// GhiChu: ''
// },
// {
// id: 'tn_003',
// HoTenThanNhan: 'Trần Gia Hân',
// NgaySinhThanNhan: '2020-05-02T00:00:00Z',
// MoiQuanHeVoiQuanNhanID: 'MQH_CON', // Con gái
// NgheNghiepID: 'NN_HS', // Học sinh
// NoiO_CongTacHienTai: 'Như trên',
// TrangThaiThanNhan: 'Còn sống',
// }
// ] as ThanNhanEntryType[],
// ...
Bước 32: Xây dựng TabPanel cho "Quan hệ Gia đình"
Tạo file src\views\quan-ly-quan-nhan\chi-tiet\components\tabs\TabQuanHeGiaDinh.tsx

Mục đích: Hiển thị danh sách thân nhân, cho phép thêm, sửa, xóa.

Props:

initialData?: ThanNhanEntryType[]
idQuanNhan: string
State: Tương tự các tab DataGrid trước.

Component Vuexy (MUI & @mui/x-data-grid): Tương tự.

Cấu hình cột cho DataGrid:

HoTenThanNhan
NgaySinhThanNhan (định dạng dd/MM/yyyy)
MoiQuanHeVoiQuanNhanID (hiển thị tên)
NgheNghiepID (hiển thị tên)
NoiO_CongTacHienTai
SoDienThoaiLienHe
TrangThaiThanNhan (Còn sống/Đã mất)
LaDangVien (Có/Không)
Hành động: Sửa, Xóa. (Các trường nhạy cảm như TinhHinhChinhTriThanNhan, LichSuChinhTriLuuY sẽ được xem/sửa trong Dialog).
TypeScript

// src\views\quan-ly-quan-nhan\chi-tiet\components\tabs\TabQuanHeGiaDinh.tsx
'use client';

import React, { useState, useEffect } from 'react';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import { DataGrid, GridColDef, GridRenderCellParams, GridValueFormatterParams } from '@mui/x-data-grid';
import IconButton from '@mui/material/IconButton';
import Tooltip from '@mui/material/Tooltip';

import IconPlus from '@tabler/icons-react/dist/esm/icons/IconPlus';
import IconEdit from '@tabler/icons-react/dist/esm/icons/IconEdit';
import IconTrash from '@tabler/icons-react/dist/esm/icons/IconTrash';

import { ThanNhanEntryType } from '../../mockDataChiTiet';
// Giả định có DialogThemSuaThanNhan và DialogXacNhanXoaItem
// import DialogThemSuaThanNhan from './DialogThemSuaThanNhan';
// import DialogXacNhanXoaItem from '../../../../components/DialogXacNhanXoaItem';

interface TabQuanHeGiaDinhProps {
initialData?: ThanNhanEntryType[];
idQuanNhan: string;
}

const formatDateDatagridQHGD = (dateString?: string | null): string => {
if (!dateString) return '';
try {
const date = new Date(dateString);
if (isNaN(date.getTime())) return 'Không hợp lệ';
return `<span class="math-inline">\{String\(date\.getDate\(\)\)\.padStart\(2, '0'\)\}/</span>{String(date.getMonth() + 1).padStart(2, '0')}/${date.getFullYear()}`;
} catch (e) { return 'Không hợp lệ'; }
};
const mapIdToStringQHGD = (id?: string, type?: string) => id || 'N/A'; // Placeholder

const TabQuanHeGiaDinh = ({ initialData = [], idQuanNhan }: TabQuanHeGiaDinhProps) => {
const [listData, setListData] = useState<ThanNhanEntryType[]>(initialData);
const [openAddEditDialog, setOpenAddEditDialog] = useState(false);
const [editingData, setEditingData] = useState<ThanNhanEntryType | null>(null);
const [openConfirmDelete, setOpenConfirmDelete] = useState(false);
const [deletingId, setDeletingId] = useState<string | null>(null);
const [paginationModel, setPaginationModel] = useState({ page: 0, pageSize: 5 });

useEffect(() => {
setListData(initialData);
}, [initialData]);

const handleOpenAddDialog = () => { setEditingData(null); setOpenAddEditDialog(true); };
const handleOpenEditDialog = (rowData: ThanNhanEntryType) => { setEditingData(rowData); setOpenAddEditDialog(true); };

const handleCloseDialogs = () => {
setOpenAddEditDialog(false);
setEditingData(null);
};

const handleSaveData = (savedData: ThanNhanEntryType) => {
console.log('Saving data (Thân nhân):', savedData, 'for QN ID:', idQuanNhan);
if (editingData) {
setListData(prev => prev.map(item => (item.id === savedData.id ? savedData : item)));
} else {
setListData(prev => [...prev, { ...savedData, id: `new_tn_${Date.now()}` }]);
}
handleCloseDialogs();
};

const handleOpenDeleteDialog = (id: string) => { setDeletingId(id); setOpenConfirmDelete(true); };
const handleCloseConfirmDelete = () => { setOpenConfirmDelete(false); setDeletingId(null);};
const handleConfirmDelete = () => {
if (deletingId) {
console.log('Deleting Thân nhân ID:', deletingId);
setListData(prev => prev.filter(item => item.id !== deletingId));
handleCloseConfirmDelete();
}
};

const columns: GridColDef[] = [
{ field: 'HoTenThanNhan', headerName: 'Họ tên Thân nhân', width: 200 },
{
field: 'NgaySinhThanNhan',
headerName: 'Ngày sinh',
width: 120,
valueFormatter: (params: GridValueFormatterParams<string | undefined>) => formatDateDatagridQHGD(params.value)
},
{
field: 'MoiQuanHeVoiQuanNhanID',
headerName: 'Mối quan hệ',
width: 150,
valueGetter: params => mapIdToStringQHGD(params.value, 'moiQuanHe')
},
{
field: 'NgheNghiepID',
headerName: 'Nghề nghiệp',
width: 150,
valueGetter: params => mapIdToStringQHGD(params.value, 'ngheNghiep')
},
{ field: 'NoiO_CongTacHienTai', headerName: 'Nơi ở/Công tác', width: 250 },
{ field: 'SoDienThoaiLienHe', headerName: 'SĐT Liên hệ', width: 130 },
{
field: 'LaDangVien',
headerName: 'Là Đảng viên',
width: 130,
type: 'boolean',
renderCell: (params: GridRenderCellParams) => (params.value ? 'Có' : 'Không')
},
{ field: 'TrangThaiThanNhan', headerName: 'Trạng thái', width: 120},
{
field: 'actions',
headerName: 'Hành động',
width: 120,
sortable: false,
filterable: false,
renderCell: (params: GridRenderCellParams) => (
<Box>
<Tooltip title="Sửa">
<IconButton size="small" onClick={() => handleOpenEditDialog(params.row as ThanNhanEntryType)}>
<IconEdit size={20} />
</IconButton>
</Tooltip>
<Tooltip title="Xóa">
<IconButton size="small" onClick={() => handleOpenDeleteDialog(params.row.id as string)}>
<IconTrash size={20} />
</IconButton>
</Tooltip>
</Box>
)
}
// Các trường nhạy cảm: TinhHinhChinhTriThanNhan, LichSuChinhTriLuuY, HoanCanhKinhTe, ThamGiaTCCTXH
// sẽ được quản lý trong DialogThemSuaThanNhan
];

return (
<Box>
<Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
<Typography variant="h6" sx={{ color: 'primary.main' }}>
Quan hệ Gia đình
</Typography>
<Button
variant="contained"
startIcon={<IconPlus />}
onClick={handleOpenAddDialog} >
Thêm Thân nhân
</Button>
</Box>

      <DataGrid
        autoHeight
        rows={listData}
        columns={columns}
        pageSizeOptions={[5, 10, 25]}
        paginationModel={paginationModel}
        onPaginationModelChange={setPaginationModel}
        getRowId={(row) => row.id}
        sx={{
            '& .MuiDataGrid-columnHeaders': { backgroundColor: 'customColors.tableHeaderBg' }
        }}
      />

      {/* Placeholders for Dialogs */}
      {openAddEditDialog && <Typography sx={{mt: 2, p:2, border: '1px dashed grey'}}>Dialog Thêm/Sửa Thân nhân (Placeholder - Data: {JSON.stringify(editingData)})</Typography>}
      {openConfirmDelete && <Typography sx={{mt: 2, p:2, border: '1px dashed red'}}>Dialog Xác nhận Xóa (Placeholder - ID: {deletingId})</Typography>}
    </Box>

);
};

export default TabQuanHeGiaDinh;
Bước 33: Tạo Component DialogThemSuaThanNhan.tsx (Sơ bộ)
Form này sẽ chứa tất cả các trường của ThanNhanEntryType, bao gồm cả các trường nhạy cảm. Cần lưu ý về việc kiểm soát quyền truy cập và hiển thị các trường này trong môi trường thực tế.

TypeScript

// src\views\quan-ly-quan-nhan\chi-tiet\components\tabs\DialogThemSuaThanNhan.tsx (Sơ bộ)
// 'use client';
// import React, { useState, useEffect } from 'react';
// import Dialog from '@mui/material/Dialog';
// import DialogTitle from '@mui/material/DialogTitle';
// // ... (các imports khác)
// import { ThanNhanEntryType, TrangThaiThanNhanType } from '../../mockDataChiTiet';
// import Grid from '@mui/material/Grid';
// import TextField from '@mui/material/TextField';
// import FormControlLabel from '@mui/material/FormControlLabel';
// import Checkbox from '@mui/material/Checkbox';
// import RadioGroup from '@mui/material/RadioGroup';
// import Radio from '@mui/material/Radio';
// import FormControl from '@mui/material/FormControl';
// import FormLabel from '@mui/material/FormLabel';
// import InputLabel from '@mui/material/InputLabel';
// import Select, { SelectChangeEvent } from '@mui/material/Select';
// import MenuItem from '@mui/material/MenuItem';

// interface DialogThanNhanProps {
// open: boolean;
// onClose: () => void;
// onSubmit: (data: ThanNhanEntryType) => void;
// initialData: ThanNhanEntryType | null;
// }

// const DialogThemSuaThanNhan = ({ open, onClose, onSubmit, initialData }: DialogThanNhanProps) => {
// const [formData, setFormData] = useState<Partial<ThanNhanEntryType>>(initialData || { LaDangVien: false, TrangThaiThanNhan: 'Còn sống' });

// useEffect(() => {
// setFormData(initialData || { LaDangVien: false, TrangThaiThanNhan: 'Còn sống' });
// }, [initialData, open]);

// const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | SelectChangeEvent<string>) => {
// const target = e.target as HTMLInputElement; // Type assertion
// const { name, value, type, checked } = target;
// setFormData(prev => ({
// ...prev,
// [name]: type === 'checkbox' ? checked : value
// }));
// };

// // const handleDateChange = (name: string, date: Date | null) => { /_ ... _/ };

// const handleSubmit = () => {
// onSubmit(formData as ThanNhanEntryType); // Cần validate
// };

// const trangThaiOptions: TrangThaiThanNhanType[] = ['Còn sống', 'Đã mất'];

// return (
// <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
// <DialogTitle>{initialData ? 'Sửa Thông tin Thân nhân' : 'Thêm Thông tin Thân nhân'}</DialogTitle>
// <DialogContent>
// <Grid container spacing={3} sx={{mt:1}}>
// <Grid item xs={12} sm={6}>
// <TextField name="HoTenThanNhan" label="Họ tên Thân nhân" value={formData.HoTenThanNhan || ''} onChange={handleChange} fullWidth required />
// </Grid>
// {/_ <Grid item xs={12} sm={6}>
// <DatePicker label="Ngày sinh Thân nhân" ... />
// </Grid> _/}
// <Grid item xs={12} sm={6}>
// <TextField name="NgaySinhThanNhan" label="Ngày sinh (YYYY-MM-DD)" type="date" InputLabelProps={{ shrink: true }} value={formData.NgaySinhThanNhan?.substring(0,10) || ''} onChange={handleChange} fullWidth />
// </Grid>
// <Grid item xs={12} sm={6}>
// <TextField name="MoiQuanHeVoiQuanNhanID" label="ID Mối quan hệ" value={formData.MoiQuanHeVoiQuanNhanID || ''} onChange={handleChange} fullWidth required />
// </Grid>
// {/_ ... Các trường khác: NgheNghiepID (Select), NoiO_CongTacHienTai, SoDienThoaiLienHe ... _/}
// <Grid item xs={12}>
// <TextField name="NoiO_CongTacHienTai" label="Nơi ở/Công tác hiện tại" value={formData.NoiO_CongTacHienTai || ''} onChange={handleChange} fullWidth />
// </Grid>

// <Grid item xs={12} sm={6}>
// <FormControl component="fieldset">
// <FormLabel component="legend">Là Đảng viên</FormLabel>
// <RadioGroup row name="LaDangVien" value={formData.LaDangVien ? 'true' : 'false'} onChange={(e) => setFormData(prev => ({...prev, LaDangVien: e.target.value === 'true'}))}>
// <FormControlLabel value="true" control={<Radio />} label="Có" />
// <FormControlLabel value="false" control={<Radio />} label="Không" />
// </RadioGroup>
// </FormControl>
// </Grid>
// <Grid item xs={12} sm={6}>
// <FormControl fullWidth>
// <InputLabel id="trang-thai-tn-label">Trạng thái Thân nhân</InputLabel>
// <Select
// labelId="trang-thai-tn-label"
// name="TrangThaiThanNhan"
// value={formData.TrangThaiThanNhan || ''}
// label="Trạng thái Thân nhân"
// onChange={handleChange}
// >
// {trangThaiOptions.map((option) => (
// <MenuItem key={option} value={option}>{option}</MenuItem>
// ))}
// </Select>
// </FormControl>
// </Grid>
// {formData.TrangThaiThanNhan === 'Đã mất' && (
// <Grid item xs={12} sm={6}>
// <TextField name="NgayMat" label="Ngày mất (YYYY-MM-DD)" type="date" InputLabelProps={{ shrink: true }} value={formData.NgayMat?.substring(0,10) || ''} onChange={handleChange} fullWidth />
// </Grid>
// )}
// {/_ Các trường nhạy cảm - cần cân nhắc hiển thị/sửa dựa trên vai trò người dùng _/}
// <Grid item xs={12}>
// <TextField name="HoanCanhKinhTe" label="Hoàn cảnh kinh tế" value={formData.HoanCanhKinhTe || ''} onChange={handleChange} fullWidth multiline rows={2}/>
// </Grid>
// <Grid item xs={12}>
// <TextField name="ThamGiaTCCTXH" label="Tham gia Tổ chức CT-XH" value={formData.ThamGiaTCCTXH || ''} onChange={handleChange} fullWidth />
// </Grid>
// <Grid item xs={12}>
// <TextField name="TinhHinhChinhTriThanNhan" label="Tình hình Chính trị Thân nhân" helperText="Thông tin cần kiểm soát" value={formData.TinhHinhChinhTriThanNhan || ''} onChange={handleChange} fullWidth multiline rows={3}/>
// </Grid>
// <Grid item xs={12}>
// <TextField name="LichSuChinhTriLuuY" label="Vấn đề Lịch sử Chính trị cần lưu ý" helperText="Thông tin cần kiểm soát" value={formData.LichSuChinhTriLuuY || ''} onChange={handleChange} fullWidth multiline rows={3}/>
// </Grid>
// <Grid item xs={12}>
// <TextField name="GhiChu" label="Ghi chú" value={formData.GhiChu || ''} onChange={handleChange} fullWidth multiline rows={2}/>
// </Grid>
// </Grid>
// </DialogContent>
// <DialogActions>
// <Button onClick={onClose}>Hủy</Button>
// <Button onClick={handleSubmit} variant="contained">Lưu</Button>
// </DialogActions>
// </Dialog>
// );
// };
// export default DialogThemSuaThanNhan;
Bước 34: Cập nhật KhuVucTabsChiTiet.tsx để sử dụng TabQuanHeGiaDinh
Chỉnh sửa file src\views\quan-ly-quan-nhan\chi-tiet\components\KhuVucTabsChiTiet.tsx:

TypeScript

// src\views\quan-ly-quan-nhan\chi-tiet\components\KhuVucTabsChiTiet.tsx
// ... (các imports khác)
import TabQuanHeGiaDinh from './tabs/TabQuanHeGiaDinh';
// ... (import các tab khác khi tạo xong)

// interface TabsProps { ... }

const KhuVucTabsChiTiet = ({ quanNhanData, activeTab, handleTabChange }: TabsProps) => {
const tabContentList: { [key: string]: React.ReactNode } = {
'thong-tin-chung': <TabThongTinChung data={quanNhanData.baseInfo} />,
'ly-lich-ca-nhan': <TabLyLichCaNhan data={quanNhanData.lyLichCaNhan} />,
'qua-trinh-cong-tac': <TabQuaTrinhCongTac initialData={quanNhanData.quaTrinhCongTac} idQuanNhan={quanNhanData.baseInfo.SoHieuQuanNhan} />,
'qua-trinh-dao-tao': <TabQuaTrinhDaoTao initialData={quanNhanData.quaTrinhDaoTao} idQuanNhan={quanNhanData.baseInfo.SoHieuQuanNhan} />,
'khen-thuong': <TabKhenThuong initialData={quanNhanData.khenThuong} idQuanNhan={quanNhanData.baseInfo.SoHieuQuanNhan} />,
'ky-luat': <TabKyLuat initialData={quanNhanData.kyLuat} idQuanNhan={quanNhanData.baseInfo.SoHieuQuanNhan} />,
'suc-khoe': <TabSucKhoe initialData={quanNhanData.sucKhoe} idQuanNhan={quanNhanData.baseInfo.SoHieuQuanNhan} />,
'quan-he-gia-dinh': <TabQuanHeGiaDinh initialData={quanNhanData.thanNhan} idQuanNhan={quanNhanData.baseInfo.SoHieuQuanNhan} />,
'che-do-chinh-sach': <div>Nội dung Tab Chế độ Chính sách</div>,
// ... các tab khác
};

// ... (phần còn lại của component giữ nguyên)
return (
<TabContext value={activeTab}>
<Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
<TabList /_ ...props... _/ >
{/_ ...Tabs... _/}
</TabList>
</Box>
{Object.keys(tabContentList).map(tabValue => (
<TabPanel key={tabValue} value={tabValue} sx={{ p: 0 }}>
<CardContent>
{tabContentList[tabValue]}
</CardContent>
</TabPanel>
))}
</TabContext>
);
};

export default KhuVucTabsChiTiet;
