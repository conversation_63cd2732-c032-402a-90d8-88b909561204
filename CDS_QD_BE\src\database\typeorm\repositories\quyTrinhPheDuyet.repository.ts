/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { QuyTrinhPheDuyetEntity } from '~/database/typeorm/entities/quyTrinhPheDuyet.entity';

@Injectable()
export class QuyTrinhPheDuyetRepository extends Repository<QuyTrinhPheDuyetEntity> {
    constructor(private dataSource: DataSource) {
        super(QuyTrinhPheDuyetEntity, dataSource.createEntityManager());
    }
}
