/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 *  Swagger
 * The  API documents
 * OpenAPI spec version: 1.0
 */
import { useInfiniteQuery, useMutation, useQuery } from '@tanstack/react-query'
import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseInfiniteQueryResult,
  DefinedUseQueryResult,
  InfiniteData,
  MutationFunction,
  QueryClient,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseInfiniteQueryOptions,
  UseInfiniteQueryResult,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult
} from '@tanstack/react-query'

import type { MediaControllerUploadBody } from '.././model'

import { customInstance } from '../../mutator/custom-instance'
import type { ApiResponse } from '../../types'

export const mediaControllerUpload = (mediaControllerUploadBody: MediaControllerUploadBody, signal?: AbortSignal) => {
  const formData = new FormData()
  if (mediaControllerUploadBody.file !== undefined) {
    formData.append(`file`, mediaControllerUploadBody.file)
  }

  return customInstance<ApiResponse<void>>({
    url: `/media/upload`,
    method: 'POST',
    headers: { 'Content-Type': 'multipart/form-data' },
    data: formData,
    signal
  })
}

export const getMediaControllerUploadMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof mediaControllerUpload>>,
    TError,
    { data: MediaControllerUploadBody },
    TContext
  >
}): UseMutationOptions<
  Awaited<ReturnType<typeof mediaControllerUpload>>,
  TError,
  { data: MediaControllerUploadBody },
  TContext
> => {
  const mutationKey = ['mediaControllerUpload']
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } }

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof mediaControllerUpload>>,
    { data: MediaControllerUploadBody }
  > = props => {
    const { data } = props ?? {}

    return mediaControllerUpload(data)
  }

  return { mutationFn, ...mutationOptions }
}

export type MediaControllerUploadMutationResult = NonNullable<Awaited<ReturnType<typeof mediaControllerUpload>>>
export type MediaControllerUploadMutationBody = MediaControllerUploadBody
export type MediaControllerUploadMutationError = unknown

export const useMediaControllerUpload = <TError = unknown, TContext = unknown>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof mediaControllerUpload>>,
      TError,
      { data: MediaControllerUploadBody },
      TContext
    >
  },
  queryClient?: QueryClient
): UseMutationResult<
  Awaited<ReturnType<typeof mediaControllerUpload>>,
  TError,
  { data: MediaControllerUploadBody },
  TContext
> => {
  const mutationOptions = getMediaControllerUploadMutationOptions(options)

  return useMutation(mutationOptions, queryClient)
}
export const mediaControllerFindAll = (signal?: AbortSignal) => {
  return customInstance<ApiResponse<void>>({ url: `/media`, method: 'GET', signal })
}

export const getMediaControllerFindAllQueryKey = () => {
  return [`/media`] as const
}

export const getMediaControllerFindAllInfiniteQueryOptions = <
  TData = InfiniteData<Awaited<ReturnType<typeof mediaControllerFindAll>>>,
  TError = unknown
>(options?: {
  query?: Partial<UseInfiniteQueryOptions<Awaited<ReturnType<typeof mediaControllerFindAll>>, TError, TData>>
}) => {
  const { query: queryOptions } = options ?? {}

  const queryKey = queryOptions?.queryKey ?? getMediaControllerFindAllQueryKey()

  const queryFn: QueryFunction<Awaited<ReturnType<typeof mediaControllerFindAll>>> = ({ signal }) =>
    mediaControllerFindAll(signal)

  return { queryKey, queryFn, staleTime: 10000, ...queryOptions } as UseInfiniteQueryOptions<
    Awaited<ReturnType<typeof mediaControllerFindAll>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> }
}

export type MediaControllerFindAllInfiniteQueryResult = NonNullable<Awaited<ReturnType<typeof mediaControllerFindAll>>>
export type MediaControllerFindAllInfiniteQueryError = unknown

export function useMediaControllerFindAllInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof mediaControllerFindAll>>>,
  TError = unknown
>(
  options: {
    query: Partial<UseInfiniteQueryOptions<Awaited<ReturnType<typeof mediaControllerFindAll>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof mediaControllerFindAll>>,
          TError,
          Awaited<ReturnType<typeof mediaControllerFindAll>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): DefinedUseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useMediaControllerFindAllInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof mediaControllerFindAll>>>,
  TError = unknown
>(
  options?: {
    query?: Partial<UseInfiniteQueryOptions<Awaited<ReturnType<typeof mediaControllerFindAll>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof mediaControllerFindAll>>,
          TError,
          Awaited<ReturnType<typeof mediaControllerFindAll>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useMediaControllerFindAllInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof mediaControllerFindAll>>>,
  TError = unknown
>(
  options?: {
    query?: Partial<UseInfiniteQueryOptions<Awaited<ReturnType<typeof mediaControllerFindAll>>, TError, TData>>
  },
  queryClient?: QueryClient
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }

export function useMediaControllerFindAllInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof mediaControllerFindAll>>>,
  TError = unknown
>(
  options?: {
    query?: Partial<UseInfiniteQueryOptions<Awaited<ReturnType<typeof mediaControllerFindAll>>, TError, TData>>
  },
  queryClient?: QueryClient
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
  const queryOptions = getMediaControllerFindAllInfiniteQueryOptions(options)

  const query = useInfiniteQuery(queryOptions, queryClient) as UseInfiniteQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>
  }

  query.queryKey = queryOptions.queryKey

  return query
}

export const getMediaControllerFindAllQueryOptions = <
  TData = Awaited<ReturnType<typeof mediaControllerFindAll>>,
  TError = unknown
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof mediaControllerFindAll>>, TError, TData>>
}) => {
  const { query: queryOptions } = options ?? {}

  const queryKey = queryOptions?.queryKey ?? getMediaControllerFindAllQueryKey()

  const queryFn: QueryFunction<Awaited<ReturnType<typeof mediaControllerFindAll>>> = ({ signal }) =>
    mediaControllerFindAll(signal)

  return { queryKey, queryFn, staleTime: 10000, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof mediaControllerFindAll>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> }
}

export type MediaControllerFindAllQueryResult = NonNullable<Awaited<ReturnType<typeof mediaControllerFindAll>>>
export type MediaControllerFindAllQueryError = unknown

export function useMediaControllerFindAll<TData = Awaited<ReturnType<typeof mediaControllerFindAll>>, TError = unknown>(
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof mediaControllerFindAll>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof mediaControllerFindAll>>,
          TError,
          Awaited<ReturnType<typeof mediaControllerFindAll>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useMediaControllerFindAll<TData = Awaited<ReturnType<typeof mediaControllerFindAll>>, TError = unknown>(
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof mediaControllerFindAll>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof mediaControllerFindAll>>,
          TError,
          Awaited<ReturnType<typeof mediaControllerFindAll>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useMediaControllerFindAll<TData = Awaited<ReturnType<typeof mediaControllerFindAll>>, TError = unknown>(
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof mediaControllerFindAll>>, TError, TData>> },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }

export function useMediaControllerFindAll<TData = Awaited<ReturnType<typeof mediaControllerFindAll>>, TError = unknown>(
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof mediaControllerFindAll>>, TError, TData>> },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
  const queryOptions = getMediaControllerFindAllQueryOptions(options)

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>
  }

  query.queryKey = queryOptions.queryKey

  return query
}

export const mediaControllerFindOne = (id: string, signal?: AbortSignal) => {
  return customInstance<ApiResponse<ApiResponse<void>>>({ url: `/media/${id}`, method: 'GET', signal })
}

export const getMediaControllerFindOneQueryKey = (id: string) => {
  return [`/media/${id}`] as const
}

export const getMediaControllerFindOneInfiniteQueryOptions = <
  TData = InfiniteData<Awaited<ReturnType<typeof mediaControllerFindOne>>>,
  TError = unknown
>(
  id: string,
  options?: {
    query?: Partial<UseInfiniteQueryOptions<Awaited<ReturnType<typeof mediaControllerFindOne>>, TError, TData>>
  }
) => {
  const { query: queryOptions } = options ?? {}

  const queryKey = queryOptions?.queryKey ?? getMediaControllerFindOneQueryKey(id)

  const queryFn: QueryFunction<Awaited<ReturnType<typeof mediaControllerFindOne>>> = ({ signal }) =>
    mediaControllerFindOne(id, signal)

  return { queryKey, queryFn, enabled: !!id, staleTime: 10000, ...queryOptions } as UseInfiniteQueryOptions<
    Awaited<ReturnType<typeof mediaControllerFindOne>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> }
}

export type MediaControllerFindOneInfiniteQueryResult = NonNullable<Awaited<ReturnType<typeof mediaControllerFindOne>>>
export type MediaControllerFindOneInfiniteQueryError = unknown

export function useMediaControllerFindOneInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof mediaControllerFindOne>>>,
  TError = unknown
>(
  id: string,
  options: {
    query: Partial<UseInfiniteQueryOptions<Awaited<ReturnType<typeof mediaControllerFindOne>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof mediaControllerFindOne>>,
          TError,
          Awaited<ReturnType<typeof mediaControllerFindOne>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): DefinedUseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useMediaControllerFindOneInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof mediaControllerFindOne>>>,
  TError = unknown
>(
  id: string,
  options?: {
    query?: Partial<UseInfiniteQueryOptions<Awaited<ReturnType<typeof mediaControllerFindOne>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof mediaControllerFindOne>>,
          TError,
          Awaited<ReturnType<typeof mediaControllerFindOne>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useMediaControllerFindOneInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof mediaControllerFindOne>>>,
  TError = unknown
>(
  id: string,
  options?: {
    query?: Partial<UseInfiniteQueryOptions<Awaited<ReturnType<typeof mediaControllerFindOne>>, TError, TData>>
  },
  queryClient?: QueryClient
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }

export function useMediaControllerFindOneInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof mediaControllerFindOne>>>,
  TError = unknown
>(
  id: string,
  options?: {
    query?: Partial<UseInfiniteQueryOptions<Awaited<ReturnType<typeof mediaControllerFindOne>>, TError, TData>>
  },
  queryClient?: QueryClient
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
  const queryOptions = getMediaControllerFindOneInfiniteQueryOptions(id, options)

  const query = useInfiniteQuery(queryOptions, queryClient) as UseInfiniteQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>
  }

  query.queryKey = queryOptions.queryKey

  return query
}

export const getMediaControllerFindOneQueryOptions = <
  TData = Awaited<ReturnType<typeof mediaControllerFindOne>>,
  TError = unknown
>(
  id: string,
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof mediaControllerFindOne>>, TError, TData>> }
) => {
  const { query: queryOptions } = options ?? {}

  const queryKey = queryOptions?.queryKey ?? getMediaControllerFindOneQueryKey(id)

  const queryFn: QueryFunction<Awaited<ReturnType<typeof mediaControllerFindOne>>> = ({ signal }) =>
    mediaControllerFindOne(id, signal)

  return { queryKey, queryFn, enabled: !!id, staleTime: 10000, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof mediaControllerFindOne>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> }
}

export type MediaControllerFindOneQueryResult = NonNullable<Awaited<ReturnType<typeof mediaControllerFindOne>>>
export type MediaControllerFindOneQueryError = unknown

export function useMediaControllerFindOne<TData = Awaited<ReturnType<typeof mediaControllerFindOne>>, TError = unknown>(
  id: string,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof mediaControllerFindOne>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof mediaControllerFindOne>>,
          TError,
          Awaited<ReturnType<typeof mediaControllerFindOne>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useMediaControllerFindOne<TData = Awaited<ReturnType<typeof mediaControllerFindOne>>, TError = unknown>(
  id: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof mediaControllerFindOne>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof mediaControllerFindOne>>,
          TError,
          Awaited<ReturnType<typeof mediaControllerFindOne>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useMediaControllerFindOne<TData = Awaited<ReturnType<typeof mediaControllerFindOne>>, TError = unknown>(
  id: string,
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof mediaControllerFindOne>>, TError, TData>> },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }

export function useMediaControllerFindOne<TData = Awaited<ReturnType<typeof mediaControllerFindOne>>, TError = unknown>(
  id: string,
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof mediaControllerFindOne>>, TError, TData>> },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
  const queryOptions = getMediaControllerFindOneQueryOptions(id, options)

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>
  }

  query.queryKey = queryOptions.queryKey

  return query
}

export const mediaControllerRemove = (id: string) => {
  return customInstance<ApiResponse<void>>({ url: `/media/${id}`, method: 'DELETE' })
}

export const getMediaControllerRemoveMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<Awaited<ReturnType<typeof mediaControllerRemove>>, TError, { id: string }, TContext>
}): UseMutationOptions<Awaited<ReturnType<typeof mediaControllerRemove>>, TError, { id: string }, TContext> => {
  const mutationKey = ['mediaControllerRemove']
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } }

  const mutationFn: MutationFunction<Awaited<ReturnType<typeof mediaControllerRemove>>, { id: string }> = props => {
    const { id } = props ?? {}

    return mediaControllerRemove(id)
  }

  return { mutationFn, ...mutationOptions }
}

export type MediaControllerRemoveMutationResult = NonNullable<Awaited<ReturnType<typeof mediaControllerRemove>>>

export type MediaControllerRemoveMutationError = unknown

export const useMediaControllerRemove = <TError = unknown, TContext = unknown>(
  options?: {
    mutation?: UseMutationOptions<Awaited<ReturnType<typeof mediaControllerRemove>>, TError, { id: string }, TContext>
  },
  queryClient?: QueryClient
): UseMutationResult<Awaited<ReturnType<typeof mediaControllerRemove>>, TError, { id: string }, TContext> => {
  const mutationOptions = getMediaControllerRemoveMutationOptions(options)

  return useMutation(mutationOptions, queryClient)
}
