# Hướng dẫn Validate Form với React Hook Form và Zod

## 1. Tổng quan
- **React Hook Form**: Thư viện quản lý form hiệu suất cao
- **Zod**: Thư viện validation schema với TypeScript first
- **@hookform/resolvers**: Cầu nối giữa React Hook Form và các schema validators

## 2. Cài đặt

```bash
pnpm add react-hook-form @hookform/resolvers zod
```

## 3. Khởi tạo Schema với Zod

```typescript
// schemas/user.schema.ts
import { z } from 'zod';

export const userSchema = z.object({
  username: z
    .string()
    .min(3, 'Tên đăng nhập phải có ít nhất 3 ký tự')
    .max(20, 'Tên đăng nhập không quá 20 ký tự'),
    
  email: z
    .string()
    .email('Email không hợp lệ')
    .min(1, '<PERSON><PERSON> là bắt buộc'),
    
  password: z
    .string()
    .min(8, '<PERSON><PERSON>t khẩu phải có ít nhất 8 ký tự')
    .regex(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,}$/,
      'Mật khẩu phải chứa ít nhất 1 chữ hoa, 1 chữ thường và 1 số'
    ),
    
  confirmPassword: z.string()
}).refine(
  (data) => data.password === data.confirmPassword,
  {
    message: 'Mật khẩu không khớp',
    path: ['confirmPassword']
  }
);

export type UserFormData = z.infer<typeof userSchema>;
```

## 4. Tạo Form Component

```tsx
// components/forms/UserForm.tsx
'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { userSchema, UserFormData } from '@/schemas/user.schema';
import { Button, TextField, Stack, Alert, Box } from '@mui/material';

export function UserForm() {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<UserFormData>({
    resolver: zodResolver(userSchema),
    defaultValues: {
      username: '',
      email: '',
      password: '',
      confirmPassword: '',
    },
  });

  const onSubmit = async (data: UserFormData) => {
    try {
      // Xử lý submit form
      console.log(data);
      
      // Reset form sau khi submit thành công
      reset();
      
      // Hiển thị thông báo thành công
      alert('Đăng ký thành công!');
    } catch (error) {
      console.error('Lỗi khi đăng ký:', error);
    }
  };

  return (
    <Box component="form" onSubmit={handleSubmit(onSubmit)} noValidate>
      <Stack spacing={3} maxWidth={500}>
        <TextField
          label="Tên đăng nhập"
          {...register('username')}
          error={!!errors.username}
          helperText={errors.username?.message}
          fullWidth
          required
        />
        
        <TextField
          label="Email"
          type="email"
          {...register('email')}
          error={!!errors.email}
          helperText={errors.email?.message}
          fullWidth
          required
        />
        
        <TextField
          label="Mật khẩu"
          type="password"
          {...register('password')}
          error={!!errors.password}
          helperText={errors.password?.message}
          fullWidth
          required
        />
        
        <TextField
          label="Xác nhận mật khẩu"
          type="password"
          {...register('confirmPassword')}
          error={!!errors.confirmPassword}
          helperText={errors.confirmPassword?.message}
          fullWidth
          required
        />
        
        <Button 
          type="submit" 
          variant="contained" 
          size="large"
          disabled={isSubmitting}
        >
          {isSubmitting ? 'Đang xử lý...' : 'Đăng ký'}
        </Button>
      </Stack>
    </Box>
  );
}
```

## 5. Xử lý Form với API

```tsx
// components/forms/UserFormWithAPI.tsx
'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { userSchema, UserFormData } from '@/schemas/user.schema';
import { useMutation } from '@tanstack/react-query';
import { userService } from '@/services/user.service';
import { toast } from 'react-toastify';

export function UserFormWithAPI() {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    setError,
  } = useForm<UserFormData>({
    resolver: zodResolver(userSchema),
  });

  const { mutate: createUser } = useMutation({
    mutationFn: (data: UserFormData) => userService.createUser(data),
    onSuccess: () => {
      toast.success('Tạo người dùng thành công!');
      reset();
    },
    onError: (error: any) => {
      // Xử lý lỗi từ API
      if (error.response?.data?.errors) {
        error.response.data.errors.forEach((err: any) => {
          setError(err.path[0], {
            type: 'manual',
            message: err.message,
          });
        });
      } else {
        toast.error('Đã xảy ra lỗi khi tạo người dùng');
      }
    },
  });

  return (
    <form onSubmit={handleSubmit((data) => createUser(data))}>
      {/* Form fields giống như ví dụ trước */}
    </form>
  );
}
```

## 6. Custom Form Controls

```tsx
// components/forms/FormInput.tsx
import { 
  TextField, 
  TextFieldProps,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
} from '@mui/material';
import { Control, Controller } from 'react-hook-form';

interface FormInputProps extends Omit<TextFieldProps, 'name'> {
  name: string;
  control: Control<any>;
  label: string;
  options?: { value: string | number; label: string }[];
}

export function FormInput({ 
  name, 
  control, 
  label, 
  options,
  ...props 
}: FormInputProps) {
  if (options) {
    return (
      <Controller
        name={name}
        control={control}
        render={({ field, fieldState: { error } }) => (
          <FormControl fullWidth error={!!error}>
            <InputLabel>{label}</InputLabel>
            <Select
              {...field}
              label={label}
              value={field.value || ''}
            >
              {options.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
            {error && <FormHelperText>{error.message}</FormHelperText>}
          </FormControl>
        )}
      />
    );
  }

  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState: { error } }) => (
        <TextField
          {...field}
          {...props}
          label={label}
          error={!!error}
          helperText={error?.message || props.helperText}
          fullWidth
        />
      )}
    />
  );
}
```

## 7. Sử dụng Custom Form Controls

```tsx
// components/forms/UserFormWithCustomInputs.tsx
'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { userSchema } from '@/schemas/user.schema';
import { Button, Stack, Box } from '@mui/material';
import { FormInput } from './FormInput';

const roles = [
  { value: 'admin', label: 'Quản trị viên' },
  { value: 'editor', label: 'Biên tập viên' },
  { value: 'viewer', label: 'Người xem' },
];

export function UserFormWithCustomInputs() {
  const {
    control,
    handleSubmit,
    formState: { isSubmitting },
  } = useForm({
    resolver: zodResolver(userSchema),
  });

  const onSubmit = (data: any) => {
    console.log(data);
  };

  return (
    <Box component="form" onSubmit={handleSubmit(onSubmit)} noValidate>
      <Stack spacing={3} maxWidth={500}>
        <FormInput
          name="username"
          control={control}
          label="Tên đăng nhập"
          required
        />
        
        <FormInput
          name="email"
          control={control}
          label="Email"
          type="email"
          required
        />
        
        <FormInput
          name="role"
          control={control}
          label="Vai trò"
          options={roles}
          required
        />
        
        <Button 
          type="submit" 
          variant="contained" 
          size="large"
          disabled={isSubmitting}
        >
          {isSubmitting ? 'Đang lưu...' : 'Lưu'}
        </Button>
      </Stack>
    </Box>
  );
}
```

## 8. Best Practices

### 8.1. Tái sử dụng Schema
- Tạo các schema nhỏ và kết hợp lại
- Sử dụng `.extend()` để mở rộng schema
- Tách biệt schema với component

### 8.2. Hiệu suất
- Sử dụng `useFormContext` cho form lớn
- Tránh re-render không cần thiết với `React.memo`
- Sử dụng `shouldUnregister: true` cho dynamic forms

### 8.3. Bảo mật
- Luôn validate dữ liệu cả phía client và server
- Không tin tưởc dữ liệu từ client
- Sử dụng CSRF token cho các form quan trọng

## 9. Xử lý lỗi nâng cao

```typescript
// utils/error-handler.ts
export function handleFormErrors(
  error: any,
  setError: (field: string, error: { type: string; message: string }) => void
) {
  if (error.response?.data?.errors) {
    error.response.data.errors.forEach((err: any) => {
      setError(err.path.join('.'), {
        type: 'manual',
        message: err.message,
      });
    });
  } else {
    console.error('Lỗi không xác định:', error);
  }
}
```

## 10. Tài liệu tham khảo
- [React Hook Form Documentation](https://react-hook-form.com/)
- [Zod Documentation](https://zod.dev/)
- [Material-UI Form Components](https://mui.com/material-ui/react-text-field/)
