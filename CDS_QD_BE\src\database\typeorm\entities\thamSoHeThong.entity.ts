import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { AbstractEntity } from './abstract.entity';

@Entity({ name: 'tham_so_he_thongs' })
export class ThamSoHeThongEntity extends AbstractEntity {
    @PrimaryGeneratedColumn('increment', { name: 'id', type: 'int', unsigned: true })
    id: number;

    @Column({ name: 'ma_tham_so', type: 'varchar', length: 100, unique: true, nullable: false })
    maThamSo: string;

    @Column({ name: 'ten_tham_so', type: 'varchar', length: 255, nullable: false })
    tenThamSo: string;

    @Column({ name: 'gia_tri_tham_so', type: 'text', nullable: true })
    giaTriThamSo?: string;

    @Column({ name: 'kieu_du_lieu', type: 'varchar', length: 50, default: 'TEXT' })
    kieuDuLieu: string;

    @Column({ name: 'mo_ta', type: 'varchar', length: 500, nullable: true })
    moTa?: string;

    @Column({ name: 'sua_doi_boi_admin', type: 'boolean', default: true })
    suaDoiBoiAdmin: boolean;

    @Column({ name: 'pham_vi_module', type: 'varchar', length: 50, nullable: true })
    phamViModule?: string;
}
