/* eslint-disable @typescript-eslint/no-var-requires */
const { generateEntity, importEntityToModule, generateRepo, importRepositoryToModule, importRepositoryToService } = require('./function');

// Kiểm tra xem các tham số đã được cung cấp chưa
if (process.argv.length < 3) {
    console.log('Cách sử dụng: node generate-combo.js <tableName1> [tableName2] ...');
    console.log('Ví dụ: node generate-combo.js loai_danh_mucs gia_tri_danh_mucs');
    process.exit(1);
}

// Lấy các tham số từ dòng lệnh (bỏ qua node và đường dẫn script)
const tableNames = process.argv.slice(2);

console.log(`Chuẩn bị tạo ${tableNames.length} cặp entity/repository...`);

/**
 * Chuyển đổi snake_case thành camelCase
 * @param {string} str - Chuỗi snake_case cần chuyển đổi
 * @returns {string} - Chuỗi camelCase
 */
function snakeToCamel(str) {
    return str.replace(/([-_][a-z])/g, (group) => group.toUpperCase().replace('-', '').replace('_', ''));
}

/**
 * Chuyển đổi camelCase thành PascalCase
 * @param {string} str - Chuỗi camelCase cần chuyển đổi
 * @returns {string} - Chuỗi PascalCase
 */
function capitalizeFirstLetter(str) {
    return str.charAt(0).toUpperCase() + str.slice(1);
}

/**
 * Tạo entity và repository
 * @param {string} tableName - Tên bảng dữ liệu
 */
function createEntityAndRepository(tableName) {
    // Chuyển đổi tên bảng sang camelCase cho tên file
    // Ví dụ: 'loai_danh_mucs' -> 'loaiDanhMucs'
    const camelCaseName = snakeToCamel(tableName.toLowerCase());

    // Loại bỏ 's' ở cuối nếu có
    const singularName = camelCaseName.endsWith('s') ? camelCaseName.slice(0, -1) : camelCaseName;

    console.log(`\nĐang tạo entity và repository cho ${singularName} (bảng ${tableName})...`);

    // Tạo entity
    console.log(`Tạo entity ${singularName}...`);
    generateEntity(tableName.toLowerCase(), singularName);

    console.log(`Import ${singularName} entity vào module...`);
    importEntityToModule(singularName);

    // Tạo repository
    console.log(`Tạo repository ${singularName}...`);
    generateRepo(singularName);

    console.log(`Import ${singularName} repository vào module...`);
    importRepositoryToModule(singularName);

    console.log(`Import ${singularName} repository vào service...`);
    importRepositoryToService(singularName);

    // Hiển thị thông tin về tên đã tạo
    const pascalName = capitalizeFirstLetter(singularName);
    console.log(`✅ Đã tạo xong entity ${pascalName}Entity và repository ${pascalName}Repository!`);
}

// Xử lý từng tên bảng
tableNames.forEach((tableName) => createEntityAndRepository(tableName));

console.log('\nHoàn tất! Đã tạo tất cả các entity và repository.');
