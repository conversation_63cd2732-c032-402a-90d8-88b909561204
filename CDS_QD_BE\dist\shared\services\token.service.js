"use strict";Object.defineProperty(exports,"__esModule",{value:true});Object.defineProperty(exports,"TokenService",{enumerable:true,get:function(){return TokenService}});const _common=require("@nestjs/common");const _config=require("@nestjs/config");const _bcrypt=/*#__PURE__*/_interop_require_wildcard(require("bcrypt"));const _crypto=require("crypto");const _jsonwebtoken=/*#__PURE__*/_interop_require_wildcard(require("jsonwebtoken"));const _enum=require("../../common/enums/enum");const _accountrepository=require("../../database/typeorm/repositories/account.repository");const _cacheservice=require("./cache.service");const _utilservice=require("./util.service");function _getRequireWildcardCache(nodeInterop){if(typeof WeakMap!=="function")return null;var cacheBabelInterop=new WeakMap;var cacheNodeInterop=new WeakMap;return(_getRequireWildcardCache=function(nodeInterop){return nodeInterop?cacheNodeInterop:cacheBabelInterop})(nodeInterop)}function _interop_require_wildcard(obj,nodeInterop){if(!nodeInterop&&obj&&obj.__esModule){return obj}if(obj===null||typeof obj!=="object"&&typeof obj!=="function"){return{default:obj}}var cache=_getRequireWildcardCache(nodeInterop);if(cache&&cache.has(obj)){return cache.get(obj)}var newObj={__proto__:null};var hasPropertyDescriptor=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var key in obj){if(key!=="default"&&Object.prototype.hasOwnProperty.call(obj,key)){var desc=hasPropertyDescriptor?Object.getOwnPropertyDescriptor(obj,key):null;if(desc&&(desc.get||desc.set)){Object.defineProperty(newObj,key,desc)}else{newObj[key]=obj[key]}}}newObj.default=obj;if(cache){cache.set(obj,newObj)}return newObj}function _ts_decorate(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect==="object"&&typeof Reflect.decorate==="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)if(d=decorators[i])r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r;return c>3&&r&&Object.defineProperty(target,key,r),r}function _ts_metadata(k,v){if(typeof Reflect==="object"&&typeof Reflect.metadata==="function")return Reflect.metadata(k,v)}let TokenService=class TokenService{createAuthToken(data){try{const{id,secretToken}=data;const tokenVersion=this.utilService.generateString(16);const{authTokenSecret,authTokenName,authExpiresIn}=this.configService.get("token");const exp=Math.floor(Date.now()/1e3)+authExpiresIn;const payload={exp,id,authTokenName,secretToken,jti:tokenVersion};const authToken=_jsonwebtoken.sign(payload,authTokenSecret);const authTokenExpiresIn=exp;return{authToken,authTokenExpiresIn}}catch(err){console.log("createAuthToken error",err);throw new _common.HttpException("Unable to create authToken",500)}}async verifyAuthToken(data){try{if(!data.authToken)return{id:null,user:null};const{authToken}=data;const jwtRegex=/(^[A-Za-z0-9-_]*\.[A-Za-z0-9-_]*\.[A-Za-z0-9-_]*$)/;const res={id:null,user:null};if(RegExp(jwtRegex).exec(authToken)){const{authTokenName,tokenData}=await this.getTokentData(authToken);const account=await this.getAccount(tokenData.id);if(process.env.LOGIN_SINGLE_DEVICE==="true"&&tokenData.secretToken&&account?.secretToken!==tokenData.secretToken)return res;if(account&&authTokenName===tokenData.authTokenName){res.id=account.id;res.user=account.user}}return res}catch(err){console.log("verifyAuthToken error",err);return{id:null,user:null}}}async getAccount(id){const key=`account:${id}`;const cached=await this.cacheService.getJson(key);if(cached)return cached;const account=await this.accountRepository.findOne({select:["id","password","secretToken"],where:{id:id},relations:["user","user.role","user.userRoles","user.userRoles.role"]});this.cacheService.setJson(key,account,_enum.CACHE_TIME.ONE_HOUR);this.cacheService.setJson(`userData:${account?.user?.id}`,account?.user,_enum.CACHE_TIME.ONE_WEEK);return account}async getTokentData(authToken){const cacheKey=`tokenData:${authToken}`;const tokenDataCached=await this.cacheService.getJson(cacheKey);if(tokenDataCached)return tokenDataCached;const{authTokenSecret,authTokenName}=this.configService.get("token");const tokenData=_jsonwebtoken.verify(authToken,authTokenSecret);const res={tokenData:{id:tokenData["id"],secretToken:tokenData["secretToken"],authTokenName:tokenData["authTokenName"],jti:tokenData["jti"]},authTokenName};this.cacheService.setJson(cacheKey,res,_enum.CACHE_TIME.THIRTY_MINUTES);return res}createRefreshToken(data){try{const{id,secretToken}=data;const tokenVersion=this.utilService.generateString(16);const{refreshTokenSecret,refreshTokenName,refreshExpiresIn}=this.configService.get("token");const exp=Math.floor(Date.now()/1e3)+refreshExpiresIn;const payload={exp,id,refreshTokenName,secretToken,jti:tokenVersion};const refreshToken=_jsonwebtoken.sign(payload,refreshTokenSecret);const refreshTokenExpiresIn=exp;return{refreshToken,refreshTokenExpiresIn}}catch(err){console.log("createRefreshToken error",err);throw new Error("createRefreshToken error")}}verifyRefreshToken(data){try{const{refreshToken}=data;const{refreshTokenSecret,refreshTokenName}=this.configService.get("token");const tokenData=_jsonwebtoken.verify(refreshToken,refreshTokenSecret);console.log("Verify refresh token data",tokenData);if(refreshTokenName===tokenData.refreshTokenName){return{id:tokenData.id,secretToken:tokenData.secretToken,jti:tokenData.jti}}return null}catch(err){console.log("verifyRefreshToken error",err);return null}}validateUrl(url){const removedQuery=url.split("?");const arr=removedQuery[0].split("/");for(let i=0;i<arr.length;i++){if(arr[i]&&(!isNaN(+arr[i])||arr[i].includes("0_"))){arr[i]=":id"}}return arr.join("/")}static sha1(str){const shasum=(0,_crypto.createHash)("sha1");shasum.update(str);return shasum.digest("hex")}hashPassword(password){const salt=_bcrypt.genSaltSync(Number(process.env.SALT_ROUNDS)||8);const hash=_bcrypt.hashSync(password,salt);return{salt,hash}}isPasswordCorrect(plainPassword,hash){return _bcrypt.compareSync(plainPassword,hash)}constructor(configService,accountRepository,utilService,cacheService){this.configService=configService;this.accountRepository=accountRepository;this.utilService=utilService;this.cacheService=cacheService}};TokenService=_ts_decorate([(0,_common.Injectable)(),_ts_metadata("design:type",Function),_ts_metadata("design:paramtypes",[typeof _config.ConfigService==="undefined"?Object:_config.ConfigService,typeof _accountrepository.AccountRepository==="undefined"?Object:_accountrepository.AccountRepository,typeof _utilservice.UtilService==="undefined"?Object:_utilservice.UtilService,typeof _cacheservice.CacheService==="undefined"?Object:_cacheservice.CacheService])],TokenService);
//# sourceMappingURL=token.service.js.map