# Quy trình xây dựng giao diện trang danh sách hồ sơ quân nhân

## Cấu trúc thư mục và quy chuẩn đặt tên

1. **Cấu trúc thư mục:**
   - Trang chính: `src\app\[lang]\(dashboard)\quan-ly-quan-nhan\danh-sach\page.tsx`
   - Components: `src\views\quan-ly-quan-nhan\danh-sach\*.tsx`
   - Dữ liệu mẫu: `src\views\quan-ly-quan-nhan\danh-sach\mockData.ts`

2. **Quy chuẩn đặt tên:**
   - Tên component: Tiếng Việt không dấu, viết liền, chữ cái đầu mỗi từ viết hoa (PascalCase)
   - Tên file: Trùng với tên component
   - Tên biến, hàm: camelCase, bắt đầu bằng động từ cho các hàm xử lý sự kiện (handle...)
   - Tên hằng số: UPPER_SNAKE_CASE

3. **Các component đã tạo:**
   - `DanhSachQuanNhanPage.tsx`: Component chính
   - `BangDuLieu.tsx`: Component bảng dữ liệu (tích hợp bộ lọc và các nút hành động)
   - `ThongKeQuanNhan.tsx`: Component thống kê
   - `DialogThayDoiTrangThai.tsx`: Dialog thay đổi trạng thái
   - `DialogXacNhanXoa.tsx`: Dialog xác nhận xóa
   - `mockData.ts`: Dữ liệu mẫu và các type định nghĩa

## Các bước thực hiện chi tiết

### Bước 1: Tạo thư mục và cấu trúc file
1. Tạo thư mục `src\views\quan-ly-quan-nhan\danh-sach` để chứa các component
2. Đảm bảo trang chính `src\app\[lang]\(dashboard)\quan-ly-quan-nhan\danh-sach\page.tsx` đã tồn tại

### Bước 2: Tạo file dữ liệu mẫu (mockData.ts)
1. Định nghĩa các type cần thiết:
   - `QuanNhanType`: Interface mô tả cấu trúc dữ liệu quân nhân
   - `QuanNhanStatus`: Type cho trạng thái quân nhân ('active', 'inactive', 'leave', 'deceased')
   - `CapBac`: Type cho cấp bậc quân nhân
   - `ChucVu`: Type cho chức vụ quân nhân

2. Tạo đối tượng ánh xạ trạng thái:
   - `quanNhanStatusObj`: Ánh xạ từ trạng thái sang màu sắc và nhãn hiển thị

3. Tạo dữ liệu mẫu:
   - `mockQuanNhanList`: Mảng các đối tượng quân nhân mẫu

### Bước 3: Tạo component ThongKeQuanNhan
1. Tạo file `ThongKeQuanNhan.tsx` với các chức năng:
   - Hiển thị 4 thẻ thống kê: Tổng số, Đang công tác, Tạm nghỉ, Xuất ngũ
   - Mỗi thẻ hiển thị số liệu, biểu tượng, và phần trăm thay đổi

2. Sử dụng các component:
   - Grid để bố trí các thẻ
   - HorizontalWithSubtitle từ @components/card-statistics

### Bước 4: Tạo component BangDuLieu
1. Tạo file `BangDuLieu.tsx` với các chức năng:
   - Hiển thị bộ lọc (tìm kiếm, lọc theo Đơn vị, Cấp bậc, Chức vụ, Trạng thái)
   - Hiển thị các nút hành động (Xuất file, Thêm mới Quân nhân)
   - Hiển thị bảng dữ liệu quân nhân
   - Hỗ trợ chọn nhiều dòng (checkbox)
   - Hỗ trợ sắp xếp
   - Hỗ trợ phân trang
   - Hiển thị các hành động trên mỗi dòng

2. Props:
   - `rows`: Dữ liệu quân nhân
   - `onViewDetails`: Callback khi xem chi tiết
   - `onEdit`: Callback khi sửa
   - `onOpenChangeStatusDialog`: Callback khi mở dialog thay đổi trạng thái
   - `onOpenDeleteDialog`: Callback khi mở dialog xác nhận xóa
   - `onAdd`: Callback khi thêm mới
   - `onExport`: Callback khi xuất file
   - `onApplyFilters`: Callback khi áp dụng bộ lọc
   - `onResetFilters`: Callback khi xóa bộ lọc
   - `pageSize`: Số dòng mỗi trang
   - `onPageChange`: Callback khi thay đổi trang
   - `onPageSizeChange`: Callback khi thay đổi số dòng mỗi trang
   - `onSortModelChange`: Callback khi thay đổi sắp xếp
   - `rowsPerPageOptions`: Tùy chọn số dòng mỗi trang

3. State nội bộ:
   - `searchTerm`: Từ khóa tìm kiếm
   - `donVi`: Đơn vị được chọn
   - `capBac`: Cấp bậc được chọn
   - `chucVu`: Chức vụ được chọn
   - `trangThai`: Trạng thái được chọn
   - `globalFilter`: Bộ lọc toàn cục

4. Cấu hình columns:
   - Checkbox chọn dòng
   - Số hiệu QN
   - Họ tên (kèm avatar)
   - Ngày sinh
   - Cấp bậc
   - Chức vụ
   - Đơn vị
   - Trạng thái (hiển thị bằng Chip với màu tương ứng)
   - Hành động (Xem chi tiết, Sửa, Menu với các tùy chọn khác)

5. Sử dụng thư viện @tanstack/react-table:
   - createColumnHelper
   - useReactTable
   - getCoreRowModel
   - getPaginationRowModel
   - getSortedRowModel
   - getFilteredRowModel

6. Sử dụng các component MUI:
   - Card, CardHeader, CardContent
   - Typography
   - Chip
   - Checkbox
   - IconButton
   - Grid
   - Button
   - Box
   - MenuItem
   - CustomTextField
   - OptionMenu (component tùy chỉnh)
   - TablePaginationComponent (component tùy chỉnh)

### Bước 5: Tạo component DialogThayDoiTrangThai
1. Tạo file `DialogThayDoiTrangThai.tsx` với các chức năng:
   - Hiển thị dialog thay đổi trạng thái
   - Cho phép chọn trạng thái mới từ dropdown

2. Props:
   - `open`: Trạng thái mở/đóng của dialog
   - `onClose`: Callback khi đóng dialog
   - `onSubmit`: Callback khi xác nhận thay đổi trạng thái

3. State nội bộ:
   - `selectedStatus`: Trạng thái được chọn

4. Sử dụng các component MUI:
   - Dialog, DialogTitle, DialogContent, DialogActions
   - CustomTextField
   - MenuItem
   - Button

### Bước 6: Tạo component DialogXacNhanXoa
1. Tạo file `DialogXacNhanXoa.tsx` với các chức năng:
   - Hiển thị dialog xác nhận xóa
   - Hiển thị thông báo xác nhận

2. Props:
   - `open`: Trạng thái mở/đóng của dialog
   - `onClose`: Callback khi đóng dialog
   - `onConfirm`: Callback khi xác nhận xóa

3. Sử dụng các component MUI:
   - Dialog, DialogTitle, DialogContent, DialogContentText, DialogActions
   - Button

### Bước 7: Tạo component chính DanhSachQuanNhanPage
1. Tạo file `DanhSachQuanNhanPage.tsx` với các chức năng:
   - Quản lý state cho toàn bộ trang
   - Xử lý các sự kiện từ các component con
   - Kết hợp các component con thành giao diện hoàn chỉnh

2. State:
   - `quanNhanList`: Danh sách quân nhân gốc
   - `filteredQuanNhanList`: Danh sách quân nhân đã lọc
   - `pageSize`: Số dòng mỗi trang
   - `isChangeStatusDialogOpen`: Trạng thái mở/đóng của dialog thay đổi trạng thái
   - `isConfirmDeleteDialogOpen`: Trạng thái mở/đóng của dialog xác nhận xóa
   - `selectedQuanNhanId`: ID của quân nhân đang được chọn

3. Các hàm xử lý sự kiện:
   - `handleApplyFilters`: Xử lý khi áp dụng bộ lọc
   - `handleResetFilters`: Xử lý khi xóa bộ lọc
   - `handlePageChange`: Xử lý khi thay đổi trang
   - `handlePageSizeChange`: Xử lý khi thay đổi số dòng mỗi trang
   - `handleSortChange`: Xử lý khi thay đổi sắp xếp
   - `handleAddQuanNhan`: Xử lý khi thêm mới quân nhân
   - `handleExport`: Xử lý khi xuất file
   - `handleViewDetails`: Xử lý khi xem chi tiết
   - `handleEdit`: Xử lý khi sửa
   - `handleOpenChangeStatusDialog`: Xử lý khi mở dialog thay đổi trạng thái
   - `handleOpenDeleteDialog`: Xử lý khi mở dialog xác nhận xóa
   - `handleCloseDialogs`: Xử lý khi đóng dialog
   - `handleConfirmChangeStatus`: Xử lý khi xác nhận thay đổi trạng thái
   - `handleConfirmDelete`: Xử lý khi xác nhận xóa

4. Sử dụng các component MUI:
   - Grid
   - Typography

5. Sử dụng các component đã tạo:
   - ThongKeQuanNhan
   - BangDuLieu
   - DialogThayDoiTrangThai
   - DialogXacNhanXoa

### Bước 8: Cập nhật trang danh sách quân nhân
1. Cập nhật file `src\app\[lang]\(dashboard)\quan-ly-quan-nhan\danh-sach\page.tsx`:
   - Import component DanhSachQuanNhanPage
   - Sử dụng component DanhSachQuanNhanPage

## Lưu ý quan trọng

1. **Cấu trúc dự án:**
   - Trang chính (page.tsx) nên đơn giản, chỉ import và sử dụng component chính
   - Logic xử lý nên đặt trong các component trong thư mục views
   - Dữ liệu mẫu nên tách riêng để dễ thay thế bằng API thực tế sau này

2. **Quy chuẩn đặt tên:**
   - Đặt tên component bằng tiếng Việt không dấu, viết liền, chữ cái đầu mỗi từ viết hoa
   - Đặt tên biến, hàm theo camelCase
   - Đặt tên hằng số theo UPPER_SNAKE_CASE
   - Tên hàm xử lý sự kiện nên bắt đầu bằng "handle"

3. **Quản lý state:**
   - State nên được quản lý ở cấp cao nhất cần thiết
   - Truyền dữ liệu và callback xuống các component con qua props
   - Sử dụng useState cho state đơn giản, useReducer cho state phức tạp

4. **Xử lý sự kiện:**
   - Các hàm xử lý sự kiện nên được định nghĩa ở component cha
   - Truyền callback xuống component con qua props
   - Sử dụng console.log để ghi lại các sự kiện trong quá trình phát triển

5. **Tái sử dụng component:**
   - Tách các component nhỏ để dễ tái sử dụng
   - Sử dụng props để truyền dữ liệu và callback
   - Định nghĩa rõ ràng kiểu dữ liệu cho props

6. **Hiệu suất:**
   - Sử dụng useMemo để tránh tính toán lại không cần thiết
   - Sử dụng useCallback để tránh tạo lại hàm không cần thiết
   - Sử dụng React.memo để tránh render lại không cần thiết

7. **Responsive:**
   - Sử dụng Grid của MUI để làm layout responsive
   - Sử dụng các breakpoint (xs, sm, md, lg, xl) để điều chỉnh layout theo kích thước màn hình

8. **Phân trang:**
   - Sử dụng TablePaginationComponent để đảm bảo phân trang giống với các trang khác trong dự án
   - Cấu hình table để hỗ trợ lọc và sắp xếp

9. **Internationalization:**
   - Sử dụng tham số [lang] trong đường dẫn để hỗ trợ đa ngôn ngữ
   - Sử dụng getLocalizedUrl để tạo đường dẫn có ngôn ngữ

10. **Testing:**
    - Viết test cho các component quan trọng
    - Sử dụng Jest và React Testing Library
    - Test các trường hợp edge case

## Kết luận

Quy trình xây dựng giao diện trang danh sách hồ sơ quân nhân đã được thực hiện theo các bước chi tiết trên. Các component được tổ chức theo cấu trúc rõ ràng, với quy chuẩn đặt tên nhất quán và logic xử lý được phân tách hợp lý. Mô hình này có thể được áp dụng cho các trang khác trong dự án để đảm bảo tính nhất quán và dễ bảo trì.
