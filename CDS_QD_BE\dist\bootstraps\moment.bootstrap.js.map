{"version": 3, "sources": ["../../src/bootstraps/moment.bootstrap.ts"], "sourcesContent": ["import moment from 'moment';\r\n\r\nfunction initMoment() {\r\n    moment.tz.setDefault(process.env.TZ);\r\n    moment.updateLocale('en', {\r\n        week: {\r\n            dow: 1,\r\n        },\r\n    });\r\n\r\n    console.log('LOG:: initMoment:', moment().format('YYYY-MM-DD HH:mm:ss'));\r\n}\r\n\r\nexport function bootstrapMoment(): void {\r\n    initMoment();\r\n}\r\n"], "names": ["bootstrapMoment", "initMoment", "moment", "tz", "<PERSON><PERSON><PERSON><PERSON>", "process", "env", "TZ", "updateLocale", "week", "dow", "console", "log", "format"], "mappings": "oGAagBA,yDAAAA,+EAbG,+FAEnB,SAASC,aACLC,eAAM,CAACC,EAAE,CAACC,UAAU,CAACC,QAAQC,GAAG,CAACC,EAAE,EACnCL,eAAM,CAACM,YAAY,CAAC,KAAM,CACtBC,KAAM,CACFC,IAAK,CACT,CACJ,GAEAC,QAAQC,GAAG,CAAC,oBAAqBV,GAAAA,eAAM,IAAGW,MAAM,CAAC,uBACrD,CAEO,SAASb,kBACZC,YACJ"}