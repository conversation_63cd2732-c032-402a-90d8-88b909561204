{"version": 3, "sources": ["../../../../src/database/typeorm/repositories/user.repository.ts"], "sourcesContent": ["import { Injectable } from '@nestjs/common';\nimport { DataSource, Repository } from 'typeorm';\nimport { UserEntity } from '~/database/typeorm/entities/user.entity';\n\n@Injectable()\nexport class UserRepository extends Repository<UserEntity> {\n    constructor(private dataSource: DataSource) {\n        super(UserEntity, dataSource.createEntityManager());\n    }\n\n    findOneUserWithAllRelationsById = (id: number) => {\n        return this.findOne({\n            where: { id: id },\n            relations: ['role', 'avatar', 'account'],\n        });\n    };\n\n    // Find user with all roles (including multiple roles)\n    findOneUserWithAllRolesById = (id: number) => {\n        return this.findOne({\n            where: { id: id },\n            relations: ['role', 'userRoles', 'userRoles.role', 'avatar', 'account'],\n        });\n    };\n\n    findOneWithRalations = ({ where, relations }: { where: any; relations: string[] }) => {\n        const builder = this.createQueryBuilder('entity');\n        if (where) {\n            builder.where(where);\n        }\n\n        if (relations.length) {\n            relations.forEach((relation) => {\n                builder.leftJoinAndMapOne(`entity.${relation}`, `entity.${relation}`, relation, `${relation}.id = entity.${relation}Id`);\n            });\n        }\n\n        return builder.getOne();\n    };\n\n    // Find user by account ID with all roles\n    findOneUserWithRolesByAccountId = (accountId: number) => {\n        return this.findOne({\n            where: { accountId },\n            relations: ['role', 'userRoles', 'userRoles.role'],\n        });\n    };\n\n    // Get all roles for a user (both single role and multiple roles)\n    async getAllUserRoles(userId: number) {\n        const user = await this.findOne({\n            where: { id: userId },\n            relations: ['role', 'userRoles', 'userRoles.role'],\n        });\n\n        if (!user) return [];\n\n        const roles = [];\n\n        // Add single role if exists\n        if (user.role) {\n            roles.push(user.role);\n        }\n\n        // Add multiple roles if exists\n        if (user.userRoles && user.userRoles.length > 0) {\n            user.userRoles.forEach((userRole) => {\n                if (userRole.role) {\n                    roles.push(userRole.role);\n                }\n            });\n        }\n\n        // Remove duplicates based on role ID\n        const uniqueRoles = roles.filter((role, index, self) => index === self.findIndex((r) => r.id === role.id));\n\n        return uniqueRoles;\n    }\n}\n"], "names": ["UserRepository", "Repository", "getAllUserRoles", "userId", "user", "findOne", "where", "id", "relations", "roles", "role", "push", "userRoles", "length", "for<PERSON>ach", "userRole", "uniqueRoles", "filter", "index", "self", "findIndex", "r", "constructor", "dataSource", "UserEntity", "createEntityManager", "findOneUserWithAllRelationsById", "findOneUserWithAllRolesById", "findOneWithRalations", "builder", "createQueryBuilder", "relation", "leftJoinAndMapOne", "getOne", "findOneUserWithRolesByAccountId", "accountId"], "mappings": "oGAKaA,wDAAAA,wCALc,yCACY,qCACZ,wkBAGpB,IAAA,AAAMA,eAAN,MAAMA,uBAAuBC,mBAAU,CA4C1C,MAAMC,gBAAgBC,MAAc,CAAE,CAClC,MAAMC,KAAO,MAAM,IAAI,CAACC,OAAO,CAAC,CAC5BC,MAAO,CAAEC,GAAIJ,MAAO,EACpBK,UAAW,CAAC,OAAQ,YAAa,iBAAiB,AACtD,GAEA,GAAI,CAACJ,KAAM,MAAO,EAAE,CAEpB,MAAMK,MAAQ,EAAE,CAGhB,GAAIL,KAAKM,IAAI,CAAE,CACXD,MAAME,IAAI,CAACP,KAAKM,IAAI,CACxB,CAGA,GAAIN,KAAKQ,SAAS,EAAIR,KAAKQ,SAAS,CAACC,MAAM,CAAG,EAAG,CAC7CT,KAAKQ,SAAS,CAACE,OAAO,CAAC,AAACC,WACpB,GAAIA,SAASL,IAAI,CAAE,CACfD,MAAME,IAAI,CAACI,SAASL,IAAI,CAC5B,CACJ,EACJ,CAGA,MAAMM,YAAcP,MAAMQ,MAAM,CAAC,CAACP,KAAMQ,MAAOC,OAASD,QAAUC,KAAKC,SAAS,CAAC,AAACC,GAAMA,EAAEd,EAAE,GAAKG,KAAKH,EAAE,GAExG,OAAOS,WACX,CAvEAM,YAAY,AAAQC,UAAsB,CAAE,CACxC,KAAK,CAACC,sBAAU,CAAED,WAAWE,mBAAmB,SADhCF,WAAAA,gBAIpBG,gCAAkC,AAACnB,KAC/B,OAAO,IAAI,CAACF,OAAO,CAAC,CAChBC,MAAO,CAAEC,GAAIA,EAAG,EAChBC,UAAW,CAAC,OAAQ,SAAU,UAAU,AAC5C,EACJ,OAGAmB,4BAA8B,AAACpB,KAC3B,OAAO,IAAI,CAACF,OAAO,CAAC,CAChBC,MAAO,CAAEC,GAAIA,EAAG,EAChBC,UAAW,CAAC,OAAQ,YAAa,iBAAkB,SAAU,UAAU,AAC3E,EACJ,OAEAoB,qBAAuB,CAAC,CAAEtB,KAAK,CAAEE,SAAS,CAAuC,IAC7E,MAAMqB,QAAU,IAAI,CAACC,kBAAkB,CAAC,UACxC,GAAIxB,MAAO,CACPuB,QAAQvB,KAAK,CAACA,MAClB,CAEA,GAAIE,UAAUK,MAAM,CAAE,CAClBL,UAAUM,OAAO,CAAC,AAACiB,WACfF,QAAQG,iBAAiB,CAAC,CAAC,OAAO,EAAED,SAAS,CAAC,CAAE,CAAC,OAAO,EAAEA,SAAS,CAAC,CAAEA,SAAU,CAAC,EAAEA,SAAS,aAAa,EAAEA,SAAS,EAAE,CAAC,CAC3H,EACJ,CAEA,OAAOF,QAAQI,MAAM,EACzB,OAGAC,gCAAkC,AAACC,YAC/B,OAAO,IAAI,CAAC9B,OAAO,CAAC,CAChBC,MAAO,CAAE6B,SAAU,EACnB3B,UAAW,CAAC,OAAQ,YAAa,iBAAiB,AACtD,EACJ,CAtCA,CAsEJ"}