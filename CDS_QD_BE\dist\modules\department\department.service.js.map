{"version": 3, "sources": ["../../../src/modules/department/department.service.ts"], "sourcesContent": ["import { Injectable } from '@nestjs/common';\nimport { FilterDto } from '~/common/dtos/filter.dto';\nimport { DepartmentRepository } from '~/database/typeorm/repositories/department.repository';\nimport { UserRepository } from '~/database/typeorm/repositories/user.repository';\nimport { UtilService } from '~/shared/services';\nimport { CreateDepartmentDto } from './dto/create-department.dto';\nimport { UpdateDepartmentDto } from './dto/update-department.dto';\n\n@Injectable()\nexport class DepartmentService {\n    constructor(\n        private readonly departmentRepository: DepartmentRepository,\n        private readonly userRepository: UserRepository,\n        private readonly utilService: UtilService,\n    ) {}\n\n    create(createDepartmentDto: CreateDepartmentDto) {\n        return this.departmentRepository.save(this.departmentRepository.create(createDepartmentDto));\n    }\n\n    async findAll(queries: FilterDto) {\n        const { builder, take, pagination } = this.utilService.getQueryBuilderAndPagination(this.departmentRepository, queries);\n\n        if (!this.utilService.isEmpty(queries.search)) {\n            builder.andWhere('entity.name ILIKE :search', { search: `%${queries.search}%` });\n        }\n\n        builder.select(['entity']);\n\n        const [result, total] = await builder.getManyAndCount();\n        const totalPages = Math.ceil(total / take);\n        return {\n            data: result,\n            pagination: {\n                ...pagination,\n                totalRecords: total,\n                totalPages: totalPages,\n            },\n        };\n    }\n\n    findOne(id: number) {\n        return this.departmentRepository.findOneBy({ id });\n    }\n\n    update(id: number, updateDepartmentDto: UpdateDepartmentDto) {\n        return this.departmentRepository.update(id, updateDepartmentDto);\n    }\n\n    remove(id: number) {\n        // set null for all user in this department\n        // this.userRepository.update({ departmentId: id }, { departmentId: null });\n        return this.departmentRepository.delete(id);\n    }\n}\n"], "names": ["DepartmentService", "create", "createDepartmentDto", "departmentRepository", "save", "findAll", "queries", "builder", "take", "pagination", "utilService", "getQueryBuilderAndPagination", "isEmpty", "search", "andWhere", "select", "result", "total", "getManyAndCount", "totalPages", "Math", "ceil", "data", "totalRecords", "findOne", "id", "findOneBy", "update", "updateDepartmentDto", "remove", "delete", "constructor", "userRepository"], "mappings": "oGASaA,2DAAAA,2CATc,sDAEU,2FACN,+EACH,skBAKrB,IAAA,AAAMA,kBAAN,MAAMA,kBAOTC,OAAOC,mBAAwC,CAAE,CAC7C,OAAO,IAAI,CAACC,oBAAoB,CAACC,IAAI,CAAC,IAAI,CAACD,oBAAoB,CAACF,MAAM,CAACC,qBAC3E,CAEA,MAAMG,QAAQC,OAAkB,CAAE,CAC9B,KAAM,CAAEC,OAAO,CAAEC,IAAI,CAAEC,UAAU,CAAE,CAAG,IAAI,CAACC,WAAW,CAACC,4BAA4B,CAAC,IAAI,CAACR,oBAAoB,CAAEG,SAE/G,GAAI,CAAC,IAAI,CAACI,WAAW,CAACE,OAAO,CAACN,QAAQO,MAAM,EAAG,CAC3CN,QAAQO,QAAQ,CAAC,4BAA6B,CAAED,OAAQ,CAAC,CAAC,EAAEP,QAAQO,MAAM,CAAC,CAAC,CAAC,AAAC,EAClF,CAEAN,QAAQQ,MAAM,CAAC,CAAC,SAAS,EAEzB,KAAM,CAACC,OAAQC,MAAM,CAAG,MAAMV,QAAQW,eAAe,GACrD,MAAMC,WAAaC,KAAKC,IAAI,CAACJ,MAAQT,MACrC,MAAO,CACHc,KAAMN,OACNP,WAAY,CACR,GAAGA,UAAU,CACbc,aAAcN,MACdE,WAAYA,UAChB,CACJ,CACJ,CAEAK,QAAQC,EAAU,CAAE,CAChB,OAAO,IAAI,CAACtB,oBAAoB,CAACuB,SAAS,CAAC,CAAED,EAAG,EACpD,CAEAE,OAAOF,EAAU,CAAEG,mBAAwC,CAAE,CACzD,OAAO,IAAI,CAACzB,oBAAoB,CAACwB,MAAM,CAACF,GAAIG,oBAChD,CAEAC,OAAOJ,EAAU,CAAE,CAGf,OAAO,IAAI,CAACtB,oBAAoB,CAAC2B,MAAM,CAACL,GAC5C,CA3CAM,YACI,AAAiB5B,oBAA0C,CAC3D,AAAiB6B,cAA8B,CAC/C,AAAiBtB,WAAwB,CAC3C,MAHmBP,qBAAAA,0BACA6B,eAAAA,oBACAtB,YAAAA,WAClB,CAwCP"}