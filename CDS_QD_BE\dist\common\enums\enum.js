"use strict";Object.defineProperty(exports,"__esModule",{value:true});function _export(target,all){for(var name in all)Object.defineProperty(target,name,{enumerable:true,get:Object.getOwnPropertyDescriptor(all,name).get})}_export(exports,{get CACHE_TIME(){return CACHE_TIME},get MEDIA_TYPE(){return MEDIA_TYPE},get SCHEDULE_TYPE(){return SCHEDULE_TYPE},get USER_ROLE(){return USER_ROLE},get USER_STATUS(){return USER_STATUS}});var USER_STATUS=/*#__PURE__*/function(USER_STATUS){USER_STATUS["ACTIVE"]="ACTIVE";USER_STATUS["DISABLED"]="DISABLED";return USER_STATUS}({});var USER_ROLE=/*#__PURE__*/function(USER_ROLE){USER_ROLE[USER_ROLE["ADMIN"]=1]="ADMIN";USER_ROLE[USER_ROLE["USER"]=2]="USER";return USER_ROLE}({});var SCHEDULE_TYPE=/*#__PURE__*/function(SCHEDULE_TYPE){SCHEDULE_TYPE["CRON"]="cron";SCHEDULE_TYPE["TIMEOUT"]="timeout";SCHEDULE_TYPE["INTERVAL"]="interval";return SCHEDULE_TYPE}({});var MEDIA_TYPE=/*#__PURE__*/function(MEDIA_TYPE){MEDIA_TYPE["IMAGE"]="IMAGE";MEDIA_TYPE["VIDEO"]="VIDEO";MEDIA_TYPE["AUDIO"]="AUDIO";MEDIA_TYPE["DOCUMENT"]="DOCUMENT";MEDIA_TYPE["MISC"]="MISC";return MEDIA_TYPE}({});var CACHE_TIME=/*#__PURE__*/function(CACHE_TIME){CACHE_TIME[CACHE_TIME["ONE_MINUTE"]=60]="ONE_MINUTE";CACHE_TIME[CACHE_TIME["THIRTY_MINUTES"]=1800]="THIRTY_MINUTES";CACHE_TIME[CACHE_TIME["ONE_HOUR"]=3600]="ONE_HOUR";CACHE_TIME[CACHE_TIME["ONE_DAY"]=86400]="ONE_DAY";CACHE_TIME[CACHE_TIME["ONE_WEEK"]=604800]="ONE_WEEK";CACHE_TIME[CACHE_TIME["ONE_MONTH"]=2592e3]="ONE_MONTH";CACHE_TIME[CACHE_TIME["ONE_YEAR"]=31536e3]="ONE_YEAR";return CACHE_TIME}({});
//# sourceMappingURL=enum.js.map