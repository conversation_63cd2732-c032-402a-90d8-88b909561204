Trang **"<PERSON><PERSON><PERSON> giá, <PERSON>ế<PERSON> loại"** trong Module Công tác Đảng.

---

**Trang: <PERSON><PERSON><PERSON>i<PERSON>, <PERSON>ế<PERSON> lo<PERSON> (`/${locale}/cong-tac-dang/danh-gia-xep-loai`)**

*   **<PERSON><PERSON><PERSON> (Tiêu đề hiển thị):** `<PERSON>u<PERSON>n lý <PERSON> gi<PERSON>, Xếp loại Đảng viên và Tổ chức Đảng`
*   **Mục đích chính của Trang:** Cung cấp một giao diện tập trung để quản lý các đợt đ<PERSON>h giá, xếp loại chất lượng hàng năm (hoặc định kỳ) cho cả đảng viên và tổ chức Đảng. Hỗ trợ quy trình từ việc đảng viên tự kiểm điểm, chi bộ/đảng bộ nhậ<PERSON> x<PERSON><PERSON>, đến cấp <PERSON><PERSON> có thẩm quyền công nhận kết quả.

---

#### I. <PERSON><PERSON><PERSON> năng trang `DanhGiaXepLoaiDangPage`

*   **<PERSON><PERSON> cục tổng quan của Trang:** Trang này sẽ được tổ chức theo dạng các Tab để phân chia các khu vực chức năng chính.
    1.  Thanh điều hướng phân cấp (Breadcrumbs).
    2.  Tiêu đề trang.
    3.  Khu vực các Tab điều hướng: "Đợt đánh giá", "Đảng viên Tự đánh giá & Chi bộ Nhận xét", "Tổ chức Đảng Đánh giá", "Lịch sử & Kết quả Tổng hợp".

---

**A. Tab 1: "Đợt đánh giá"**

1.  **Chức năng "Quản lý các Đợt đánh giá"**
    *   **Tổng quan:** Cho phép người có thẩm quyền (ví dụ: Văn phòng Đảng ủy, Ban Tổ chức) tạo và quản lý các đợt đánh giá, xếp loại chất lượng.
    *   **Chi tiết:**
        *   **Hiển thị Danh sách Đợt đánh giá:**
            *   Trình bày dưới dạng bảng các đợt đánh giá đã tạo.
            *   **Các cột thông tin cho mỗi đợt:** "Tên/Tiêu đề Đợt đánh giá", "Năm đánh giá", "Thời gian bắt đầu", "Thời gian kết thúc", "Phạm vi áp dụng" (ví dụ: Toàn Đảng bộ, Khối A, Khối B), "Trạng thái Đợt" (ví dụ: Đang chuẩn bị, Đang tiến hành, Đã kết thúc, Đã khóa).
            *   Hỗ trợ tìm kiếm, lọc theo năm, trạng thái.
            *   Phân trang.
        *   **Nút "Tạo Đợt đánh giá mới":**
            *   Mở giao diện (form/dialog) để nhập thông tin cho một đợt đánh giá mới.
            *   **Các trường cần nhập:** Tên/Tiêu đề đợt, Năm đánh giá, Thời gian bắt đầu/kết thúc (cho các hoạt động như nộp bản kiểm điểm, chi bộ họp...), Phạm vi áp dụng (có thể chọn các Tổ chức Đảng cụ thể), Mô tả/Hướng dẫn chi tiết cho đợt đánh giá.
            *   Nút "Lưu Đợt đánh giá".
        *   **Hành động trên mỗi dòng Đợt đánh giá:**
            *   **"Xem chi tiết/Cấu hình":** Xem lại thông tin và có thể cấu hình các tiêu chí, biểu mẫu (nếu hệ thống hỗ trợ).
            *   **"Sửa thông tin Đợt":** Cho phép chỉnh sửa các thông tin cơ bản của đợt (nếu chưa bắt đầu hoặc có quyền).
            *   **"Thay đổi Trạng thái Đợt":** Ví dụ: Chuyển từ "Đang chuẩn bị" sang "Đang tiến hành", hoặc "Kết thúc Đợt".
            *   **"Xóa Đợt"** (nếu chưa có dữ liệu phát sinh).

---

**B. Tab 2: "Đảng viên Tự đánh giá & Chi bộ Nhận xét"**

1.  **Chức năng "Theo dõi Đảng viên Nộp Bản tự kiểm điểm"**
    *   **Tổng quan:** Cho phép Chi ủy/Bí thư Chi bộ theo dõi tình hình nộp bản tự kiểm điểm của các đảng viên trong chi bộ mình trong một đợt đánh giá cụ thể.
    *   **Chi tiết:**
        *   **Lựa chọn Đợt đánh giá và Tổ chức Đảng (Chi bộ):** Người dùng cần chọn đợt đánh giá và chi bộ mình quản lý.
        *   **Hiển thị Danh sách Đảng viên của Chi bộ:**
            *   Trình bày dưới dạng bảng.
            *   **Các cột thông tin:** "Mã Đảng viên", "Họ tên Đảng viên", "Trạng thái Nộp Bản kiểm điểm" (Chưa nộp, Đã nộp), "Ngày nộp", "File Bản kiểm điểm" (nếu đã nộp, có link xem/tải).
        *   **(Nếu hệ thống cho phép đảng viên tự upload):** Đảng viên khi đăng nhập sẽ thấy giao diện riêng để nộp bản kiểm điểm của mình cho đợt đánh giá hiện tại.
        *   **(Đối với Chi ủy/Bí thư):** Có thể có chức năng "Nhắc nhở nộp" hoặc "Ghi nhận đã nộp" (nếu đảng viên nộp bản cứng).

2.  **Chức năng "Chi bộ Thực hiện Nhận xét, Đề nghị Xếp loại cho Đảng viên"**
    *   **Tổng quan:** Sau khi đảng viên nộp bản tự kiểm điểm, Chi bộ sẽ tổ chức họp để nhận xét, đánh giá và đề nghị mức xếp loại chất lượng cho từng đảng viên.
    *   **Chi tiết (Giao diện cho Chi ủy/Bí thư sau khi chọn Đợt đánh giá và Đảng viên):**
        *   Hiển thị thông tin đảng viên và bản tự kiểm điểm đã nộp.
        *   **Khu vực nhập liệu/lựa chọn kết quả của Chi bộ:**
            *   "Mức Đảng viên tự xếp loại": Hiển thị từ bản kiểm điểm của đảng viên.
            *   "Ý kiến đóng góp của các đảng viên trong Chi bộ" (tóm tắt).
            *   "Xếp loại Chi bộ đề nghị": Trường lựa chọn từ danh mục `DmXepLoaiChatLuong`.
            *   "Nội dung Nhận xét của Chi bộ": Trường văn bản chi tiết.
            *   "File Biên bản họp Chi bộ xét duyệt": Cho phép upload file biên bản.
        *   **Nút "Lưu Nhận xét & Đề nghị của Chi bộ".**
        *   Sau khi lưu, thông tin này sẽ được chuyển lên cấp ủy cấp trên (nếu có) hoặc chờ cấp ủy có thẩm quyền công nhận.

---

**C. Tab 3: "Tổ chức Đảng Đánh giá & Cấp ủy Công nhận"**

1.  **Chức năng "Cấp ủy có thẩm quyền Đánh giá, Xếp loại Tổ chức Đảng"**
    *   **Tổng quan:** Cấp ủy cấp trên (hoặc theo quy định) thực hiện đánh giá, xếp loại chất lượng cho các Tổ chức Đảng cấp dưới trực thuộc trong một đợt đánh giá.
    *   **Chi tiết:**
        *   **Lựa chọn Đợt đánh giá.**
        *   **Hiển thị Danh sách Tổ chức Đảng cấp dưới cần đánh giá:**
            *   Trình bày dưới dạng bảng.
            *   **Các cột thông tin:** "Mã TCD", "Tên Tổ chức Đảng", "Xếp loại tự đánh giá của TCD (nếu có)", "Xếp loại Cấp ủy công nhận".
        *   **Hành động "Thực hiện Đánh giá/Công nhận" cho mỗi Tổ chức Đảng:**
            *   Mở giao diện (form/dialog) hiển thị các tiêu chí (nếu có), báo cáo tự đánh giá của TCD (nếu có).
            *   **Khu vực nhập liệu/lựa chọn kết quả của Cấp ủy:**
                *   "Xếp loại Cấp ủy công nhận cho Tổ chức Đảng": Trường lựa chọn từ `DmXepLoaiChatLuongTCD` (danh mục riêng cho TCD).
                *   "Nội dung Nhận xét, Đánh giá của Cấp ủy".
                *   "File Báo cáo/Biên bản đánh giá của Cấp ủy": Cho phép upload.
            *   **Nút "Lưu Kết quả Đánh giá Tổ chức Đảng".**

2.  **Chức năng "Cấp ủy có thẩm quyền Công nhận Kết quả Xếp loại Đảng viên"**
    *   **Tổng quan:** Cấp ủy có thẩm quyền (theo phân cấp) xem xét đề nghị từ Chi bộ/Đảng bộ cấp dưới và ra quyết định công nhận kết quả xếp loại chất lượng cho từng đảng viên.
    *   **Chi tiết:**
        *   **Lựa chọn Đợt đánh giá và Tổ chức Đảng (Chi bộ/Đảng bộ cơ sở).**
        *   **Hiển thị Danh sách Đảng viên đã được Chi bộ đề nghị xếp loại:**
            *   Trình bày dưới dạng bảng.
            *   **Các cột thông tin:** "Mã ĐV", "Họ tên ĐV", "Xếp loại Chi bộ đề nghị", "Xếp loại Cấp ủy công nhận" (ô để nhập/chọn), "Số QĐ công nhận", "Ngày QĐ", "File QĐ".
        *   **Hành động "Công nhận Xếp loại" cho mỗi Đảng viên (hoặc hàng loạt nếu kết quả giống nhau):**
            *   Mở giao diện (form/dialog) để:
                *   Chọn "Xếp loại Cấp ủy công nhận" từ `DmXepLoaiChatLuong`.
                *   Nhập "Số Quyết định Công nhận".
                *   Chọn "Ngày Quyết định Công nhận".
                *   Upload "File Quyết định Công nhận".
                *   Nhập "Ghi chú" (nếu có).
            *   **Nút "Lưu và Công nhận".**
            *   Sau khi công nhận, kết quả sẽ được cập nhật vào hồ sơ đảng viên.

---

**D. Tab 4: "Lịch sử & Kết quả Tổng hợp"**

1.  **Chức năng "Xem Lịch sử Đánh giá, Xếp loại của Đảng viên"**
    *   **Tổng quan:** Cho phép tra cứu và xem lại kết quả đánh giá, xếp loại của một đảng viên cụ thể qua các năm/các đợt.
    *   **Chi tiết:**
        *   **Tìm kiếm Đảng viên.**
        *   Hiển thị dạng bảng/danh sách các kỳ đánh giá của đảng viên đó: "Năm", "Xếp loại Tự nhận", "Xếp loại Chi bộ Đề nghị", "Xếp loại Cấp ủy Công nhận".

2.  **Chức năng "Xem Lịch sử Đánh giá, Xếp loại của Tổ chức Đảng"**
    *   **Tổng quan:** Cho phép tra cứu và xem lại kết quả đánh giá, xếp loại của một Tổ chức Đảng cụ thể qua các năm/các đợt.
    *   **Chi tiết:**
        *   **Tìm kiếm Tổ chức Đảng.**
        *   Hiển thị dạng bảng/danh sách các kỳ đánh giá của TCD đó: "Năm", "Xếp loại Cấp ủy Công nhận".

3.  **Chức năng "Thống kê, Báo cáo Kết quả Đánh giá, Xếp loại"**
    *   **Tổng quan:** Cung cấp các báo cáo tổng hợp về tình hình đánh giá, xếp loại chất lượng trong toàn Đảng bộ hoặc theo từng cấp.
    *   **Chi tiết:**
        *   Giao diện chọn loại báo cáo, phạm vi (Tổ chức Đảng), thời gian (Đợt đánh giá/Năm).
        *   Ví dụ báo cáo: Thống kê tỷ lệ đảng viên HTXSNV, HTTNV... theo từng chi bộ, đảng bộ.
        *   Xuất báo cáo.

---

#### II. Bảng dữ liệu được sử dụng trên trang `DanhGiaXepLoaiDangPage`

*   **Bảng `DotDanhGiaXepLoai` (Bảng quản lý các đợt đánh giá - cần tạo mới nếu chưa có)**
    *   `ID: number` (BIGINT, PK, AUTO_INCREMENT)
    *   `TenDotDanhGia: string` (NVARCHAR(500) NOT NULL)
    *   `NamDanhGia: number` (INT NOT NULL)
    *   `ThoiGianBatDau: date` (DATE)
    *   `ThoiGianKetThuc: date` (DATE)
    *   `PhamViApDung_Text: string` (NTEXT NULL) - Mô tả phạm vi, hoặc có thể là liên kết nhiều-nhiều với bảng `ToChucDang` nếu muốn chọn cụ thể.
    *   `TrangThaiDotID: number` (INT, FK REFERENCES DmTrangThaiDotDanhGia(ID))
    *   `MoTaHuongDan: string` (NTEXT NULL)

*   **Bảng `BanKiemDiemDangVien` (PartyMemberSelfCriticisms)** (Đã mô tả ở phần Chi tiết Hồ sơ Đảng viên)
    *   `ID: number`
    *   `DangVienID: string`
    *   `NamDanhGia: number` (Có thể thay bằng `DotDanhGiaID` để liên kết chặt chẽ với đợt)
    *   `NoiDungTuKiemDiem_URL: string`
    *   `NoiDungTuKiemDiem_Text: string`
    *   `TuNhanXepLoaiID: number` (FK REFERENCES DmXepLoaiChatLuong(ID))
    *   `NgayNop: date`
    *   `DotDanhGiaID: number` (BIGINT, FK REFERENCES DotDanhGiaXepLoai(ID)) - **Thêm trường này để liên kết**

*   **Bảng `DanhGiaXepLoaiDangVien` (PartyMemberRatings)** (Đã mô tả ở phần Chi tiết Hồ sơ Đảng viên)
    *   `ID: number`
    *   `DangVienID: string`
    *   `NamDanhGia: number` (Có thể thay bằng `DotDanhGiaID`)
    *   `DotDanhGiaID: number` (BIGINT, FK REFERENCES DotDanhGiaXepLoai(ID)) - **Cần có trường này**
    *   `BanKiemDiemID: number` (FK NULL REFERENCES BanKiemDiemDangVien(ID))
    *   `XepLoaiChiBoDeNghiID: number` (FK REFERENCES DmXepLoaiChatLuong(ID))
    *   `YKienDongGopCuaChiBo: string` (NTEXT)
    *   `FileBienBanHopChiBoXetURL: string` (VARCHAR(255) NULL)
    *   `XepLoaiCapUyCongNhanID: number` (FK REFERENCES DmXepLoaiChatLuong(ID))
    *   `SoQuyetDinhCongNhan: string` (VARCHAR(50) NULL)
    *   `NgayQuyetDinhCongNhan: date` (DATE NULL)
    *   `FileQuyetDinhCongNhanURL: string` (VARCHAR(255) NULL)
    *   `GhiChu: string` (NTEXT)

*   **Bảng `DanhGiaXepLoaiToChucDang` (PartyOrganizationRatings)** (Tương tự `DanhGiaXepLoaiDangVien` nhưng cho Tổ chức Đảng)
    *   `ID: number` (BIGINT, PK, AUTO_INCREMENT)
    *   `ToChucDangID: number` (BIGINT, NOT NULL, FK REFERENCES ToChucDang(ID))
    *   `NamDanhGia: number` (INT, NOT NULL) (Hoặc `DotDanhGiaID`)
    *   `DotDanhGiaID: number` (BIGINT, FK REFERENCES DotDanhGiaXepLoai(ID)) - **Cần có trường này**
    *   `XepLoaiTuDanhGiaID: number` (INT, FK NULL REFERENCES DmXepLoaiChatLuongTCD(ID)) - Xếp loại TCD tự đánh giá (nếu có)
    *   `FileBaoCaoTuDanhGiaURL: string` (VARCHAR(255) NULL)
    *   `XepLoaiCapTrenCongNhanID: number` (INT, FK REFERENCES DmXepLoaiChatLuongTCD(ID)) - Xếp loại cấp trên công nhận
    *   `NhanXetDanhGiaCuaCapTren: string` (NTEXT NULL)
    *   `SoQuyetDinhCongNhan_TCD: string` (VARCHAR(50) NULL)
    *   `NgayQuyetDinhCongNhan_TCD: date` (DATE NULL)
    *   `FileQuyetDinhCongNhan_TCD_URL: string` (VARCHAR(255) NULL)
    *   `GhiChu: string` (NTEXT)

*   **Bảng `DangVien` (PartyMembers)** (Để lấy danh sách đảng viên cần đánh giá)
    *   `MaDangVien: string`
    *   `HoVaTen: string`
    *   `ToChucDangSinhHoatID: number`

*   **Bảng `ToChucDang` (PartyOrganizations)** (Để lấy danh sách TCD cần đánh giá và phạm vi áp dụng của đợt)
    *   `ID: number`
    *   `TenToChucDang: string`

*   **Danh mục `DmXepLoaiChatLuong`** (Cho Đảng viên)
    *   `ID: number` (PK)
    *   `TenXepLoai: string` (Ví dụ: HTXSNV, HTTNV, HTNV, KHTNV)

*   **Danh mục `DmXepLoaiChatLuongTCD`** (Cho Tổ chức Đảng - có thể dùng chung `DmXepLoaiChatLuong` nếu mức giống nhau, hoặc tách riêng nếu khác)
    *   `ID: number` (PK)
    *   `TenXepLoaiTCD: string` (Ví dụ: Trong sạch vững mạnh tiêu biểu, TSVM, HTTNV, HTNV)

*   **Danh mục `DmTrangThaiDotDanhGia` (cần tạo mới)**
    *   `ID: number` (PK)
    *   `TenTrangThai: string` (Ví dụ: Đang chuẩn bị, Đang tiến hành, Đã kết thúc, Đã khóa sổ)

---

#### III. Liên kết với page khác

1.  **Trang Chi tiết Hồ sơ Đảng viên:**
    *   Tab "Tự kiểm điểm & Đánh giá" trong chi tiết hồ sơ đảng viên sẽ hiển thị kết quả đánh giá, xếp loại của đảng viên đó qua các năm, lấy dữ liệu từ các bảng `BanKiemDiemDangVien` và `DanhGiaXepLoaiDangVien`.
2.  **Trang Chi tiết Tổ chức Đảng:**
    *   Có thể có một mục/tab hiển thị lịch sử đánh giá, xếp loại của Tổ chức Đảng đó, lấy dữ liệu từ bảng `DanhGiaXepLoaiToChucDang`.

---
