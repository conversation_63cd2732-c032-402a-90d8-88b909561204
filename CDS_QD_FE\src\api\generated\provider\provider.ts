/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 *  Swagger
 * The  API documents
 * OpenAPI spec version: 1.0
 */
import { useInfiniteQuery, useMutation, useQuery } from '@tanstack/react-query'
import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseInfiniteQueryResult,
  DefinedUseQueryResult,
  InfiniteData,
  MutationFunction,
  QueryClient,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseInfiniteQueryOptions,
  UseInfiniteQueryResult,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult
} from '@tanstack/react-query'

import type { CreateProviderDto, ProviderControllerFindAllParams, UpdateProviderDto } from '.././model'

import { customInstance } from '../../mutator/custom-instance'
import type { ApiResponse } from '../../types'

export const providerControllerCreate = (createProviderDto: CreateProviderDto, signal?: AbortSignal) => {
  return customInstance<ApiResponse<void>>({
    url: `/provider`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: createProviderDto,
    signal
  })
}

export const getProviderControllerCreateMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof providerControllerCreate>>,
    TError,
    { data: CreateProviderDto },
    TContext
  >
}): UseMutationOptions<
  Awaited<ReturnType<typeof providerControllerCreate>>,
  TError,
  { data: CreateProviderDto },
  TContext
> => {
  const mutationKey = ['providerControllerCreate']
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } }

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof providerControllerCreate>>,
    { data: CreateProviderDto }
  > = props => {
    const { data } = props ?? {}

    return providerControllerCreate(data)
  }

  return { mutationFn, ...mutationOptions }
}

export type ProviderControllerCreateMutationResult = NonNullable<Awaited<ReturnType<typeof providerControllerCreate>>>
export type ProviderControllerCreateMutationBody = CreateProviderDto
export type ProviderControllerCreateMutationError = unknown

export const useProviderControllerCreate = <TError = unknown, TContext = unknown>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof providerControllerCreate>>,
      TError,
      { data: CreateProviderDto },
      TContext
    >
  },
  queryClient?: QueryClient
): UseMutationResult<
  Awaited<ReturnType<typeof providerControllerCreate>>,
  TError,
  { data: CreateProviderDto },
  TContext
> => {
  const mutationOptions = getProviderControllerCreateMutationOptions(options)

  return useMutation(mutationOptions, queryClient)
}
export const providerControllerFindAll = (params?: ProviderControllerFindAllParams, signal?: AbortSignal) => {
  return customInstance<ApiResponse<void>>({ url: `/provider`, method: 'GET', params, signal })
}

export const getProviderControllerFindAllQueryKey = (params?: ProviderControllerFindAllParams) => {
  return [`/provider`, ...(params ? [params] : [])] as const
}

export const getProviderControllerFindAllInfiniteQueryOptions = <
  TData = InfiniteData<Awaited<ReturnType<typeof providerControllerFindAll>>, ProviderControllerFindAllParams['page']>,
  TError = unknown
>(
  params?: ProviderControllerFindAllParams,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof providerControllerFindAll>>,
        TError,
        TData,
        Awaited<ReturnType<typeof providerControllerFindAll>>,
        QueryKey,
        ProviderControllerFindAllParams['page']
      >
    >
  }
) => {
  const { query: queryOptions } = options ?? {}

  const queryKey = queryOptions?.queryKey ?? getProviderControllerFindAllQueryKey(params)

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof providerControllerFindAll>>,
    QueryKey,
    ProviderControllerFindAllParams['page']
  > = ({ signal, pageParam }) => providerControllerFindAll({ ...params, page: pageParam || params?.['page'] }, signal)

  return { queryKey, queryFn, staleTime: 10000, ...queryOptions } as UseInfiniteQueryOptions<
    Awaited<ReturnType<typeof providerControllerFindAll>>,
    TError,
    TData,
    Awaited<ReturnType<typeof providerControllerFindAll>>,
    QueryKey,
    ProviderControllerFindAllParams['page']
  > & { queryKey: DataTag<QueryKey, TData, TError> }
}

export type ProviderControllerFindAllInfiniteQueryResult = NonNullable<
  Awaited<ReturnType<typeof providerControllerFindAll>>
>
export type ProviderControllerFindAllInfiniteQueryError = unknown

export function useProviderControllerFindAllInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof providerControllerFindAll>>, ProviderControllerFindAllParams['page']>,
  TError = unknown
>(
  params: undefined | ProviderControllerFindAllParams,
  options: {
    query: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof providerControllerFindAll>>,
        TError,
        TData,
        Awaited<ReturnType<typeof providerControllerFindAll>>,
        QueryKey,
        ProviderControllerFindAllParams['page']
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof providerControllerFindAll>>,
          TError,
          Awaited<ReturnType<typeof providerControllerFindAll>>,
          QueryKey
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): DefinedUseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useProviderControllerFindAllInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof providerControllerFindAll>>, ProviderControllerFindAllParams['page']>,
  TError = unknown
>(
  params?: ProviderControllerFindAllParams,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof providerControllerFindAll>>,
        TError,
        TData,
        Awaited<ReturnType<typeof providerControllerFindAll>>,
        QueryKey,
        ProviderControllerFindAllParams['page']
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof providerControllerFindAll>>,
          TError,
          Awaited<ReturnType<typeof providerControllerFindAll>>,
          QueryKey
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useProviderControllerFindAllInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof providerControllerFindAll>>, ProviderControllerFindAllParams['page']>,
  TError = unknown
>(
  params?: ProviderControllerFindAllParams,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof providerControllerFindAll>>,
        TError,
        TData,
        Awaited<ReturnType<typeof providerControllerFindAll>>,
        QueryKey,
        ProviderControllerFindAllParams['page']
      >
    >
  },
  queryClient?: QueryClient
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }

export function useProviderControllerFindAllInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof providerControllerFindAll>>, ProviderControllerFindAllParams['page']>,
  TError = unknown
>(
  params?: ProviderControllerFindAllParams,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof providerControllerFindAll>>,
        TError,
        TData,
        Awaited<ReturnType<typeof providerControllerFindAll>>,
        QueryKey,
        ProviderControllerFindAllParams['page']
      >
    >
  },
  queryClient?: QueryClient
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
  const queryOptions = getProviderControllerFindAllInfiniteQueryOptions(params, options)

  const query = useInfiniteQuery(queryOptions, queryClient) as UseInfiniteQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>
  }

  query.queryKey = queryOptions.queryKey

  return query
}

export const getProviderControllerFindAllQueryOptions = <
  TData = Awaited<ReturnType<typeof providerControllerFindAll>>,
  TError = unknown
>(
  params?: ProviderControllerFindAllParams,
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof providerControllerFindAll>>, TError, TData>> }
) => {
  const { query: queryOptions } = options ?? {}

  const queryKey = queryOptions?.queryKey ?? getProviderControllerFindAllQueryKey(params)

  const queryFn: QueryFunction<Awaited<ReturnType<typeof providerControllerFindAll>>> = ({ signal }) =>
    providerControllerFindAll(params, signal)

  return { queryKey, queryFn, staleTime: 10000, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof providerControllerFindAll>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> }
}

export type ProviderControllerFindAllQueryResult = NonNullable<Awaited<ReturnType<typeof providerControllerFindAll>>>
export type ProviderControllerFindAllQueryError = unknown

export function useProviderControllerFindAll<
  TData = Awaited<ReturnType<typeof providerControllerFindAll>>,
  TError = unknown
>(
  params: undefined | ProviderControllerFindAllParams,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof providerControllerFindAll>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof providerControllerFindAll>>,
          TError,
          Awaited<ReturnType<typeof providerControllerFindAll>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useProviderControllerFindAll<
  TData = Awaited<ReturnType<typeof providerControllerFindAll>>,
  TError = unknown
>(
  params?: ProviderControllerFindAllParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof providerControllerFindAll>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof providerControllerFindAll>>,
          TError,
          Awaited<ReturnType<typeof providerControllerFindAll>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useProviderControllerFindAll<
  TData = Awaited<ReturnType<typeof providerControllerFindAll>>,
  TError = unknown
>(
  params?: ProviderControllerFindAllParams,
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof providerControllerFindAll>>, TError, TData>> },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }

export function useProviderControllerFindAll<
  TData = Awaited<ReturnType<typeof providerControllerFindAll>>,
  TError = unknown
>(
  params?: ProviderControllerFindAllParams,
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof providerControllerFindAll>>, TError, TData>> },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
  const queryOptions = getProviderControllerFindAllQueryOptions(params, options)

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>
  }

  query.queryKey = queryOptions.queryKey

  return query
}

export const providerControllerFindOne = (id: string, signal?: AbortSignal) => {
  return customInstance<ApiResponse<ApiResponse<void>>>({ url: `/provider/${id}`, method: 'GET', signal })
}

export const getProviderControllerFindOneQueryKey = (id: string) => {
  return [`/provider/${id}`] as const
}

export const getProviderControllerFindOneInfiniteQueryOptions = <
  TData = InfiniteData<Awaited<ReturnType<typeof providerControllerFindOne>>>,
  TError = unknown
>(
  id: string,
  options?: {
    query?: Partial<UseInfiniteQueryOptions<Awaited<ReturnType<typeof providerControllerFindOne>>, TError, TData>>
  }
) => {
  const { query: queryOptions } = options ?? {}

  const queryKey = queryOptions?.queryKey ?? getProviderControllerFindOneQueryKey(id)

  const queryFn: QueryFunction<Awaited<ReturnType<typeof providerControllerFindOne>>> = ({ signal }) =>
    providerControllerFindOne(id, signal)

  return { queryKey, queryFn, enabled: !!id, staleTime: 10000, ...queryOptions } as UseInfiniteQueryOptions<
    Awaited<ReturnType<typeof providerControllerFindOne>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> }
}

export type ProviderControllerFindOneInfiniteQueryResult = NonNullable<
  Awaited<ReturnType<typeof providerControllerFindOne>>
>
export type ProviderControllerFindOneInfiniteQueryError = unknown

export function useProviderControllerFindOneInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof providerControllerFindOne>>>,
  TError = unknown
>(
  id: string,
  options: {
    query: Partial<UseInfiniteQueryOptions<Awaited<ReturnType<typeof providerControllerFindOne>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof providerControllerFindOne>>,
          TError,
          Awaited<ReturnType<typeof providerControllerFindOne>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): DefinedUseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useProviderControllerFindOneInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof providerControllerFindOne>>>,
  TError = unknown
>(
  id: string,
  options?: {
    query?: Partial<UseInfiniteQueryOptions<Awaited<ReturnType<typeof providerControllerFindOne>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof providerControllerFindOne>>,
          TError,
          Awaited<ReturnType<typeof providerControllerFindOne>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useProviderControllerFindOneInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof providerControllerFindOne>>>,
  TError = unknown
>(
  id: string,
  options?: {
    query?: Partial<UseInfiniteQueryOptions<Awaited<ReturnType<typeof providerControllerFindOne>>, TError, TData>>
  },
  queryClient?: QueryClient
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }

export function useProviderControllerFindOneInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof providerControllerFindOne>>>,
  TError = unknown
>(
  id: string,
  options?: {
    query?: Partial<UseInfiniteQueryOptions<Awaited<ReturnType<typeof providerControllerFindOne>>, TError, TData>>
  },
  queryClient?: QueryClient
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
  const queryOptions = getProviderControllerFindOneInfiniteQueryOptions(id, options)

  const query = useInfiniteQuery(queryOptions, queryClient) as UseInfiniteQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>
  }

  query.queryKey = queryOptions.queryKey

  return query
}

export const getProviderControllerFindOneQueryOptions = <
  TData = Awaited<ReturnType<typeof providerControllerFindOne>>,
  TError = unknown
>(
  id: string,
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof providerControllerFindOne>>, TError, TData>> }
) => {
  const { query: queryOptions } = options ?? {}

  const queryKey = queryOptions?.queryKey ?? getProviderControllerFindOneQueryKey(id)

  const queryFn: QueryFunction<Awaited<ReturnType<typeof providerControllerFindOne>>> = ({ signal }) =>
    providerControllerFindOne(id, signal)

  return { queryKey, queryFn, enabled: !!id, staleTime: 10000, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof providerControllerFindOne>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> }
}

export type ProviderControllerFindOneQueryResult = NonNullable<Awaited<ReturnType<typeof providerControllerFindOne>>>
export type ProviderControllerFindOneQueryError = unknown

export function useProviderControllerFindOne<
  TData = Awaited<ReturnType<typeof providerControllerFindOne>>,
  TError = unknown
>(
  id: string,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof providerControllerFindOne>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof providerControllerFindOne>>,
          TError,
          Awaited<ReturnType<typeof providerControllerFindOne>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useProviderControllerFindOne<
  TData = Awaited<ReturnType<typeof providerControllerFindOne>>,
  TError = unknown
>(
  id: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof providerControllerFindOne>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof providerControllerFindOne>>,
          TError,
          Awaited<ReturnType<typeof providerControllerFindOne>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useProviderControllerFindOne<
  TData = Awaited<ReturnType<typeof providerControllerFindOne>>,
  TError = unknown
>(
  id: string,
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof providerControllerFindOne>>, TError, TData>> },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }

export function useProviderControllerFindOne<
  TData = Awaited<ReturnType<typeof providerControllerFindOne>>,
  TError = unknown
>(
  id: string,
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof providerControllerFindOne>>, TError, TData>> },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
  const queryOptions = getProviderControllerFindOneQueryOptions(id, options)

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>
  }

  query.queryKey = queryOptions.queryKey

  return query
}

export const providerControllerUpdate = (id: string, updateProviderDto: UpdateProviderDto) => {
  return customInstance<ApiResponse<ApiResponse<void>>>({
    url: `/provider/${id}`,
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json' },
    data: updateProviderDto
  })
}

export const getProviderControllerUpdateMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof providerControllerUpdate>>,
    TError,
    { id: string; data: UpdateProviderDto },
    TContext
  >
}): UseMutationOptions<
  Awaited<ReturnType<typeof providerControllerUpdate>>,
  TError,
  { id: string; data: UpdateProviderDto },
  TContext
> => {
  const mutationKey = ['providerControllerUpdate']
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } }

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof providerControllerUpdate>>,
    { id: string; data: UpdateProviderDto }
  > = props => {
    const { id, data } = props ?? {}

    return providerControllerUpdate(id, data)
  }

  return { mutationFn, ...mutationOptions }
}

export type ProviderControllerUpdateMutationResult = NonNullable<Awaited<ReturnType<typeof providerControllerUpdate>>>
export type ProviderControllerUpdateMutationBody = UpdateProviderDto
export type ProviderControllerUpdateMutationError = unknown

export const useProviderControllerUpdate = <TError = unknown, TContext = unknown>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof providerControllerUpdate>>,
      TError,
      { id: string; data: UpdateProviderDto },
      TContext
    >
  },
  queryClient?: QueryClient
): UseMutationResult<
  Awaited<ReturnType<typeof providerControllerUpdate>>,
  TError,
  { id: string; data: UpdateProviderDto },
  TContext
> => {
  const mutationOptions = getProviderControllerUpdateMutationOptions(options)

  return useMutation(mutationOptions, queryClient)
}
export const providerControllerRemove = (id: string) => {
  return customInstance<ApiResponse<void>>({ url: `/provider/${id}`, method: 'DELETE' })
}

export const getProviderControllerRemoveMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<Awaited<ReturnType<typeof providerControllerRemove>>, TError, { id: string }, TContext>
}): UseMutationOptions<Awaited<ReturnType<typeof providerControllerRemove>>, TError, { id: string }, TContext> => {
  const mutationKey = ['providerControllerRemove']
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } }

  const mutationFn: MutationFunction<Awaited<ReturnType<typeof providerControllerRemove>>, { id: string }> = props => {
    const { id } = props ?? {}

    return providerControllerRemove(id)
  }

  return { mutationFn, ...mutationOptions }
}

export type ProviderControllerRemoveMutationResult = NonNullable<Awaited<ReturnType<typeof providerControllerRemove>>>

export type ProviderControllerRemoveMutationError = unknown

export const useProviderControllerRemove = <TError = unknown, TContext = unknown>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof providerControllerRemove>>,
      TError,
      { id: string },
      TContext
    >
  },
  queryClient?: QueryClient
): UseMutationResult<Awaited<ReturnType<typeof providerControllerRemove>>, TError, { id: string }, TContext> => {
  const mutationOptions = getProviderControllerRemoveMutationOptions(options)

  return useMutation(mutationOptions, queryClient)
}
