/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { ThanhVienCapUyEntity } from '~/database/typeorm/entities/thanhVienCapUy.entity';

@Injectable()
export class ThanhVienCapUyRepository extends Repository<ThanhVienCapUyEntity> {
    constructor(private dataSource: DataSource) {
        super(ThanhVienCapUyEntity, dataSource.createEntityManager());
    }
}
