{"version": 3, "sources": ["../../../src/database/typeorm/database.module.ts"], "sourcesContent": ["import { Global, Module } from '@nestjs/common';\nimport { ConfigService } from '@nestjs/config';\nimport { TypeOrmModule } from '@nestjs/typeorm';\nimport { DataSource } from 'typeorm';\nimport { DatabaseService } from '~/database/typeorm/database.service';\nimport { AccountEntity } from '~/database/typeorm/entities/account.entity';\nimport { DepartmentEntity } from '~/database/typeorm/entities/department.entity';\nimport { MediaEntity } from '~/database/typeorm/entities/media.entity';\nimport { PermissionEntity } from '~/database/typeorm/entities/permission.entity';\nimport { ProviderEntity } from '~/database/typeorm/entities/provider.entity';\nimport { RoleEntity } from '~/database/typeorm/entities/role.entity';\nimport { UserEntity } from '~/database/typeorm/entities/user.entity';\nimport { UserLogEntity } from '~/database/typeorm/entities/userLog.entity';\nimport { WarehouseEntity } from '~/database/typeorm/entities/warehouse.entity';\nimport { WarehouseTypeEntity } from '~/database/typeorm/entities/warehouseType.entity';\nimport { AccountRepository } from '~/database/typeorm/repositories/account.repository';\nimport { DepartmentRepository } from '~/database/typeorm/repositories/department.repository';\nimport { MediaRepository } from '~/database/typeorm/repositories/media.repository';\nimport { PermissionRepository } from '~/database/typeorm/repositories/permission.repository';\nimport { ProviderRepository } from '~/database/typeorm/repositories/provider.repository';\nimport { RoleRepository } from '~/database/typeorm/repositories/role.repository';\nimport { UserRepository } from '~/database/typeorm/repositories/user.repository';\nimport { UserLogRepository } from '~/database/typeorm/repositories/userLog.repository';\nimport { WarehouseRepository } from '~/database/typeorm/repositories/warehouse.repository';\nimport { WarehouseTypeRepository } from '~/database/typeorm/repositories/warehouseType.repository';\nimport { LoaiDanhMucEntity } from '~/database/typeorm/entities/loaiDanhMuc.entity';\nimport { LoaiDanhMucRepository } from '~/database/typeorm/repositories/loaiDanhMuc.repository';\nimport { GiaTriDanhMucEntity } from '~/database/typeorm/entities/giaTriDanhMuc.entity';\nimport { GiaTriDanhMucRepository } from '~/database/typeorm/repositories/giaTriDanhMuc.repository';\nimport { ThamSoHeThongEntity } from '~/database/typeorm/entities/thamSoHeThong.entity';\nimport { ThamSoHeThongRepository } from '~/database/typeorm/repositories/thamSoHeThong.repository';\n\nimport { QuanNhanEntity } from '~/database/typeorm/entities/quanNhan.entity';\nimport { QuanNhanRepository } from '~/database/typeorm/repositories/quanNhan.repository';\nimport { LyLichCanNhanEntity } from '~/database/typeorm/entities/lyLichCanNhan.entity';\nimport { LyLichCanNhanRepository } from '~/database/typeorm/repositories/lyLichCanNhan.repository';\nimport { QuaTrinhCongTacEntity } from '~/database/typeorm/entities/quaTrinhCongTac.entity';\nimport { QuaTrinhCongTacRepository } from '~/database/typeorm/repositories/quaTrinhCongTac.repository';\nimport { QuaTrinhDaoTaoEntity } from '~/database/typeorm/entities/quaTrinhDaoTao.entity';\nimport { QuaTrinhDaoTaoRepository } from '~/database/typeorm/repositories/quaTrinhDaoTao.repository';\nimport { DeXuatKhenThuongEntity } from '~/database/typeorm/entities/deXuatKhenThuong.entity';\nimport { DeXuatKhenThuongRepository } from '~/database/typeorm/repositories/deXuatKhenThuong.repository';\nimport { QuyetDinhKhenThuongEntity } from '~/database/typeorm/entities/quyetDinhKhenThuong.entity';\nimport { QuyetDinhKhenThuongRepository } from '~/database/typeorm/repositories/quyetDinhKhenThuong.repository';\nimport { HoSoVuViecKyLuatEntity } from '~/database/typeorm/entities/hoSoVuViecKyLuat.entity';\nimport { HoSoVuViecKyLuatRepository } from '~/database/typeorm/repositories/hoSoVuViecKyLuat.repository';\nimport { BienBanHoiDongKyLuatEntity } from '~/database/typeorm/entities/bienBanHoiDongKyLuat.entity';\nimport { BienBanHoiDongKyLuatRepository } from '~/database/typeorm/repositories/bienBanHoiDongKyLuat.repository';\nimport { TaiLieuVuViecKyLuatEntity } from '~/database/typeorm/entities/taiLieuVuViecKyLuat.entity';\nimport { TaiLieuVuViecKyLuatRepository } from '~/database/typeorm/repositories/taiLieuVuViecKyLuat.repository';\nimport { QuyetDinhKyLuatEntity } from '~/database/typeorm/entities/quyetDinhKyLuat.entity';\nimport { QuyetDinhKyLuatRepository } from '~/database/typeorm/repositories/quyetDinhKyLuat.repository';\nimport { HoSoSucKhoeEntity } from '~/database/typeorm/entities/hoSoSucKhoe.entity';\nimport { HoSoSucKhoeRepository } from '~/database/typeorm/repositories/hoSoSucKhoe.repository';\nimport { QuanHeGiaDinhEntity } from '~/database/typeorm/entities/quanHeGiaDinh.entity';\nimport { QuanHeGiaDinhRepository } from '~/database/typeorm/repositories/quanHeGiaDinh.repository';\nimport { TheoDoiCheDoChinhSachEntity } from '~/database/typeorm/entities/theoDoiCheDoChinhSach.entity';\nimport { TheoDoiCheDoChinhSachRepository } from '~/database/typeorm/repositories/theoDoiCheDoChinhSach.repository';\nimport { DangVienEntity } from '~/database/typeorm/entities/dangVien.entity';\nimport { DangVienRepository } from '~/database/typeorm/repositories/dangVien.repository';\nimport { HoSoDangVienEntity } from '~/database/typeorm/entities/hoSoDangVien.entity';\nimport { HoSoDangVienRepository } from '~/database/typeorm/repositories/hoSoDangVien.repository';\nimport { DeXuatKhenThuongDangVienEntity } from '~/database/typeorm/entities/deXuatKhenThuongDangVien.entity';\nimport { DeXuatKhenThuongDangVienRepository } from '~/database/typeorm/repositories/deXuatKhenThuongDangVien.repository';\nimport { QuyetDinhKhenThuongDangVienEntity } from '~/database/typeorm/entities/quyetDinhKhenThuongDangVien.entity';\nimport { QuyetDinhKhenThuongDangVienRepository } from '~/database/typeorm/repositories/quyetDinhKhenThuongDangVien.repository';\nimport { BanKiemDiemDangVienEntity } from '~/database/typeorm/entities/banKiemDiemDangVien.entity';\nimport { BanKiemDiemDangVienRepository } from '~/database/typeorm/repositories/banKiemDiemDangVien.repository';\nimport { HoSoVuViecKyLuatDangVienEntity } from '~/database/typeorm/entities/hoSoVuViecKyLuatDangVien.entity';\nimport { HoSoVuViecKyLuatDangVienRepository } from '~/database/typeorm/repositories/hoSoVuViecKyLuatDangVien.repository';\nimport { BienBanHoiDongKyLuatDangEntity } from '~/database/typeorm/entities/bienBanHoiDongKyLuatDang.entity';\nimport { BienBanHoiDongKyLuatDangRepository } from '~/database/typeorm/repositories/bienBanHoiDongKyLuatDang.repository';\nimport { QuyetDinhKyLuatDangVienEntity } from '~/database/typeorm/entities/quyetDinhKyLuatDangVien.entity';\nimport { QuyetDinhKyLuatDangVienRepository } from '~/database/typeorm/repositories/quyetDinhKyLuatDangVien.repository';\nimport { CapUyNhiemKyEntity } from '~/database/typeorm/entities/capUyNhiemKy.entity';\nimport { CapUyNhiemKyRepository } from '~/database/typeorm/repositories/capUyNhiemKy.repository';\nimport { ThanhVienCapUyEntity } from '~/database/typeorm/entities/thanhVienCapUy.entity';\nimport { ThanhVienCapUyRepository } from '~/database/typeorm/repositories/thanhVienCapUy.repository';\nimport { KeHoachSinhHoatDangEntity } from '~/database/typeorm/entities/keHoachSinhHoatDang.entity';\nimport { KeHoachSinhHoatDangRepository } from '~/database/typeorm/repositories/keHoachSinhHoatDang.repository';\nimport { BuoiSinhHoatDangEntity } from '~/database/typeorm/entities/buoiSinhHoatDang.entity';\nimport { BuoiSinhHoatDangRepository } from '~/database/typeorm/repositories/buoiSinhHoatDang.repository';\nimport { NghiQuyetDangEntity } from '~/database/typeorm/entities/nghiQuyetDang.entity';\nimport { NghiQuyetDangRepository } from '~/database/typeorm/repositories/nghiQuyetDang.repository';\nimport { HoSoPhatTrienDangVienEntity } from '~/database/typeorm/entities/hoSoPhatTrienDangVien.entity';\nimport { HoSoPhatTrienDangVienRepository } from '~/database/typeorm/repositories/hoSoPhatTrienDangVien.repository';\nimport { DanhGiaXepLoaiDangVienEntity } from '~/database/typeorm/entities/danhGiaXepLoaiDangVien.entity';\nimport { DanhGiaXepLoaiDangVienRepository } from '~/database/typeorm/repositories/danhGiaXepLoaiDangVien.repository';\nimport { DanhGiaXepLoaiToChucDangEntity } from '~/database/typeorm/entities/danhGiaXepLoaiToChucDang.entity';\nimport { DanhGiaXepLoaiToChucDangRepository } from '~/database/typeorm/repositories/danhGiaXepLoaiToChucDang.repository';\nimport { ThuChiDangPhiEntity } from '~/database/typeorm/entities/thuChiDangPhi.entity';\nimport { ThuChiDangPhiRepository } from '~/database/typeorm/repositories/thuChiDangPhi.repository';\nimport { KeHoachKiemTraGiamSatDangEntity } from '~/database/typeorm/entities/keHoachKiemTraGiamSatDang.entity';\nimport { KeHoachKiemTraGiamSatDangRepository } from '~/database/typeorm/repositories/keHoachKiemTraGiamSatDang.repository';\nimport { CuocKiemTraGiamSatDangEntity } from '~/database/typeorm/entities/cuocKiemTraGiamSatDang.entity';\nimport { CuocKiemTraGiamSatDangRepository } from '~/database/typeorm/repositories/cuocKiemTraGiamSatDang.repository';\nimport { KeHoachGiaoDucChinhTriEntity } from '~/database/typeorm/entities/keHoachGiaoDucChinhTri.entity';\nimport { KeHoachGiaoDucChinhTriRepository } from '~/database/typeorm/repositories/keHoachGiaoDucChinhTri.repository';\nimport { HoatDongGiaoDucChinhTriEntity } from '~/database/typeorm/entities/hoatDongGiaoDucChinhTri.entity';\nimport { HoatDongGiaoDucChinhTriRepository } from '~/database/typeorm/repositories/hoatDongGiaoDucChinhTri.repository';\nimport { ThamGiaGiaoDucChinhTriEntity } from '~/database/typeorm/entities/thamGiaGiaoDucChinhTri.entity';\nimport { ThamGiaGiaoDucChinhTriRepository } from '~/database/typeorm/repositories/thamGiaGiaoDucChinhTri.repository';\nimport { KetQuaHocTapChinhTriEntity } from '~/database/typeorm/entities/ketQuaHocTapChinhTri.entity';\nimport { KetQuaHocTapChinhTriRepository } from '~/database/typeorm/repositories/ketQuaHocTapChinhTri.repository';\nimport { GhiNhanTinhHinhTuTuongEntity } from '~/database/typeorm/entities/ghiNhanTinhHinhTuTuong.entity';\nimport { GhiNhanTinhHinhTuTuongRepository } from '~/database/typeorm/repositories/ghiNhanTinhHinhTuTuong.repository';\nimport { BienPhapTacDongTuTuongEntity } from '~/database/typeorm/entities/bienPhapTacDongTuTuong.entity';\nimport { BienPhapTacDongTuTuongRepository } from '~/database/typeorm/repositories/bienPhapTacDongTuTuong.repository';\nimport { KeHoachTuyenTruyenVanHoaEntity } from '~/database/typeorm/entities/keHoachTuyenTruyenVanHoa.entity';\nimport { KeHoachTuyenTruyenVanHoaRepository } from '~/database/typeorm/repositories/keHoachTuyenTruyenVanHoa.repository';\nimport { HoatDongTuyenTruyenVanHoaEntity } from '~/database/typeorm/entities/hoatDongTuyenTruyenVanHoa.entity';\nimport { HoatDongTuyenTruyenVanHoaRepository } from '~/database/typeorm/repositories/hoatDongTuyenTruyenVanHoa.repository';\nimport { PhongTraoThiDuaChinhTriEntity } from '~/database/typeorm/entities/phongTraoThiDuaChinhTri.entity';\nimport { PhongTraoThiDuaChinhTriRepository } from '~/database/typeorm/repositories/phongTraoThiDuaChinhTri.repository';\nimport { ChinhSachHauPhuongEntity } from '~/database/typeorm/entities/chinhSachHauPhuong.entity';\nimport { ChinhSachHauPhuongRepository } from '~/database/typeorm/repositories/chinhSachHauPhuong.repository';\nimport { DoiTuongChinhSachHauPhuongEntity } from '~/database/typeorm/entities/doiTuongChinhSachHauPhuong.entity';\nimport { DoiTuongChinhSachHauPhuongRepository } from '~/database/typeorm/repositories/doiTuongChinhSachHauPhuong.repository';\nimport { ThongBaoEntity } from '~/database/typeorm/entities/thongBao.entity';\nimport { ThongBaoRepository } from '~/database/typeorm/repositories/thongBao.repository';\nimport { QuyTrinhPheDuyetEntity } from '~/database/typeorm/entities/quyTrinhPheDuyet.entity';\nimport { QuyTrinhPheDuyetRepository } from '~/database/typeorm/repositories/quyTrinhPheDuyet.repository';\nimport { YeuCauPheDuyetEntity } from '~/database/typeorm/entities/yeuCauPheDuyet.entity';\nimport { YeuCauPheDuyetRepository } from '~/database/typeorm/repositories/yeuCauPheDuyet.repository';\nimport { BuocPheDuyetEntity } from '~/database/typeorm/entities/buocPheDuyet.entity';\nimport { BuocPheDuyetRepository } from '~/database/typeorm/repositories/buocPheDuyet.repository';\nimport { LichSuPheDuyetEntity } from '~/database/typeorm/entities/lichSuPheDuyet.entity';\nimport { LichSuPheDuyetRepository } from '~/database/typeorm/repositories/lichSuPheDuyet.repository';\nimport { NhatKyHeThongEntity } from '~/database/typeorm/entities/nhatKyHeThong.entity';\nimport { NhatKyHeThongRepository } from '~/database/typeorm/repositories/nhatKyHeThong.repository';\nimport { DuLieuTepTinEntity } from '~/database/typeorm/entities/duLieuTepTin.entity';\nimport { DuLieuTepTinRepository } from '~/database/typeorm/repositories/duLieuTepTin.repository';\nimport { UsersRoleEntity } from '~/database/typeorm/entities/usersRole.entity';\nimport { UsersRoleRepository } from '~/database/typeorm/repositories/usersRole.repository';\n\nconst entities = [\n    RoleEntity,\n    UserEntity,\n    PermissionEntity,\n    MediaEntity,\n    AccountEntity,\n    DepartmentEntity,\n    WarehouseEntity,\n    UserLogEntity,\n    WarehouseTypeEntity,\n    ProviderEntity,\n    LoaiDanhMucEntity,\n    GiaTriDanhMucEntity,\n    ThamSoHeThongEntity,\n    QuanNhanEntity,\n    LyLichCanNhanEntity,\n    QuaTrinhCongTacEntity,\n    QuaTrinhDaoTaoEntity,\n    DeXuatKhenThuongEntity,\n    QuyetDinhKhenThuongEntity,\n    HoSoVuViecKyLuatEntity,\n    BienBanHoiDongKyLuatEntity,\n    TaiLieuVuViecKyLuatEntity,\n    QuyetDinhKyLuatEntity,\n    HoSoSucKhoeEntity,\n    QuanHeGiaDinhEntity,\n    TheoDoiCheDoChinhSachEntity,\n    DangVienEntity,\n    HoSoDangVienEntity,\n    DeXuatKhenThuongDangVienEntity,\n    QuyetDinhKhenThuongDangVienEntity,\n    BanKiemDiemDangVienEntity,\n    HoSoVuViecKyLuatDangVienEntity,\n    BienBanHoiDongKyLuatDangEntity,\n    QuyetDinhKyLuatDangVienEntity,\n    CapUyNhiemKyEntity,\n    ThanhVienCapUyEntity,\n    KeHoachSinhHoatDangEntity,\n    BuoiSinhHoatDangEntity,\n    NghiQuyetDangEntity,\n    HoSoPhatTrienDangVienEntity,\n    DanhGiaXepLoaiDangVienEntity,\n    DanhGiaXepLoaiToChucDangEntity,\n    ThuChiDangPhiEntity,\n    KeHoachKiemTraGiamSatDangEntity,\n    CuocKiemTraGiamSatDangEntity,\n    KeHoachGiaoDucChinhTriEntity,\n    HoatDongGiaoDucChinhTriEntity,\n    ThamGiaGiaoDucChinhTriEntity,\n    KetQuaHocTapChinhTriEntity,\n    GhiNhanTinhHinhTuTuongEntity,\n    BienPhapTacDongTuTuongEntity,\n    KeHoachTuyenTruyenVanHoaEntity,\n    HoatDongTuyenTruyenVanHoaEntity,\n    PhongTraoThiDuaChinhTriEntity,\n    ChinhSachHauPhuongEntity,\n    DoiTuongChinhSachHauPhuongEntity,\n    ThongBaoEntity,\n    QuyTrinhPheDuyetEntity,\n    YeuCauPheDuyetEntity,\n    BuocPheDuyetEntity,\n    LichSuPheDuyetEntity,\n    NhatKyHeThongEntity,\n    DuLieuTepTinEntity,\n    UsersRoleEntity,\n];\n\nconst repositories = [\n    DepartmentRepository,\n    UserRepository,\n    AccountRepository,\n    MediaRepository,\n    PermissionRepository,\n    RoleRepository,\n    WarehouseRepository,\n    UserLogRepository,\n    WarehouseTypeRepository,\n    ProviderRepository,\n    LoaiDanhMucRepository,\n    GiaTriDanhMucRepository,\n    ThamSoHeThongRepository,\n    QuanNhanRepository,\n    LyLichCanNhanRepository,\n    QuaTrinhCongTacRepository,\n    QuaTrinhDaoTaoRepository,\n    DeXuatKhenThuongRepository,\n    QuyetDinhKhenThuongRepository,\n    HoSoVuViecKyLuatRepository,\n    BienBanHoiDongKyLuatRepository,\n    TaiLieuVuViecKyLuatRepository,\n    QuyetDinhKyLuatRepository,\n    HoSoSucKhoeRepository,\n    QuanHeGiaDinhRepository,\n    TheoDoiCheDoChinhSachRepository,\n    DangVienRepository,\n    HoSoDangVienRepository,\n    DeXuatKhenThuongDangVienRepository,\n    QuyetDinhKhenThuongDangVienRepository,\n    BanKiemDiemDangVienRepository,\n    HoSoVuViecKyLuatDangVienRepository,\n    BienBanHoiDongKyLuatDangRepository,\n    QuyetDinhKyLuatDangVienRepository,\n    CapUyNhiemKyRepository,\n    ThanhVienCapUyRepository,\n    KeHoachSinhHoatDangRepository,\n    BuoiSinhHoatDangRepository,\n    NghiQuyetDangRepository,\n    HoSoPhatTrienDangVienRepository,\n    DanhGiaXepLoaiDangVienRepository,\n    DanhGiaXepLoaiToChucDangRepository,\n    ThuChiDangPhiRepository,\n    KeHoachKiemTraGiamSatDangRepository,\n    CuocKiemTraGiamSatDangRepository,\n    KeHoachGiaoDucChinhTriRepository,\n    HoatDongGiaoDucChinhTriRepository,\n    ThamGiaGiaoDucChinhTriRepository,\n    KetQuaHocTapChinhTriRepository,\n    GhiNhanTinhHinhTuTuongRepository,\n    BienPhapTacDongTuTuongRepository,\n    KeHoachTuyenTruyenVanHoaRepository,\n    HoatDongTuyenTruyenVanHoaRepository,\n    PhongTraoThiDuaChinhTriRepository,\n    ChinhSachHauPhuongRepository,\n    DoiTuongChinhSachHauPhuongRepository,\n    ThongBaoRepository,\n    QuyTrinhPheDuyetRepository,\n    YeuCauPheDuyetRepository,\n    BuocPheDuyetRepository,\n    LichSuPheDuyetRepository,\n    NhatKyHeThongRepository,\n    DuLieuTepTinRepository,\n    UsersRoleRepository,\n];\n\n@Global()\n@Module({\n    imports: [\n        TypeOrmModule.forRootAsync({\n            useFactory: (configService: ConfigService) => ({\n                ...configService.get('database'),\n                entities,\n            }),\n            inject: [ConfigService],\n            // dataSource receives the configured DataSourceOptions\n            // and returns a Promise<DataSource>.\n            dataSourceFactory: async (options) => {\n                const dataSource = await new DataSource(options).initialize();\n                return dataSource;\n            },\n        }),\n        // TypeOrmModule.forFeature(entities),\n    ],\n    providers: [DatabaseService, ...repositories],\n    exports: [DatabaseService],\n})\nexport class DatabaseModule {}\n"], "names": ["DatabaseModule", "entities", "RoleEntity", "UserEntity", "PermissionEntity", "MediaEntity", "AccountEntity", "DepartmentEntity", "WarehouseEntity", "UserLogEntity", "WarehouseTypeEntity", "ProviderEntity", "LoaiDanhMucEntity", "GiaTriDanhMucEntity", "ThamSoHeThongEntity", "QuanNhanEntity", "LyLichCanNhanEntity", "QuaTrinhCongTacEntity", "QuaTrinhDaoTaoEntity", "DeXuatKhenThuongEntity", "QuyetDinhKhenThuongEntity", "HoSoVuViecKyLuatEntity", "BienBanHoiDongKyLuatEntity", "TaiLieuVuViecKyLuatEntity", "QuyetDinhKyLuatEntity", "HoSoSucKhoeEntity", "QuanHeGiaDinhEntity", "TheoDoiCheDoChinhSachEntity", "DangVienEntity", "HoSoDangVienEntity", "DeXuatKhenThuongDangVienEntity", "QuyetDinhKhenThuongDangVienEntity", "BanKiemDiemDangVienEntity", "HoSoVuViecKyLuatDangVienEntity", "BienBanHoiDongKyLuatDangEntity", "QuyetDinhKyLuatDangVienEntity", "CapUyNhiemKyEntity", "ThanhVienCapUyEntity", "KeHoachSinhHoatDangEntity", "BuoiSinhHoatDangEntity", "NghiQuyetDangEntity", "HoSoPhatTrienDangVienEntity", "DanhGiaXepLoaiDangVienEntity", "DanhGiaXepLoaiToChucDangEntity", "ThuChiDangPhiEntity", "KeHoachKiemTraGiamSatDangEntity", "CuocKiemTraGiamSatDangEntity", "KeHoachGiaoDucChinhTriEntity", "HoatDongGiaoDucChinhTriEntity", "ThamGiaGiaoDucChinhTriEntity", "KetQuaHocTapChinhTriEntity", "GhiNhanTinhHinhTuTuongEntity", "BienPhapTacDongTuTuongEntity", "KeHoachTuyenTruyenVanHoaEntity", "HoatDongTuyenTruyenVanHoaEntity", "PhongTraoThiDuaChinhTriEntity", "ChinhSachHauPhuongEntity", "DoiTuongChinhSachHauPhuongEntity", "ThongBaoEntity", "QuyTrinhPheDuyetEntity", "YeuCauPheDuyetEntity", "BuocPheDuyetEntity", "LichSuPheDuyetEntity", "NhatKyHeThongEntity", "DuLieuTepTinEntity", "UsersRoleEntity", "repositories", "DepartmentRepository", "UserRepository", "AccountRepository", "MediaRepository", "PermissionRepository", "RoleRepository", "WarehouseRepository", "UserLogRepository", "WarehouseTypeRepository", "ProviderRepository", "LoaiDanhMucRepository", "GiaTriDanhMucRepository", "ThamSoHeThongRepository", "QuanNhanRepository", "LyLichCanNhanRepository", "QuaTrinhCongTacRepository", "QuaTrinhDaoTaoRepository", "DeXuatKhenThuongRepository", "QuyetDinhKhenThuongRepository", "HoSoVuViecKyLuatRepository", "BienBanHoiDongKyLuatRepository", "TaiLieuVuViecKyLuatRepository", "QuyetDinhKyLuatRepository", "HoSoSucKhoeRepository", "QuanHeGiaDinhRepository", "TheoDoiCheDoChinhSachRepository", "DangVienRepository", "HoSoDangVienRepository", "DeXuatKhenThuongDangVienRepository", "QuyetDinhKhenThuongDangVienRepository", "BanKiemDiemDangVienRepository", "HoSoVuViecKyLuatDangVienRepository", "BienBanHoiDongKyLuatDangRepository", "QuyetDinhKyLuatDangVienRepository", "CapUyNhiemKyRepository", "ThanhVienCapUyRepository", "KeHoachSinhHoatDangRepository", "BuoiSinhHoatDangRepository", "NghiQuyetDangRepository", "HoSoPhatTrienDangVienRepository", "DanhGiaXepLoaiDangVienRepository", "DanhGiaXepLoaiToChucDangRepository", "ThuChiDangPhiRepository", "KeHoachKiemTraGiamSatDangRepository", "CuocKiemTraGiamSatDangRepository", "KeHoachGiaoDucChinhTriRepository", "HoatDongGiaoDucChinhTriRepository", "ThamGiaGiaoDucChinhTriRepository", "KetQuaHocTapChinhTriRepository", "GhiNhanTinhHinhTuTuongRepository", "BienPhapTacDongTuTuongRepository", "KeHoachTuyenTruyenVanHoaRepository", "HoatDongTuyenTruyenVanHoaRepository", "PhongTraoThiDuaChinhTriRepository", "ChinhSachHauPhuongRepository", "DoiTuongChinhSachHauPhuongRepository", "ThongBaoRepository", "QuyTrinhPheDuyetRepository", "YeuCauPheDuyetRepository", "BuocPheDuyetRepository", "LichSuPheDuyetRepository", "NhatKyHeThongRepository", "DuLieuTepTinRepository", "UsersRoleRepository", "imports", "TypeOrmModule", "forRootAsync", "useFactory", "configService", "get", "inject", "ConfigService", "dataSourceFactory", "options", "dataSource", "DataSource", "initialize", "providers", "DatabaseService", "exports"], "mappings": "oGAkSaA,wDAAAA,wCAlSkB,wCACD,yCACA,2CACH,0CACK,mDACF,6DACG,2DACL,2DACK,8DACF,wDACJ,oDACA,uDACG,4DACE,kEACI,oEACF,yEACG,uEACL,uEACK,0EACF,oEACJ,gEACA,mEACG,wEACE,8EACI,4EACN,sEACI,4EACF,0EACI,8EACJ,0EACI,yEAET,gEACI,yEACC,0EACI,gFACF,8EACI,iFACL,4EACI,kFACF,gFACI,uFACD,sFACI,uFACP,gFACI,wFACA,wFACI,2FACL,sFACI,sFACR,8EACI,8EACR,sEACI,4EACF,0EACI,sFACI,0FACI,iFACjB,gEACI,wEACA,wEACI,wFACQ,gGACI,uGACD,sGACI,kGACZ,sFACI,+FACC,gGACI,oGACJ,gGACI,mGACL,8FACI,uFACf,wEACI,8EACF,4EACI,qFACC,sFACI,uFACP,gFACI,iFACP,0EACI,sFACI,0FACI,+FACH,4FACI,kGACF,gGACI,yFACf,0EACI,0FACQ,kGACI,mGACP,4FACI,gGACJ,4FACI,iGACH,8FACI,iGACL,4FACI,8FACN,wFACI,8FACF,4FACI,gGACJ,4FACI,kGACF,gGACI,qGACH,kGACI,oGACN,8FACI,6FACT,oFACI,gGACI,oGACI,sFACtB,gEACI,4EACI,gFACI,kFACN,4EACI,8EACN,wEACI,8EACF,4EACI,+EACL,0EACI,6EACL,wEACI,yEACP,kEACI,ydAEpC,MAAMC,SAAW,CACbC,sBAAU,CACVC,sBAAU,CACVC,kCAAgB,CAChBC,wBAAW,CACXC,4BAAa,CACbC,kCAAgB,CAChBC,gCAAe,CACfC,4BAAa,CACbC,wCAAmB,CACnBC,8BAAc,CACdC,oCAAiB,CACjBC,wCAAmB,CACnBC,wCAAmB,CACnBC,8BAAc,CACdC,wCAAmB,CACnBC,4CAAqB,CACrBC,0CAAoB,CACpBC,8CAAsB,CACtBC,oDAAyB,CACzBC,8CAAsB,CACtBC,sDAA0B,CAC1BC,oDAAyB,CACzBC,4CAAqB,CACrBC,oCAAiB,CACjBC,wCAAmB,CACnBC,wDAA2B,CAC3BC,8BAAc,CACdC,sCAAkB,CAClBC,8DAA8B,CAC9BC,oEAAiC,CACjCC,oDAAyB,CACzBC,8DAA8B,CAC9BC,8DAA8B,CAC9BC,4DAA6B,CAC7BC,sCAAkB,CAClBC,0CAAoB,CACpBC,oDAAyB,CACzBC,8CAAsB,CACtBC,wCAAmB,CACnBC,wDAA2B,CAC3BC,0DAA4B,CAC5BC,8DAA8B,CAC9BC,wCAAmB,CACnBC,gEAA+B,CAC/BC,0DAA4B,CAC5BC,0DAA4B,CAC5BC,4DAA6B,CAC7BC,0DAA4B,CAC5BC,sDAA0B,CAC1BC,0DAA4B,CAC5BC,0DAA4B,CAC5BC,8DAA8B,CAC9BC,gEAA+B,CAC/BC,4DAA6B,CAC7BC,kDAAwB,CACxBC,kEAAgC,CAChCC,8BAAc,CACdC,8CAAsB,CACtBC,0CAAoB,CACpBC,sCAAkB,CAClBC,0CAAoB,CACpBC,wCAAmB,CACnBC,sCAAkB,CAClBC,gCAAe,CAClB,CAED,MAAMC,aAAe,CACjBC,0CAAoB,CACpBC,8BAAc,CACdC,oCAAiB,CACjBC,gCAAe,CACfC,0CAAoB,CACpBC,8BAAc,CACdC,wCAAmB,CACnBC,oCAAiB,CACjBC,gDAAuB,CACvBC,sCAAkB,CAClBC,4CAAqB,CACrBC,gDAAuB,CACvBC,gDAAuB,CACvBC,sCAAkB,CAClBC,gDAAuB,CACvBC,oDAAyB,CACzBC,kDAAwB,CACxBC,sDAA0B,CAC1BC,4DAA6B,CAC7BC,sDAA0B,CAC1BC,8DAA8B,CAC9BC,4DAA6B,CAC7BC,oDAAyB,CACzBC,4CAAqB,CACrBC,gDAAuB,CACvBC,gEAA+B,CAC/BC,sCAAkB,CAClBC,8CAAsB,CACtBC,sEAAkC,CAClCC,4EAAqC,CACrCC,4DAA6B,CAC7BC,sEAAkC,CAClCC,sEAAkC,CAClCC,oEAAiC,CACjCC,8CAAsB,CACtBC,kDAAwB,CACxBC,4DAA6B,CAC7BC,sDAA0B,CAC1BC,gDAAuB,CACvBC,gEAA+B,CAC/BC,kEAAgC,CAChCC,sEAAkC,CAClCC,gDAAuB,CACvBC,wEAAmC,CACnCC,kEAAgC,CAChCC,kEAAgC,CAChCC,oEAAiC,CACjCC,kEAAgC,CAChCC,8DAA8B,CAC9BC,kEAAgC,CAChCC,kEAAgC,CAChCC,sEAAkC,CAClCC,wEAAmC,CACnCC,oEAAiC,CACjCC,0DAA4B,CAC5BC,0EAAoC,CACpCC,sCAAkB,CAClBC,sDAA0B,CAC1BC,kDAAwB,CACxBC,8CAAsB,CACtBC,kDAAwB,CACxBC,gDAAuB,CACvBC,8CAAsB,CACtBC,wCAAmB,CACtB,CAuBM,IAAA,AAAMlI,eAAN,MAAMA,eAAgB,wEAnBzBmI,QAAS,CACLC,sBAAa,CAACC,YAAY,CAAC,CACvBC,WAAY,AAACC,eAAkC,CAAA,CAC3C,GAAGA,cAAcC,GAAG,CAAC,WAAW,CAChCvI,QACJ,CAAA,EACAwI,OAAQ,CAACC,qBAAa,CAAC,CAGvBC,kBAAmB,MAAOC,UACtB,MAAMC,WAAa,MAAM,IAAIC,oBAAU,CAACF,SAASG,UAAU,GAC3D,OAAOF,UACX,CACJ,GAEH,CACDG,UAAW,CAACC,gCAAe,IAAK/E,aAAa,CAC7CgF,QAAS,CAACD,gCAAe,CAAC"}