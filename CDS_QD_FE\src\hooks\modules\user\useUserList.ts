import { useUserControllerFindAll } from '@/api/generated/user/user';
import { useCallback } from 'react';

export function useUserList(initialParams = {}) {
  const { data, isLoading, error, refetch } = useUserControllerFindAll(initialParams);

  const refresh = useCallback(() => {
    refetch();
  }, [refetch]);

  return {
    userList: data?.data || [],
    pagination: data?.pagination,
    isLoading,
    error,
    refresh
  };
}
