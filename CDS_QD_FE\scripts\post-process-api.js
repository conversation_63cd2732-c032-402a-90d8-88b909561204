/**
 * Script for post-processing API files generated by <PERSON>val
 * This script automatically detects endpoints and applies appropriate response types
 * based on naming conventions and API structure analysis
 *
 * No need to update this script when adding new modules to the API
 */
const fs = require('fs')
const path = require('path')
const { globSync } = require('glob')

// Path to the directory containing files generated by <PERSON><PERSON>
const generatedDir = path.resolve(__dirname, '../src/api/generated')

/**
 * Common rules for methods
 * Format: {
 *   methodName: {
 *     pattern: RegExp to identify the method,
 *     responseFormat: Function to create response type based on controller name
 *   }
 * }
 */
const commonMethodRules = {
  findAll: {
    pattern: /findAll$/,
    responseFormat: controllerName => `PaginatedResponse<${getEntityName(controllerName)}Response>`
  },
  findOne: {
    pattern: /findOne$/,
    responseFormat: controllerName => `${getEntityName(controllerName)}Response`
  },
  create: {
    pattern: /create$/,
    responseFormat: controllerName => `${getEntityName(controllerName)}Response`
  },
  update: {
    pattern: /update$/,
    responseFormat: controllerName => `${getEntityName(controllerName)}Response`
  },
  remove: {
    pattern: /remove$/,
    responseFormat: () => `ApiResponse<void>`
  },
  delete: {
    pattern: /delete$/,
    responseFormat: () => `ApiResponse<void>`
  },
  login: {
    pattern: /login$/,
    responseFormat: () => `LoginResponse`
  },
  logout: {
    pattern: /logout$/,
    responseFormat: () => `ApiResponse<void>`
  },
  renewToken: {
    pattern: /renewToken$/,
    responseFormat: () => `TokenResponse`
  },
  forgotPassword: {
    pattern: /forgotPassword$/,
    responseFormat: () => `ApiResponse<void>`
  },
  resetPassword: {
    pattern: /resetPassword$/,
    responseFormat: () => `ApiResponse<void>`
  }
}

/**
 * Get entity name from controller name
 * Example: userController -> User, roleController -> Role
 * @param {string} controllerName - Controller name
 * @returns {string} - Entity name
 */
function getEntityName(controllerName) {
  // Remove "Controller" from the name
  const entityName = controllerName.replace(/Controller$/, '')

  // Capitalize first letter
  return entityName.charAt(0).toUpperCase() + entityName.slice(1)
}

/**
 * Get controller name from endpoint name
 * Example: userControllerFindAll -> userController
 * @param {string} endpointName - Endpoint name
 * @returns {string} - Controller name
 */
function getControllerName(endpointName) {
  // Find position of "Controller"
  const controllerIndex = endpointName.indexOf('Controller')
  if (controllerIndex === -1) return ''

  // Extract controller name
  return endpointName.substring(0, controllerIndex + 'Controller'.length)
}

/**
 * Get method name from endpoint name
 * Example: userControllerFindAll -> findAll
 * @param {string} endpointName - Endpoint name
 * @returns {string} - Method name
 */
function getMethodName(endpointName) {
  // Find position of "Controller"
  const controllerIndex = endpointName.indexOf('Controller')
  if (controllerIndex === -1) return endpointName

  // Extract method name
  return endpointName.substring(controllerIndex + 'Controller'.length)
}

// No need to declare type imports list here as they will be detected automatically

try {
  // Find all .ts files in the generated directory
  const files = globSync(`${generatedDir}/**/*.ts`)

  // Process each file
  files.forEach(file => {
    // Skip files in the model directory
    if (file.includes('/model/') || file.includes('\\model\\')) {
      return
    }

    // Read file content
    let content = fs.readFileSync(file, 'utf8')

    // Extract all function names from the file
    const functionRegex = /export const (\w+) = \(/g
    let match
    const endpoints = []

    while ((match = functionRegex.exec(content)) !== null) {
      endpoints.push(match[1])
    }

    if (endpoints.length === 0) {
      return
    }

    // Determine response types for each endpoint
    const responseTypes = {}
    const typesNeeded = new Set()

    endpoints.forEach(endpoint => {
      // Get controller and method names
      const controllerName = getControllerName(endpoint)
      const methodName = getMethodName(endpoint)

      // Default is ApiResponse<void>
      let responseType = 'ApiResponse<void>'

      if (controllerName) {
        // Check if method matches any common rule
        for (const rule of Object.values(commonMethodRules)) {
          if (rule.pattern.test(methodName)) {
            responseType = rule.responseFormat(controllerName)
            break
          }
        }
      }

      responseTypes[endpoint] = responseType

      // Add required type imports based on the response type
      // Extract type names from response type (e.g., 'PaginatedResponse<UserResponse>' -> ['PaginatedResponse', 'UserResponse'])
      const typeMatches = responseType.match(/([A-Z][a-zA-Z]+)(?:<([A-Z][a-zA-Z]+)>)?/g) || []

      typeMatches.forEach(type => {
        // Handle generic types (e.g., 'PaginatedResponse<UserResponse>')
        if (type.includes('<')) {
          const [outerType, innerType] = type.split(/[<>]/)

          if (outerType) typesNeeded.add(outerType)
          if (innerType) typesNeeded.add(innerType)
        } else {
          typesNeeded.add(type)
        }
      })
    })

    // Add imports if needed
    if (typesNeeded.size > 0) {
      const importTypes = Array.from(typesNeeded).join(', ')
      const importStatement = `import type { ${importTypes} } from '../../types'\n`

      // Check if import already exists
      if (!content.includes("from '../../types'")) {
        // Find position to add import
        const importIndex = content.lastIndexOf('import')
        const importEndIndex = content.indexOf('\n', importIndex) + 1

        // Add import
        content = content.slice(0, importEndIndex) + importStatement + content.slice(importEndIndex)
      }
    }

    // Replace void return types with correct types
    endpoints.forEach(endpoint => {
      if (responseTypes[endpoint]) {
        const pattern = new RegExp(`export const ${endpoint} = [\\s\\S]*?return customInstance<void>`, 'g')

        content = content.replace(pattern, match => {
          return match.replace('void', responseTypes[endpoint])
        })
      }
    })

    // Write updated content back to file
    fs.writeFileSync(file, content, 'utf8')
    console.log(`Processed ${file}`)
  })

  console.log('Post-processing completed!')
} catch (error) {
  console.error('Error:', error)
}
