/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { DeXuatKhenThuongEntity } from '~/database/typeorm/entities/deXuatKhenThuong.entity';

@Injectable()
export class DeXuatKhenThuongRepository extends Repository<DeXuatKhenThuongEntity> {
    constructor(private dataSource: DataSource) {
        super(DeXuatKhenThuongEntity, dataSource.createEntityManager());
    }
}
