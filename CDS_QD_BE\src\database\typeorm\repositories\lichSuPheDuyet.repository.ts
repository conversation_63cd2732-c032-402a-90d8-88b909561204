/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { LichSuPheDuyetEntity } from '~/database/typeorm/entities/lichSuPheDuyet.entity';

@Injectable()
export class LichSuPheDuyetRepository extends Repository<LichSuPheDuyetEntity> {
    constructor(private dataSource: DataSource) {
        super(LichSuPheDuyetEntity, dataSource.createEntityManager());
    }
}
