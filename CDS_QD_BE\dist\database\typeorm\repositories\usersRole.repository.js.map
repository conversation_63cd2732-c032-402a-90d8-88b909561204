{"version": 3, "sources": ["../../../../src/database/typeorm/repositories/usersRole.repository.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\n\nimport { Injectable } from '@nestjs/common';\nimport { DataSource, Repository } from 'typeorm';\nimport { UsersRoleEntity } from '~/database/typeorm/entities/usersRole.entity';\n\n@Injectable()\nexport class UsersRoleRepository extends Repository<UsersRoleEntity> {\n    constructor(private dataSource: DataSource) {\n        super(UsersRoleEntity, dataSource.createEntityManager());\n    }\n\n    // Find all roles for a specific user\n    async findRolesByUserId(userId: number) {\n        return this.find({\n            where: { userId },\n            relations: ['role'],\n        });\n    }\n\n    // Find all users for a specific role\n    async findUsersByRoleId(roleId: number) {\n        return this.find({\n            where: { roleId },\n            relations: ['user'],\n        });\n    }\n\n    // Add role to user\n    async addRoleToUser(userId: number, roleId: number) {\n        const existingUserRole = await this.findOne({\n            where: { userId, roleId },\n        });\n\n        if (existingUserRole) {\n            return existingUserRole;\n        }\n\n        const userRole = this.create({ userId, roleId });\n        return this.save(userRole);\n    }\n\n    // Remove role from user\n    async removeRoleFromUser(userId: number, roleId: number) {\n        return this.delete({ userId, roleId });\n    }\n\n    // Remove all roles from user\n    async removeAllRolesFromUser(userId: number) {\n        return this.delete({ userId });\n    }\n\n    // Check if user has specific role\n    async userHasRole(userId: number, roleId: number): Promise<boolean> {\n        const userRole = await this.findOne({\n            where: { userId, roleId },\n        });\n        return !!userRole;\n    }\n}\n"], "names": ["UsersRoleRepository", "Repository", "findRolesByUserId", "userId", "find", "where", "relations", "findUsersByRoleId", "roleId", "addRoleToUser", "existingUserRole", "findOne", "userRole", "create", "save", "removeRoleFromUser", "delete", "removeAllRolesFromUser", "userHasRole", "constructor", "dataSource", "UsersRoleEntity", "createEntityManager"], "mappings": "oGAOaA,6DAAAA,6CALc,yCACY,0CACP,6kBAGzB,IAAA,AAAMA,oBAAN,MAAMA,4BAA4BC,mBAAU,CAM/C,MAAMC,kBAAkBC,MAAc,CAAE,CACpC,OAAO,IAAI,CAACC,IAAI,CAAC,CACbC,MAAO,CAAEF,MAAO,EAChBG,UAAW,CAAC,OAAO,AACvB,EACJ,CAGA,MAAMC,kBAAkBC,MAAc,CAAE,CACpC,OAAO,IAAI,CAACJ,IAAI,CAAC,CACbC,MAAO,CAAEG,MAAO,EAChBF,UAAW,CAAC,OAAO,AACvB,EACJ,CAGA,MAAMG,cAAcN,MAAc,CAAEK,MAAc,CAAE,CAChD,MAAME,iBAAmB,MAAM,IAAI,CAACC,OAAO,CAAC,CACxCN,MAAO,CAAEF,OAAQK,MAAO,CAC5B,GAEA,GAAIE,iBAAkB,CAClB,OAAOA,gBACX,CAEA,MAAME,SAAW,IAAI,CAACC,MAAM,CAAC,CAAEV,OAAQK,MAAO,GAC9C,OAAO,IAAI,CAACM,IAAI,CAACF,SACrB,CAGA,MAAMG,mBAAmBZ,MAAc,CAAEK,MAAc,CAAE,CACrD,OAAO,IAAI,CAACQ,MAAM,CAAC,CAAEb,OAAQK,MAAO,EACxC,CAGA,MAAMS,uBAAuBd,MAAc,CAAE,CACzC,OAAO,IAAI,CAACa,MAAM,CAAC,CAAEb,MAAO,EAChC,CAGA,MAAMe,YAAYf,MAAc,CAAEK,MAAc,CAAoB,CAChE,MAAMI,SAAW,MAAM,IAAI,CAACD,OAAO,CAAC,CAChCN,MAAO,CAAEF,OAAQK,MAAO,CAC5B,GACA,MAAO,CAAC,CAACI,QACb,CAlDAO,YAAY,AAAQC,UAAsB,CAAE,CACxC,KAAK,CAACC,gCAAe,CAAED,WAAWE,mBAAmB,SADrCF,WAAAA,UAEpB,CAiDJ"}