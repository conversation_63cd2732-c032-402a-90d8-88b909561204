/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { DeXuatKhenThuongDangVienEntity } from '~/database/typeorm/entities/deXuatKhenThuongDangVien.entity';

@Injectable()
export class DeXuatKhenThuongDangVienRepository extends Repository<DeXuatKhenThuongDangVienEntity> {
    constructor(private dataSource: DataSource) {
        super(DeXuatKhenThuongDangVienEntity, dataSource.createEntityManager());
    }
}
