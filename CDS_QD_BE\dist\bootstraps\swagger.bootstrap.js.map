{"version": 3, "sources": ["../../src/bootstraps/swagger.bootstrap.ts"], "sourcesContent": ["import { INestApplication } from '@nestjs/common';\r\nimport { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';\r\nimport { writeFileSync } from 'node:fs';\r\n\r\nexport function bootstrapSwagger(app: INestApplication, appVersion: string): void {\r\n    if (process.env.NODE_ENV === 'development') {\r\n        const config = new DocumentBuilder()\r\n            .setTitle(`${process.env.APP_NAME} Swagger`)\r\n            .setDescription(`The ${process.env.APP_NAME} API documents`)\r\n            .setVersion(appVersion)\r\n            .addBearerAuth()\r\n            .setLicense('CDS-QD.dev', 'https://cdsqd.dev')\r\n            .build();\r\n        const document = SwaggerModule.createDocument(app, config);\r\n        SwaggerModule.setup('docs', app, document, {\r\n            swaggerOptions: {\r\n                tagsSorter: 'alpha',\r\n                // operationsSorter: 'alpha',\r\n                persistAuthorization: true,\r\n                docExpansion: 'none',\r\n            },\r\n        });\r\n\r\n        writeFileSync('./swagger-spec.json', JSON.stringify(document, null, 2));\r\n    }\r\n}\r\n"], "names": ["bootstrapSwagger", "app", "appVersion", "process", "env", "NODE_ENV", "config", "DocumentBuilder", "setTitle", "APP_NAME", "setDescription", "setVersion", "addBearerAuth", "setLicense", "build", "document", "SwaggerModule", "createDocument", "setup", "swaggerOptions", "<PERSON><PERSON><PERSON><PERSON>", "persistAuthorization", "docExpansion", "writeFileSync", "JSON", "stringify"], "mappings": "oGAIgBA,0DAAAA,2CAH+B,yCACjB,WAEvB,SAASA,iBAAiBC,GAAqB,CAAEC,UAAkB,EACtE,GAAIC,QAAQC,GAAG,CAACC,QAAQ,GAAK,cAAe,CACxC,MAAMC,OAAS,IAAIC,wBAAe,GAC7BC,QAAQ,CAAC,CAAC,EAAEL,QAAQC,GAAG,CAACK,QAAQ,CAAC,QAAQ,CAAC,EAC1CC,cAAc,CAAC,CAAC,IAAI,EAAEP,QAAQC,GAAG,CAACK,QAAQ,CAAC,cAAc,CAAC,EAC1DE,UAAU,CAACT,YACXU,aAAa,GACbC,UAAU,CAAC,aAAc,qBACzBC,KAAK,GACV,MAAMC,SAAWC,sBAAa,CAACC,cAAc,CAAChB,IAAKK,QACnDU,sBAAa,CAACE,KAAK,CAAC,OAAQjB,IAAKc,SAAU,CACvCI,eAAgB,CACZC,WAAY,QAEZC,qBAAsB,KACtBC,aAAc,MAClB,CACJ,GAEAC,GAAAA,qBAAa,EAAC,sBAAuBC,KAAKC,SAAS,CAACV,SAAU,KAAM,GACxE,CACJ"}