import { useUserControllerCreate } from '@/api/generated/user/user';
import type { CreateUserDto } from '@/api/generated/model';

export function useCreateUser() {
  const { mutate, isLoading, error, isSuccess } = useUserControllerCreate();

  const createUser = (userData: CreateUserDto) => {
    return mutate({ data: userData });
  };

  return {
    createUser,
    isLoading,
    error,
    isSuccess
  };
}
