// src/api/index.ts
import axios from 'axios'

import { authInterceptor } from './interceptors/auth.interceptor'
import { errorInterceptor } from './interceptors/error.interceptor'

const apiClient = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// Thêm interceptors
apiClient.interceptors.request.use(authInterceptor)
apiClient.interceptors.response.use(
  response => response.data, // Trả về data trực tiếp
  errorInterceptor
)

export default apiClient
