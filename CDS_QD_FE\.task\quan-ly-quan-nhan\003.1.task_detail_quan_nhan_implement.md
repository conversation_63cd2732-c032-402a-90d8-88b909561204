Quy trình xây dựng giao diện trang "Xem chi tiết H<PERSON> sơ Quân nhân"
I. Tổng quan và Mục tiêu
Tên Page (Tiêu đề hiển thị): <PERSON> tiết <PERSON><PERSON> sơ Quân nhân: [Họ và tên Khai sinh Quân nhân]
Mục đích: Cung cấp một giao diện chi tiết, toàn diện về thông tin của một quân nhân. Giao diện cần đảm bả<PERSON> t<PERSON> r<PERSON>, <PERSON><PERSON>, <PERSON><PERSON> đ<PERSON> hư<PERSON>, cho ph<PERSON><PERSON> người dùng (c<PERSON> quyền) xem, và thực hiện các thao tác cơ bản liên quan đến hồ sơ.
Layout tham khảo từ Vuexy: Sử dụng cấu trúc layout chung của Vuexy, tương tự các trang chi tiết có sử dụng Card và Tabs để tổ chức thông tin (ví dụ: trang User Profile hoặc Account Settings trong template Vuexy).
II. <PERSON><PERSON><PERSON> trú<PERSON> thư mục và Quy chuẩn đặt tên (Duy trì như thống nhất)
Cấu trúc thư mục:

Trang chính (Route Handler): src\app\[lang]\(dashboard)\quan-ly-quan-nhan\chi-tiet\[idQuanNhan]\page.tsx
Component chính của trang: src\views\quan-ly-quan-nhan\chi-tiet\ChiTietQuanNhanPage.tsx
Components con cho từng phần lớn:
src\views\quan-ly-quan-nhan\chi-tiet\components\BreadcrumbsChiTietQN.tsx
src\views\quan-ly-quan-nhan\chi-tiet\components\HeaderThongTinTongQuan.tsx
src\views\quan-ly-quan-nhan\chi-tiet\components\KhuVucTabsChiTiet.tsx
Components con cho từng Tab Panel (đặt trong thư mục con của components hoặc tabs):
src\views\quan-ly-quan-nhan\chi-tiet\components\tabs\TabThongTinChung.tsx
src\views\quan-ly-quan-nhan\chi-tiet\components\tabs\TabLyLichCaNhan.tsx
... (các tab khác)
Types/Interfaces: src\types\apps\quanLyQuanNhanTypes.ts (sử dụng chung hoặc mở rộng) hoặc src\views\quan-ly-quan-nhan\chi-tiet\typesChiTiet.ts.
Dữ liệu mẫu: src\views\quan-ly-quan-nhan\chi-tiet\mockDataChiTiet.ts.
Quy chuẩn đặt tên: (Giữ nguyên như đã thống nhất)

Component: PascalCase (ví dụ: ChiTietQuanNhanPage, HeaderThongTinTongQuan).
File: Trùng tên component.
Biến, hàm: camelCase.
Hằng số: UPPER_SNAKE_CASE.
III. Các bước thực hiện chi tiết (Prototype)
Bước 1: Tạo thư mục và cấu trúc file cơ bản
Thư mục cho trang chi tiết:

Tạo src\views\quan-ly-quan-nhan\chi-tiet\
Tạo src\views\quan-ly-quan-nhan\chi-tiet\components\
Tạo src\views\quan-ly-quan-nhan\chi-tiet\components\tabs\
File cho trang chính (Next.js Page Route):

Tạo src\app\[lang]\(dashboard)\quan-ly-quan-nhan\chi-tiet\[idQuanNhan]\page.tsx.
File cho component chính của trang:

Tạo src\views\quan-ly-quan-nhan\chi-tiet\ChiTietQuanNhanPage.tsx.
File dữ liệu mẫu:

Tạo src\views\quan-ly-quan-nhan\chi-tiet\mockDataChiTiet.ts.
Định nghĩa các type hoặc interface cần thiết: QuanNhanDetailType, QuanNhanBaseType (thông tin cơ bản), LyLichCaNhanType, QuaTrinhCongTacEntryType, QuaTrinhDaoTaoEntryType, KhenThuongEntryType, KyLuatEntryType, ThongTinSucKhoeEntryType, ThanNhanEntryType, CheDoChinhSachEntryType.
TypeScript

// src\views\quan-ly-quan-nhan\chi-tiet\mockDataChiTiet.ts (Sơ bộ)
// (Đầy đủ các type sẽ được hoàn thiện dần)
export type QuanNhanStatusChiTiet = 'active' | 'inactive' | 'leave' | 'deceased' | 'pending'; // Mở rộng nếu cần

export const quanNhanStatusChiTietObj: Record<
QuanNhanStatusChiTiet,
{ color: 'success' | 'secondary' | 'warning' | 'error' | 'info' | 'primary' | 'default'; label: string }

> = {
> active: { color: 'success', label: 'Đang công tác' },
> inactive: { color: 'secondary', label: 'Đã xuất ngũ' },
> leave: { color: 'warning', label: 'Nghỉ hưu' },
> deceased: { color: 'error', label: 'Đã mất/Hy sinh' },
> pending: { color: 'info', label: 'Chờ duyệt' } // Ví dụ
> };

export interface QuanNhanBaseType {
AnhChanDungURL?: string;
HoVaTenKhaiSinh: string;
SoHieuQuanNhan: string;
CapBacHienTaiID: string; // Sẽ map sang tên
ChucVuHienTaiID: string; // Sẽ map sang tên
DonViID: string; // Sẽ map sang tên
TrangThaiHoSo: QuanNhanStatusChiTiet;
// Các trường khác cho Tab Thông tin chung
TenThuongDung?: string;
NgaySinh: string; // ISO Date string
GioiTinh: 'Nam' | 'Nữ' | 'Khác';
SoCCCD_CMTQD?: string;
NgayNhapNgu: string; // ISO Date string
Email?: string;
SoDienThoai?: string;
NgayTaoHSCN?: string; // ISO Date string - Ngày tạo hồ sơ quân nhân
NguoiTaoHSCNID?: string; // ID người tạo
NgayCapNhatHSCN?: string; // ISO Date string - Ngày cập nhật hồ sơ quân nhân
NguoiCapNhatHSCNID?: string; // ID người cập nhật
}

// ... (Định nghĩa các interface khác: LyLichCaNhanType, etc.)

export interface QuanNhanDetailType {
baseInfo: QuanNhanBaseType;
lyLichCaNhan?: any; // LyLichCaNhanType;
quaTrinhCongTac?: any[]; // QuaTrinhCongTacEntryType[];
quaTrinhDaoTao?: any[]; // QuaTrinhDaoTaoEntryType[];
khenThuong?: any[]; // KhenThuongEntryType[];
kyLuat?: any[]; // KyLuatEntryType[];
sucKhoe?: any[]; // ThongTinSucKhoeEntryType[];
thanNhan?: any[]; // ThanNhanEntryType[];
cheDoChinhSach?: any[]; // CheDoChinhSachEntryType[];
// auditLog?: any[]; // AuditLogEntryType[];
}

export const mockQuanNhanChiTietData: QuanNhanDetailType = {
baseInfo: {
AnhChanDungURL: '/images/avatars/1.png', // Sử dụng ảnh mẫu từ Vuexy
HoVaTenKhaiSinh: 'Trần Văn Chiến Thắng',
SoHieuQuanNhan: 'QN123456',
CapBacHienTaiID: 'CB001', // Trung úy
ChucVuHienTaiID: 'CV002', // Phó Đại đội trưởng
DonViID: 'DV003', // Tiểu đoàn 1, Trung đoàn 2, Sư đoàn 3
TrangThaiHoSo: 'active',
TenThuongDung: 'Ba Thắng',
NgaySinh: '1992-08-15T00:00:00Z',
GioiTinh: 'Nam',
SoCCCD_CMTQD: '012345678910',
NgayNhapNgu: '2012-09-10T00:00:00Z',
Email: '<EMAIL>',
SoDienThoai: '**********',
NgayTaoHSCN: '2020-01-20T10:00:00Z',
NguoiTaoHSCNID: 'AdminUser01',
NgayCapNhatHSCN: '2024-05-10T15:30:00Z',
NguoiCapNhatHSCNID: 'ManagerUser02'
},
// Khởi tạo rỗng hoặc với dữ liệu mẫu cho các phần khác
lyLichCaNhan: {},
quaTrinhCongTac: [],
// ...
};
Bước 2: Xây dựng Component Chính ChiTietQuanNhanPage.tsx
Import cần thiết: React, useState, useEffect, useParams, useRouter (từ next/navigation), Grid, CircularProgress, Typography.

Import components con: BreadcrumbsChiTietQN, HeaderThongTinTongQuan, KhuVucTabsChiTiet.

Import types và mock data: QuanNhanDetailType, mockQuanNhanChiTietData.

State quản lý:

quanNhanData: QuanNhanDetailType | null = null
isLoading: boolean = true
error: string | null = null
activeTab: string = 'thong-tin-chung' (Giá trị tab mặc định)
Logic Fetch dữ liệu (trong useEffect):

Lấy idQuanNhan từ useParams().
Giả lập API call (sử dụng setTimeout và mockQuanNhanChiTietData).
Xử lý isLoading, quanNhanData, error.
Hàm xử lý:

handleTabChange(event: React.SyntheticEvent, newValue: string)
handleEditProfile() (điều hướng đến trang chỉnh sửa)
Các hàm callbacks cho "Hành động khác" (In, Thay đổi trạng thái, Xem lịch sử) - sẽ được truyền xuống HeaderThongTinTongQuan.
Cấu trúc JSX:

Hiển thị CircularProgress nếu isLoading.
Hiển thị lỗi nếu error.
Nếu có quanNhanData, render:
<BreadcrumbsChiTietQN hoVaTenKhaiSinh={quanNhanData.baseInfo.HoVaTenKhaiSinh} />
<HeaderThongTinTongQuan quanNhanBase={quanNhanData.baseInfo} onEdit={handleEditProfile} ... />
<KhuVucTabsChiTiet quanNhanData={quanNhanData} activeTab={activeTab} handleTabChange={handleTabChange} />
TypeScript

// src\views\quan-ly-quan-nhan\chi-tiet\ChiTietQuanNhanPage.tsx
'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';

import Grid from '@mui/material/Grid';
import CircularProgress from '@mui/material/CircularProgress';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box'; // Thêm Box để căn giữa loading

import BreadcrumbsChiTietQN from './components/BreadcrumbsChiTietQN';
import HeaderThongTinTongQuan from './components/HeaderThongTinTongQuan';
import KhuVucTabsChiTiet from './components/KhuVucTabsChiTiet';

import { QuanNhanDetailType, mockQuanNhanChiTietData } from './mockDataChiTiet';
// import { getLocalizedUrl } from '@/utils/i18n'; // Nếu có

const ChiTietQuanNhanPage = () => {
const params = useParams();
const router = useRouter();
const idQuanNhan = params.idQuanNhan as string;
const locale = params.lang || 'vi';

const [quanNhanData, setQuanNhanData] = useState<QuanNhanDetailType | null>(null);
const [isLoading, setIsLoading] = useState<boolean>(true);
const [error, setError] = useState<string | null>(null);
const [activeTab, setActiveTab] = useState<string>('thong-tin-chung');

useEffect(() => {
const fetchChiTietData = async (id: string) => {
setIsLoading(true);
setError(null);
try {
// Simulate API call
await new Promise(resolve => setTimeout(resolve, 1000));
// For prototype, use mock data. In real app, check if id matches or fetch by id.
if (id) { // Giả định ID luôn hợp lệ cho prototype
setQuanNhanData(mockQuanNhanChiTietData);
} else {
setError('ID Quân nhân không hợp lệ.');
}
} catch (e) {
console.error(e);
setError('Lỗi tải dữ liệu chi tiết quân nhân.');
} finally {
setIsLoading(false);
}
};

    if (idQuanNhan) {
      fetchChiTietData(idQuanNhan);
    } else {
      setError('Không tìm thấy ID Quân nhân.');
      setIsLoading(false);
    }

}, [idQuanNhan]);

const handleTabChange = (event: React.SyntheticEvent, newValue: string) => {
setActiveTab(newValue);
};

const handleEditProfile = () => {
// const editUrl = getLocalizedUrl(`<span class="math-inline">\{locale\}/quan\-ly\-quan\-nhan/chinh\-sua/</span>{idQuanNhan}`);
router.push(`/<span class="math-inline">\{locale\}/quan\-ly\-quan\-nhan/chinh\-sua/</span>{idQuanNhan}`);
};

const handlePrintProfile = () => { console.log('Action: Print Profile for QN:', idQuanNhan); /_ Logic in Hồ sơ _/ };
const handleChangeStatus = () => { console.log('Action: Change Status for QN:', idQuanNhan); /_ Mở Dialog thay đổi trạng thái _/ };
const handleViewHistory = () => { console.log('Action: View History for QN:', idQuanNhan); /_ Điều hướng/Mở tab Lịch sử thay đổi _/ setActiveTab('lich-su-thay-doi'); };

if (isLoading) {
return (
<Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 'calc(100vh - 200px)' }}>
<CircularProgress />
</Box>
);
}

if (error) {
return <Typography color="error" textAlign="center" variant="h6">{error}</Typography>;
}

if (!quanNhanData) {
return <Typography textAlign="center" variant="h6">Không có dữ liệu quân nhân để hiển thị.</Typography>;
}

return (
<Grid container spacing={3}> {/_ Vuexy thường dùng spacing={6} _/}
<Grid item xs={12}>
<BreadcrumbsChiTietQN hoVaTenKhaiSinh={quanNhanData.baseInfo.HoVaTenKhaiSinh} />
</Grid>
<Grid item xs={12}>
<HeaderThongTinTongQuan
          quanNhanBase={quanNhanData.baseInfo}
          onEdit={handleEditProfile}
          onPrintProfile={handlePrintProfile}
          onChangeStatus={handleChangeStatus}
          onViewHistory={handleViewHistory}
        />
</Grid>
<Grid item xs={12}>
<KhuVucTabsChiTiet
          quanNhanData={quanNhanData}
          activeTab={activeTab}
          handleTabChange={handleTabChange}
        />
</Grid>
</Grid>
);
};

export default ChiTietQuanNhanPage;

Tuyệt vời! Với vai trò là Tech Lead, tôi sẽ tiếp tục hướng dẫn bạn xây dựng prototype cho trang "Xem chi tiết Hồ sơ Quân nhân", bám sát mô tả và các component Vuexy đã thống nhất.

Dưới đây là nội dung chi tiết từ đầu cho đến trước khi bắt đầu mô tả Tab 1: "Thông tin Chung":

Quy trình xây dựng giao diện trang "Xem chi tiết Hồ sơ Quân nhân"
I. Tổng quan và Mục tiêu
Tên Page (Tiêu đề hiển thị): Chi tiết Hồ sơ Quân nhân: [Họ và tên Khai sinh Quân nhân]
Mục đích: Cung cấp một giao diện chi tiết, toàn diện về thông tin của một quân nhân. Giao diện cần đảm bảo tính rõ ràng, dễ đọc, dễ điều hướng, cho phép người dùng (có quyền) xem, và thực hiện các thao tác cơ bản liên quan đến hồ sơ.
Layout tham khảo từ Vuexy: Sử dụng cấu trúc layout chung của Vuexy, tương tự các trang chi tiết có sử dụng Card và Tabs để tổ chức thông tin (ví dụ: trang User Profile hoặc Account Settings trong template Vuexy).
II. Cấu trúc thư mục và Quy chuẩn đặt tên (Duy trì như thống nhất)
Cấu trúc thư mục:

Trang chính (Route Handler): src\app\[lang]\(dashboard)\quan-ly-quan-nhan\chi-tiet\[idQuanNhan]\page.tsx
Component chính của trang: src\views\quan-ly-quan-nhan\chi-tiet\ChiTietQuanNhanPage.tsx
Components con cho từng phần lớn:
src\views\quan-ly-quan-nhan\chi-tiet\components\BreadcrumbsChiTietQN.tsx
src\views\quan-ly-quan-nhan\chi-tiet\components\HeaderThongTinTongQuan.tsx
src\views\quan-ly-quan-nhan\chi-tiet\components\KhuVucTabsChiTiet.tsx
Components con cho từng Tab Panel (đặt trong thư mục con của components hoặc tabs):
src\views\quan-ly-quan-nhan\chi-tiet\components\tabs\TabThongTinChung.tsx
src\views\quan-ly-quan-nhan\chi-tiet\components\tabs\TabLyLichCaNhan.tsx
... (các tab khác)
Types/Interfaces: src\types\apps\quanLyQuanNhanTypes.ts (sử dụng chung hoặc mở rộng) hoặc src\views\quan-ly-quan-nhan\chi-tiet\typesChiTiet.ts.
Dữ liệu mẫu: src\views\quan-ly-quan-nhan\chi-tiet\mockDataChiTiet.ts.
Quy chuẩn đặt tên: (Giữ nguyên như đã thống nhất)

Component: PascalCase (ví dụ: ChiTietQuanNhanPage, HeaderThongTinTongQuan).
File: Trùng tên component.
Biến, hàm: camelCase.
Hằng số: UPPER_SNAKE_CASE.
III. Các bước thực hiện chi tiết (Prototype)
Bước 1: Tạo thư mục và cấu trúc file cơ bản
Thư mục cho trang chi tiết:

Tạo src\views\quan-ly-quan-nhan\chi-tiet\
Tạo src\views\quan-ly-quan-nhan\chi-tiet\components\
Tạo src\views\quan-ly-quan-nhan\chi-tiet\components\tabs\
File cho trang chính (Next.js Page Route):

Tạo src\app\[lang]\(dashboard)\quan-ly-quan-nhan\chi-tiet\[idQuanNhan]\page.tsx.
File cho component chính của trang:

Tạo src\views\quan-ly-quan-nhan\chi-tiet\ChiTietQuanNhanPage.tsx.
File dữ liệu mẫu:

Tạo src\views\quan-ly-quan-nhan\chi-tiet\mockDataChiTiet.ts.
Định nghĩa các type hoặc interface cần thiết: QuanNhanDetailType, QuanNhanBaseType (thông tin cơ bản), LyLichCaNhanType, QuaTrinhCongTacEntryType, QuaTrinhDaoTaoEntryType, KhenThuongEntryType, KyLuatEntryType, ThongTinSucKhoeEntryType, ThanNhanEntryType, CheDoChinhSachEntryType.
TypeScript

// src\views\quan-ly-quan-nhan\chi-tiet\mockDataChiTiet.ts (Sơ bộ)
// (Đầy đủ các type sẽ được hoàn thiện dần)
export type QuanNhanStatusChiTiet = 'active' | 'inactive' | 'leave' | 'deceased' | 'pending'; // Mở rộng nếu cần

export const quanNhanStatusChiTietObj: Record<
QuanNhanStatusChiTiet,
{ color: 'success' | 'secondary' | 'warning' | 'error' | 'info' | 'primary' | 'default'; label: string }

> = {
> active: { color: 'success', label: 'Đang công tác' },
> inactive: { color: 'secondary', label: 'Đã xuất ngũ' },
> leave: { color: 'warning', label: 'Nghỉ hưu' },
> deceased: { color: 'error', label: 'Đã mất/Hy sinh' },
> pending: { color: 'info', label: 'Chờ duyệt' } // Ví dụ
> };

export interface QuanNhanBaseType {
AnhChanDungURL?: string;
HoVaTenKhaiSinh: string;
SoHieuQuanNhan: string;
CapBacHienTaiID: string; // Sẽ map sang tên
ChucVuHienTaiID: string; // Sẽ map sang tên
DonViID: string; // Sẽ map sang tên
TrangThaiHoSo: QuanNhanStatusChiTiet;
// Các trường khác cho Tab Thông tin chung
TenThuongDung?: string;
NgaySinh: string; // ISO Date string
GioiTinh: 'Nam' | 'Nữ' | 'Khác';
SoCCCD_CMTQD?: string;
NgayNhapNgu: string; // ISO Date string
Email?: string;
SoDienThoai?: string;
NgayTaoHSCN?: string; // ISO Date string - Ngày tạo hồ sơ quân nhân
NguoiTaoHSCNID?: string; // ID người tạo
NgayCapNhatHSCN?: string; // ISO Date string - Ngày cập nhật hồ sơ quân nhân
NguoiCapNhatHSCNID?: string; // ID người cập nhật
}

// ... (Định nghĩa các interface khác: LyLichCaNhanType, etc.)

export interface QuanNhanDetailType {
baseInfo: QuanNhanBaseType;
lyLichCaNhan?: any; // LyLichCaNhanType;
quaTrinhCongTac?: any[]; // QuaTrinhCongTacEntryType[];
quaTrinhDaoTao?: any[]; // QuaTrinhDaoTaoEntryType[];
khenThuong?: any[]; // KhenThuongEntryType[];
kyLuat?: any[]; // KyLuatEntryType[];
sucKhoe?: any[]; // ThongTinSucKhoeEntryType[];
thanNhan?: any[]; // ThanNhanEntryType[];
cheDoChinhSach?: any[]; // CheDoChinhSachEntryType[];
// auditLog?: any[]; // AuditLogEntryType[];
}

export const mockQuanNhanChiTietData: QuanNhanDetailType = {
baseInfo: {
AnhChanDungURL: '/images/avatars/1.png', // Sử dụng ảnh mẫu từ Vuexy
HoVaTenKhaiSinh: 'Trần Văn Chiến Thắng',
SoHieuQuanNhan: 'QN123456',
CapBacHienTaiID: 'CB001', // Trung úy
ChucVuHienTaiID: 'CV002', // Phó Đại đội trưởng
DonViID: 'DV003', // Tiểu đoàn 1, Trung đoàn 2, Sư đoàn 3
TrangThaiHoSo: 'active',
TenThuongDung: 'Ba Thắng',
NgaySinh: '1992-08-15T00:00:00Z',
GioiTinh: 'Nam',
SoCCCD_CMTQD: '012345678910',
NgayNhapNgu: '2012-09-10T00:00:00Z',
Email: '<EMAIL>',
SoDienThoai: '**********',
NgayTaoHSCN: '2020-01-20T10:00:00Z',
NguoiTaoHSCNID: 'AdminUser01',
NgayCapNhatHSCN: '2024-05-10T15:30:00Z',
NguoiCapNhatHSCNID: 'ManagerUser02'
},
// Khởi tạo rỗng hoặc với dữ liệu mẫu cho các phần khác
lyLichCaNhan: {},
quaTrinhCongTac: [],
// ...
};
Bước 2: Xây dựng Component Chính ChiTietQuanNhanPage.tsx
Import cần thiết: React, useState, useEffect, useParams, useRouter (từ next/navigation), Grid, CircularProgress, Typography.

Import components con: BreadcrumbsChiTietQN, HeaderThongTinTongQuan, KhuVucTabsChiTiet.

Import types và mock data: QuanNhanDetailType, mockQuanNhanChiTietData.

State quản lý:

quanNhanData: QuanNhanDetailType | null = null
isLoading: boolean = true
error: string | null = null
activeTab: string = 'thong-tin-chung' (Giá trị tab mặc định)
Logic Fetch dữ liệu (trong useEffect):

Lấy idQuanNhan từ useParams().
Giả lập API call (sử dụng setTimeout và mockQuanNhanChiTietData).
Xử lý isLoading, quanNhanData, error.
Hàm xử lý:

handleTabChange(event: React.SyntheticEvent, newValue: string)
handleEditProfile() (điều hướng đến trang chỉnh sửa)
Các hàm callbacks cho "Hành động khác" (In, Thay đổi trạng thái, Xem lịch sử) - sẽ được truyền xuống HeaderThongTinTongQuan.
Cấu trúc JSX:

Hiển thị CircularProgress nếu isLoading.
Hiển thị lỗi nếu error.
Nếu có quanNhanData, render:
<BreadcrumbsChiTietQN hoVaTenKhaiSinh={quanNhanData.baseInfo.HoVaTenKhaiSinh} />
<HeaderThongTinTongQuan quanNhanBase={quanNhanData.baseInfo} onEdit={handleEditProfile} ... />
<KhuVucTabsChiTiet quanNhanData={quanNhanData} activeTab={activeTab} handleTabChange={handleTabChange} />
TypeScript

// src\views\quan-ly-quan-nhan\chi-tiet\ChiTietQuanNhanPage.tsx
'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';

import Grid from '@mui/material/Grid';
import CircularProgress from '@mui/material/CircularProgress';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box'; // Thêm Box để căn giữa loading

import BreadcrumbsChiTietQN from './components/BreadcrumbsChiTietQN';
import HeaderThongTinTongQuan from './components/HeaderThongTinTongQuan';
import KhuVucTabsChiTiet from './components/KhuVucTabsChiTiet';

import { QuanNhanDetailType, mockQuanNhanChiTietData } from './mockDataChiTiet';
// import { getLocalizedUrl } from '@/utils/i18n'; // Nếu có

const ChiTietQuanNhanPage = () => {
const params = useParams();
const router = useRouter();
const idQuanNhan = params.idQuanNhan as string;
const locale = params.lang || 'vi';

const [quanNhanData, setQuanNhanData] = useState<QuanNhanDetailType | null>(null);
const [isLoading, setIsLoading] = useState<boolean>(true);
const [error, setError] = useState<string | null>(null);
const [activeTab, setActiveTab] = useState<string>('thong-tin-chung');

useEffect(() => {
const fetchChiTietData = async (id: string) => {
setIsLoading(true);
setError(null);
try {
// Simulate API call
await new Promise(resolve => setTimeout(resolve, 1000));
// For prototype, use mock data. In real app, check if id matches or fetch by id.
if (id) { // Giả định ID luôn hợp lệ cho prototype
setQuanNhanData(mockQuanNhanChiTietData);
} else {
setError('ID Quân nhân không hợp lệ.');
}
} catch (e) {
console.error(e);
setError('Lỗi tải dữ liệu chi tiết quân nhân.');
} finally {
setIsLoading(false);
}
};

    if (idQuanNhan) {
      fetchChiTietData(idQuanNhan);
    } else {
      setError('Không tìm thấy ID Quân nhân.');
      setIsLoading(false);
    }

}, [idQuanNhan]);

const handleTabChange = (event: React.SyntheticEvent, newValue: string) => {
setActiveTab(newValue);
};

const handleEditProfile = () => {
// const editUrl = getLocalizedUrl(`<span class="math-inline">\{locale\}/quan\-ly\-quan\-nhan/chinh\-sua/</span>{idQuanNhan}`);
router.push(`/<span class="math-inline">\{locale\}/quan\-ly\-quan\-nhan/chinh\-sua/</span>{idQuanNhan}`);
};

const handlePrintProfile = () => { console.log('Action: Print Profile for QN:', idQuanNhan); /_ Logic in Hồ sơ _/ };
const handleChangeStatus = () => { console.log('Action: Change Status for QN:', idQuanNhan); /_ Mở Dialog thay đổi trạng thái _/ };
const handleViewHistory = () => { console.log('Action: View History for QN:', idQuanNhan); /_ Điều hướng/Mở tab Lịch sử thay đổi _/ setActiveTab('lich-su-thay-doi'); };

if (isLoading) {
return (
<Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 'calc(100vh - 200px)' }}>
<CircularProgress />
</Box>
);
}

if (error) {
return <Typography color="error" textAlign="center" variant="h6">{error}</Typography>;
}

if (!quanNhanData) {
return <Typography textAlign="center" variant="h6">Không có dữ liệu quân nhân để hiển thị.</Typography>;
}

return (
<Grid container spacing={3}> {/_ Vuexy thường dùng spacing={6} _/}
<Grid item xs={12}>
<BreadcrumbsChiTietQN hoVaTenKhaiSinh={quanNhanData.baseInfo.HoVaTenKhaiSinh} />
</Grid>
<Grid item xs={12}>
<HeaderThongTinTongQuan
          quanNhanBase={quanNhanData.baseInfo}
          onEdit={handleEditProfile}
          onPrintProfile={handlePrintProfile}
          onChangeStatus={handleChangeStatus}
          onViewHistory={handleViewHistory}
        />
</Grid>
<Grid item xs={12}>
<KhuVucTabsChiTiet
          quanNhanData={quanNhanData}
          activeTab={activeTab}
          handleTabChange={handleTabChange}
        />
</Grid>
</Grid>
);
};

export default ChiTietQuanNhanPage;
Bước 3: Tạo Component BreadcrumbsChiTietQN.tsx
Vị trí file: src\views\quan-ly-quan-nhan\chi-tiet\components\BreadcrumbsChiTietQN.tsx

Props: hoVaTenKhaiSinh: string

Component Vuexy (MUI): Breadcrumbs, Link (từ @mui/material hoặc next/link), Typography.

Nội dung: Trang chủ / Quản lý Quân nhân / Danh sách Quân nhân / Chi tiết: [Họ và tên Khai sinh Quân nhân]

TypeScript

// src\views\quan-ly-quan-nhan\chi-tiet\components\BreadcrumbsChiTietQN.tsx
'use client';

import React from 'react';
import { useParams } from 'next/navigation';
import NextLink from 'next/link';

import MUIBreadcrumbs from '@mui/material/Breadcrumbs';
import Link from '@mui/material/Link';
import Typography from '@mui/material/Typography';
// import NavigateNextIcon from '@mui/icons-material/NavigateNext'; // Vuexy thường dùng icon này

// import { getLocalizedUrl } from '@/utils/i18n'; // Nếu có

interface BreadcrumbsProps {
hoVaTenKhaiSinh: string;
}

const BreadcrumbsChiTietQN = ({ hoVaTenKhaiSinh }: BreadcrumbsProps) => {
const params = useParams();
const locale = params.lang || 'vi';

const getPath = (path: string) => `/<span class="math-inline">\{locale\}</span>{path}`;

return (
// Vuexy có thể đã có sẵn 1 component Breadcrumb tùy chỉnh, nếu không thì dùng MUI
<MUIBreadcrumbs aria-label="breadcrumb" /_separator={<NavigateNextIcon fontSize="small" />}_/>
<NextLink href={getPath('/dashboard')} passHref legacyBehavior>
<Link underline="hover" color="inherit">
Trang chủ
</Link>
</NextLink>
<NextLink href={getPath('/quan-ly-quan-nhan/danh-sach')} passHref legacyBehavior>
<Link underline="hover" color="inherit">
Quản lý Quân nhân
</Link>
</NextLink>
{/_ <NextLink href={getPath('/quan-ly-quan-nhan/danh-sach')} passHref legacyBehavior>
<Link underline="hover" color="inherit">
Danh sách Quân nhân
</Link>
</NextLink> _/}
<Typography color="text.primary">Chi tiết: {hoVaTenKhaiSinh}</Typography>
</MUIBreadcrumbs>
);
};

export default BreadcrumbsChiTietQN;
Bước 4: Tạo Component HeaderThongTinTongQuan.tsx
Vị trí file: src\views\quan-ly-quan-nhan\chi-tiet\components\HeaderThongTinTongQuan.tsx

Props:

quanNhanBase: QuanNhanBaseType
onEdit: () => void
onPrintProfile: () => void
onChangeStatus: () => void
onViewHistory: () => void
Component Vuexy (MUI): Card, CardContent, Grid, Avatar, Typography, Button, IconButton, Menu, MenuItem, CustomChip (từ @core/components/mui/chip của Vuexy hoặc tương tự).

Icons: tabler-edit, tabler-dots-vertical, tabler-printer, tabler-switch-horizontal, tabler-history (sử dụng thư viện tabler-icons-react).
Cấu trúc JSX:

Card bao ngoài.
CardContent chứa Grid container.
Grid item cho Avatar (căn giữa, kích thước lớn).
Grid item cho khối thông tin text (Họ tên, Số hiệu, Cấp bậc, Chức vụ, Đơn vị).
CustomChip cho Trạng thái Hồ sơ.
Grid item (hoặc Box căn phải/flex) cho các nút hành động.
TypeScript

// src\views\quan-ly-quan-nhan\chi-tiet\components\HeaderThongTinTongQuan.tsx
'use client';

import React, { useState } from 'react';

import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Grid from '@mui/material/Grid';
import Avatar from '@mui/material/Avatar';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import IconButton from '@mui/material/IconButton';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import Box from '@mui/material/Box';

// Custom Chip từ Vuexy
import CustomChip from '@core/components/mui/chip';

// Icons
import IconEdit from '@tabler/icons-react/dist/esm/icons/IconEdit';
import IconDotsVertical from '@tabler/icons-react/dist/esm/icons/IconDotsVertical';
import IconPrinter from '@tabler/icons-react/dist/esm/icons/IconPrinter';
import IconSwitchHorizontal from '@tabler/icons-react/dist/esm/icons/IconSwitchHorizontal';
import IconHistory from '@tabler/icons-react/dist/esm/icons/IconHistory';

import { QuanNhanBaseType, quanNhanStatusChiTietObj } from '../mockDataChiTiet';

interface HeaderProps {
quanNhanBase: QuanNhanBaseType;
onEdit: () => void;
onPrintProfile: () => void;
onChangeStatus: () => void;
onViewHistory: () => void;
}

// Hàm giả định map ID sang tên - Sẽ cần logic thực tế hoặc dữ liệu đầy đủ hơn
const mapIdToName = (id: string, type: 'capBac' | 'chucVu' | 'donVi') => {
// Trong thực tế, bạn sẽ có danh mục hoặc API để lấy tên
if (type === 'capBac') return `Cấp bậc ${id.replace('CB','').padStart(3,'0')}`;
if (type === 'chucVu') return `Chức vụ ${id.replace('CV','').padStart(3,'0')}`;
if (type === 'donVi') return `Đơn vị ${id.replace('DV','').padStart(3,'0')}`;
return id;
};

const HeaderThongTinTongQuan = ({
quanNhanBase,
onEdit,
onPrintProfile,
onChangeStatus,
onViewHistory
}: HeaderProps) => {
const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
const openMenu = Boolean(anchorEl);

const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => setAnchorEl(event.currentTarget);
const handleMenuClose = () => setAnchorEl(null);

const statusDisplay = quanNhanStatusChiTietObj[quanNhanBase.TrangThaiHoSo] || {
label: quanNhanBase.TrangThaiHoSo,
color: 'default'
};

return (
<Card>
<CardContent>
<Grid container spacing={3} alignItems="center">
<Grid item xs={12} sm={3} md={2} sx={{ display: 'flex', justifyContent: 'center' }}>
<Avatar
src={quanNhanBase.AnhChanDungURL || '/images/avatars/default-avatar.png'}
alt={quanNhanBase.HoVaTenKhaiSinh}
sx={{ width: 120, height: 120, border: theme => `3px solid ${theme.palette.primary.main}` }}
/>
</Grid>

          <Grid item xs={12} sm={9} md={6}>
            <Typography variant="h4" sx={{ mb: 1 }}>{quanNhanBase.HoVaTenKhaiSinh}</Typography>
            <Typography variant="body1">Số hiệu QN: {quanNhanBase.SoHieuQuanNhan}</Typography>
            <Typography variant="body1">Cấp bậc: {mapIdToName(quanNhanBase.CapBacHienTaiID, 'capBac')}</Typography>
            <Typography variant="body1">Chức vụ: {mapIdToName(quanNhanBase.ChucVuHienTaiID, 'chucVu')}</Typography>
            <Typography variant="body1" sx={{ mb: 1 }}>Đơn vị: {mapIdToName(quanNhanBase.DonViID, 'donVi')}</Typography>
            <CustomChip
              label={statusDisplay.label}
              color={statusDisplay.color}
              skin="light"
              size="small"
              rounded // Thêm rounded nếu Vuexy style yêu cầu
            />
          </Grid>

          <Grid item xs={12} md={4} sx={{ display: 'flex', flexDirection: { xs: 'row', md: 'column' }, alignItems: 'center', justifyContent: { xs: 'space-between', md: 'flex-start' }, gap: 2, mt: { xs: 2, md: 0 } }}>
            <Button
              variant="contained"
              color="primary"
              startIcon={<IconEdit />}
              onClick={onEdit}
              sx={{ width: {xs: 'auto', md: '100%'} }}
            >
              Chỉnh sửa Hồ sơ
            </Button>
            <Box>
              <IconButton
                aria-label="Hành động khác"
                onClick={handleMenuOpen}
              >
                <IconDotsVertical />
              </IconButton>
              <Menu
                anchorEl={anchorEl}
                open={openMenu}
                onClose={handleMenuClose}
                anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
                transformOrigin={{ vertical: 'top', horizontal: 'right' }}
              >
                <MenuItem onClick={() => { handleMenuClose(); onPrintProfile(); }}>
                  <IconPrinter size={18} style={{ marginRight: 8 }} /> In Hồ sơ 02a/SQN
                </MenuItem>
                <MenuItem onClick={() => { handleMenuClose(); onChangeStatus(); }}>
                  <IconSwitchHorizontal size={18} style={{ marginRight: 8 }} /> Thay đổi Trạng thái
                </MenuItem>
                <MenuItem onClick={() => { handleMenuClose(); onViewHistory(); }}>
                  <IconHistory size={18} style={{ marginRight: 8 }} /> Xem Lịch sử Thay đổi
                </MenuItem>
              </Menu>
            </Box>
          </Grid>
        </Grid>
      </CardContent>
    </Card>

);
};

export default HeaderThongTinTongQuan;

Bước 5: Tạo Component KhuVucTabsChiTiet.tsx (Cấu trúc và TabList)
Vị trí file: src\views\quan-ly-quan-nhan\chi-tiet\components\KhuVucTabsChiTiet.tsx

Props:

quanNhanData: QuanNhanDetailType
activeTab: string
handleTabChange: (event: React.SyntheticEvent, newValue: string) => void
Component Vuexy (MUI): TabContext, TabList, TabPanel (từ @mui/lab), Tab, Box.

Cấu trúc JSX ban đầu:

TabContext bao ngoài.
Box chứa TabList.
TabList với variant="scrollable" và scrollButtons="auto" để hỗ trợ nhiều tab.
Các Tab component với label và value tương ứng:
"Thông tin Chung" (value: thong-tin-chung)
"Lý lịch Cá nhân" (value: ly-lich-ca-nhan)
"Quá trình Công tác" (value: qua-trinh-cong-tac)
"Quá trình Đào tạo" (value: qua-trinh-dao-tao)
"Khen thưởng" (value: khen-thuong)
"Kỷ luật" (value: ky-luat)
"Sức khỏe" (value: suc-khoe)
"Quan hệ Gia đình" (value: quan-he-gia-dinh)
"Chế độ Chính sách" (value: che-do-chinh-sach)
"(Tùy chọn) Lịch sử Thay đổi Hồ sơ" (value: lich-su-thay-doi)
Các TabPanel sẽ được import và render tương ứng (ban đầu có thể là placeholder).
TypeScript

// src\views\quan-ly-quan-nhan\chi-tiet\components\KhuVucTabsChiTiet.tsx
'use client';

import React from 'react';
import Box from '@mui/material/Box';
import Tab from '@mui/material/Tab';
import { TabContext, TabList, TabPanel } from '@mui/lab';
import CardContent from '@mui/material/CardContent'; // Vuexy thường có CardContent bao quanh TabPanel

import { QuanNhanDetailType } from '../mockDataChiTiet';

// Placeholder cho các TabPanel components (sẽ được tạo sau)
// import TabThongTinChung from './tabs/TabThongTinChung';
// ...

interface TabsProps {
quanNhanData: QuanNhanDetailType;
activeTab: string;
handleTabChange: (event: React.SyntheticEvent, newValue: string) => void;
}

const KhuVucTabsChiTiet = ({ quanNhanData, activeTab, handleTabChange }: TabsProps) => {
const tabContentList: { [key: string]: React.ReactNode } = {
'thong-tin-chung': <div>{/_ <TabThongTinChung data={quanNhanData.baseInfo} /> _/} Nội dung Tab Thông tin Chung</div>,
'ly-lich-ca-nhan': <div>{/_ <TabLyLichCaNhan data={quanNhanData.lyLichCaNhan} /> _/} Nội dung Tab Lý lịch Cá nhân</div>,
'qua-trinh-cong-tac': <div>{/_ <TabQuaTrinhCongTac data={quanNhanData.quaTrinhCongTac} /> _/} Nội dung Tab Quá trình Công tác</div>,
'qua-trinh-dao-tao': <div>{/_ <TabQuaTrinhDaoTao data={quanNhanData.quaTrinhDaoTao} /> _/} Nội dung Tab Quá trình Đào tạo</div>,
'khen-thuong': <div>{/_ <TabKhenThuong data={quanNhanData.khenThuong} /> _/} Nội dung Tab Khen thưởng</div>,
'ky-luat': <div>{/_ <TabKyLuat data={quanNhanData.kyLuat} /> _/} Nội dung Tab Kỷ luật</div>,
'suc-khoe': <div>{/_ <TabSucKhoe data={quanNhanData.sucKhoe} /> _/} Nội dung Tab Sức khỏe</div>,
'quan-he-gia-dinh': <div>{/_ <TabQuanHeGiaDinh data={quanNhanData.thanNhan} /> _/} Nội dung Tab Quan hệ Gia đình</div>,
'che-do-chinh-sach': <div>{/_ <TabCheDoChinhSach data={quanNhanData.cheDoChinhSach} /> _/} Nội dung Tab Chế độ Chính sách</div>,
'lich-su-thay-doi': <div>{/_ <TabLichSuThayDoi data={quanNhanData.auditLog} /> _/} Nội dung Tab Lịch sử Thay đổi</div>,
};

return (
<TabContext value={activeTab}>
<Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
<TabList
          onChange={handleTabChange}
          aria-label="Chi tiết hồ sơ quân nhân"
          variant="scrollable"
          scrollButtons="auto"
        >
<Tab label="Thông tin Chung" value="thong-tin-chung" />
<Tab label="Lý lịch Cá nhân" value="ly-lich-ca-nhan" />
<Tab label="Quá trình Công tác" value="qua-trinh-cong-tac" />
<Tab label="Quá trình Đào tạo" value="qua-trinh-dao-tao" />
<Tab label="Khen thưởng" value="khen-thuong" />
<Tab label="Kỷ luật" value="ky-luat" />
<Tab label="Sức khỏe" value="suc-khoe" />
<Tab label="Quan hệ Gia đình" value="quan-he-gia-dinh" />
<Tab label="Chế độ Chính sách" value="che-do-chinh-sach" />
<Tab label="Lịch sử Thay đổi" value="lich-su-thay-doi" /> {/_ (Tùy chọn) _/}
</TabList>
</Box>
{Object.keys(tabContentList).map(tabValue => (
<TabPanel key={tabValue} value={tabValue} sx={{ p: 0 }}> {/_ Vuexy hay reset padding của TabPanel và dùng CardContent _/}
<CardContent> {/_ Bọc nội dung TabPanel trong CardContent như Vuexy _/}
{tabContentList[tabValue]}
</CardContent>
</TabPanel>
))}
</TabContext>
);
};

export default KhuVucTabsChiTiet;
Bước 6: Cập nhật file page.tsx của route (Nếu chưa làm hoặc cần điều chỉnh)
File: src\app\[lang]\(dashboard)\quan-ly-quan-nhan\chi-tiet\[idQuanNhan]\page.tsx

Nội dung: Đảm bảo import và render ChiTietQuanNhanPage.

TypeScript

// src\app\[lang]\(dashboard)\quan-ly-quan-nhan\chi-tiet\[idQuanNhan]\page.tsx
import React from 'react';
import ChiTietQuanNhanPage from '@/views/quan-ly-quan-nhan/chi-tiet/ChiTietQuanNhanPage'; // Cập nhật đường dẫn nếu cần

// Optional: metadata cho trang
// export async function generateMetadata({ params }: { params: { idQuanNhan: string } }) {
// // Fetch data dựa trên idQuanNhan để lấy tên
// // const quanNhanName = await fetchQuanNhanNameById(params.idQuanNhan);
// return {
// title: `Chi tiết Quân nhân: ${params.idQuanNhan}`, // Thay bằng tên thật sau khi fetch
// };
// }

const Page = () => {
// idQuanNhan và lang sẽ được lấy thông qua useParams trong ChiTietQuanNhanPage
return <ChiTietQuanNhanPage />;
};

export default Page;
