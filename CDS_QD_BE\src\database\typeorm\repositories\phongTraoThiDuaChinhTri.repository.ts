/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { PhongTraoThiDuaChinhTriEntity } from '~/database/typeorm/entities/phongTraoThiDuaChinhTri.entity';

@Injectable()
export class PhongTraoThiDuaChinhTriRepository extends Repository<PhongTraoThiDuaChinhTriEntity> {
    constructor(private dataSource: DataSource) {
        super(PhongTraoThiDuaChinhTriEntity, dataSource.createEntityManager());
    }
}
