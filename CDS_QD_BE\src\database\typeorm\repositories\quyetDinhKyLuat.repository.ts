/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { QuyetDinhKyLuatEntity } from '~/database/typeorm/entities/quyetDinhKyLuat.entity';

@Injectable()
export class QuyetDinhKyLuatRepository extends Repository<QuyetDinhKyLuatEntity> {
    constructor(private dataSource: DataSource) {
        super(QuyetDinhKyLuatEntity, dataSource.createEntityManager());
    }
}
