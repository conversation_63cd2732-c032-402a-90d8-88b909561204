# Hướng dẫn triển khai Custom Hooks cho API Services

## 1. C<PERSON>u trú<PERSON> thư mục
```
src/
├── hooks/                  # Th<PERSON> mục chứa tất cả custom hooks
│   ├── auth/               # Hooks liên quan đến xác thực
│   ├── cong-tac-dang/      # Hooks cho module công tác đảng
│   │   ├── useDangVien.ts
│   │   └── useVanBan.ts
│   └── shared/             # Hooks dùng chung
└── lib/
    └── services/        # Các service gọi API
        ├── auth.service.ts
        └── dang-vien.service.ts
```

## 2. Quy ước đặt tên
- Bắt đầu bằng `use`
- <PERSON><PERSON> tả rõ mục đích
- Ví dụ: `useDangVien`, `useDangVienList`, `useCreateDangVien`

## 3. Mẫu code cơ bản

### 3.1. Hook cho danh sách
```typescript
// hooks/cong-tac-dang/useDangVienList.ts
import { useQuery } from '@tanstack/react-query';
import { dangVienService } from '@/lib/services/dang-vien.service';

export function useDangVienList(params = {}) {
  return useQuery({
    queryKey: ['dang-vien', params],
    queryFn: () => dangVienService.getList(params),
    // Các tùy chọn khác...
  });
}
```

### 3.2. Hook cho tạo mới/cập nhật
```typescript
// hooks/cong-tac-dang/useDangVienMutation.ts
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { dangVienService } from '@/lib/services/dang-vien.service';

export function useCreateDangVien() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data) => dangVienService.create(data),
    onSuccess: () => {
      queryClient.invalidateQueries(['dang-vien']);
      // Hiển thị thông báo thành công
    },
    // Xử lý lỗi
  });
}
```

## 4. Sử dụng trong component

```typescript
// components/DanhSachDangVien.tsx
'use client';

import { useDangVienList, useDeleteDangVien } from '@/hooks/cong-tac-dang/useDangVien';

export function DanhSachDangVien() {
  const { data, isLoading } = useDangVienList();
  const { mutate: deleteDangVien } = useDeleteDangVien();

  if (isLoading) return <div>Đang tải...</div>;

  return (
    <div>
      {data?.map((dangVien) => (
        <div key={dangVien.id}>
          {dangVien.hoTen}
          <button onClick={() => deleteDangVien(dangVien.id)}>Xóa</button>
        </div>
      ))}
    </div>
  );
}
```

## 5. Best Practices

1. **Mỗi hook chỉ làm một việc**
   - Tách biệt hook đọc (query) và ghi (mutation)
   - Ví dụ: `useDangVienList`, `useCreateDangVien`, `useUpdateDangVien`

2. **Xử lý lỗi tập trung**
   - Sử dụng interceptor trong http client để xử lý lỗi chung
   - Xử lý lỗi cụ thể trong từng hook khi cần

3. **Tối ưu hiệu năng**
   - Sử dụng `select` để chỉ lấy dữ liệu cần thiết
   - Sử dụng `staleTime` và `cacheTime` phù hợp

4. **Type Safety**
   - Định nghĩa rõ ràng các kiểu dữ liệu
   - Sử dụng TypeScript để bắt lỗi lúc biên dịch

5. **Tài liệu**
   - Thêm JSDoc cho mỗi hook
   - Mô tả rõ params và return value
