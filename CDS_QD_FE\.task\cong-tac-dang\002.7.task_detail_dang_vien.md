# Quy trình xây dựng giao diện trang "Xem chi tiết Hồ sơ Đảng viên"

---

**Trang: Xem chi tiết Hồ sơ Đảng viên (`/${locale}/cong-tac-dang/chi-tiet-dang-vien/{idDangVien}`)**

**IV. Chi tiết Nội dung Tab 7: "Quá trình Phát triển Đảng"**

- **Mục đích:** Hiển thị chi tiết quá trình một cá nhân (quần chúng ưu tú hoặc đảng viên dự bị) được xem xét, bồi dưỡng, và kết nạp vào <PERSON> (hoặc chuyển Đảng chính thức nếu đang là dự bị). Tab này đặc biệt quan trọng đối với các đối tượng đang trong giai đoạn cảm tình <PERSON>, đảng viên dự bị, hoặc để xem lại lịch sử phát triển của một đảng viên chính thức.
- **Hiển thị:** Tab này có thể chỉ hiển thị nếu có dữ liệu phát triển Đảng liên quan đến `DangVienID` (hoặc `SoHieuQuanNhan` nếu hồ sơ phát triển liên kết qua đó trước khi có `MaDangVien`).
- **Bố cục:** Thường sẽ hiển thị thông tin tổng quan của hồ sơ phát triển và sau đó là danh sách các bước/giai đoạn trong quy trình, cùng với các tài liệu đính kèm tương ứng.
- **Nút hành động chung cho Tab:**

  - Nếu đảng viên hiện tại đang trong một quy trình phát triển Đảng dở dang (ví dụ, đang là dự bị chờ chuyển chính thức), có thể có nút "Tiếp tục Quy trình" hoặc các nút hành động cụ thể cho bước hiện tại (ví dụ: "Nộp Bản tự kiểm điểm đảng viên dự bị", "Đề nghị Chuyển chính thức"). Các hành động này sẽ phụ thuộc vào trạng thái hiện tại của quy trình và quyền của người dùng.
  - Đối với đảng viên chính thức, tab này chủ yếu mang tính chất xem lại lịch sử.

- **Các trường dữ liệu và chức năng hiển thị (read-only trên trang xem chi tiết hồ sơ đảng viên, trừ các hành động đặc biệt nếu có):**

  1.  **Khu vực Thông tin Chung của Hồ sơ Phát triển Đảng (Nếu có một bản ghi `HoSoPhatTrienDangVien` liên kết với đảng viên này):**

      - **Mục đích:** Hiển thị các thông tin tổng quan về quá trình phát triển.
      - **Các trường hiển thị (từ bảng `HoSoPhatTrienDangVien`):**
        - **ID Hồ sơ Phát triển:** `HoSoPhatTrienDangVien.ID` (Mã nội bộ của hồ sơ).
        - **Đối tượng là Quân nhân (nếu có):** `HoSoPhatTrienDangVien.DoiTuongQuanNhanID` (Số hiệu QN).
        - **Họ tên Đối tượng ngoài QĐ (nếu có):** `HoSoPhatTrienDangVien.HoTenDoiTuongNgoai`.
        - **Ngày tạo Hồ sơ Phát triển:** `HoSoPhatTrienDangVien.NgayTaoHoSo`.
        - **Trạng thái Quy trình Hiện tại:** Tên trạng thái từ `DmTrangThaiPhatTrienDang.TenTrangThai` (liên kết qua `HoSoPhatTrienDangVien.TrangThaiQuyTrinhID`).
        - **Đảng viên Hướng dẫn 1 (nếu có):** Tên đảng viên từ `DangVien.HoVaTen` (liên kết qua `HoSoPhatTrienDangVien.NguoiHuongDan1ID`).
        - **Đảng viên Hướng dẫn 2 (nếu có):** Tên đảng viên từ `DangVien.HoVaTen` (liên kết qua `HoSoPhatTrienDangVien.NguoiHuongDan2ID`).
        - **Tổ chức Đảng Đề nghị Kết nạp (nếu có):** Tên tổ chức từ `ToChucDang.TenToChucDang` (liên kết qua `HoSoPhatTrienDangVien.ToChucDangDeNghiKetNapID`).
        - **Số Quyết định Kết nạp (nếu có):** `HoSoPhatTrienDangVien.SoQuyetDinhKetNap`.
        - **Ngày Quyết định Kết nạp (nếu có):** `HoSoPhatTrienDangVien.NgayQuyetDinhKetNap`.
        - **Ngày Công nhận Chính thức Dự kiến (nếu là dự bị):** `HoSoPhatTrienDangVien.NgayCongNhanChinhThucDuKien`.
        - **Số Quyết định Chính thức (nếu có):** `HoSoPhatTrienDangVien.SoQuyetDinhChinhThuc`.
        - **Ngày Quyết định Chính thức (nếu có):** `HoSoPhatTrienDangVien.NgayQuyetDinhChinhThuc`.
        - **Lý do Tạm dừng/Trả lại (nếu có):** `HoSoPhatTrienDangVien.LyDoTamDungTraLai`.
        - **Ghi chú Quy trình (nếu có):** `HoSoPhatTrienDangVien.GhiChuQuyTrinh`.

  2.  **Khu vực Các Bước/Giai đoạn và Tài liệu trong Quy trình Phát triển Đảng:**
      - **Mục đích:** Hiển thị chi tiết các bước đã trải qua hoặc cần thực hiện trong quy trình, cùng với các tài liệu tương ứng.
      - **Bố cục:** Có thể là một dạng `Stepper` (các bước tuần tự) hoặc một bảng/danh sách các tài liệu được nhóm theo giai đoạn/loại.
      - **Hiển thị danh sách các tài liệu (từ bảng `TaiLieuPhatTrienDangVien` liên kết với `HoSoPhatTrienDangVien.ID`):**
        - **Cột "Tên Tài liệu":** `TaiLieuPhatTrienDangVien.TenTaiLieu`.
        - **Cột "Loại Tài liệu":** Tên loại từ `DmLoaiTaiLieuPhatTrienDang.TenLoaiTaiLieu` (liên kết qua `TaiLieuPhatTrienDangVien.LoaiTaiLieuPhatTrienID`). Ví dụ: "Đơn xin vào Đảng", "Lý lịch 2-KNĐ", "Nghị quyết Chi bộ", "Quyết định Kết nạp", "Bản tự kiểm điểm đảng viên dự bị", "Quyết định Công nhận Chính thức"...
        - **Cột "Ngày Phát hành/Nộp":** `TaiLieuPhatTrienDangVien.NgayPhatHanhTaiLieu` hoặc `TaiLieuPhatTrienDangVien.NgayNopTaiLieu`.
        - **Cột "File Đính kèm":**
          - Liên kết/nút "Xem/Tải" file từ `TaiLieuPhatTrienDangVien.FileURL`. Cần kiểm soát quyền truy cập và giải mã nếu cần.
        - **Cột "Ghi chú Tài liệu (nếu có)":** `TaiLieuPhatTrienDangVien.GhiChu`.
        - **Hành động trên dòng (tùy theo quyền và trạng thái quy trình):**
          - "Xem File".
          - "Tải File".
          - **(Nếu đang trong quy trình và cho phép sửa/thay thế):** "Thay thế File", "Xóa File".

- **Thông tin liên quan từ bảng `HoSoPhatTrienDangVien` (PartyAdmissionDossiers):** (Đã liệt kê ở mục 1 của Tab này)

- **Thông tin liên quan từ bảng `TaiLieuPhatTrienDangVien` (PartyAdmissionDocuments):**

  - `ID` (PK)
  - `HoSoPhatTrienID` (FK - để lọc theo hồ sơ phát triển của đảng viên hiện tại)
  - `TenTaiLieu`
  - `LoaiTaiLieuPhatTrienID` (FK đến `DmLoaiTaiLieuPhatTrienDang`)
  - `FileURL`
  - `NgayPhatHanhTaiLieu`
  - `NgayNopTaiLieu`
  - `GhiChu`

- **Thông tin liên quan từ các bảng Danh mục (Dm\*) và bảng khác cần join để hiển thị tên/thông tin:**

  - `DmTrangThaiPhatTrienDang`: `ID`, `TenTrangThaiPhatTrien` (hoặc tên tương ứng) - Ví dụ: Tạo nguồn, Thẩm tra lý lịch, Học lớp nhận thức, Chi bộ xem xét, Đảng ủy cơ sở xem xét, Quyết định kết nạp, Dự bị, Chuyển chính thức.
  - `DmLoaiTaiLieuPhatTrienDang`: `ID`, `TenLoaiTaiLieu` (hoặc tên tương ứng) - Ví dụ: Đơn xin vào Đảng, Lý lịch người xin vào Đảng, Nghị quyết giới thiệu, Quyết định kết nạp, Bản tự kiểm điểm Đảng viên dự bị, Nghị quyết đề nghị công nhận chính thức, Quyết định công nhận chính thức.
  - `DangVien` (để lấy tên người hướng dẫn): `MaDangVien`, `HoVaTen`.
  - `ToChucDang` (để lấy tên tổ chức đề nghị): `ID`, `TenToChucDang`.
  - `QuanNhan` (để lấy tên đối tượng nếu là QN): `SoHieuQuanNhan`, `HoVaTenKhaiSinh`.

- **Chức năng trong Tab này:**
  - **Hiển thị toàn bộ quá trình phát triển Đảng:** Giúp theo dõi các mốc thời gian, quyết định và tài liệu liên quan.
  - **Xem và tải các tài liệu:** Cho phép truy cập các văn bản quan trọng trong hồ sơ phát triển.
  - **(Nếu người dùng có quyền và hồ sơ đang trong trạng thái cho phép):**
    - **Nộp/Upload tài liệu cho các bước cụ thể:** Ví dụ, sau khi học lớp nhận thức về Đảng, có thể cần nộp giấy chứng nhận.
    - **Thực hiện các hành động chuyển bước trong quy trình:** Ví dụ, sau khi chi bộ họp xét, người có thẩm quyền có thể nhấn nút "Đề nghị Đảng ủy Cơ sở xét duyệt" và cập nhật trạng thái quy trình. Các hành động này thường được thực hiện tại trang quản lý "Phát triển Đảng viên" chung, nhưng có thể có lối tắt hoặc hiển thị trạng thái tại đây.

---
