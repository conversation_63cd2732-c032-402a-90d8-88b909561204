{"version": 3, "sources": ["../../../src/common/dtos/filter.dto.ts"], "sourcesContent": ["import { ApiProperty } from '@nestjs/swagger';\nimport { Transform } from 'class-transformer';\nimport { IsOptional } from 'class-validator';\n\nexport class FilterDto {\n    @ApiProperty({ required: false, type: Number, default: 1 })\n    @IsOptional()\n    @Transform(({ value }) => parseInt(value))\n    page: number;\n\n    @ApiProperty({ required: false, type: Number, default: 10 })\n    @IsOptional()\n    @Transform(({ value }) => parseInt(value))\n    perPage: number;\n\n    @ApiProperty({ required: false, default: 'id.ASC' })\n    @IsOptional()\n    sortBy: string;\n\n    @ApiProperty({ required: false })\n    @IsOptional()\n    search: string;\n\n    @ApiProperty({ required: false })\n    @IsOptional()\n    type: string;\n}\n"], "names": ["Filter<PERSON><PERSON>", "required", "type", "Number", "default", "value", "parseInt"], "mappings": "oGAIaA,mDAAAA,oCAJe,mDACF,mDACC,gkBAEpB,IAAA,AAAMA,UAAN,MAAMA,UAsBb,0CArBmBC,SAAU,MAAOC,KAAMC,OAAQC,QAAS,sEAE3C,CAAEC,KAAK,CAAE,GAAKC,SAASD,uHAGpBJ,SAAU,MAAOC,KAAMC,OAAQC,QAAS,uEAE3C,CAAEC,KAAK,CAAE,GAAKC,SAASD,0HAGpBJ,SAAU,MAAOG,QAAS,6JAI1BH,SAAU,0JAIVA,SAAU"}