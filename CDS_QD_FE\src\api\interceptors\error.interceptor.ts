// src/api/interceptors/error.interceptor.ts
import { AxiosError } from 'axios'
import { toast } from 'react-toastify'

export const errorInterceptor = (error: AxiosError) => {
  const message = (error.response?.data as any)?.message || 'Đã xảy ra lỗi'

  // Xử lý các mã lỗi cụ thể
  if (error.response?.status === 401) {
    // Xử lý lỗi xác thực - có thể redirect về trang login
    // window.location.href = "/login"
  }

  toast.error(message)
  return Promise.reject(error)
}
