# Quy trình xây dựng giao diện trang "Xem chi tiết Hồ sơ Đảng viên"

---

**Trang: Xem chi tiết Hồ sơ Đảng viên (`/${locale}/cong-tac-dang/chi-tiet-dang-vien/{idDangVien}`)**

**IV. Chi tiết Nội dung Tab (T<PERSON>y chọn) 9: "Lịch sử Thay đổi"**

- **Mụ<PERSON> đích:** Cung cấp một bản ghi chi tiết về tất cả các thay đổi đã được thực hiện trên hồ sơ của đảng viên này. Điều này rất quan trọng cho mục đích kiểm toán, theo dõi và đảm bảo tính minh bạch, chính xác của dữ liệu.
- **Hiển thị:** Tab này sẽ hiển thị nếu hệ thống có chức năng ghi Audit Log chi tiết và người dùng có quyền xem các log này.
- **Bố cục:** Chủ yếu là một bảng hiển thị danh sách các bản ghi log liên quan đến hồ sơ đảng viên đang xem. Có thể có các bộ lọc theo thời gian hoặc loại hành động.
- **Nút hành động chung cho Tab:** Thường chỉ có chức năng xem và lọc, không có hành động sửa đổi log.

- **Các trường dữ liệu và chức năng hiển thị (read-only):**

  1.  **Khu vực Lọc (Tùy chọn, nếu lịch sử thay đổi dài):**

      - **Lọc theo "Khoảng thời gian thay đổi":** Cho phép chọn "Từ ngày" - "Đến ngày".
      - **Lọc theo "Người thực hiện thay đổi":** Trường nhập liệu hoặc lựa chọn người dùng (nếu danh sách nhỏ).
      - **Lọc theo "Loại hành động":** Trường lựa chọn (ví dụ: Tạo mới, Cập nhật, Thay đổi trạng thái, Thêm file hồ sơ...).
      - **Lọc theo "Trường dữ liệu bị thay đổi" (nếu log đủ chi tiết):** Cho phép xem các thay đổi trên một trường cụ thể.
      - **Nút "Áp dụng bộ lọc".**

  2.  **Khu vực Hiển thị Lịch sử Thay đổi (Dạng Bảng):**
      - **Mục đích:** Liệt kê chi tiết từng hành động thay đổi trên hồ sơ đảng viên.
      - **Các cột thông tin hiển thị cho mỗi bản ghi log (dữ liệu từ bảng `AuditLog` đã được lọc theo đối tượng là đảng viên này):**
        - **Cột "Thời gian":**
          - Hiển thị `AuditLog.ThoiGian` (Ngày giờ thực hiện thay đổi).
        - **Cột "Người thực hiện":**
          - Hiển thị `AuditLog.TenDangNhap` (Tên đăng nhập của người thực hiện) hoặc Họ tên người thực hiện (nếu có liên kết).
        - **Cột "Địa chỉ IP" (nếu ghi nhận):**
          - Hiển thị `AuditLog.DiaChiIP`.
        - **Cột "Module Tác động":**
          - Hiển thị `AuditLog.ModuleTacDong` (Ví dụ: "Công tác Đảng - Hồ sơ Đảng viên", "Công tác Đảng - Sinh hoạt Đảng").
        - **Cột "Chức năng Tác động":**
          - Hiển thị `AuditLog.ChucNangTacDong` (Ví dụ: "Cập nhật Thông tin Chung Đảng viên", "Thêm Bản tự kiểm điểm", "Thay đổi Trạng thái Đảng tịch").
        - **Cột "Loại Hành động":**
          - Hiển thị `AuditLog.HanhDong` (Ví dụ: CREATE, UPDATE, DELETE (logic), CHANGE_STATUS, UPLOAD_FILE).
        - **Cột "Bảng Dữ liệu Bị Tác động" (nếu có):**
          - Hiển thị `AuditLog.TenBangBiTacDong` (Ví dụ: `DangVien`, `HoSoDangVien`, `BanKiemDiemDangVien`).
        - **Cột "ID Bản ghi Bị Tác động" (nếu có):**
          - Hiển thị `AuditLog.KhoaChinhBanGhi` (Ví dụ: `MaDangVien` của hồ sơ đảng viên, hoặc ID của bản ghi `BanKiemDiemDangVien`).
        - **Cột "Mô tả Hành động / Thay đổi":**
          - Hiển thị `AuditLog.MoTaHanhDong` (Mô tả tóm tắt về hành động hoặc các trường đã thay đổi).
        - **Hành động trên dòng (tùy theo quyền và độ chi tiết của log):**
          - **"Xem chi tiết Log":** Mở một dialog hoặc panel hiển thị đầy đủ thông tin của bản ghi log, đặc biệt quan trọng là:
            - `AuditLog.DuLieuCu`: Dữ liệu cũ của các trường bị thay đổi (nếu là UPDATE, có thể ở dạng JSON/XML/Text).
            - `AuditLog.DuLieuMoi`: Dữ liệu mới của các trường bị thay đổi (nếu là UPDATE/CREATE, có thể ở dạng JSON/XML/Text).
            - `AuditLog.ThongDiepLoi` (nếu hành động gây lỗi).

- **Thông tin liên quan từ bảng `AuditLog` cần thiết (đã được lọc theo `DoiTuongIDBiTacDong` là `MaDangVien` của đảng viên đang xem, hoặc các `KhoaChinhBanGhi` liên quan đến đảng viên này trong các bảng khác):**

  - `ID` (PK)
  - `ThoiGian`
  - `NguoiDungID` (FK đến `NguoiDungHeThong`)
  - `TenDangNhap` (Lưu lại để tránh mất thông tin nếu người dùng bị xóa, hoặc lấy từ `NguoiDungHeThong`)
  - `DiaChiIP`
  - `ModuleTacDong`
  - `ChucNangTacDong`
  - `HanhDong`
  - `TenBangBiTacDong`
  - `KhoaChinhBanGhi` (ID của bản ghi trong `TenBangBiTacDong`)
  - `DoiTuongIDBiTacDong` (ID của đối tượng chính, ví dụ `MaDangVien`)
  - `MoTaHanhDong`
  - `DuLieuCu` (TEXT)
  - `DuLieuMoi` (TEXT)
  - `TrangThaiHanhDongID` (FK đến `DmTrangThaiHanhDong` - Thành công/Thất bại)
  - `MaLoi`
  - `ThongDiepLoi`
  - `IsSystemEvent` (BOOLEAN - Để phân biệt sự kiện hệ thống và hành động người dùng)

- **Thông tin liên quan từ bảng `NguoiDungHeThong` (UserAccounts) để hiển thị tên người thực hiện:**

  - `ID` (PK)
  - `HoTenNguoiDung` (hoặc tên tương ứng)

- **Chức năng trong Tab này:**
  - **Hiển thị lịch sử thay đổi:** Cung cấp một cái nhìn chi tiết về ai đã làm gì, khi nào, trên hồ sơ đảng viên này và các dữ liệu liên quan.
  - **Lọc và tìm kiếm log:** Giúp người quản trị dễ dàng điều tra các vấn đề hoặc xem lại các thay đổi cụ thể.
  - **Xem chi tiết sự thay đổi (dữ liệu cũ/mới):** Tính năng quan trọng nhất của audit log, cho thấy rõ ràng nội dung đã được thay đổi.

---
