{"version": 3, "sources": ["../../../../src/modules/profile/dto/update-profile.dto.ts"], "sourcesContent": ["import { ApiProperty } from '@nestjs/swagger';\nimport { Transform } from 'class-transformer';\nimport { IsOptional } from 'class-validator';\n\nexport class UpdateProfileDto {\n    @ApiProperty()\n    @IsOptional()\n    hoTen: string;\n\n    @ApiProperty()\n    @IsOptional()\n    email: string;\n\n    @ApiProperty()\n    @IsOptional()\n    avatarId: string;\n\n    @ApiProperty()\n    @IsOptional()\n    soDienThoai: string;\n}\n"], "names": ["UpdateProfileDto"], "mappings": "oGAIaA,0DAAAA,2CAJe,iDAED,gkBAEpB,IAAA,AAAMA,iBAAN,MAAMA,iBAgBb"}