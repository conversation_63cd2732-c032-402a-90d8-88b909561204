# Quy trình xây dựng giao diện trang "Xem chi tiết Hồ sơ Đảng viên"

---

**Trang: Xem chi tiết Hồ sơ Đảng viên (`/${locale}/cong-tac-dang/chi-tiet-dang-vien/{idDangVien}`)**

**IV. Chi tiết Nội dung Tab 2: "<PERSON> dõi <PERSON> hoạt"**

- **M<PERSON><PERSON> đích:** Hiển thị lịch sử tham gia các buổi sinh hoạt Đảng của đảng viên, bao gồm tình trạng tham gia, ý kiến phát biểu và việc thực hiện nhiệm vụ được giao trong các buổi sinh hoạt đó. Dữ liệu chủ yếu từ bảng `TheoDoiSinhHoatCaNhan` và các bảng liên quan như `BuoiSinhHoatDang`.
- **<PERSON><PERSON> cục:** Chủ yếu là một bảng hiển thị danh sách các buổi sinh hoạt đảng viên đã có tương tác, có thể có các bộ lọc theo thời gian hoặc loại sinh hoạt.
- **Nút hành động chung cho Tab:** Thường thì tab này chỉ để hiển thị thông tin tổng hợp. Việc ghi nhận tham gia sinh hoạt, ý kiến phát biểu sẽ được thực hiện tại trang quản lý "Buổi Sinh hoạt Đảng" (khi điểm danh hoặc cập nhật biên bản). Tuy nhiên, có thể có nút "Xem chi tiết buổi sinh hoạt" cho mỗi dòng.

- **Các trường dữ liệu và chức năng hiển thị (read-only trên trang xem chi tiết hồ sơ đảng viên):**

  1.  **Khu vực Lọc (Tùy chọn, nếu danh sách dài):**

      - **Lọc theo khoảng thời gian:** Cho phép chọn "Từ ngày" - "Đến ngày" để xem lịch sử sinh hoạt trong một khoảng cụ thể.
      - **Lọc theo loại buổi sinh hoạt (nếu có thông tin này từ `BuoiSinhHoatDang`):** Ví dụ: Sinh hoạt thường kỳ, Sinh hoạt chuyên đề.
      - **Nút "Áp dụng bộ lọc".**

  2.  **Khu vực Hiển thị Lịch sử Sinh hoạt (Dạng Bảng):**
      - **Mục đích:** Liệt kê chi tiết từng buổi sinh hoạt mà đảng viên có liên quan.
      - **Các cột thông tin hiển thị cho mỗi buổi sinh hoạt (dữ liệu từ `TheoDoiSinhHoatCaNhan` và join với `BuoiSinhHoatDang`):**
        - **Cột "Tên/Nội dung Buổi sinh hoạt":**
          - Hiển thị `BuoiSinhHoatDang.TenKeHoach` (nếu buổi SH có liên kết với kế hoạch) hoặc một phần nội dung chính của buổi sinh hoạt.
          - Có thể là một liên kết để xem chi tiết thông tin của buổi sinh hoạt đó (nếu có trang riêng cho chi tiết buổi sinh hoạt).
        - **Cột "Thời gian Diễn ra":**
          - Hiển thị `BuoiSinhHoatDang.ThoiGianBatDau`. Có thể hiển thị cả `ThoiGianKetThuc` nếu có.
        - **Cột "Tổ chức Đảng (Chi bộ)":**
          - Hiển thị tên của Tổ chức Đảng tổ chức buổi sinh hoạt (lấy từ `ToChucDang.TenToChucDang` liên kết qua `BuoiSinhHoatDang.ToChucDangID`).
        - **Cột "Tình trạng Tham gia":**
          - Hiển thị "Có mặt" nếu `TheoDoiSinhHoatCaNhan.CoMat` là TRUE.
          - Hiển thị "Vắng mặt" nếu `TheoDoiSinhHoatCaNhan.CoMat` là FALSE.
        - **Cột "Lý do Vắng mặt (nếu vắng)":**
          - Hiển thị tên lý do từ `DmLyDoVangMat.TenLyDo` (liên kết qua `TheoDoiSinhHoatCaNhan.LyDoVangMatID`).
          - Nếu không có `LyDoVangMatID` mà chỉ có text, thì hiển thị text đó (cần làm rõ cấu trúc bảng `DmLyDoVangMat` hoặc trường `LyDoVangMat` trong `TheoDoiSinhHoatCaNhan` có thể là text tự do không). _Giả sử `LyDoVangMatID` là FK đến bảng danh mục lý do._
        - **Cột "Ý kiến Phát biểu chính":**
          - Hiển thị một phần nội dung từ `TheoDoiSinhHoatCaNhan.YKienPhatBieu`. Có thể có tooltip hoặc nút "Xem thêm" nếu nội dung dài.
        - **Cột "Thực hiện Nhiệm vụ được giao":**
          - Hiển thị một phần nội dung từ `TheoDoiSinhHoatCaNhan.ThucHienNhiemVuGiao`.
        - **Cột "Tự đánh giá/Nhận xét của Chi bộ":**
          - Hiển thị một phần nội dung từ `TheoDoiSinhHoatCaNhan.TuDanhGiaChiBoNhanXet`.
        - **Cột "Ghi chú (nếu có)":**
          - Hiển thị `TheoDoiSinhHoatCaNhan.GhiChu`.
      - **Chức năng của bảng:**
        - Sắp xếp theo các cột (ví dụ: theo Thời gian diễn ra).
        - Phân trang nếu lịch sử sinh hoạt của đảng viên quá dài.

- **Thông tin liên quan từ bảng `BuoiSinhHoatDang` (PartyMeetings) cần join để hiển thị:**

  - `ID` (PK của BuoiSinhHoatDang, để join với `TheoDoiSinhHoatCaNhan.BuoiSinhHoatID`)
  - `ToChucDangID` (FK, để lấy tên Tổ chức Đảng)
  - `KeHoachSinhHoatID` (FK, để lấy tên/chủ đề từ Kế hoạch nếu có)
  - `LoaiSinhHoatID` (FK, để hiển thị loại hình sinh hoạt)
  - `ThoiGianBatDau`
  - `ThoiGianKetThuc` (nếu có)
  - `DiaDiem` (có thể hiển thị nếu cần)
  - `NoiDungChinhDuKien` (để làm tên/nội dung tóm tắt buổi sinh hoạt)

- **Thông tin liên quan từ các bảng Danh mục (Dm\*) cần join để hiển thị tên:**

  - `DmLyDoVangMat`: `ID`, `TenLyDoVangMat` (hoặc tên tương ứng)
  - `DmLoaiSinhHoatDang` (nếu `BuoiSinhHoatDang.LoaiSinhHoatID` được dùng để lọc hoặc hiển thị)

- **Chức năng trong Tab này:**
  - Chủ yếu là hiển thị thông tin.
  - Mỗi dòng trong bảng có thể có một nút/liên kết "Xem chi tiết buổi sinh hoạt" để điều hướng người dùng đến giao diện chi tiết của buổi sinh hoạt Đảng đó (nếu có một trang riêng cho việc quản lý chi tiết các buổi sinh hoạt). Điều này giúp người dùng xem được toàn bộ biên bản, nghị quyết (nếu có) của buổi sinh hoạt đó.
  - Việc cập nhật thông tin tham gia sinh hoạt của đảng viên (ví dụ: điểm danh, ghi nhận ý kiến) thường được thực hiện tại chức năng quản lý "Buổi Sinh hoạt Đảng" chứ không trực tiếp tại hồ sơ cá nhân của từng đảng viên trong tab này.

---
