# ENV
NODE_ENV=development

# PORT =================
PORT=8080

# APP_NAME =============
APP_NAME=

# Bcrypt ===============
SALT_ROUNDS=8

# Timezone ===============
TZ=Asia/Ho_Chi_Minh

# Token Keys ===========
AUTH_TOKEN_SECRET=
AUTH_TOKEN_NAME=
PASSCODE_TOKEN_SECRET=
PASSCODE_TOKEN_NAME=
REFRESH_TOKEN_NAME=
REFRESH_TOKEN_SECRET=
AUTH_TOKEN_EXP=
PASSCODE_TOKEN_EXP=
REFRESH_TOKEN_EXP=
PASSCODE=
LOGIN_SINGLE_DEVICE=

# TYPEORM TYPEORM ==============
DATABASE_TYPE=
DATABASE_PORT=
DATABASE_HOST=
DATABASE_USERNAME=
DATABASE_PASSWORD=
DATABASE_DB_NAME=
DATABASE_SYNCHRONIZE=
DATABASE_LOGGING=
DATABASE_SSL=
DATABASE_CA_FILE=
DATABASE_TIMEZONE='+07:00'

# REDIS ==============
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_PREFIX=

# MAIL ==============
MAIL_HOST=
MAIL_USER=
MAIL_PASSWORD=
MAIL_FROM=

# GOOGLE ==============
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=