{"version": 3, "sources": ["../../../src/common/constants/constant.ts"], "sourcesContent": ["export const NoPermissionResponse = {\n    result: false,\n    message: 'No permission.',\n};\n\n// export const DEFAULT_TIMEZONE = 'Asia/Ho_Chi_Minh';\n\nexport const DESC = 'DESC';\nexport const ASC = 'ASC';\n\nexport const FROM_DEFAULT = '2021-01-01 00:00:00';\nexport const TO_DEFAULT = '2121-12-31 23:59:59';\n\nexport const DEFAULT_PAGE = 1;\nexport const DEFAULT_PER_PAGE = 10;\n\nexport const BYPASS_PERMISSION = 'BYPASS_PERMISSION';\nexport const ONLY_ADMIN = 'ONLY_ADMIN';\n\nexport const DEFAULT_TIMESTAMP_FORMAT = 'YYYY-MM-DD HH:mm:ss';\nexport const HUMAN_TIMESTAMP_FORMAT = 'HH:mm DD/MM/YYYY';\n"], "names": ["ASC", "BYPASS_PERMISSION", "DEFAULT_PAGE", "DEFAULT_PER_PAGE", "DEFAULT_TIMESTAMP_FORMAT", "DESC", "FROM_DEFAULT", "HUMAN_TIMESTAMP_FORMAT", "NoPermissionResponse", "ONLY_ADMIN", "TO_DEFAULT", "result", "message"], "mappings": "mPAQaA,aAAAA,SAQAC,2BAAAA,uBAHAC,sBAAAA,kBACAC,0BAAAA,sBAKAC,kCAAAA,8BAZAC,cAAAA,UAGAC,sBAAAA,kBAUAC,gCAAAA,4BApBAC,8BAAAA,0BAiBAC,oBAAAA,gBANAC,oBAAAA,cAXN,MAAMF,qBAAuB,CAChCG,OAAQ,MACRC,QAAS,gBACb,EAIO,MAAMP,KAAO,OACb,MAAML,IAAM,MAEZ,MAAMM,aAAe,sBACrB,MAAMI,WAAa,sBAEnB,MAAMR,aAAe,EACrB,MAAMC,iBAAmB,GAEzB,MAAMF,kBAAoB,oBAC1B,MAAMQ,WAAa,aAEnB,MAAML,yBAA2B,sBACjC,MAAMG,uBAAyB"}