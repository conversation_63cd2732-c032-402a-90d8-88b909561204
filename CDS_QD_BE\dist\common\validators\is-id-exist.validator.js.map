{"version": 3, "sources": ["../../../src/common/validators/is-id-exist.validator.ts"], "sourcesContent": ["import { Injectable } from '@nestjs/common';\r\nimport { ValidationArguments, ValidationOptions, ValidatorConstraint, ValidatorConstraintInterface, registerDecorator } from 'class-validator';\r\nimport { DatabaseService } from '~/database/typeorm/database.service';\r\n\r\n@ValidatorConstraint({ name: 'IsIdExist', async: true })\r\n@Injectable()\r\nexport class IsIdAlreadyExistConstraint implements ValidatorConstraintInterface {\r\n    constructor(private readonly database: DatabaseService) {}\r\n\r\n    async validate(id: number, args: ValidationArguments) {\r\n        return !!(await this.database[args.constraints[0]?.entity]?.countBy({ id }));\r\n    }\r\n\r\n    defaultMessage(validationArguments?: ValidationArguments): string {\r\n        return `ID ${validationArguments?.value} không tồn tại`;\r\n    }\r\n}\r\n\r\nexport function IsIdExist(data: { entity: string }, validationOptions?: ValidationOptions) {\r\n    return function (object: object, propertyName: string) {\r\n        registerDecorator({\r\n            target: object.constructor,\r\n            propertyName: propertyName,\r\n            options: validationOptions,\r\n            constraints: [data],\r\n            validator: IsIdAlreadyExistConstraint,\r\n        });\r\n    };\r\n}\r\n"], "names": ["IsIdAlreadyExistConstraint", "IsIdExist", "validate", "id", "args", "database", "constraints", "entity", "countBy", "defaultMessage", "validationArguments", "value", "constructor", "name", "async", "data", "validationOptions", "object", "propertyName", "registerDecorator", "target", "options", "validator"], "mappings": "mPAMaA,oCAAAA,gCAYGC,mBAAAA,mCAlBW,gDACkG,kDAC7F,wlBAIzB,IAAA,AAAMD,2BAAN,MAAMA,2BAGT,MAAME,SAASC,EAAU,CAAEC,IAAyB,CAAE,CAClD,MAAO,CAAC,CAAE,MAAM,IAAI,CAACC,QAAQ,CAACD,KAAKE,WAAW,CAAC,EAAE,EAAEC,OAAO,EAAEC,QAAQ,CAAEL,EAAG,EAC7E,CAEAM,eAAeC,mBAAyC,CAAU,CAC9D,MAAO,CAAC,GAAG,EAAEA,qBAAqBC,MAAM,iBAAc,CAAC,AAC3D,CARAC,YAAY,AAAiBP,QAAyB,CAAE,MAA3BA,SAAAA,QAA4B,CAS7D,oFAZuBQ,KAAM,YAAaC,MAAO,oOAc1C,SAASb,UAAUc,IAAwB,CAAEC,iBAAqC,EACrF,OAAO,SAAUC,MAAc,CAAEC,YAAoB,EACjDC,GAAAA,iCAAiB,EAAC,CACdC,OAAQH,OAAOL,WAAW,CAC1BM,aAAcA,aACdG,QAASL,kBACTV,YAAa,CAACS,KAAK,CACnBO,UAAWtB,0BACf,EACJ,CACJ"}