/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 *  Swagger
 * The  API documents
 * OpenAPI spec version: 1.0
 */
import { useInfiniteQuery, useMutation, useQuery } from '@tanstack/react-query'
import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseInfiniteQueryResult,
  DefinedUseQueryResult,
  InfiniteData,
  MutationFunction,
  QueryClient,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseInfiniteQueryOptions,
  UseInfiniteQueryResult,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult
} from '@tanstack/react-query'

import type { ChangePasswordDto, UpdateProfileDto } from '.././model'

import { customInstance } from '../../mutator/custom-instance'
import type { ApiResponse } from '../../types'

export const profileControllerFindOne = (signal?: AbortSignal) => {
  return customInstance<ApiResponse<void>>({ url: `/profile`, method: 'GET', signal })
}

export const getProfileControllerFindOneQueryKey = () => {
  return [`/profile`] as const
}

export const getProfileControllerFindOneInfiniteQueryOptions = <
  TData = InfiniteData<Awaited<ReturnType<typeof profileControllerFindOne>>>,
  TError = unknown
>(options?: {
  query?: Partial<UseInfiniteQueryOptions<Awaited<ReturnType<typeof profileControllerFindOne>>, TError, TData>>
}) => {
  const { query: queryOptions } = options ?? {}

  const queryKey = queryOptions?.queryKey ?? getProfileControllerFindOneQueryKey()

  const queryFn: QueryFunction<Awaited<ReturnType<typeof profileControllerFindOne>>> = ({ signal }) =>
    profileControllerFindOne(signal)

  return { queryKey, queryFn, staleTime: 10000, ...queryOptions } as UseInfiniteQueryOptions<
    Awaited<ReturnType<typeof profileControllerFindOne>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> }
}

export type ProfileControllerFindOneInfiniteQueryResult = NonNullable<
  Awaited<ReturnType<typeof profileControllerFindOne>>
>
export type ProfileControllerFindOneInfiniteQueryError = unknown

export function useProfileControllerFindOneInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof profileControllerFindOne>>>,
  TError = unknown
>(
  options: {
    query: Partial<UseInfiniteQueryOptions<Awaited<ReturnType<typeof profileControllerFindOne>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof profileControllerFindOne>>,
          TError,
          Awaited<ReturnType<typeof profileControllerFindOne>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): DefinedUseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useProfileControllerFindOneInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof profileControllerFindOne>>>,
  TError = unknown
>(
  options?: {
    query?: Partial<UseInfiniteQueryOptions<Awaited<ReturnType<typeof profileControllerFindOne>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof profileControllerFindOne>>,
          TError,
          Awaited<ReturnType<typeof profileControllerFindOne>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useProfileControllerFindOneInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof profileControllerFindOne>>>,
  TError = unknown
>(
  options?: {
    query?: Partial<UseInfiniteQueryOptions<Awaited<ReturnType<typeof profileControllerFindOne>>, TError, TData>>
  },
  queryClient?: QueryClient
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }

export function useProfileControllerFindOneInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof profileControllerFindOne>>>,
  TError = unknown
>(
  options?: {
    query?: Partial<UseInfiniteQueryOptions<Awaited<ReturnType<typeof profileControllerFindOne>>, TError, TData>>
  },
  queryClient?: QueryClient
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
  const queryOptions = getProfileControllerFindOneInfiniteQueryOptions(options)

  const query = useInfiniteQuery(queryOptions, queryClient) as UseInfiniteQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>
  }

  query.queryKey = queryOptions.queryKey

  return query
}

export const getProfileControllerFindOneQueryOptions = <
  TData = Awaited<ReturnType<typeof profileControllerFindOne>>,
  TError = unknown
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof profileControllerFindOne>>, TError, TData>>
}) => {
  const { query: queryOptions } = options ?? {}

  const queryKey = queryOptions?.queryKey ?? getProfileControllerFindOneQueryKey()

  const queryFn: QueryFunction<Awaited<ReturnType<typeof profileControllerFindOne>>> = ({ signal }) =>
    profileControllerFindOne(signal)

  return { queryKey, queryFn, staleTime: 10000, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof profileControllerFindOne>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> }
}

export type ProfileControllerFindOneQueryResult = NonNullable<Awaited<ReturnType<typeof profileControllerFindOne>>>
export type ProfileControllerFindOneQueryError = unknown

export function useProfileControllerFindOne<
  TData = Awaited<ReturnType<typeof profileControllerFindOne>>,
  TError = unknown
>(
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof profileControllerFindOne>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof profileControllerFindOne>>,
          TError,
          Awaited<ReturnType<typeof profileControllerFindOne>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useProfileControllerFindOne<
  TData = Awaited<ReturnType<typeof profileControllerFindOne>>,
  TError = unknown
>(
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof profileControllerFindOne>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof profileControllerFindOne>>,
          TError,
          Awaited<ReturnType<typeof profileControllerFindOne>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useProfileControllerFindOne<
  TData = Awaited<ReturnType<typeof profileControllerFindOne>>,
  TError = unknown
>(
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof profileControllerFindOne>>, TError, TData>> },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }

export function useProfileControllerFindOne<
  TData = Awaited<ReturnType<typeof profileControllerFindOne>>,
  TError = unknown
>(
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof profileControllerFindOne>>, TError, TData>> },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
  const queryOptions = getProfileControllerFindOneQueryOptions(options)

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>
  }

  query.queryKey = queryOptions.queryKey

  return query
}

export const profileControllerUpdate = (updateProfileDto: UpdateProfileDto) => {
  return customInstance<ApiResponse<ApiResponse<void>>>({
    url: `/profile`,
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json' },
    data: updateProfileDto
  })
}

export const getProfileControllerUpdateMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof profileControllerUpdate>>,
    TError,
    { data: UpdateProfileDto },
    TContext
  >
}): UseMutationOptions<
  Awaited<ReturnType<typeof profileControllerUpdate>>,
  TError,
  { data: UpdateProfileDto },
  TContext
> => {
  const mutationKey = ['profileControllerUpdate']

  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } }

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof profileControllerUpdate>>,
    { data: UpdateProfileDto }
  > = props => {
    const { data } = props ?? {}

    return profileControllerUpdate(data)
  }

  return { mutationFn, ...mutationOptions }
}

export type ProfileControllerUpdateMutationResult = NonNullable<Awaited<ReturnType<typeof profileControllerUpdate>>>
export type ProfileControllerUpdateMutationBody = UpdateProfileDto
export type ProfileControllerUpdateMutationError = unknown

export const useProfileControllerUpdate = <TError = unknown, TContext = unknown>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof profileControllerUpdate>>,
      TError,
      { data: UpdateProfileDto },
      TContext
    >
  },
  queryClient?: QueryClient
): UseMutationResult<
  Awaited<ReturnType<typeof profileControllerUpdate>>,
  TError,
  { data: UpdateProfileDto },
  TContext
> => {
  const mutationOptions = getProfileControllerUpdateMutationOptions(options)

  return useMutation(mutationOptions, queryClient)
}

export const profileControllerChangePassword = (changePasswordDto: ChangePasswordDto) => {
  return customInstance<ApiResponse<void>>({
    url: `/profile/change-password`,
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json' },
    data: changePasswordDto
  })
}

export const getProfileControllerChangePasswordMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof profileControllerChangePassword>>,
    TError,
    { data: ChangePasswordDto },
    TContext
  >
}): UseMutationOptions<
  Awaited<ReturnType<typeof profileControllerChangePassword>>,
  TError,
  { data: ChangePasswordDto },
  TContext
> => {
  const mutationKey = ['profileControllerChangePassword']

  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } }

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof profileControllerChangePassword>>,
    { data: ChangePasswordDto }
  > = props => {
    const { data } = props ?? {}

    return profileControllerChangePassword(data)
  }

  return { mutationFn, ...mutationOptions }
}

export type ProfileControllerChangePasswordMutationResult = NonNullable<
  Awaited<ReturnType<typeof profileControllerChangePassword>>
>
export type ProfileControllerChangePasswordMutationBody = ChangePasswordDto
export type ProfileControllerChangePasswordMutationError = unknown

export const useProfileControllerChangePassword = <TError = unknown, TContext = unknown>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof profileControllerChangePassword>>,
      TError,
      { data: ChangePasswordDto },
      TContext
    >
  },
  queryClient?: QueryClient
): UseMutationResult<
  Awaited<ReturnType<typeof profileControllerChangePassword>>,
  TError,
  { data: ChangePasswordDto },
  TContext
> => {
  const mutationOptions = getProfileControllerChangePasswordMutationOptions(options)

  return useMutation(mutationOptions, queryClient)
}
