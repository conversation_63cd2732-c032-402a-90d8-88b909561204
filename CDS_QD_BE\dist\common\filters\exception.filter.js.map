{"version": 3, "sources": ["../../../src/common/filters/exception.filter.ts"], "sourcesContent": ["import { ArgumentsHost, Catch, ExceptionFilter, HttpException, HttpStatus, Logger } from '@nestjs/common';\n\n@Catch()\nexport class AllExceptionsFilter implements ExceptionFilter {\n    catch(exception: any, host: ArgumentsHost) {\n        const ctx = host.switchToHttp();\n        const response = ctx.getResponse();\n        const request = ctx.getRequest();\n        const status = exception instanceof HttpException ? exception.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR;\n        let message = `An error occurred with error code ${status}`;\n        switch (status) {\n            case 500:\n                message = 'Internal server error';\n                break;\n            case 400:\n                message = exception.response.message || exception.message || 'Bad request';\n                break;\n            case 401:\n                message = exception.response.message || exception.message || 'Unauthorized';\n                break;\n            case 403:\n                message = exception.response.message || exception.message || 'Forbidden';\n                break;\n            case 404:\n                message = exception.response.message || exception.message || 'Not found';\n                break;\n            case 409:\n                message = exception.response.message || exception.message || 'Conflict';\n                break;\n            case 417:\n                message = exception.response.message || exception.message || 'Expectation Failed';\n                break;\n            case 422:\n                message = exception.response.message || exception.message || 'Unprocessable Entity';\n                break;\n            default:\n                message = exception.response.message || exception.message || message;\n                break;\n        }\n\n        Logger.error(`[${status}] {${request.url}, ${response?.req?.route?.path}, ${request.method}}: ${exception?.message}`, 'ExceptionFilter');\n        // console.error(exception, exception.stack);\n\n        response.status(status).json({\n            result: false,\n            message: message,\n            data: exception?.response?.data || null,\n            statusCode: status,\n            // path: request.url,\n        });\n    }\n}\n"], "names": ["AllExceptionsFilter", "catch", "exception", "host", "ctx", "switchToHttp", "response", "getResponse", "request", "getRequest", "status", "HttpException", "getStatus", "HttpStatus", "INTERNAL_SERVER_ERROR", "message", "<PERSON><PERSON>", "error", "url", "req", "route", "path", "method", "json", "result", "data", "statusCode"], "mappings": "oGAGaA,6DAAAA,6CAH4E,ocAGlF,IAAA,AAAMA,oBAAN,MAAMA,oBACTC,MAAMC,SAAc,CAAEC,IAAmB,CAAE,CACvC,MAAMC,IAAMD,KAAKE,YAAY,GAC7B,MAAMC,SAAWF,IAAIG,WAAW,GAChC,MAAMC,QAAUJ,IAAIK,UAAU,GAC9B,MAAMC,OAASR,qBAAqBS,qBAAa,CAAGT,UAAUU,SAAS,GAAKC,kBAAU,CAACC,qBAAqB,CAC5G,IAAIC,QAAU,CAAC,kCAAkC,EAAEL,OAAO,CAAC,CAC3D,OAAQA,QACJ,KAAK,IACDK,QAAU,wBACV,KACJ,MAAK,IACDA,QAAUb,UAAUI,QAAQ,CAACS,OAAO,EAAIb,UAAUa,OAAO,EAAI,cAC7D,KACJ,MAAK,IACDA,QAAUb,UAAUI,QAAQ,CAACS,OAAO,EAAIb,UAAUa,OAAO,EAAI,eAC7D,KACJ,MAAK,IACDA,QAAUb,UAAUI,QAAQ,CAACS,OAAO,EAAIb,UAAUa,OAAO,EAAI,YAC7D,KACJ,MAAK,IACDA,QAAUb,UAAUI,QAAQ,CAACS,OAAO,EAAIb,UAAUa,OAAO,EAAI,YAC7D,KACJ,MAAK,IACDA,QAAUb,UAAUI,QAAQ,CAACS,OAAO,EAAIb,UAAUa,OAAO,EAAI,WAC7D,KACJ,MAAK,IACDA,QAAUb,UAAUI,QAAQ,CAACS,OAAO,EAAIb,UAAUa,OAAO,EAAI,qBAC7D,KACJ,MAAK,IACDA,QAAUb,UAAUI,QAAQ,CAACS,OAAO,EAAIb,UAAUa,OAAO,EAAI,uBAC7D,KACJ,SACIA,QAAUb,UAAUI,QAAQ,CAACS,OAAO,EAAIb,UAAUa,OAAO,EAAIA,QAC7D,KACR,CAEAC,cAAM,CAACC,KAAK,CAAC,CAAC,CAAC,EAAEP,OAAO,GAAG,EAAEF,QAAQU,GAAG,CAAC,EAAE,EAAEZ,UAAUa,KAAKC,OAAOC,KAAK,EAAE,EAAEb,QAAQc,MAAM,CAAC,GAAG,EAAEpB,WAAWa,QAAQ,CAAC,CAAE,mBAGtHT,SAASI,MAAM,CAACA,QAAQa,IAAI,CAAC,CACzBC,OAAQ,MACRT,QAASA,QACTU,KAAMvB,WAAWI,UAAUmB,MAAQ,KACnCC,WAAYhB,MAEhB,EACJ,CACJ"}