/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { HoSoPhatTrienDangVienEntity } from '~/database/typeorm/entities/hoSoPhatTrienDangVien.entity';

@Injectable()
export class HoSoPhatTrienDangVienRepository extends Repository<HoSoPhatTrienDangVienEntity> {
    constructor(private dataSource: DataSource) {
        super(HoSoPhatTrienDangVienEntity, dataSource.createEntityManager());
    }
}
