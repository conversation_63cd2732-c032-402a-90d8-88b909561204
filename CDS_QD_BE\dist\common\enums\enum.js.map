{"version": 3, "sources": ["../../../src/common/enums/enum.ts"], "sourcesContent": ["export enum USER_STATUS {\n    ACTIVE = 'ACTIVE',\n    DISABLED = 'DISABLED',\n}\n\nexport enum USER_ROLE {\n    ADMIN = 1,\n    USER = 2,\n}\n\nexport enum SCHEDULE_TYPE {\n    CRON = 'cron',\n    TIMEOUT = 'timeout',\n    INTERVAL = 'interval',\n}\n\nexport enum MEDIA_TYPE {\n    IMAGE = 'IMAGE',\n    VIDEO = 'VIDEO',\n    AUDIO = 'AUDIO',\n    DOCUMENT = 'DOCUMENT',\n    MISC = 'MISC',\n}\n\nexport enum CACHE_TIME {\n    ONE_MINUTE = 60,\n    THIRTY_MINUTES = 1800,\n    ONE_HOUR = 3600,\n    ONE_DAY = 86400,\n    ONE_WEEK = 604800,\n    ONE_MONTH = 2592000,\n    ONE_YEAR = 31536000,\n}\n"], "names": ["CACHE_TIME", "MEDIA_TYPE", "SCHEDULE_TYPE", "USER_ROLE", "USER_STATUS"], "mappings": "mPAwBYA,oBAAAA,gBARAC,oBAAAA,gBANAC,uBAAAA,mBALAC,mBAAAA,eALAC,qBAAAA,eAAL,IAAA,AAAKA,kCAAAA,sFAAAA,iBAKL,IAAA,AAAKD,gCAAAA,gGAAAA,eAKL,IAAA,AAAKD,oCAAAA,2HAAAA,mBAML,IAAA,AAAKD,iCAAAA,mKAAAA,gBAQL,IAAA,AAAKD,iCAAAA,8YAAAA"}