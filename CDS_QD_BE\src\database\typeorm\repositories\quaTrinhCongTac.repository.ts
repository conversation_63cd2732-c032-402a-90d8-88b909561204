/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { QuaTrinhCongTacEntity } from '~/database/typeorm/entities/quaTrinhCongTac.entity';

@Injectable()
export class QuaTrinhCongTacRepository extends Repository<QuaTrinhCongTacEntity> {
    constructor(private dataSource: DataSource) {
        super(QuaTrinhCongTacEntity, dataSource.createEntityManager());
    }
}
