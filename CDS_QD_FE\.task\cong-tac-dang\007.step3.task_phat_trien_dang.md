# Quy trình xây dựng giao diện trang "Chi tiết Quy trình Phát triển Đảng viên"

---

**Trang: Chi tiết Quy trình Phát triển Đảng viên (`/${locale}/cong-tac-dang/phat-trien-dang-vien/chi-tiet/{idHoSoPhatTrien}`)**

**IV. Khu vực "Chi tiết Bước Hiện tại/Đ<PERSON><PERSON><PERSON> chọn"**

**Bước 3: Bồi dưỡng Nhận thức về Đảng**

- **Tên Bước Hiển thị trên Stepper Chính:** `Học nhận thức về Đảng`
- **Mục đích của Bước:** Quần chúng ưu tú tham gia lớp học bồi dưỡng nhận thức về Đảng để trang bị những kiến thức c<PERSON> b<PERSON>, hiểu biết về Điều l<PERSON>, <PERSON><PERSON><PERSON><PERSON>, m<PERSON><PERSON>i<PERSON>, l<PERSON> tưởng của <PERSON>. <PERSON><PERSON><PERSON> quả học tập là một trong những điều kiện để xem xét kết nạp.
- **Trạng thái Quy trình (`DmTrangThaiPhatTrienDang.ID`) tương ứng:** Ví dụ ID cho "Đang Học Lớp Nhận thức" hoặc "Chờ Kết quả Học Lớp Nhận thức".

- **Hành động/Nội dung chính trong Bước 3:**

  1.  **Thông tin Lớp học Bồi dưỡng Nhận thức về Đảng:**
      - **Trường nhập liệu/hiển thị "Tên Lớp học/Khóa học":** (Ví dụ: "Lớp Bồi dưỡng Nhận thức về Đảng Khóa II/2024").
      - **Trường nhập liệu/hiển thị "Đơn vị Tổ chức Lớp":** (Ví dụ: "Trung tâm Bồi dưỡng Chính trị Quận X", "Đảng ủy Quân đoàn Y").
      - **Trường chọn ngày "Thời gian Học (Từ ngày - Đến ngày)":**
      - **(Tùy chọn) Trường nhập liệu "Địa điểm Học":**
  2.  **Kết quả Học tập:**
      - **Trường nhập liệu/hiển thị "Kết quả/Điểm số (nếu có)":**
      - **Trường lựa chọn/hiển thị "Xếp loại (nếu có)":** (Ví dụ: Giỏi, Khá, Đạt).
  3.  **Ghi nhận việc hoàn thành lớp học:**
      - Checkbox hoặc nút "Xác nhận đã hoàn thành lớp học".

- **Danh sách Tài liệu Yêu cầu/Đã nộp/Phát sinh (Fix cứng hoặc tùy chọn cho Bước 3):**

  1.  **Loại Tài liệu:** "Giấy chứng nhận Hoàn thành Lớp Bồi dưỡng Nhận thức về Đảng"
      - **Trạng thái:** "Chưa nộp" / "Đã nộp" (ngày nộp).
      - **File đính kèm:** (Nếu đã nộp) Hiển thị tên file với nút "Xem/Tải".
      - **Hành động:** (Cho người quản lý hồ sơ hoặc quần chúng tự nộp nếu quy trình cho phép) Nút "Nộp Giấy chứng nhận" (mở giao diện upload file, tự động gán `LoaiTaiLieuPhatTrienID` là "Giấy chứng nhận học lớp nhận thức").
  2.  **(Tùy chọn) Loại Tài liệu:** "Bài thu hoạch Lớp Bồi dưỡng Nhận thức về Đảng"
      - **Trạng thái:** "Chưa nộp" / "Đã nộp" (ngày nộp).
      - **File đính kèm:** (Nếu đã nộp) Hiển thị tên file với nút "Xem/Tải".
      - **Hành động:** Nút "Nộp Bài thu hoạch".

- **Nút Hành động cho Bước 3 (Tùy theo vai trò người dùng và trạng thái hiện tại của hồ sơ):**
  - **Nút "Lưu thông tin Lớp học & Kết quả":**
    - **Hành động:** Lưu các thông tin đã nhập/chỉnh sửa ở trên (Tên lớp, Đơn vị tổ chức, Thời gian, Kết quả, Xếp loại).
    - **Logic:** Cập nhật các trường tương ứng trong bảng `HoSoPhatTrienDangVien` (có thể cần thêm các trường mới vào bảng này để lưu thông tin lớp học, hoặc tạo bảng riêng nếu thông tin lớp học phức tạp và dùng chung cho nhiều người). _Để đơn giản trong prototype ban đầu, có thể thêm các trường này vào `HoSoPhatTrienDangVien`._
  - **Nút "Hoàn thành Bồi dưỡng Nhận thức & Chuyển sang Giới thiệu vào Đảng":**
    - **Điều kiện hiển thị:** Khi đã xác nhận hoàn thành lớp học và các tài liệu cần thiết (ví dụ: Giấy chứng nhận) đã được nộp.
    - **Hành động:**
      - Lưu lại thông tin lớp học và kết quả (nếu có thay đổi chưa lưu).
      - Cập nhật `HoSoPhatTrienDangVien.TrangThaiQuyTrinhID` sang ID của bước "Đảng viên Chính thức Giới thiệu".
      - Hiển thị thông báo thành công.
      - Giao diện tự động chuyển sang hiển thị chi tiết của Bước 4.
  - **Nút "Học lại/Chưa đạt":**
    - **Điều kiện hiển thị:** Nếu kết quả học tập không đạt.
    - **Hành động:** Mở dialog yêu cầu nhập lý do/kế hoạch học lại, có thể giữ nguyên trạng thái "Đang Học Lớp Nhận thức" hoặc chuyển sang một trạng thái "Chờ Học lại", thông báo cho đối tượng.

---

**II. Bảng Dữ liệu Liên quan Chính cho Bước 3:**

- **Bảng `HoSoPhatTrienDangVien`:**

  - `ID: number`
  - `TrangThaiQuyTrinhID: number` (Sẽ được cập nhật khi chuyển bước)
  - **(Các trường mới có thể cần thêm để lưu thông tin lớp học, nếu không tạo bảng riêng):**
    - `TenLopHocNhanThuc: string` (NVARCHAR(255) NULL)
    - `DonViToChucLopNhanThuc: string` (NVARCHAR(255) NULL)
    - `ThoiGianHocNhanThucTu: date` (DATE NULL)
    - `ThoiGianHocNhanThucDen: date` (DATE NULL)
    - `KetQuaHocNhanThuc: string` (VARCHAR(100) NULL) (Ví dụ: "8.5", "Giỏi")
    - `DaHoanThanhLopNhanThuc: boolean` (BOOLEAN DEFAULT FALSE)

- **Bảng `TaiLieuPhatTrienDangVien`:**

  - `HoSoPhatTrienID: number`
  - `LoaiTaiLieuPhatTrienID: number` (FK đến `DmLoaiTaiLieuPhatTrienDang` - ID của "Giấy chứng nhận Hoàn thành Lớp Bồi dưỡng Nhận thức về Đảng", "Bài thu hoạch Lớp Bồi dưỡng...")
  - `FileURL: string`
  - `TenTaiLieu: string`
  - `NgayNopTaiLieu: date`

- **Danh mục `DmLoaiTaiLieuPhatTrienDang`:** Cần có các loại tài liệu tương ứng với bước này.
- **Danh mục `DmTrangThaiPhatTrienDang`:** Cần có các trạng thái như "Đang Học Lớp Nhận thức", "Chờ Kết quả Học Lớp Nhận thức", "Đã hoàn thành Lớp Nhận thức".

---
