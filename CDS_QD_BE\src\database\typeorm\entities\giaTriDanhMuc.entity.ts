import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany, PrimaryGeneratedColumn, Relation } from 'typeorm';
import { AbstractEntity } from './abstract.entity';
import { LoaiDanhMucEntity } from './loaiDanhMuc.entity';

@Entity({ name: 'gia_tri_danh_mucs' })
export class GiaTriDanhMucEntity extends AbstractEntity {
    @PrimaryGeneratedColumn('increment', { name: 'id', type: 'int', unsigned: true })
    id: number;

    @Column({ name: 'danh_muc_id', type: 'int', unsigned: true, nullable: false })
    danhMucId: number;

    @Column({ name: 'ma_gia_tri', type: 'varchar', length: 100, nullable: false })
    maGiaTri: string;

    @Column({ name: 'ten_gia_tri', type: 'varchar', length: 255, nullable: false })
    tenGiaTri: string;

    @Column({ name: 'gia_tri_cha_id', type: 'int', unsigned: true, nullable: true })
    giaTriChaId?: number;

    @Column({ name: 'mo_ta', type: 'varchar', length: 1000, nullable: true })
    moTa?: string;

    @Column({ name: 'thu_tu_hien_thi', type: 'int', default: 0 })
    thuTuHienThi: number;

    @Column({ name: 'con_hieu_luc', type: 'boolean', default: true })
    conHieuLuc: boolean;

    @Column({ name: 'thuoc_tinh_mo_rong', type: 'jsonb', nullable: true })
    thuocTinhMoRong?: Record<string, any>;

    /* RELATION */
    @ManyToOne(() => LoaiDanhMucEntity, { onDelete: 'CASCADE' })
    @JoinColumn({ name: 'danh_muc_id', referencedColumnName: 'id' })
    danhMuc: Relation<LoaiDanhMucEntity>;

    @ManyToOne(() => GiaTriDanhMucEntity, { onDelete: 'SET NULL' })
    @JoinColumn({ name: 'gia_tri_cha_id', referencedColumnName: 'id' })
    giaTriCha?: Relation<GiaTriDanhMucEntity>;

    @OneToMany(() => GiaTriDanhMucEntity, (giaTri) => giaTri.giaTriCha)
    giaTriCon: Relation<GiaTriDanhMucEntity[]>;
}
