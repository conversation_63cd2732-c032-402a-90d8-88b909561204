"use strict";Object.defineProperty(exports,"__esModule",{value:true});Object.defineProperty(exports,"LogMiddleware",{enumerable:true,get:function(){return LogMiddleware}});const _common=require("@nestjs/common");const _databaseservice=require("../../database/typeorm/database.service");const _services=require("../../shared/services");function _ts_decorate(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect==="object"&&typeof Reflect.decorate==="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)if(d=decorators[i])r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r;return c>3&&r&&Object.defineProperty(target,key,r),r}function _ts_metadata(k,v){if(typeof Reflect==="object"&&typeof Reflect.metadata==="function")return Reflect.metadata(k,v)}let LogMiddleware=class LogMiddleware{use(req,res,next){const{ip,method,originalUrl}=req;const userAgent=req.get("user-agent")||"";res.on("finish",()=>{const{statusCode}=res;_common.Logger.log(`[${statusCode}] ${originalUrl}, ${method} - ${userAgent} - ${ip}`)});next()}constructor(database,utilService){this.database=database;this.utilService=utilService}};LogMiddleware=_ts_decorate([(0,_common.Injectable)(),_ts_metadata("design:type",Function),_ts_metadata("design:paramtypes",[typeof _databaseservice.DatabaseService==="undefined"?Object:_databaseservice.DatabaseService,typeof _services.UtilService==="undefined"?Object:_services.UtilService])],LogMiddleware);
//# sourceMappingURL=log.middleware.js.map