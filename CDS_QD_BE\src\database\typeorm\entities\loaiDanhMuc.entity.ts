import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { AbstractEntity } from './abstract.entity';

@Entity({ name: 'loai_danh_mucs' })
export class LoaiDanhMucEntity extends AbstractEntity {
    @PrimaryGeneratedColumn('increment', { name: 'id', type: 'int', unsigned: true })
    id: number;

    @Column({ name: 'ma_danh_muc', type: 'varchar', length: 50, unique: true, nullable: false })
    maDanhMuc: string;

    @Column({ name: 'ten_danh_muc', type: 'varchar', length: 255, nullable: false })
    tenDanhMuc: string;

    @Column({ name: 'mo_ta', type: 'varchar', length: 500, nullable: true })
    moTa?: string;

    @Column({ name: 'is_cau_truc_cay', type: 'boolean', default: false })
    isCauTrucCay: boolean;

    @Column({ name: 'modules_su_dung', type: 'varchar', length: 255, nullable: true })
    modulesSuDung?: string;
}
