/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { KeHoachSinhHoatDangEntity } from '~/database/typeorm/entities/keHoachSinhHoatDang.entity';

@Injectable()
export class KeHoachSinhHoatDangRepository extends Repository<KeHoachSinhHoatDangEntity> {
    constructor(private dataSource: DataSource) {
        super(KeHoachSinhHoatDangEntity, dataSource.createEntityManager());
    }
}
