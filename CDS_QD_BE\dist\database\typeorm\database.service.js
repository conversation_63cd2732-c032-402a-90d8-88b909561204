"use strict";Object.defineProperty(exports,"__esModule",{value:true});Object.defineProperty(exports,"DatabaseService",{enumerable:true,get:function(){return DatabaseService}});const _common=require("@nestjs/common");const _enum=require("../../common/enums/enum");const _accountrepository=require("./repositories/account.repository");const _departmentrepository=require("./repositories/department.repository");const _mediarepository=require("./repositories/media.repository");const _permissionrepository=require("./repositories/permission.repository");const _providerrepository=require("./repositories/provider.repository");const _rolerepository=require("./repositories/role.repository");const _userrepository=require("./repositories/user.repository");const _userLogrepository=require("./repositories/userLog.repository");const _warehouserepository=require("./repositories/warehouse.repository");const _warehouseTyperepository=require("./repositories/warehouseType.repository");const _cacheservice=require("../../shared/services/cache.service");const _loaiDanhMucrepository=require("./repositories/loaiDanhMuc.repository");const _giaTriDanhMucrepository=require("./repositories/giaTriDanhMuc.repository");const _thamSoHeThongrepository=require("./repositories/thamSoHeThong.repository");const _quanNhanrepository=require("./repositories/quanNhan.repository");const _lyLichCanNhanrepository=require("./repositories/lyLichCanNhan.repository");const _quaTrinhCongTacrepository=require("./repositories/quaTrinhCongTac.repository");const _quaTrinhDaoTaorepository=require("./repositories/quaTrinhDaoTao.repository");const _deXuatKhenThuongrepository=require("./repositories/deXuatKhenThuong.repository");const _quyetDinhKhenThuongrepository=require("./repositories/quyetDinhKhenThuong.repository");const _hoSoVuViecKyLuatrepository=require("./repositories/hoSoVuViecKyLuat.repository");const _bienBanHoiDongKyLuatrepository=require("./repositories/bienBanHoiDongKyLuat.repository");const _taiLieuVuViecKyLuatrepository=require("./repositories/taiLieuVuViecKyLuat.repository");const _quyetDinhKyLuatrepository=require("./repositories/quyetDinhKyLuat.repository");const _hoSoSucKhoerepository=require("./repositories/hoSoSucKhoe.repository");const _quanHeGiaDinhrepository=require("./repositories/quanHeGiaDinh.repository");const _theoDoiCheDoChinhSachrepository=require("./repositories/theoDoiCheDoChinhSach.repository");const _dangVienrepository=require("./repositories/dangVien.repository");const _hoSoDangVienrepository=require("./repositories/hoSoDangVien.repository");const _deXuatKhenThuongDangVienrepository=require("./repositories/deXuatKhenThuongDangVien.repository");const _quyetDinhKhenThuongDangVienrepository=require("./repositories/quyetDinhKhenThuongDangVien.repository");const _banKiemDiemDangVienrepository=require("./repositories/banKiemDiemDangVien.repository");const _hoSoVuViecKyLuatDangVienrepository=require("./repositories/hoSoVuViecKyLuatDangVien.repository");const _bienBanHoiDongKyLuatDangrepository=require("./repositories/bienBanHoiDongKyLuatDang.repository");const _quyetDinhKyLuatDangVienrepository=require("./repositories/quyetDinhKyLuatDangVien.repository");const _capUyNhiemKyrepository=require("./repositories/capUyNhiemKy.repository");const _thanhVienCapUyrepository=require("./repositories/thanhVienCapUy.repository");const _keHoachSinhHoatDangrepository=require("./repositories/keHoachSinhHoatDang.repository");const _buoiSinhHoatDangrepository=require("./repositories/buoiSinhHoatDang.repository");const _nghiQuyetDangrepository=require("./repositories/nghiQuyetDang.repository");const _hoSoPhatTrienDangVienrepository=require("./repositories/hoSoPhatTrienDangVien.repository");const _danhGiaXepLoaiDangVienrepository=require("./repositories/danhGiaXepLoaiDangVien.repository");const _danhGiaXepLoaiToChucDangrepository=require("./repositories/danhGiaXepLoaiToChucDang.repository");const _thuChiDangPhirepository=require("./repositories/thuChiDangPhi.repository");const _keHoachKiemTraGiamSatDangrepository=require("./repositories/keHoachKiemTraGiamSatDang.repository");const _cuocKiemTraGiamSatDangrepository=require("./repositories/cuocKiemTraGiamSatDang.repository");const _keHoachGiaoDucChinhTrirepository=require("./repositories/keHoachGiaoDucChinhTri.repository");const _hoatDongGiaoDucChinhTrirepository=require("./repositories/hoatDongGiaoDucChinhTri.repository");const _thamGiaGiaoDucChinhTrirepository=require("./repositories/thamGiaGiaoDucChinhTri.repository");const _ketQuaHocTapChinhTrirepository=require("./repositories/ketQuaHocTapChinhTri.repository");const _ghiNhanTinhHinhTuTuongrepository=require("./repositories/ghiNhanTinhHinhTuTuong.repository");const _bienPhapTacDongTuTuongrepository=require("./repositories/bienPhapTacDongTuTuong.repository");const _keHoachTuyenTruyenVanHoarepository=require("./repositories/keHoachTuyenTruyenVanHoa.repository");const _hoatDongTuyenTruyenVanHoarepository=require("./repositories/hoatDongTuyenTruyenVanHoa.repository");const _phongTraoThiDuaChinhTrirepository=require("./repositories/phongTraoThiDuaChinhTri.repository");const _chinhSachHauPhuongrepository=require("./repositories/chinhSachHauPhuong.repository");const _doiTuongChinhSachHauPhuongrepository=require("./repositories/doiTuongChinhSachHauPhuong.repository");const _thongBaorepository=require("./repositories/thongBao.repository");const _quyTrinhPheDuyetrepository=require("./repositories/quyTrinhPheDuyet.repository");const _yeuCauPheDuyetrepository=require("./repositories/yeuCauPheDuyet.repository");const _buocPheDuyetrepository=require("./repositories/buocPheDuyet.repository");const _lichSuPheDuyetrepository=require("./repositories/lichSuPheDuyet.repository");const _nhatKyHeThongrepository=require("./repositories/nhatKyHeThong.repository");const _duLieuTepTinrepository=require("./repositories/duLieuTepTin.repository");const _usersRolerepository=require("./repositories/usersRole.repository");function _ts_decorate(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect==="object"&&typeof Reflect.decorate==="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)if(d=decorators[i])r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r;return c>3&&r&&Object.defineProperty(target,key,r),r}function _ts_metadata(k,v){if(typeof Reflect==="object"&&typeof Reflect.metadata==="function")return Reflect.metadata(k,v)}let DatabaseService=class DatabaseService{loadDepartmentsToCache(){this.department.find().then(departments=>{departments.forEach(department=>{this.cacheService.setJson(`department:${department.id}`,department,_enum.CACHE_TIME.ONE_MONTH)})})}loadPermissionsByRoleToCache(){this.role.find({relations:["permissions"]}).then(roles=>{roles.forEach(role=>{this.cacheService.setJson(`permissions:${role.id}`,role.permissions.map(p=>p.action),_enum.CACHE_TIME.ONE_MONTH)})})}constructor(department,user,account,media,permission,role,userLog,warehouse,warehouseType,provider,cacheService,loaiDanhMuc,giaTriDanhMuc,thamSoHeThong,quanNhan,lyLichCanNhan,quaTrinhCongTac,quaTrinhDaoTao,deXuatKhenThuong,quyetDinhKhenThuong,hoSoVuViecKyLuat,bienBanHoiDongKyLuat,taiLieuVuViecKyLuat,quyetDinhKyLuat,hoSoSucKhoe,quanHeGiaDinh,theoDoiCheDoChinhSach,dangVien,hoSoDangVien,deXuatKhenThuongDangVien,quyetDinhKhenThuongDangVien,banKiemDiemDangVien,hoSoVuViecKyLuatDangVien,bienBanHoiDongKyLuatDang,quyetDinhKyLuatDangVien,capUyNhiemKy,thanhVienCapUy,keHoachSinhHoatDang,buoiSinhHoatDang,nghiQuyetDang,hoSoPhatTrienDangVien,danhGiaXepLoaiDangVien,danhGiaXepLoaiToChucDang,thuChiDangPhi,keHoachKiemTraGiamSatDang,cuocKiemTraGiamSatDang,keHoachGiaoDucChinhTri,hoatDongGiaoDucChinhTri,thamGiaGiaoDucChinhTri,ketQuaHocTapChinhTri,ghiNhanTinhHinhTuTuong,bienPhapTacDongTuTuong,keHoachTuyenTruyenVanHoa,hoatDongTuyenTruyenVanHoa,phongTraoThiDuaChinhTri,chinhSachHauPhuong,doiTuongChinhSachHauPhuong,thongBao,quyTrinhPheDuyet,yeuCauPheDuyet,buocPheDuyet,lichSuPheDuyet,nhatKyHeThong,duLieuTepTin,usersRole){this.department=department;this.user=user;this.account=account;this.media=media;this.permission=permission;this.role=role;this.userLog=userLog;this.warehouse=warehouse;this.warehouseType=warehouseType;this.provider=provider;this.cacheService=cacheService;this.loaiDanhMuc=loaiDanhMuc;this.giaTriDanhMuc=giaTriDanhMuc;this.thamSoHeThong=thamSoHeThong;this.quanNhan=quanNhan;this.lyLichCanNhan=lyLichCanNhan;this.quaTrinhCongTac=quaTrinhCongTac;this.quaTrinhDaoTao=quaTrinhDaoTao;this.deXuatKhenThuong=deXuatKhenThuong;this.quyetDinhKhenThuong=quyetDinhKhenThuong;this.hoSoVuViecKyLuat=hoSoVuViecKyLuat;this.bienBanHoiDongKyLuat=bienBanHoiDongKyLuat;this.taiLieuVuViecKyLuat=taiLieuVuViecKyLuat;this.quyetDinhKyLuat=quyetDinhKyLuat;this.hoSoSucKhoe=hoSoSucKhoe;this.quanHeGiaDinh=quanHeGiaDinh;this.theoDoiCheDoChinhSach=theoDoiCheDoChinhSach;this.dangVien=dangVien;this.hoSoDangVien=hoSoDangVien;this.deXuatKhenThuongDangVien=deXuatKhenThuongDangVien;this.quyetDinhKhenThuongDangVien=quyetDinhKhenThuongDangVien;this.banKiemDiemDangVien=banKiemDiemDangVien;this.hoSoVuViecKyLuatDangVien=hoSoVuViecKyLuatDangVien;this.bienBanHoiDongKyLuatDang=bienBanHoiDongKyLuatDang;this.quyetDinhKyLuatDangVien=quyetDinhKyLuatDangVien;this.capUyNhiemKy=capUyNhiemKy;this.thanhVienCapUy=thanhVienCapUy;this.keHoachSinhHoatDang=keHoachSinhHoatDang;this.buoiSinhHoatDang=buoiSinhHoatDang;this.nghiQuyetDang=nghiQuyetDang;this.hoSoPhatTrienDangVien=hoSoPhatTrienDangVien;this.danhGiaXepLoaiDangVien=danhGiaXepLoaiDangVien;this.danhGiaXepLoaiToChucDang=danhGiaXepLoaiToChucDang;this.thuChiDangPhi=thuChiDangPhi;this.keHoachKiemTraGiamSatDang=keHoachKiemTraGiamSatDang;this.cuocKiemTraGiamSatDang=cuocKiemTraGiamSatDang;this.keHoachGiaoDucChinhTri=keHoachGiaoDucChinhTri;this.hoatDongGiaoDucChinhTri=hoatDongGiaoDucChinhTri;this.thamGiaGiaoDucChinhTri=thamGiaGiaoDucChinhTri;this.ketQuaHocTapChinhTri=ketQuaHocTapChinhTri;this.ghiNhanTinhHinhTuTuong=ghiNhanTinhHinhTuTuong;this.bienPhapTacDongTuTuong=bienPhapTacDongTuTuong;this.keHoachTuyenTruyenVanHoa=keHoachTuyenTruyenVanHoa;this.hoatDongTuyenTruyenVanHoa=hoatDongTuyenTruyenVanHoa;this.phongTraoThiDuaChinhTri=phongTraoThiDuaChinhTri;this.chinhSachHauPhuong=chinhSachHauPhuong;this.doiTuongChinhSachHauPhuong=doiTuongChinhSachHauPhuong;this.thongBao=thongBao;this.quyTrinhPheDuyet=quyTrinhPheDuyet;this.yeuCauPheDuyet=yeuCauPheDuyet;this.buocPheDuyet=buocPheDuyet;this.lichSuPheDuyet=lichSuPheDuyet;this.nhatKyHeThong=nhatKyHeThong;this.duLieuTepTin=duLieuTepTin;this.usersRole=usersRole}};DatabaseService=_ts_decorate([(0,_common.Injectable)(),_ts_metadata("design:type",Function),_ts_metadata("design:paramtypes",[typeof _departmentrepository.DepartmentRepository==="undefined"?Object:_departmentrepository.DepartmentRepository,typeof _userrepository.UserRepository==="undefined"?Object:_userrepository.UserRepository,typeof _accountrepository.AccountRepository==="undefined"?Object:_accountrepository.AccountRepository,typeof _mediarepository.MediaRepository==="undefined"?Object:_mediarepository.MediaRepository,typeof _permissionrepository.PermissionRepository==="undefined"?Object:_permissionrepository.PermissionRepository,typeof _rolerepository.RoleRepository==="undefined"?Object:_rolerepository.RoleRepository,typeof _userLogrepository.UserLogRepository==="undefined"?Object:_userLogrepository.UserLogRepository,typeof _warehouserepository.WarehouseRepository==="undefined"?Object:_warehouserepository.WarehouseRepository,typeof _warehouseTyperepository.WarehouseTypeRepository==="undefined"?Object:_warehouseTyperepository.WarehouseTypeRepository,typeof _providerrepository.ProviderRepository==="undefined"?Object:_providerrepository.ProviderRepository,typeof _cacheservice.CacheService==="undefined"?Object:_cacheservice.CacheService,typeof _loaiDanhMucrepository.LoaiDanhMucRepository==="undefined"?Object:_loaiDanhMucrepository.LoaiDanhMucRepository,typeof _giaTriDanhMucrepository.GiaTriDanhMucRepository==="undefined"?Object:_giaTriDanhMucrepository.GiaTriDanhMucRepository,typeof _thamSoHeThongrepository.ThamSoHeThongRepository==="undefined"?Object:_thamSoHeThongrepository.ThamSoHeThongRepository,typeof _quanNhanrepository.QuanNhanRepository==="undefined"?Object:_quanNhanrepository.QuanNhanRepository,typeof _lyLichCanNhanrepository.LyLichCanNhanRepository==="undefined"?Object:_lyLichCanNhanrepository.LyLichCanNhanRepository,typeof _quaTrinhCongTacrepository.QuaTrinhCongTacRepository==="undefined"?Object:_quaTrinhCongTacrepository.QuaTrinhCongTacRepository,typeof _quaTrinhDaoTaorepository.QuaTrinhDaoTaoRepository==="undefined"?Object:_quaTrinhDaoTaorepository.QuaTrinhDaoTaoRepository,typeof _deXuatKhenThuongrepository.DeXuatKhenThuongRepository==="undefined"?Object:_deXuatKhenThuongrepository.DeXuatKhenThuongRepository,typeof _quyetDinhKhenThuongrepository.QuyetDinhKhenThuongRepository==="undefined"?Object:_quyetDinhKhenThuongrepository.QuyetDinhKhenThuongRepository,typeof _hoSoVuViecKyLuatrepository.HoSoVuViecKyLuatRepository==="undefined"?Object:_hoSoVuViecKyLuatrepository.HoSoVuViecKyLuatRepository,typeof _bienBanHoiDongKyLuatrepository.BienBanHoiDongKyLuatRepository==="undefined"?Object:_bienBanHoiDongKyLuatrepository.BienBanHoiDongKyLuatRepository,typeof _taiLieuVuViecKyLuatrepository.TaiLieuVuViecKyLuatRepository==="undefined"?Object:_taiLieuVuViecKyLuatrepository.TaiLieuVuViecKyLuatRepository,typeof _quyetDinhKyLuatrepository.QuyetDinhKyLuatRepository==="undefined"?Object:_quyetDinhKyLuatrepository.QuyetDinhKyLuatRepository,typeof _hoSoSucKhoerepository.HoSoSucKhoeRepository==="undefined"?Object:_hoSoSucKhoerepository.HoSoSucKhoeRepository,typeof _quanHeGiaDinhrepository.QuanHeGiaDinhRepository==="undefined"?Object:_quanHeGiaDinhrepository.QuanHeGiaDinhRepository,typeof _theoDoiCheDoChinhSachrepository.TheoDoiCheDoChinhSachRepository==="undefined"?Object:_theoDoiCheDoChinhSachrepository.TheoDoiCheDoChinhSachRepository,typeof _dangVienrepository.DangVienRepository==="undefined"?Object:_dangVienrepository.DangVienRepository,typeof _hoSoDangVienrepository.HoSoDangVienRepository==="undefined"?Object:_hoSoDangVienrepository.HoSoDangVienRepository,typeof _deXuatKhenThuongDangVienrepository.DeXuatKhenThuongDangVienRepository==="undefined"?Object:_deXuatKhenThuongDangVienrepository.DeXuatKhenThuongDangVienRepository,typeof _quyetDinhKhenThuongDangVienrepository.QuyetDinhKhenThuongDangVienRepository==="undefined"?Object:_quyetDinhKhenThuongDangVienrepository.QuyetDinhKhenThuongDangVienRepository,typeof _banKiemDiemDangVienrepository.BanKiemDiemDangVienRepository==="undefined"?Object:_banKiemDiemDangVienrepository.BanKiemDiemDangVienRepository,typeof _hoSoVuViecKyLuatDangVienrepository.HoSoVuViecKyLuatDangVienRepository==="undefined"?Object:_hoSoVuViecKyLuatDangVienrepository.HoSoVuViecKyLuatDangVienRepository,typeof _bienBanHoiDongKyLuatDangrepository.BienBanHoiDongKyLuatDangRepository==="undefined"?Object:_bienBanHoiDongKyLuatDangrepository.BienBanHoiDongKyLuatDangRepository,typeof _quyetDinhKyLuatDangVienrepository.QuyetDinhKyLuatDangVienRepository==="undefined"?Object:_quyetDinhKyLuatDangVienrepository.QuyetDinhKyLuatDangVienRepository,typeof _capUyNhiemKyrepository.CapUyNhiemKyRepository==="undefined"?Object:_capUyNhiemKyrepository.CapUyNhiemKyRepository,typeof _thanhVienCapUyrepository.ThanhVienCapUyRepository==="undefined"?Object:_thanhVienCapUyrepository.ThanhVienCapUyRepository,typeof _keHoachSinhHoatDangrepository.KeHoachSinhHoatDangRepository==="undefined"?Object:_keHoachSinhHoatDangrepository.KeHoachSinhHoatDangRepository,typeof _buoiSinhHoatDangrepository.BuoiSinhHoatDangRepository==="undefined"?Object:_buoiSinhHoatDangrepository.BuoiSinhHoatDangRepository,typeof _nghiQuyetDangrepository.NghiQuyetDangRepository==="undefined"?Object:_nghiQuyetDangrepository.NghiQuyetDangRepository,typeof _hoSoPhatTrienDangVienrepository.HoSoPhatTrienDangVienRepository==="undefined"?Object:_hoSoPhatTrienDangVienrepository.HoSoPhatTrienDangVienRepository,typeof _danhGiaXepLoaiDangVienrepository.DanhGiaXepLoaiDangVienRepository==="undefined"?Object:_danhGiaXepLoaiDangVienrepository.DanhGiaXepLoaiDangVienRepository,typeof _danhGiaXepLoaiToChucDangrepository.DanhGiaXepLoaiToChucDangRepository==="undefined"?Object:_danhGiaXepLoaiToChucDangrepository.DanhGiaXepLoaiToChucDangRepository,typeof _thuChiDangPhirepository.ThuChiDangPhiRepository==="undefined"?Object:_thuChiDangPhirepository.ThuChiDangPhiRepository,typeof _keHoachKiemTraGiamSatDangrepository.KeHoachKiemTraGiamSatDangRepository==="undefined"?Object:_keHoachKiemTraGiamSatDangrepository.KeHoachKiemTraGiamSatDangRepository,typeof _cuocKiemTraGiamSatDangrepository.CuocKiemTraGiamSatDangRepository==="undefined"?Object:_cuocKiemTraGiamSatDangrepository.CuocKiemTraGiamSatDangRepository,typeof _keHoachGiaoDucChinhTrirepository.KeHoachGiaoDucChinhTriRepository==="undefined"?Object:_keHoachGiaoDucChinhTrirepository.KeHoachGiaoDucChinhTriRepository,typeof _hoatDongGiaoDucChinhTrirepository.HoatDongGiaoDucChinhTriRepository==="undefined"?Object:_hoatDongGiaoDucChinhTrirepository.HoatDongGiaoDucChinhTriRepository,typeof _thamGiaGiaoDucChinhTrirepository.ThamGiaGiaoDucChinhTriRepository==="undefined"?Object:_thamGiaGiaoDucChinhTrirepository.ThamGiaGiaoDucChinhTriRepository,typeof _ketQuaHocTapChinhTrirepository.KetQuaHocTapChinhTriRepository==="undefined"?Object:_ketQuaHocTapChinhTrirepository.KetQuaHocTapChinhTriRepository,typeof _ghiNhanTinhHinhTuTuongrepository.GhiNhanTinhHinhTuTuongRepository==="undefined"?Object:_ghiNhanTinhHinhTuTuongrepository.GhiNhanTinhHinhTuTuongRepository,typeof _bienPhapTacDongTuTuongrepository.BienPhapTacDongTuTuongRepository==="undefined"?Object:_bienPhapTacDongTuTuongrepository.BienPhapTacDongTuTuongRepository,typeof _keHoachTuyenTruyenVanHoarepository.KeHoachTuyenTruyenVanHoaRepository==="undefined"?Object:_keHoachTuyenTruyenVanHoarepository.KeHoachTuyenTruyenVanHoaRepository,typeof _hoatDongTuyenTruyenVanHoarepository.HoatDongTuyenTruyenVanHoaRepository==="undefined"?Object:_hoatDongTuyenTruyenVanHoarepository.HoatDongTuyenTruyenVanHoaRepository,typeof _phongTraoThiDuaChinhTrirepository.PhongTraoThiDuaChinhTriRepository==="undefined"?Object:_phongTraoThiDuaChinhTrirepository.PhongTraoThiDuaChinhTriRepository,typeof _chinhSachHauPhuongrepository.ChinhSachHauPhuongRepository==="undefined"?Object:_chinhSachHauPhuongrepository.ChinhSachHauPhuongRepository,typeof _doiTuongChinhSachHauPhuongrepository.DoiTuongChinhSachHauPhuongRepository==="undefined"?Object:_doiTuongChinhSachHauPhuongrepository.DoiTuongChinhSachHauPhuongRepository,typeof _thongBaorepository.ThongBaoRepository==="undefined"?Object:_thongBaorepository.ThongBaoRepository,typeof _quyTrinhPheDuyetrepository.QuyTrinhPheDuyetRepository==="undefined"?Object:_quyTrinhPheDuyetrepository.QuyTrinhPheDuyetRepository,typeof _yeuCauPheDuyetrepository.YeuCauPheDuyetRepository==="undefined"?Object:_yeuCauPheDuyetrepository.YeuCauPheDuyetRepository,typeof _buocPheDuyetrepository.BuocPheDuyetRepository==="undefined"?Object:_buocPheDuyetrepository.BuocPheDuyetRepository,typeof _lichSuPheDuyetrepository.LichSuPheDuyetRepository==="undefined"?Object:_lichSuPheDuyetrepository.LichSuPheDuyetRepository,typeof _nhatKyHeThongrepository.NhatKyHeThongRepository==="undefined"?Object:_nhatKyHeThongrepository.NhatKyHeThongRepository,typeof _duLieuTepTinrepository.DuLieuTepTinRepository==="undefined"?Object:_duLieuTepTinrepository.DuLieuTepTinRepository,typeof _usersRolerepository.UsersRoleRepository==="undefined"?Object:_usersRolerepository.UsersRoleRepository])],DatabaseService);
//# sourceMappingURL=database.service.js.map