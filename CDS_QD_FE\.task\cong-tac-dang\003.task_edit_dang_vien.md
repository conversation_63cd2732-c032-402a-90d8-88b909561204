# Quy trình xây dựng giao diện trang "Chỉnh sửa <PERSON><PERSON> sơ Đảng viên"

---

**Trang: Chỉnh sửa <PERSON>ồ sơ Đảng viên (`/${locale}/cong-tac-dang/chinh-sua-dang-vien/{idDangVien}`)**

- **<PERSON><PERSON><PERSON> (Tiêu đề hiển thị):** `Chỉnh sửa Hồ sơ Đảng viên: [Họ tên Đảng viên]`
- **M<PERSON>c đích chính của Trang:** Cho phép người dùng có thẩm quyền cập nhật các thông tin cơ bản và một số thông tin Đảng cốt lõi của một đảng viên cụ thể. Trang này tập trung vào các thông tin thuộc về bản thân đảng viên, không bao gồm việc quản lý chi tiết các quá trình liên quan như sinh ho<PERSON>, đ<PERSON><PERSON> g<PERSON> (vì các quá trình đó đã có chức năng thêm/sửa/xóa riêng trong các tab tương ứng của trang "Xem chi tiết").

---

#### I. Chức năng trang

1.  **Chức năng "Hiển thị thông tin Đảng viên hiện tại"**

    - **Tổng quan:** Khi trang được tải, toàn bộ thông tin hiện có của đảng viên (mà trang này cho phép chỉnh sửa) sẽ được điền sẵn vào các trường nhập liệu tương ứng.
    - **Chi tiết:**
      - Hệ thống sẽ dựa vào `{idDangVien}` trên đường dẫn để truy vấn dữ liệu của đảng viên cần chỉnh sửa từ cơ sở dữ liệu.
      - Các thông tin lấy từ bảng `DangVien` và các bảng liên kết như `QuanNhan` (nếu có) sẽ được hiển thị trong các ô nhập liệu, ô chọn, v.v.
      - Các trường thông tin mang tính cốt lõi hoặc lịch sử quan trọng (ví dụ: `MaDangVien`, `NgayVaoDangDuBi`) có thể được hiển thị nhưng không cho phép chỉnh sửa (read-only) hoặc việc chỉnh sửa chúng cần một quy trình đặc biệt không thuộc phạm vi trang này.

2.  **Chức năng "Chỉnh sửa Thông tin Đảng viên"**

    - **Tổng quan:** Người dùng có thể thay đổi giá trị của các trường thông tin được phép chỉnh sửa.
    - **Chi tiết:**
      - Người dùng tương tác với các trường nhập liệu (ô văn bản, ô chọn ngày, ô lựa chọn từ danh sách) để cập nhật thông tin.
      - **Kiểm soát chặt chẽ việc sửa các trường cốt lõi:**
        - Ví dụ: Trường `NgayVaoDangChinhThuc` có thể cho phép sửa bởi người có quyền cao, nhưng cần có cảnh báo hoặc ghi nhận đặc biệt vì đây là thông tin quan trọng. Các trường như `SoTheDangVien` sau khi đã cấp phát có thể không cho sửa.
        - Hệ thống có thể vô hiệu hóa (disable) một số trường nếu người dùng không có quyền sửa đổi chúng hoặc chúng là thông tin không nên thay đổi sau khi đã thiết lập ban đầu (ví dụ: `MaDangVien`).
      - **Validation dữ liệu (Kiểm tra tính hợp lệ của dữ liệu):**
        - **Phía người dùng (Client-side):** Ngay khi người dùng nhập liệu, hệ thống có thể kiểm tra sơ bộ (ví dụ: trường bắt buộc không được để trống, định dạng email, định dạng ngày tháng).
        - **Phía hệ thống (Server-side):** Sau khi người dùng nhấn "Lưu thay đổi", hệ thống sẽ kiểm tra toàn diện tính hợp lệ của dữ liệu (ví dụ: tính duy nhất của một số trường, logic giữa các trường ngày tháng, sự tồn tại của các giá trị tham chiếu từ danh mục). Nếu có lỗi, thông báo lỗi sẽ được hiển thị cho người dùng.

3.  **Chức năng "Lưu thay đổi"**

    - **Tổng quan:** Sau khi chỉnh sửa thông tin, người dùng nhấn nút này để cập nhật những thay đổi vào cơ sở dữ liệu.
    - **Chi tiết:**
      - Khi nhấn nút "Lưu thay đổi":
        - Hệ thống thực hiện validation dữ liệu phía server.
        - Nếu dữ liệu hợp lệ:
          - Hệ thống cập nhật các trường thông tin đã thay đổi vào các bảng dữ liệu tương ứng (chủ yếu là bảng `DangVien` và các trường liên quan trong `QuanNhan` nếu có).
          - Ghi nhận lại hành động cập nhật vào nhật ký hệ thống (Audit Log), bao gồm thông tin người sửa, thời gian sửa, và các trường dữ liệu đã thay đổi (giá trị cũ và giá trị mới).
          - Hiển thị thông báo thành công cho người dùng.
          - Điều hướng người dùng trở lại trang "Xem chi tiết Hồ sơ Đảng viên" của đảng viên vừa được cập nhật, hoặc ở lại trang chỉnh sửa với thông báo thành công.
        - Nếu dữ liệu không hợp lệ:
          - Hiển thị thông báo lỗi chi tiết cho từng trường không hợp lệ ngay trên form để người dùng sửa lại.

4.  **Chức năng "Hủy bỏ thay đổi"**
    - **Tổng quan:** Cho phép người dùng hủy bỏ các thay đổi đã thực hiện trên form và quay lại trang trước đó.
    - **Chi tiết:**
      - Khi nhấn nút "Hủy":
        - Hệ thống sẽ không lưu bất kỳ thay đổi nào mà người dùng đã nhập trên form.
        - Điều hướng người dùng trở lại trang "Xem chi tiết Hồ sơ Đảng viên" hoặc trang "Danh sách Đảng viên" (tùy theo luồng điều hướng trước đó).
        - Có thể có một hộp thoại xác nhận "Bạn có chắc chắn muốn hủy các thay đổi?" trước khi thực hiện hành động hủy.

---

#### II. Bảng dữ liệu (Các trường được phép chỉnh sửa hoặc hiển thị trên form này)

- **Bảng `DangVien` (PartyMembers)** - Các trường được hiển thị và có thể cho phép chỉnh sửa (tùy thuộc vào quyền và logic nghiệp vụ):

  - `MaDangVien: string` (Thường là read-only sau khi tạo, hiển thị để biết đang sửa ai)
  - `SoHieuQuanNhan: string` (Nếu cho phép liên kết/thay đổi liên kết với Hồ sơ Quân nhân)
  - `HoVaTen: string` (Cho phép sửa nếu không liên kết với `QuanNhan` hoặc nếu logic cho phép ghi đè)
  - `TenThuongDung: string` (Tương tự `HoVaTen`)
  - `NgaySinh: date` (Tương tự `HoVaTen`)
  - `GioiTinh: number` (Tương tự `HoVaTen`, hiển thị dưới dạng lựa chọn Nam/Nữ/Khác)
  - `NgayVaoDangChinhThuc: date` (Có thể cho sửa với quyền đặc biệt, cần kiểm soát chặt)
  - `NgayVaoDangDuBi: date` (Thường là read-only sau khi nhập từ quy trình kết nạp, hiển thị)
  - `SoTheDangVien: string` (Có thể cho sửa nếu chưa cấp phát hoặc với quyền đặc biệt)
  - `ToChucDangSinhHoatID: number` (Lựa chọn từ danh sách Tổ chức Đảng, cho phép thay đổi khi chuyển sinh hoạt)
  - `TrangThaiDangTichID: number` (Lựa chọn từ danh mục Trạng thái Đảng tịch, việc thay đổi thường qua quy trình riêng nhưng có thể cho phép admin cập nhật)
  - `ChucVuDangHienTaiID: number` (Lựa chọn từ danh mục Chức vụ Đảng)
  - `IsEncrypted: boolean` (Thông tin hệ thống, thường không cho người dùng cuối sửa)
  - _(Các trường `NgayTao`, `NguoiTaoID`, `NgayCapNhat`, `NguoiCapNhatID` sẽ được hệ thống tự động cập nhật, không hiển thị trên form sửa)_

- **Bảng `QuanNhan` (Servicemen)** - Nếu `DangVien.SoHieuQuanNhan` có giá trị và một số thông tin cá nhân được đồng bộ/lấy từ đây để hiển thị và có thể cho phép sửa (việc sửa ở đây có thể cần đồng bộ ngược lại bảng `QuanNhan` hoặc chỉ là thông tin tham khảo trên hồ sơ Đảng viên). Trang này chỉ tập trung vào thông tin _của đảng viên_, nên việc sửa thông tin quân nhân gốc nên được thực hiện ở module Quản lý Quân nhân. Tuy nhiên, để nhất quán, một số trường có thể được hiển thị và cho phép sửa _trong ngữ cảnh hồ sơ Đảng viên_ nếu cần thiết:

  - `HoVaTenKhaiSinh: string` (Nếu `DangVien.HoVaTen` được đồng bộ từ đây)
  - `TenThuongDung: string` (Nếu `DangVien.TenThuongDung` được đồng bộ từ đây)
  - `NgaySinh: date` (Nếu `DangVien.NgaySinh` được đồng bộ từ đây)
  - `GioiTinh: number` (Nếu `DangVien.GioiTinh` được đồng bộ từ đây)
  - _(Các trường khác của `QuanNhan` như thông tin liên hệ, quê quán... thường không thuộc phạm vi chỉnh sửa trực tiếp của trang Hồ sơ Đảng viên mà sẽ được quản lý trong module Quản lý Quân nhân. Trang này chỉ tập trung vào các thuộc tính của `DangVien` và các thông tin cá nhân cơ bản nhất để định danh)._

- **Danh mục `DmToChucDang` (tham chiếu cho `ToChucDangSinhHoatID`)**

  - `ID: number`
  - `TenToChucDang: string`
  - (Các trường khác để hiển thị dạng cây nếu cần)

- **Danh mục `DmTrangThaiDangTich` (tham chiếu cho `TrangThaiDangTichID`)**

  - `ID: number`
  - `TenTrangThai: string` (hoặc tên tương ứng)

- **Danh mục `DmChucVuDang` (tham chiếu cho `ChucVuDangHienTaiID`)**
  - `ID: number`
  - `TenChucVu: string` (hoặc tên tương ứng)

---

#### III. Liên kết với page khác

1.  **Trang "Xem chi tiết Hồ sơ Đảng viên":**
    - Sau khi "Lưu thay đổi" thành công, hệ thống thường điều hướng người dùng về lại trang này để xem thông tin đã cập nhật.
    - Khi nhấn "Hủy", người dùng cũng được điều hướng về lại trang này.
2.  **Trang "Danh sách Hồ sơ Đảng viên":**
    - Người dùng truy cập trang chỉnh sửa này thông qua hành động "Sửa" từ trang danh sách.
    - Có thể có tùy chọn quay lại trang danh sách sau khi lưu hoặc hủy.

---

Mô tả này tập trung vào các trường và chức năng cốt lõi của trang "Chỉnh sửa Hồ sơ Đảng viên", nhấn mạnh vào việc cập nhật các thông tin thuộc bảng `DangVien` và các thông tin định danh cơ bản. Việc quản lý các file hồ sơ gốc, lịch sử sinh hoạt, tự kiểm điểm, đảng phí, khen thưởng/kỷ luật Đảng đã được tách ra thành các chức năng riêng trong các tab của trang "Xem chi tiết" và sẽ có cơ chế thêm/sửa/xóa riêng cho từng mục đó (thường thông qua các Dialog hoặc form nhỏ chuyên biệt), không phải là đối tượng chỉnh sửa chính của form "Chỉnh sửa Hồ sơ Đảng viên" này.
