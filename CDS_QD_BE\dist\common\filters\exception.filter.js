"use strict";Object.defineProperty(exports,"__esModule",{value:true});Object.defineProperty(exports,"AllExceptionsFilter",{enumerable:true,get:function(){return AllExceptionsFilter}});const _common=require("@nestjs/common");function _ts_decorate(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect==="object"&&typeof Reflect.decorate==="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)if(d=decorators[i])r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r;return c>3&&r&&Object.defineProperty(target,key,r),r}let AllExceptionsFilter=class AllExceptionsFilter{catch(exception,host){const ctx=host.switchToHttp();const response=ctx.getResponse();const request=ctx.getRequest();const status=exception instanceof _common.HttpException?exception.getStatus():_common.HttpStatus.INTERNAL_SERVER_ERROR;let message=`An error occurred with error code ${status}`;switch(status){case 500:message="Internal server error";break;case 400:message=exception.response.message||exception.message||"Bad request";break;case 401:message=exception.response.message||exception.message||"Unauthorized";break;case 403:message=exception.response.message||exception.message||"Forbidden";break;case 404:message=exception.response.message||exception.message||"Not found";break;case 409:message=exception.response.message||exception.message||"Conflict";break;case 417:message=exception.response.message||exception.message||"Expectation Failed";break;case 422:message=exception.response.message||exception.message||"Unprocessable Entity";break;default:message=exception.response.message||exception.message||message;break}_common.Logger.error(`[${status}] {${request.url}, ${response?.req?.route?.path}, ${request.method}}: ${exception?.message}`,"ExceptionFilter");response.status(status).json({result:false,message:message,data:exception?.response?.data||null,statusCode:status})}};AllExceptionsFilter=_ts_decorate([(0,_common.Catch)()],AllExceptionsFilter);
//# sourceMappingURL=exception.filter.js.map