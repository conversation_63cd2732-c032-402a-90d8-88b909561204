# Cấu trúc dự án FE - Next.js 14 App Router

## 1. Tổng quan cấu trúc

```
src/
├── app/                          # Next.js App Router
│   ├── (auth)/                   # Auth routes
│   ├── (dashboard)/              # Main app layout
│   │   ├── cong-tac-dang/        # Module công tác đảng
│   │   │   ├── danh-sach-dang-vien/  # Submodule
│   │   │   │   └── page.tsx      # Page component
│   │   │   └── [other-modules]/  # Các module khác
│   │   ├── layout.tsx           # Dashboard layout
│   │   └── page.tsx             # Dashboard home
│   └── api/                     # API routes (nếu có)
├── components/                  # Components dùng chung
│   ├── ui/                      # UI components (button, card, ...)
│   └── shared/                  # Shared components
├── hooks/                       # Custom hooks
│   ├── auth/                    # Auth hooks
│   └── [module]/                # Module-specific hooks
├── lib/                         # Utilities & configs
│   ├── api/                     # API clients
│   └── utils/                   # Helper functions
└── views/                       # View components
    └── [module]/                # Module views
        └── [submodule]/         # Submodule views
            ├── components/      # Local components
            └── [view-name].tsx  # View implementation
```

## 2. Quy ước đặt tên

### 2.1. File và thư mục
- **pages**: `kebab-case` (ví dụ: `danh-sach-dang-vien`)
- **components**: `PascalCase` (ví dụ: `UserProfile.tsx`)
- **hooks**: `useCamelCase` (ví dụ: `useUserProfile.ts`)
- **services**: `camelCase.service.ts` (ví dụ: `user.service.ts`)

### 2.2. Exports
- **Components**: Named exports (khuyến khích)
  ```typescript
  // components/ui/Button.tsx
  export function Button() { ... }
  ```

- **Hooks**: Named exports
  ```typescript
  // hooks/useUser.ts
  export function useUser() { ... }
  ```

## 3. Quy ước import

### 3.1. Thứ tự import
1. React & Next.js
2. Thư viện bên thứ ba
3. Internal modules
4. Styles

```typescript
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { useUser } from '@/hooks/useUser';
import styles from './styles.module.css';
```

## 4. Tổ chức code

### 4.1. Views
- Đặt trong thư mục `src/views/`
- Mỗi view nên có thư mục riêng
- Đặt các components con trong thư mục `components/`

### 4.2. Components
- Components dùng chung: `src/components/`
- Components dùng riêng: Đặt trong thư mục `components/` của view tương ứng

### 4.3. Hooks
- Hooks dùng chung: `src/hooks/`
- Hooks dùng riêng: Có thể đặt trong thư mục `hooks/` của view tương ứng

## 5. Lưu ý
- Sử dụng `@/` cho absolute imports
- Tuân thủ cấu trúc thư mục đã định nghĩa
- Giữ các components nhỏ và tập trung vào một nhiệm vụ
