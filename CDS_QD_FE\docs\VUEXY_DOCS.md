# Vuexy Admin NextJS Documentation

> **Lưu ý**: Tài liệu này dựa trên phiên bản mới nhất của Vuexy Admin NextJS. Đ<PERSON>m bảo luôn cập nhật template lên phiên bản mới nhất để có đầy đủ tính năng và bảo mật.

## Cài đặt
```bash
# Clone repository
git clone https://github.com/themeselection/materio-mui-react-nextjs-admin-template-free.git

# Cài đặt dependencies
npm install
# hoặc
yarn
# hoặc
pnpm install

# Chạy ứng dụng
npm run dev
# hoặc
yarn dev
# hoặc
pnpm dev
```

## Cấu trúc thư mục
```
src/
├── app/                 # Next.js App Router
│   └── [lang]/          # Đa ngôn ngữ
│       └── (dashboard)/ # Layout dashboard
├── components/          # Component dùng chung
├── configs/             # Cấu hình
├── data/                # Dữ liệu tĩnh
├── lib/                 # Thư viện tiện ích
├── styles/              # Global styles
└── views/               # Component theo page
```

## Các tính năng chính
- Hỗ trợ đa ngôn ngữ (i18n)
- Dark/Light mode
- Layout linh hoạt (Vertical/Horizontal)
- Hơn 100+ component tùy chỉnh
- Tích hợp sẵn authentication
- Hỗ trợ RTL
- Và nhiều tính năng khác...

## Hướng dẫn sử dụng

### 1. Thêm ngôn ngữ mới
1. Thêm file ngôn ngữ mới vào thư mục `public/locales/{lang}/`
2. Cập nhật cấu hình trong `src/configs/i18n.ts`

### 2. Thêm menu mới
1. Mở file `src/components/layout/vertical/Menu.tsx`
2. Thêm item menu mới vào mảng `verticalMenuData`

### 3. Tạo trang mới
1. Tạo thư mục mới trong `src/app/[lang]/(dashboard)/`
2. Tạo file `page.tsx` trong thư mục vừa tạo
3. Import và sử dụng các component từ thư mục `src/views/`

## Tích hợp API
```typescript
// Ví dụ gọi API
const fetchData = async () => {
  try {
    const response = await fetch('/api/endpoint')
    const data = await response.json()
    return data
  } catch (error) {
    console.error('Error fetching data:', error)
  }
}
```

## Tùy chỉnh theme
1. Mở file `src/configs/theme.ts`
2. Chỉnh sửa các biến theme theo ý muốn

## Build và Deploy
```bash
# Build cho production
npm run build

# Chạy production
npm start

# Build tĩnh
export NODE_OPTIONS=--openssl-legacy-provider && next build && next export
```

## 🎨 Foundation

### 1. Typography
- Sử dụng hệ thống typography của Material-UI với các biến thể từ `h1` đến `h6`, `subtitle1`, `subtitle2`, `body1`, `body2`, `button`, `caption`, `overline`
- Tùy chỉnh trong `src/configs/typography.ts`

### 2. Màu sắc (Colors)
- Hệ thống màu sắc được định nghĩa trong `src/configs/theme.ts`
- Hỗ trợ theme sáng/tối (light/dark mode)
- Màu chính (primary), màu phụ (secondary), màu thành công (success), cảnh báo (warning), lỗi (error), thông tin (info)

### 3. Bố cục (Layout)
- Hỗ trợ cả layout dọc (vertical) và ngang (horizontal)
- Thanh điều hướng (navbar) tùy chỉnh được
- Thanh bên (sidebar) có thể thu gọn
- Footer tùy chỉnh

### 4. Grid System
- Sử dụng Grid của Material-UI (dựa trên CSS Grid)
- Hỗ trợ responsive với các breakpoints: xs, sm, md, lg, xl

## 🧩 Components

### 1. Basic Components
- **Buttons**: Nút với nhiều biến thể (contained, outlined, text, icon, floating, v.v.)
- **Cards**: Thẻ hiển thị nội dung với header, content và actions
- **Dialogs**: Hộp thoại modal với nhiều kích thước và tùy chọn
- **Chips**: Thẻ nhỏ để hiển thị thông tin ngắn gọn
- **Dividers**: Đường phân cách
- **Icons**: Hơn 9,000+ icons từ Material Icons và Tabler Icons
- **Lists**: Danh sách với nhiều kiểu hiển thị
- **Menus**: Menu dropdown, context menu
- **Tabs**: Chuyển đổi giữa các nội dung
- **Tooltips**: Chú thích khi hover

### 2. Navigation
- **Breadcrumbs**: Thanh điều hướng phân cấp
- **Drawer**: Thanh điều hướng bên
- **Links**: Liên kết với các trạng thái hover/focus
- **Pagination**: Phân trang dữ liệu
- **Speed Dial**: Nút hành động nổi
- **Stepper**: Trình hướng dẫn từng bước

### 3. Data Display
- **Avatars**: Ảnh đại diện
- **Badges**: Huy hiệu thông báo
- **Chips**: Thẻ nhỏ hiển thị thông tin
- **Icons**: Thư viện icon phong phú
- **Lists**: Hiển thị danh sách dữ liệu
- **Tables**: Bảng dữ liệu với sorting, pagination, filtering
- **Timeline**: Dòng thời gian
- **Tooltips**: Chú thích thông tin

## 📝 Form Elements

### 1. Basic Form Controls
- **Text Fields**: Input text, textarea với validation
- **Selects**: Dropdown chọn một hoặc nhiều giá trị
- **Checkboxes**: Lựa chọn nhiều
- **Radio Buttons**: Lựa chọn một trong nhiều
- **Switches**: Công tắc bật/tắt
- **Sliders**: Thanh trượt chọn giá trị
- **Date/Time Pickers**: Chọn ngày/giờ
- **File Upload**: Tải lên file
- **Autocomplete**: Tự động hoàn thành

### 2. Form Layouts
- **Form Control**: Bao bọc các input với label, helper text
- **Form Group**: Nhóm các form control lại với nhau
- **Grid System**: Căn chỉnh form với grid
- **Validation**: Xác thực dữ liệu với yup hoặc formik

### 3. Advanced Form Components
- **Rich Text Editor**: Trình soạn thảo văn bản phong phú
- **File Upload**: Tải lên file với preview
- **Image Cropper**: Cắt ảnh trước khi tải lên
- **Form Wizards**: Form nhiều bước
- **Form Validation**: Xác thực dữ liệu

## 🚀 Nâng cao

### 1. Tích hợp API
```typescript
// Ví dụ sử dụng axios để gọi API
import axios from 'axios'

const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL,
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  }
})

// Sử dụng
const fetchData = async () => {
  try {
    const response = await api.get('/endpoint')
    return response.data
  } catch (error) {
    console.error('Error:', error)
    throw error
  }
}
```

### 2. Tùy chỉnh Theme
1. Mở file `src/configs/theme.ts`
2. Chỉnh sửa các biến theme:
   - Màu sắc (palette)
   - Typography
   - Khoảng cách (spacing)
   - Border radius
   - Và nhiều tùy chỉnh khác

### 3. Đa ngôn ngữ (i18n)
- Hỗ trợ đa ngôn ngữ với next-i18next
- File ngôn ngữ đặt trong `public/locales/{lang}/`
- Thêm ngôn ngữ mới trong `next-i18next.config.js`

## 📦 Build và Deploy

### Build cho Production
```bash
# Cài đặt dependencies
npm install

# Build ứng dụng
npm run build

# Chạy production
npm start

# Hoặc build tĩnh cho hosting tĩnh
export NODE_OPTIONS=--openssl-legacy-provider && next build && next export
```

### Deploy lên Vercel
1. Kết nối repository với Vercel
2. Cấu hình build command: `npm run build`
3. Output directory: `.next`
4. Deploy!

## 📚 Tài nguyên bổ sung

### Tài liệu chính thức
- [Tổng quan](https://demos.pixinvent.com/vuexy-nextjs-admin-template/documentation/)
- [Bắt đầu](https://demos.pixinvent.com/vuexy-nextjs-admin-template/documentation/docs/getting-started/installation/)
- [Foundation](https://demos.pixinvent.com/vuexy-nextjs-admin-template/documentation/docs/user-interface/foundation/typography/)
- [Components](https://demos.pixinvent.com/vuexy-nextjs-admin-template/documentation/docs/user-interface/components/accordion/)
- [Forms](https://demos.pixinvent.com/vuexy-nextjs-admin-template/documentation/docs/forms/form-layouts/)

### Hỗ trợ
- [GitHub Repository](https://github.com/themeselection/materio-mui-react-nextjs-admin-template-free)
- [Hỗ trợ kỹ thuật](https://themeselection.com/support/)
- [Cập nhật mới nhất](https://demos.pixinvent.com/vuexy-nextjs-admin-template/documentation/changelog.html)

## 👨‍💻 Mẹo phát triển

1. **Tối ưu hiệu năng**
   - Sử dụng `React.memo` cho các component không cần re-render thường xuyên
   - Sử dụng `useMemo` và `useCallback` để tránh tính toán lại không cần thiết
   - Sử dụng dynamic imports cho các component lớn

2. **Best Practices**
   - Tuân thủ cấu trúc thư mục
   - Đặt tên biến và hàm rõ ràng, có ý nghĩa
   - Sử dụng TypeScript để tăng độ tin cậy của code
   - Viết unit test cho các component quan trọng

3. **Gỡ lỗi**
   - Sử dụng React DevTools để kiểm tra component hierarchy và props
   - Sử dụng Redux DevTools để debug state management
   - Bật chế độ debug trong `next.config.js` để xem thông tin chi tiết build
