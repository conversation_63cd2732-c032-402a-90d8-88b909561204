{"version": 3, "sources": ["../../../src/common/guards/permission.guard.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\n/* eslint-disable @typescript-eslint/no-explicit-any */\nimport { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';\nimport { Reflector } from '@nestjs/core';\nimport { Request } from 'express';\nimport { Observable } from 'rxjs';\nimport { DataSource } from 'typeorm';\nimport { BYPASS_PERMISSION } from '~/common/constants/constant';\nimport { PERMISSION_KEY } from '~/common/decorators/permission.decorator';\nimport { CACHE_TIME, USER_ROLE } from '~/common/enums/enum';\nimport { UserEntity } from '~/database/typeorm/entities/user.entity';\nimport { CacheService } from '~/shared/services/cache.service';\n\nconst URLs = ['auth', 'docs'];\n\n@Injectable()\nexport class PermissionGuard implements CanActivate {\n    constructor(private reflector: Reflector, private dataSource: DataSource, private readonly cacheService: CacheService) {}\n\n    canActivate(context: ExecutionContext): boolean | Promise<boolean> | Observable<boolean> {\n        const req = context.switchToHttp().getRequest();\n        if (URLs.some((url) => req.originalUrl.includes(url))) return true;\n        const permission = this.reflector.getAllAndOverride<string>(PERMISSION_KEY, [context.getHandler(), context.getClass()]);\n        if (!permission) return false;\n\n        return this.verifyPermission({\n            req: req,\n            permission: permission[0],\n            params: req.params,\n        });\n    }\n\n    private async verifyPermission(data: { req: Request; permission: string; params: any }) {\n        try {\n            if (data.permission === BYPASS_PERMISSION) return true;\n\n            const userId = data.req.headers['_userId']; // sau khi qua authMiddleware thì đã add _userId vào headers\n            const user = await this.getUser(+userId || 0);\n            if (!user) return false;\n\n            // Check if user has admin role (either single or multiple)\n            if (this.hasAdminRole(user)) {\n                // Set primary role ID for backward compatibility\n                const primaryRoleId = user.roleId || user.userRoles?.[0]?.role?.id;\n                data.req.headers['_roleId'] = primaryRoleId?.toString() || '';\n                data.req.headers['_fullName'] = user.hoTen;\n                return true;\n            }\n\n            // Get all role IDs for the user\n            const roleIds = this.getAllUserRoleIds(user);\n            if (roleIds.length === 0) return false;\n\n            // Get permissions from all roles\n            const permissions = await this.getPermissionsFromMultipleRoles(roleIds);\n            if (permissions.length === 0) return false;\n\n            // Check if permission is in permissions\n            if (!permissions.some((p) => p.action === data.permission)) return false;\n\n            // Set primary role ID for backward compatibility\n            const primaryRoleId = user.roleId || roleIds[0];\n            data.req.headers['_roleId'] = primaryRoleId?.toString() || '';\n            data.req.headers['_fullName'] = user.hoTen;\n            data.req.headers['_allRoleIds'] = roleIds.join(','); // New header for multiple roles\n\n            return true;\n        } catch (error) {\n            console.log('LOG:: error:', error.stack);\n            console.log('LOG:: PermissionGuard:', error.message);\n            return false;\n        }\n    }\n\n    private async getPermissions(roleId: number) {\n        const key = `permissions:${roleId}`;\n        const cached = await this.cacheService.getJson(key);\n        if (cached) return cached;\n\n        const entityManager = this.dataSource.manager;\n        const permissions = await entityManager.query(`\n            SELECT p.action\n            FROM roles_permissions as rp, permissions as p\n            WHERE rp.role_id = ${roleId}\n                AND rp.permission_id = p.id\n        `);\n        this.cacheService.setJson(key, permissions, CACHE_TIME.ONE_MONTH);\n\n        return permissions;\n    }\n\n    // Get permissions from multiple roles\n    private async getPermissionsFromMultipleRoles(roleIds: number[]) {\n        if (!roleIds || roleIds.length === 0) return [];\n\n        const key = `permissions:multiple:${roleIds.sort().join(',')}`;\n        const cached = await this.cacheService.getJson(key);\n        if (cached) return cached;\n\n        const entityManager = this.dataSource.manager;\n        const permissions = await entityManager.query(`\n            SELECT DISTINCT p.action\n            FROM roles_permissions as rp, permissions as p\n            WHERE rp.role_id IN (${roleIds.join(',')})\n                AND rp.permission_id = p.id\n        `);\n\n        this.cacheService.setJson(key, permissions, CACHE_TIME.ONE_MONTH);\n        return permissions;\n    }\n\n    // Check if user has admin role (either single or multiple)\n    private hasAdminRole(user: UserEntity): boolean {\n        // Check single role\n        if (user.roleId === USER_ROLE.ADMIN) {\n            return true;\n        }\n\n        // Check multiple roles\n        if (user.userRoles && user.userRoles.length > 0) {\n            return user.userRoles.some((userRole) => userRole.role?.id === USER_ROLE.ADMIN);\n        }\n\n        return false;\n    }\n\n    // Get all role IDs for a user (both single and multiple)\n    private getAllUserRoleIds(user: UserEntity): number[] {\n        const roleIds = [];\n\n        // Add single role if exists\n        if (user.roleId) {\n            roleIds.push(user.roleId);\n        }\n\n        // Add multiple roles if exists\n        if (user.userRoles && user.userRoles.length > 0) {\n            user.userRoles.forEach((userRole) => {\n                if (userRole.role?.id) {\n                    roleIds.push(userRole.role.id);\n                }\n            });\n        }\n\n        // Remove duplicates\n        return [...new Set(roleIds)];\n    }\n\n    private async getUser(id: number): Promise<UserEntity> {\n        const key = `userData:${id}`;\n        const cached = await this.cacheService.getJson(key);\n        if (cached) return cached;\n\n        const entityManager = this.dataSource.manager;\n        const user = await entityManager.findOne(UserEntity, {\n            where: { id: id },\n            select: ['id', 'roleId', 'hoTen'],\n            relations: ['role', 'userRoles', 'userRoles.role'],\n        });\n\n        // Cache user data for 1 week\n        if (user) {\n            this.cacheService.setJson(key, user, CACHE_TIME.ONE_WEEK);\n        }\n\n        return user;\n    }\n}\n"], "names": ["PermissionGuard", "URLs", "canActivate", "context", "req", "switchToHttp", "getRequest", "some", "url", "originalUrl", "includes", "permission", "reflector", "getAllAndOverride", "PERMISSION_KEY", "<PERSON><PERSON><PERSON><PERSON>", "getClass", "verifyPermission", "params", "data", "BYPASS_PERMISSION", "userId", "headers", "user", "getUser", "hasAdminRole", "primaryRoleId", "roleId", "userRoles", "role", "id", "toString", "hoTen", "roleIds", "getAllUserRoleIds", "length", "permissions", "getPermissionsFromMultipleRoles", "p", "action", "join", "error", "console", "log", "stack", "message", "getPermissions", "key", "cached", "cacheService", "get<PERSON>son", "entityManager", "dataSource", "manager", "query", "<PERSON><PERSON><PERSON>", "CACHE_TIME", "ONE_MONTH", "sort", "USER_ROLE", "ADMIN", "userRole", "push", "for<PERSON>ach", "Set", "findOne", "UserEntity", "where", "select", "relations", "ONE_WEEK", "constructor"], "mappings": "oGAgBaA,yDAAAA,yCAd6C,sCAChC,uCAGC,mCACO,4DACH,0DACO,2CACX,2EACE,olBAE7B,MAAMC,KAAO,CAAC,OAAQ,OAAO,CAGtB,IAAA,AAAMD,gBAAN,MAAMA,gBAGTE,YAAYC,OAAyB,CAAoD,CACrF,MAAMC,IAAMD,QAAQE,YAAY,GAAGC,UAAU,GAC7C,GAAIL,KAAKM,IAAI,CAAC,AAACC,KAAQJ,IAAIK,WAAW,CAACC,QAAQ,CAACF,MAAO,OAAO,KAC9D,MAAMG,WAAa,IAAI,CAACC,SAAS,CAACC,iBAAiB,CAASC,mCAAc,CAAE,CAACX,QAAQY,UAAU,GAAIZ,QAAQa,QAAQ,GAAG,EACtH,GAAI,CAACL,WAAY,OAAO,MAExB,OAAO,IAAI,CAACM,gBAAgB,CAAC,CACzBb,IAAKA,IACLO,WAAYA,UAAU,CAAC,EAAE,CACzBO,OAAQd,IAAIc,MAAM,AACtB,EACJ,CAEA,MAAcD,iBAAiBE,IAAuD,CAAE,CACpF,GAAI,CACA,GAAIA,KAAKR,UAAU,GAAKS,2BAAiB,CAAE,OAAO,KAElD,MAAMC,OAASF,KAAKf,GAAG,CAACkB,OAAO,CAAC,UAAU,CAC1C,MAAMC,KAAO,MAAM,IAAI,CAACC,OAAO,CAAC,CAACH,QAAU,GAC3C,GAAI,CAACE,KAAM,OAAO,MAGlB,GAAI,IAAI,CAACE,YAAY,CAACF,MAAO,CAEzB,MAAMG,cAAgBH,KAAKI,MAAM,EAAIJ,KAAKK,SAAS,EAAE,CAAC,EAAE,EAAEC,MAAMC,EAChEX,CAAAA,KAAKf,GAAG,CAACkB,OAAO,CAAC,UAAU,CAAGI,eAAeK,YAAc,EAC3DZ,CAAAA,KAAKf,GAAG,CAACkB,OAAO,CAAC,YAAY,CAAGC,KAAKS,KAAK,CAC1C,OAAO,IACX,CAGA,MAAMC,QAAU,IAAI,CAACC,iBAAiB,CAACX,MACvC,GAAIU,QAAQE,MAAM,GAAK,EAAG,OAAO,MAGjC,MAAMC,YAAc,MAAM,IAAI,CAACC,+BAA+B,CAACJ,SAC/D,GAAIG,YAAYD,MAAM,GAAK,EAAG,OAAO,MAGrC,GAAI,CAACC,YAAY7B,IAAI,CAAC,AAAC+B,GAAMA,EAAEC,MAAM,GAAKpB,KAAKR,UAAU,EAAG,OAAO,MAGnE,MAAMe,cAAgBH,KAAKI,MAAM,EAAIM,OAAO,CAAC,EAAE,AAC/Cd,CAAAA,KAAKf,GAAG,CAACkB,OAAO,CAAC,UAAU,CAAGI,eAAeK,YAAc,EAC3DZ,CAAAA,KAAKf,GAAG,CAACkB,OAAO,CAAC,YAAY,CAAGC,KAAKS,KAAK,AAC1Cb,CAAAA,KAAKf,GAAG,CAACkB,OAAO,CAAC,cAAc,CAAGW,QAAQO,IAAI,CAAC,KAE/C,OAAO,IACX,CAAE,MAAOC,MAAO,CACZC,QAAQC,GAAG,CAAC,eAAgBF,MAAMG,KAAK,EACvCF,QAAQC,GAAG,CAAC,yBAA0BF,MAAMI,OAAO,EACnD,OAAO,KACX,CACJ,CAEA,MAAcC,eAAenB,MAAc,CAAE,CACzC,MAAMoB,IAAM,CAAC,YAAY,EAAEpB,OAAO,CAAC,CACnC,MAAMqB,OAAS,MAAM,IAAI,CAACC,YAAY,CAACC,OAAO,CAACH,KAC/C,GAAIC,OAAQ,OAAOA,OAEnB,MAAMG,cAAgB,IAAI,CAACC,UAAU,CAACC,OAAO,CAC7C,MAAMjB,YAAc,MAAMe,cAAcG,KAAK,CAAC;AACtD;AACA;AACA,+BAA+B,EAAE3B;AACjC;AACA,QAAQ,CAAC,EACD,IAAI,CAACsB,YAAY,CAACM,OAAO,CAACR,IAAKX,YAAaoB,gBAAU,CAACC,SAAS,EAEhE,OAAOrB,WACX,CAGA,MAAcC,gCAAgCJ,OAAiB,CAAE,CAC7D,GAAI,CAACA,SAAWA,QAAQE,MAAM,GAAK,EAAG,MAAO,EAAE,CAE/C,MAAMY,IAAM,CAAC,qBAAqB,EAAEd,QAAQyB,IAAI,GAAGlB,IAAI,CAAC,KAAK,CAAC,CAC9D,MAAMQ,OAAS,MAAM,IAAI,CAACC,YAAY,CAACC,OAAO,CAACH,KAC/C,GAAIC,OAAQ,OAAOA,OAEnB,MAAMG,cAAgB,IAAI,CAACC,UAAU,CAACC,OAAO,CAC7C,MAAMjB,YAAc,MAAMe,cAAcG,KAAK,CAAC;AACtD;AACA;AACA,iCAAiC,EAAErB,QAAQO,IAAI,CAAC,KAAK;AACrD;AACA,QAAQ,CAAC,EAED,IAAI,CAACS,YAAY,CAACM,OAAO,CAACR,IAAKX,YAAaoB,gBAAU,CAACC,SAAS,EAChE,OAAOrB,WACX,CAGA,AAAQX,aAAaF,IAAgB,CAAW,CAE5C,GAAIA,KAAKI,MAAM,GAAKgC,eAAS,CAACC,KAAK,CAAE,CACjC,OAAO,IACX,CAGA,GAAIrC,KAAKK,SAAS,EAAIL,KAAKK,SAAS,CAACO,MAAM,CAAG,EAAG,CAC7C,OAAOZ,KAAKK,SAAS,CAACrB,IAAI,CAAC,AAACsD,UAAaA,SAAShC,IAAI,EAAEC,KAAO6B,eAAS,CAACC,KAAK,CAClF,CAEA,OAAO,KACX,CAGA,AAAQ1B,kBAAkBX,IAAgB,CAAY,CAClD,MAAMU,QAAU,EAAE,CAGlB,GAAIV,KAAKI,MAAM,CAAE,CACbM,QAAQ6B,IAAI,CAACvC,KAAKI,MAAM,CAC5B,CAGA,GAAIJ,KAAKK,SAAS,EAAIL,KAAKK,SAAS,CAACO,MAAM,CAAG,EAAG,CAC7CZ,KAAKK,SAAS,CAACmC,OAAO,CAAC,AAACF,WACpB,GAAIA,SAAShC,IAAI,EAAEC,GAAI,CACnBG,QAAQ6B,IAAI,CAACD,SAAShC,IAAI,CAACC,EAAE,CACjC,CACJ,EACJ,CAGA,MAAO,IAAI,IAAIkC,IAAI/B,SAAS,AAChC,CAEA,MAAcT,QAAQM,EAAU,CAAuB,CACnD,MAAMiB,IAAM,CAAC,SAAS,EAAEjB,GAAG,CAAC,CAC5B,MAAMkB,OAAS,MAAM,IAAI,CAACC,YAAY,CAACC,OAAO,CAACH,KAC/C,GAAIC,OAAQ,OAAOA,OAEnB,MAAMG,cAAgB,IAAI,CAACC,UAAU,CAACC,OAAO,CAC7C,MAAM9B,KAAO,MAAM4B,cAAcc,OAAO,CAACC,sBAAU,CAAE,CACjDC,MAAO,CAAErC,GAAIA,EAAG,EAChBsC,OAAQ,CAAC,KAAM,SAAU,QAAQ,CACjCC,UAAW,CAAC,OAAQ,YAAa,iBAAiB,AACtD,GAGA,GAAI9C,KAAM,CACN,IAAI,CAAC0B,YAAY,CAACM,OAAO,CAACR,IAAKxB,KAAMiC,gBAAU,CAACc,QAAQ,CAC5D,CAEA,OAAO/C,IACX,CArJAgD,YAAY,AAAQ3D,SAAoB,CAAE,AAAQwC,UAAsB,CAAE,AAAiBH,YAA0B,CAAE,MAAnGrC,UAAAA,eAA8BwC,WAAAA,gBAAyCH,aAAAA,YAA6B,CAsJ5H"}