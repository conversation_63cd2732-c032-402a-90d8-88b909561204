{"version": 3, "sources": ["../../../src/common/guards/permission.guard.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\n/* eslint-disable @typescript-eslint/no-explicit-any */\nimport { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';\nimport { Reflector } from '@nestjs/core';\nimport { Request } from 'express';\nimport { Observable } from 'rxjs';\nimport { DataSource } from 'typeorm';\nimport { BYPASS_PERMISSION } from '~/common/constants/constant';\nimport { PERMISSION_KEY } from '~/common/decorators/permission.decorator';\nimport { CACHE_TIME, USER_ROLE } from '~/common/enums/enum';\nimport { UserEntity } from '~/database/typeorm/entities/user.entity';\nimport { CacheService } from '~/shared/services/cache.service';\n\nconst URLs = ['auth', 'docs'];\n\n@Injectable()\nexport class PermissionGuard implements CanActivate {\n    constructor(private reflector: Reflector, private dataSource: DataSource, private readonly cacheService: CacheService) {}\n\n    canActivate(context: ExecutionContext): boolean | Promise<boolean> | Observable<boolean> {\n        const req = context.switchToHttp().getRequest();\n        if (URLs.some((url) => req.originalUrl.includes(url))) return true;\n        const permission = this.reflector.getAllAndOverride<string>(PERMISSION_KEY, [context.getHandler(), context.getClass()]);\n        if (!permission) return false;\n\n        return this.verifyPermission({\n            req: req,\n            permission: permission[0],\n            params: req.params,\n        });\n    }\n\n    private async verifyPermission(data: { req: Request; permission: string; params: any }) {\n        try {\n            if (data.permission === BYPASS_PERMISSION) return true;\n\n            const userId = data.req.headers['_userId']; // sau khi qua authMiddleware thì đã add _kId vào body\n            const user = await this.getUser(+userId || 0);\n            if (!user) return false;\n\n            // if user is admin\n            if (user.roleId === USER_ROLE.ADMIN) {\n                data.req.headers['_roleId'] = user.roleId.toString();\n                data.req.headers['_fullName'] = user.hoTen;\n                return true;\n            }\n\n            const permissions = await this.getPermissions(user.roleId);\n            if (permissions.length === 0) return false;\n            // check if permission is in permissions\n            if (!permissions.some((p) => p.action === data.permission)) return false;\n\n            data.req.headers['_roleId'] = user.roleId.toString();\n            data.req.headers['_fullName'] = user.hoTen;\n\n            return true;\n        } catch (error) {\n            console.log('LOG:: error:', error.stack);\n            console.log('LOG:: PermissionGuard:', error.message);\n            return false;\n        }\n    }\n\n    private async getPermissions(roleId: number) {\n        const key = `permissions:${roleId}`;\n        const cached = await this.cacheService.getJson(key);\n        if (cached) return cached;\n\n        const entityManager = this.dataSource.manager;\n        const permissions = await entityManager.query(`\n            SELECT p.action\n            FROM roles_permissions as rp, permissions as p\n            WHERE rp.role_id = ${roleId}\n                AND rp.permission_id = p.id\n        `);\n        this.cacheService.setJson(key, permissions, CACHE_TIME.ONE_MONTH);\n\n        return permissions;\n    }\n\n    private async getUser(id: number): Promise<UserEntity> {\n        const key = `userData:${id}`;\n        const cached = await this.cacheService.getJson(key);\n        if (cached) return cached;\n\n        const entityManager = this.dataSource.manager;\n        const user = await entityManager.findOne(UserEntity, { where: { id: id }, select: ['id', 'roleId', 'hoTen'] });\n        return user;\n    }\n}\n"], "names": ["PermissionGuard", "URLs", "canActivate", "context", "req", "switchToHttp", "getRequest", "some", "url", "originalUrl", "includes", "permission", "reflector", "getAllAndOverride", "PERMISSION_KEY", "<PERSON><PERSON><PERSON><PERSON>", "getClass", "verifyPermission", "params", "data", "BYPASS_PERMISSION", "userId", "headers", "user", "getUser", "roleId", "USER_ROLE", "ADMIN", "toString", "hoTen", "permissions", "getPermissions", "length", "p", "action", "error", "console", "log", "stack", "message", "key", "cached", "cacheService", "get<PERSON>son", "entityManager", "dataSource", "manager", "query", "<PERSON><PERSON><PERSON>", "CACHE_TIME", "ONE_MONTH", "id", "findOne", "UserEntity", "where", "select", "constructor"], "mappings": "oGAgBaA,yDAAAA,yCAd6C,sCAChC,uCAGC,mCACO,4DACH,0DACO,2CACX,2EACE,olBAE7B,MAAMC,KAAO,CAAC,OAAQ,OAAO,CAGtB,IAAA,AAAMD,gBAAN,MAAMA,gBAGTE,YAAYC,OAAyB,CAAoD,CACrF,MAAMC,IAAMD,QAAQE,YAAY,GAAGC,UAAU,GAC7C,GAAIL,KAAKM,IAAI,CAAC,AAACC,KAAQJ,IAAIK,WAAW,CAACC,QAAQ,CAACF,MAAO,OAAO,KAC9D,MAAMG,WAAa,IAAI,CAACC,SAAS,CAACC,iBAAiB,CAASC,mCAAc,CAAE,CAACX,QAAQY,UAAU,GAAIZ,QAAQa,QAAQ,GAAG,EACtH,GAAI,CAACL,WAAY,OAAO,MAExB,OAAO,IAAI,CAACM,gBAAgB,CAAC,CACzBb,IAAKA,IACLO,WAAYA,UAAU,CAAC,EAAE,CACzBO,OAAQd,IAAIc,MAAM,AACtB,EACJ,CAEA,MAAcD,iBAAiBE,IAAuD,CAAE,CACpF,GAAI,CACA,GAAIA,KAAKR,UAAU,GAAKS,2BAAiB,CAAE,OAAO,KAElD,MAAMC,OAASF,KAAKf,GAAG,CAACkB,OAAO,CAAC,UAAU,CAC1C,MAAMC,KAAO,MAAM,IAAI,CAACC,OAAO,CAAC,CAACH,QAAU,GAC3C,GAAI,CAACE,KAAM,OAAO,MAGlB,GAAIA,KAAKE,MAAM,GAAKC,eAAS,CAACC,KAAK,CAAE,CACjCR,KAAKf,GAAG,CAACkB,OAAO,CAAC,UAAU,CAAGC,KAAKE,MAAM,CAACG,QAAQ,EAClDT,CAAAA,KAAKf,GAAG,CAACkB,OAAO,CAAC,YAAY,CAAGC,KAAKM,KAAK,CAC1C,OAAO,IACX,CAEA,MAAMC,YAAc,MAAM,IAAI,CAACC,cAAc,CAACR,KAAKE,MAAM,EACzD,GAAIK,YAAYE,MAAM,GAAK,EAAG,OAAO,MAErC,GAAI,CAACF,YAAYvB,IAAI,CAAC,AAAC0B,GAAMA,EAAEC,MAAM,GAAKf,KAAKR,UAAU,EAAG,OAAO,KAEnEQ,CAAAA,KAAKf,GAAG,CAACkB,OAAO,CAAC,UAAU,CAAGC,KAAKE,MAAM,CAACG,QAAQ,EAClDT,CAAAA,KAAKf,GAAG,CAACkB,OAAO,CAAC,YAAY,CAAGC,KAAKM,KAAK,CAE1C,OAAO,IACX,CAAE,MAAOM,MAAO,CACZC,QAAQC,GAAG,CAAC,eAAgBF,MAAMG,KAAK,EACvCF,QAAQC,GAAG,CAAC,yBAA0BF,MAAMI,OAAO,EACnD,OAAO,KACX,CACJ,CAEA,MAAcR,eAAeN,MAAc,CAAE,CACzC,MAAMe,IAAM,CAAC,YAAY,EAAEf,OAAO,CAAC,CACnC,MAAMgB,OAAS,MAAM,IAAI,CAACC,YAAY,CAACC,OAAO,CAACH,KAC/C,GAAIC,OAAQ,OAAOA,OAEnB,MAAMG,cAAgB,IAAI,CAACC,UAAU,CAACC,OAAO,CAC7C,MAAMhB,YAAc,MAAMc,cAAcG,KAAK,CAAC;AACtD;AACA;AACA,+BAA+B,EAAEtB;AACjC;AACA,QAAQ,CAAC,EACD,IAAI,CAACiB,YAAY,CAACM,OAAO,CAACR,IAAKV,YAAamB,gBAAU,CAACC,SAAS,EAEhE,OAAOpB,WACX,CAEA,MAAcN,QAAQ2B,EAAU,CAAuB,CACnD,MAAMX,IAAM,CAAC,SAAS,EAAEW,GAAG,CAAC,CAC5B,MAAMV,OAAS,MAAM,IAAI,CAACC,YAAY,CAACC,OAAO,CAACH,KAC/C,GAAIC,OAAQ,OAAOA,OAEnB,MAAMG,cAAgB,IAAI,CAACC,UAAU,CAACC,OAAO,CAC7C,MAAMvB,KAAO,MAAMqB,cAAcQ,OAAO,CAACC,sBAAU,CAAE,CAAEC,MAAO,CAAEH,GAAIA,EAAG,EAAGI,OAAQ,CAAC,KAAM,SAAU,QAAQ,AAAC,GAC5G,OAAOhC,IACX,CAvEAiC,YAAY,AAAQ5C,SAAoB,CAAE,AAAQiC,UAAsB,CAAE,AAAiBH,YAA0B,CAAE,MAAnG9B,UAAAA,eAA8BiC,WAAAA,gBAAyCH,aAAAA,YAA6B,CAwE5H"}