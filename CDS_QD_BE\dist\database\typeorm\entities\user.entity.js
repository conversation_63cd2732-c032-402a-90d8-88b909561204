"use strict";Object.defineProperty(exports,"__esModule",{value:true});Object.defineProperty(exports,"UserEntity",{enumerable:true,get:function(){return UserEntity}});const _typeorm=require("typeorm");const _abstractentity=require("./abstract.entity");const _accountentity=require("./account.entity");const _giaTriDanhMucentity=require("./giaTriDanhMuc.entity");const _duLieuTepTinentity=require("./duLieuTepTin.entity");const _quanNhanentity=require("./quanNhan.entity");const _roleentity=require("./role.entity");const _usersRoleentity=require("./usersRole.entity");const _enum=require("../../../common/enums/enum");function _ts_decorate(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect==="object"&&typeof Reflect.decorate==="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)if(d=decorators[i])r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r;return c>3&&r&&Object.defineProperty(target,key,r),r}function _ts_metadata(k,v){if(typeof Reflect==="object"&&typeof Reflect.metadata==="function")return Reflect.metadata(k,v)}let UserEntity=class UserEntity extends _abstractentity.AbstractEntity{};_ts_decorate([(0,_typeorm.PrimaryGeneratedColumn)("increment",{name:"id",type:"int",unsigned:true}),_ts_metadata("design:type",Number)],UserEntity.prototype,"id",void 0);_ts_decorate([(0,_typeorm.Column)({name:"account_id",type:"bigint",nullable:false}),_ts_metadata("design:type",Number)],UserEntity.prototype,"accountId",void 0);_ts_decorate([(0,_typeorm.Column)({name:"role_id",type:"int",unsigned:true,nullable:true}),_ts_metadata("design:type",Number)],UserEntity.prototype,"roleId",void 0);_ts_decorate([(0,_typeorm.Column)({name:"avatar_id",type:"uuid",nullable:true}),_ts_metadata("design:type",String)],UserEntity.prototype,"avatarId",void 0);_ts_decorate([(0,_typeorm.Column)({name:"ho_ten",type:"varchar",length:100,nullable:false}),_ts_metadata("design:type",String)],UserEntity.prototype,"hoTen",void 0);_ts_decorate([(0,_typeorm.Column)({name:"don_vi_id",type:"bigint",nullable:true}),_ts_metadata("design:type",Number)],UserEntity.prototype,"donViId",void 0);_ts_decorate([(0,_typeorm.Column)({name:"email",type:"varchar",length:100,nullable:true,unique:true}),_ts_metadata("design:type",String)],UserEntity.prototype,"email",void 0);_ts_decorate([(0,_typeorm.Column)({name:"so_dien_thoai",type:"varchar",length:15,nullable:true}),_ts_metadata("design:type",String)],UserEntity.prototype,"soDienThoai",void 0);_ts_decorate([(0,_typeorm.Column)({name:"quan_nhan_id",type:"varchar",length:20,nullable:true,unique:true}),_ts_metadata("design:type",String)],UserEntity.prototype,"quanNhanId",void 0);_ts_decorate([(0,_typeorm.Column)({type:"enum",enum:_enum.USER_STATUS,default:_enum.USER_STATUS.ACTIVE}),_ts_metadata("design:type",typeof _enum.USER_STATUS==="undefined"?Object:_enum.USER_STATUS)],UserEntity.prototype,"status",void 0);_ts_decorate([(0,_typeorm.Column)({name:"lan_dang_nhap_cuoi",type:"timestamp with time zone",nullable:true}),_ts_metadata("design:type",typeof Date==="undefined"?Object:Date)],UserEntity.prototype,"lanDangNhapCuoi",void 0);_ts_decorate([(0,_typeorm.Column)({name:"so_lan_dang_nhap_sai",type:"integer",default:0}),_ts_metadata("design:type",Number)],UserEntity.prototype,"soLanDangNhapSai",void 0);_ts_decorate([(0,_typeorm.Column)({name:"thoi_gian_khoa_tai_khoan",type:"timestamp with time zone",nullable:true}),_ts_metadata("design:type",typeof Date==="undefined"?Object:Date)],UserEntity.prototype,"thoiGianKhoaTaiKhoan",void 0);_ts_decorate([(0,_typeorm.Column)({name:"yeu_cau_doi_mat_khau",type:"boolean",default:false}),_ts_metadata("design:type",Boolean)],UserEntity.prototype,"yeuCauDoiMatKhau",void 0);_ts_decorate([(0,_typeorm.OneToOne)(()=>_accountentity.AccountEntity,{createForeignKeyConstraints:false}),(0,_typeorm.JoinColumn)({name:"account_id",referencedColumnName:"id"}),_ts_metadata("design:type",typeof _typeorm.Relation==="undefined"?Object:_typeorm.Relation)],UserEntity.prototype,"account",void 0);_ts_decorate([(0,_typeorm.OneToOne)(()=>_duLieuTepTinentity.DuLieuTepTinEntity,{createForeignKeyConstraints:false}),(0,_typeorm.JoinColumn)({name:"avatar_id",referencedColumnName:"id"}),_ts_metadata("design:type",typeof _typeorm.Relation==="undefined"?Object:_typeorm.Relation)],UserEntity.prototype,"avatar",void 0);_ts_decorate([(0,_typeorm.ManyToOne)(()=>_giaTriDanhMucentity.GiaTriDanhMucEntity,{createForeignKeyConstraints:false}),(0,_typeorm.JoinColumn)({name:"don_vi_id",referencedColumnName:"id"}),_ts_metadata("design:type",typeof _typeorm.Relation==="undefined"?Object:_typeorm.Relation)],UserEntity.prototype,"donVi",void 0);_ts_decorate([(0,_typeorm.OneToOne)(()=>_quanNhanentity.QuanNhanEntity,{createForeignKeyConstraints:false}),(0,_typeorm.JoinColumn)({name:"quan_nhan_id",referencedColumnName:"soHieuQuanNhan"}),_ts_metadata("design:type",typeof _typeorm.Relation==="undefined"?Object:_typeorm.Relation)],UserEntity.prototype,"quanNhan",void 0);_ts_decorate([(0,_typeorm.ManyToOne)(()=>_roleentity.RoleEntity,role=>role.id,{onDelete:"RESTRICT",onUpdate:"CASCADE",createForeignKeyConstraints:false}),(0,_typeorm.JoinColumn)({name:"role_id",referencedColumnName:"id"}),_ts_metadata("design:type",typeof _typeorm.Relation==="undefined"?Object:_typeorm.Relation)],UserEntity.prototype,"role",void 0);_ts_decorate([(0,_typeorm.OneToMany)(()=>_usersRoleentity.UsersRoleEntity,userRole=>userRole.user,{cascade:true,createForeignKeyConstraints:false}),_ts_metadata("design:type",typeof _typeorm.Relation==="undefined"?Object:_typeorm.Relation)],UserEntity.prototype,"userRoles",void 0);UserEntity=_ts_decorate([(0,_typeorm.Entity)({name:"users"})],UserEntity);
//# sourceMappingURL=user.entity.js.map