/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { TaiLieuVuViecKyLuatEntity } from '~/database/typeorm/entities/taiLieuVuViecKyLuat.entity';

@Injectable()
export class TaiLieuVuViecKyLuatRepository extends Repository<TaiLieuVuViecKyLuatEntity> {
    constructor(private dataSource: DataSource) {
        super(TaiLieuVuViecKyLuatEntity, dataSource.createEntityManager());
    }
}
