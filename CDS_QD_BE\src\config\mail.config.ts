function getMailConfig() {
    return {
        mail: {
            // transport: 'smtps://<EMAIL>:<EMAIL>',
            // or
            transport: {
                host: process.env.MAIL_HOST,
                secure: false,
                auth: {
                    user: process.env.MAIL_USER,
                    pass: process.env.MAIL_PASSWORD,
                },
            },
            defaults: {
                from: `"CDS-QD" <${process.env.MAIL_FROM}>`,
            },
        },
    };
}

export default () => {
    return getMailConfig();
};
