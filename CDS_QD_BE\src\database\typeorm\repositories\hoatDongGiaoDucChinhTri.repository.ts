/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { HoatDongGiaoDucChinhTriEntity } from '~/database/typeorm/entities/hoatDongGiaoDucChinhTri.entity';

@Injectable()
export class HoatDongGiaoDucChinhTriRepository extends Repository<HoatDongGiaoDucChinhTriEntity> {
    constructor(private dataSource: DataSource) {
        super(HoatDongGiaoDucChinhTriEntity, dataSource.createEntityManager());
    }
}
