Bước 15: Cậ<PERSON> nhật mockDataChiTiet.ts cho "Quá trình Đào tạo"
Định nghĩa QuaTrinhDaoTaoEntryType:

Thêm dữ liệu mẫu vào mockQuanNhanChiTietData.quaTrinhDaoTao.

TypeScript

// src\views\quan-ly-quan-nhan\chi-tiet\mockDataChiTiet.ts (Thêm/Cập nhật)

export type TrangThaiDaoTaoType = 'Đã hoàn thành' | 'Đang học' | 'Bị đình chỉ' | 'Bảo lưu';

export const trangThaiDaoTaoObj: Record<
TrangThaiDaoTaoType,
{ color: 'success' | 'info' | 'error' | 'warning'; label: string }

> = {
> 'Đã hoàn thành': { color: 'success', label: 'Đã hoàn thành' },
> 'Đang học': { color: 'info', label: 'Đang học' },
> 'Bị đình chỉ': { color: 'error', label: 'Bị đình chỉ' },
> '<PERSON><PERSON><PERSON> lưu': { color: 'warning', label: 'Bảo lưu' }
> };

export interface QuaTrinhDaoTaoEntryType {
id: string; // ID duy nhất cho mỗi dòng
TenKhoaHoc: string;
HinhThucDaoTaoID: string; // Sẽ map sang tên (Chính quy, Tại chức, Từ xa, Bồi dưỡng ngắn hạn...)
ChuyenNganhDaoTaoID: string; // Sẽ map sang tên
CoSoDaoTaoID: string; // Sẽ map sang tên (Tên trường, trung tâm...)
QuocGiaDaoTaoID?: string; // Sẽ map sang tên (Nếu du học)
ThoiGianBatDau: string; // ISO Date string
ThoiGianKetThuc: string; // ISO Date string
VanBangChungChiLoaiID: string; // Sẽ map sang tên (Cử nhân, Kỹ sư, Thạc sĩ, Tiến sĩ, Chứng chỉ...)
SoHieuVanBang?: string;
NgayCapVanBang?: string; // ISO Date string
NoiCapVanBang?: string; // Tên cơ quan cấp
XepLoaiTotNghiepID?: string; // Sẽ map sang tên (Xuất sắc, Giỏi, Khá, Trung bình...)
DiemSo?: number | string; // Có thể là điểm trung bình hoặc mô tả
LuanVanDeTaiTotNghiep?: string;
TrangThaiDaoTao: TrangThaiDaoTaoType;
FileDinhKemURL?: string; // URL tới file văn bằng, chứng chỉ
GhiChu?: string;
}

// Trong mockQuanNhanChiTietData:
// ...
// quaTrinhDaoTao: [
// {
// id: 'qtdt_001',
// TenKhoaHoc: 'Đại học Chỉ huy Tham mưu Lục quân',
// HinhThucDaoTaoID: 'HTDT_CQ', // Chính quy
// ChuyenNganhDaoTaoID: 'CN_CHTM', // Chỉ huy Tham mưu
// CoSoDaoTaoID: 'CSDT_SQLQ1', // Trường Sĩ quan Lục quân 1
// ThoiGianBatDau: '2008-09-01T00:00:00Z',
// ThoiGianKetThuc: '2012-07-30T00:00:00Z',
// VanBangChungChiLoaiID: 'VB_CN', // Cử nhân
// SoHieuVanBang: 'VB001234',
// NgayCapVanBang: '2012-08-15T00:00:00Z',
// NoiCapVanBang: 'Trường Sĩ quan Lục quân 1',
// XepLoaiTotNghiepID: 'XL_GIOI', // Giỏi
// DiemSo: '8.25',
// LuanVanDeTaiTotNghiep: 'Nghiên cứu chiến thuật phòng ngự khu vực đồi núi',
// TrangThaiDaoTao: 'Đã hoàn thành',
// FileDinhKemURL: '/files/vanbang/vb001234.pdf',
// GhiChu: 'Được khen thưởng sinh viên xuất sắc toàn khóa.'
// },
// {
// id: 'qtdt_002',
// TenKhoaHoc: 'Bồi dưỡng kiến thức Quốc phòng An ninh đối tượng 3',
// HinhThucDaoTaoID: 'HTDT_BD', // Bồi dưỡng
// ChuyenNganhDaoTaoID: 'CN_QPAN', // Quốc phòng an ninh
// CoSoDaoTaoID: 'CSDT_HVQP', // Học viện Quốc phòng
// ThoiGianBatDau: '2019-03-01T00:00:00Z',
// ThoiGianKetThuc: '2019-03-20T00:00:00Z',
// VanBangChungChiLoaiID: 'VB_CC', // Chứng chỉ
// SoHieuVanBang: 'CCQPAN00789',
// NgayCapVanBang: '2019-03-20T00:00:00Z',
// NoiCapVanBang: 'Học viện Quốc phòng',
// XepLoaiTotNghiepID: 'XL_KHA', // Khá
// TrangThaiDaoTao: 'Đã hoàn thành',
// FileDinhKemURL: '/files/chungchi/ccqpan00789.pdf',
// GhiChu: ''
// }
// ] as QuaTrinhDaoTaoEntryType[],
// ...
Bước 16: Xây dựng TabPanel cho "Quá trình Đào tạo"
Tạo file src\views\quan-ly-quan-nhan\chi-tiet\components\tabs\TabQuaTrinhDaoTao.tsx

Mục đích: Hiển thị lịch sử đào tạo, cho phép thêm, sửa, xóa.

Props:

initialData?: QuaTrinhDaoTaoEntryType[]
idQuanNhan: string
State: Tương tự TabQuaTrinhCongTac ( listData, openDialog, editingData, openConfirmDelete, deletingId, paginationModel).

Component Vuexy (MUI & @mui/x-data-grid): Tương tự TabQuaTrinhCongTac.

Cấu hình cột cho DataGrid:

TenKhoaHoc
HinhThucDaoTaoID (hiển thị tên)
ChuyenNganhDaoTaoID (hiển thị tên)
CoSoDaoTaoID (hiển thị tên)
QuocGiaDaoTaoID (hiển thị tên, nếu có)
ThoiGianBatDau (định dạng dd/MM/yyyy)
ThoiGianKetThuc (định dạng dd/MM/yyyy)
VanBangChungChiLoaiID (hiển thị tên)
SoHieuVanBang
NgayCapVanBang (định dạng dd/MM/yyyy)
NoiCapVanBang
XepLoaiTotNghiepID (hiển thị tên)
DiemSo
LuanVanDeTaiTotNghiep (rút gọn với tooltip)
TrangThaiDaoTao (hiển thị bằng CustomChip với màu tương ứng)
FileDinhKemURL (Link tải/xem)
GhiChu
Hành động: Sửa, Xóa, Xem file.
TypeScript

// src\views\quan-ly-quan-nhan\chi-tiet\components\tabs\TabQuaTrinhDaoTao.tsx
'use client';

import React, { useState, useEffect } from 'react';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import { DataGrid, GridColDef, GridRenderCellParams } from '@mui/x-data-grid';
import IconButton from '@mui/material/IconButton';
import Tooltip from '@mui/material/Tooltip';

import IconPlus from '@tabler/icons-react/dist/esm/icons/IconPlus';
import IconEdit from '@tabler/icons-react/dist/esm/icons/IconEdit';
import IconTrash from '@tabler/icons-react/dist/esm/icons/IconTrash';
import IconFileText from '@tabler/icons-react/dist/esm/icons/IconFileText';

import CustomChip from '@core/components/mui/chip';
import { QuaTrinhDaoTaoEntryType, trangThaiDaoTaoObj, TrangThaiDaoTaoType } from '../../mockDataChiTiet';
// Giả định có DialogThemSuaDaoTao và DialogXacNhanXoaItem
// import DialogThemSuaDaoTao from './DialogThemSuaDaoTao';
// import DialogXacNhanXoaItem from '../../../../components/DialogXacNhanXoaItem';

interface TabQuaTrinhDaoTaoProps {
initialData?: QuaTrinhDaoTaoEntryType[];
idQuanNhan: string;
}

// Helper functions (formatDate, mapIdToName - cần được định nghĩa hoặc import)
const formatDateDatagridDT = (dateString?: string | null): string => {
if (!dateString) return '';
try {
const date = new Date(dateString);
if (isNaN(date.getTime())) return 'Không hợp lệ';
return `<span class="math-inline">\{String\(date\.getDate\(\)\)\.padStart\(2, '0'\)\}/</span>{String(date.getMonth() + 1).padStart(2, '0')}/${date.getFullYear()}`;
} catch (e) { return 'Không hợp lệ'; }
};
const mapIdToStringDT = (id?: string, type?: string) => id || 'N/A'; // Placeholder

const TabQuaTrinhDaoTao = ({ initialData = [], idQuanNhan }: TabQuaTrinhDaoTaoProps) => {
const [listData, setListData] = useState<QuaTrinhDaoTaoEntryType[]>(initialData);
const [openDialog, setOpenDialog] = useState(false);
const [editingData, setEditingData] = useState<QuaTrinhDaoTaoEntryType | null>(null);
const [openConfirmDelete, setOpenConfirmDelete] = useState(false);
const [deletingId, setDeletingId] = useState<string | null>(null);
const [paginationModel, setPaginationModel] = useState({ page: 0, pageSize: 5 });

useEffect(() => {
setListData(initialData);
}, [initialData]);

const handleOpenAddDialog = () => {
setEditingData(null);
setOpenDialog(true);
};

const handleOpenEditDialog = (rowData: QuaTrinhDaoTaoEntryType) => {
setEditingData(rowData);
setOpenDialog(true);
};

const handleCloseDialog = () => {
setOpenDialog(false);
setEditingData(null);
};

const handleSaveData = (savedData: QuaTrinhDaoTaoEntryType) => {
console.log('Saving data (QTĐT):', savedData, 'for QN ID:', idQuanNhan);
if (editingData) {
setListData(prev => prev.map(item => (item.id === savedData.id ? savedData : item)));
} else {
setListData(prev => [...prev, { ...savedData, id: `new_qtdt_${Date.now()}` }]);
}
handleCloseDialog();
};

const handleOpenDeleteDialog = (id: string) => {
setDeletingId(id);
setOpenConfirmDelete(true);
};

const handleCloseConfirmDelete = () => {
setOpenConfirmDelete(false);
setDeletingId(null);
};

const handleConfirmDelete = () => {
if (deletingId) {
console.log('Deleting QTĐT ID:', deletingId);
setListData(prev => prev.filter(item => item.id !== deletingId));
handleCloseConfirmDelete();
}
};

const columns: GridColDef[] = [
{ field: 'TenKhoaHoc', headerName: 'Tên khóa học', width: 250 },
{
field: 'HinhThucDaoTaoID',
headerName: 'Hình thức',
width: 130,
valueGetter: params => mapIdToStringDT(params.value, 'hinhThucDT')
},
{
field: 'CoSoDaoTaoID',
headerName: 'Cơ sở Đào tạo',
width: 200,
valueGetter: params => mapIdToStringDT(params.value, 'coSoDT')
},
{
field: 'ThoiGianBatDau',
headerName: 'Bắt đầu',
width: 110,
valueFormatter: params => formatDateDatagridDT(params.value)
},
{
field: 'ThoiGianKetThuc',
headerName: 'Kết thúc',
width: 110,
valueFormatter: params => formatDateDatagridDT(params.value)
},
{
field: 'VanBangChungChiLoaiID',
headerName: 'Văn bằng/CC',
width: 150,
valueGetter: params => mapIdToStringDT(params.value, 'loaiVBCC')
},
{
field: 'XepLoaiTotNghiepID',
headerName: 'Xếp loại',
width: 120,
valueGetter: params => mapIdToStringDT(params.value, 'xepLoaiTN')
},
{
field: 'TrangThaiDaoTao',
headerName: 'Trạng thái',
width: 150,
renderCell: (params: GridRenderCellParams) => {
const status = params.value as TrangThaiDaoTaoType;
const statusInfo = trangThaiDaoTaoObj[status] || { label: status, color: 'default' };
return <CustomChip label={statusInfo.label} color={statusInfo.color as any} skin="light" size="small" rounded />;
}
},
{
field: 'actions',
headerName: 'Hành động',
width: 150,
sortable: false,
filterable: false,
renderCell: (params: GridRenderCellParams) => (
<Box>
<Tooltip title="Xem File">
<span> {/_ Span để Tooltip hoạt động với IconButton disabled _/}
<IconButton
size="small"
href={params.row.FileDinhKemURL || '#'}
target="\_blank"
disabled={!params.row.FileDinhKemURL}
onClick={(e) => { if (!params.row.FileDinhKemURL) e.preventDefault(); else console.log('Viewing file:', params.row.FileDinhKemURL);}} >
<IconFileText size={20} />
</IconButton>
</span>
</Tooltip>
<Tooltip title="Sửa">
<IconButton size="small" onClick={() => handleOpenEditDialog(params.row as QuaTrinhDaoTaoEntryType)}>
<IconEdit size={20} />
</IconButton>
</Tooltip>
<Tooltip title="Xóa">
<IconButton size="small" onClick={() => handleOpenDeleteDialog(params.row.id as string)}>
<IconTrash size={20} />
</IconButton>
</Tooltip>
</Box>
)
}
// Các cột chi tiết hơn (nếu cần và không làm bảng quá rộng):
// ChuyenNganhDaoTaoID, QuocGiaDaoTaoID, SoHieuVanBang, NgayCapVanBang, NoiCapVanBang, DiemSo, LuanVanDeTaiTotNghiep, GhiChu
];

return (
<Box>
<Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
<Typography variant="h6" sx={{ color: 'primary.main' }}>
Quá trình Đào tạo, Bồi dưỡng
</Typography>
<Button
variant="contained"
startIcon={<IconPlus />}
onClick={handleOpenAddDialog} >
Thêm thông tin Đào tạo
</Button>
</Box>

      <DataGrid
        autoHeight
        rows={listData}
        columns={columns}
        pageSizeOptions={[5, 10, 25]}
        paginationModel={paginationModel}
        onPaginationModelChange={setPaginationModel}
        getRowId={(row) => row.id}
        sx={{
            '& .MuiDataGrid-columnHeaders': { backgroundColor: 'customColors.tableHeaderBg' }
        }}
      />

      {/* {openDialog && (
        <DialogThemSuaDaoTao
          open={openDialog}
          onClose={handleCloseDialog}
          onSubmit={handleSaveData}
          initialData={editingData}
          idQuanNhan={idQuanNhan}
        />
      )}

      {openConfirmDelete && (
        <DialogXacNhanXoaItem
          open={openConfirmDelete}
          onClose={handleCloseConfirmDelete}
          onConfirm={handleConfirmDelete}
          itemName="thông tin đào tạo này"
        />
      )} */}
       {/* Placeholder cho Dialogs */}
       {openDialog && <Typography sx={{mt: 2, p:2, border: '1px dashed grey'}}>Dialog Thêm/Sửa Quá trình Đào tạo (Placeholder - ID Quân nhân: {idQuanNhan}, Data: {JSON.stringify(editingData)})</Typography>}
       {openConfirmDelete && <Typography sx={{mt: 2, p:2, border: '1px dashed red'}}>Dialog Xác nhận Xóa (Placeholder - ID: {deletingId})</Typography>}
    </Box>

);
};

export default TabQuaTrinhDaoTao;
Bước 17: Tạo Component DialogThemSuaDaoTao.tsx (Sơ bộ)
Tương tự như DialogThemSuaQuaTrinhCongTac.tsx, component này sẽ chứa form để nhập liệu cho một quá trình đào tạo.

TypeScript

// src\views\quan-ly-quan-nhan\chi-tiet\components\tabs\DialogThemSuaDaoTao.tsx (Sơ bộ)
// 'use client';
// import React, { useState, useEffect } from 'react';
// import Dialog from '@mui/material/Dialog';
// import DialogTitle from '@mui/material/DialogTitle';
// // ... (các imports khác tương tự DialogThemSuaQuaTrinhCongTac)
// import { QuaTrinhDaoTaoEntryType, TrangThaiDaoTaoType } from '../../mockDataChiTiet';
// import FormControl from '@mui/material/FormControl'; // Cho Select
// import InputLabel from '@mui/material/InputLabel'; // Cho Select
// import Select, { SelectChangeEvent } from '@mui/material/Select'; // Cho Select
// import MenuItem from '@mui/material/MenuItem'; // Cho Select

// interface DialogDaoTaoProps {
// open: boolean;
// onClose: () => void;
// onSubmit: (data: QuaTrinhDaoTaoEntryType) => void;
// initialData: QuaTrinhDaoTaoEntryType | null;
// idQuanNhan: string; // Không nhất thiết phải dùng nếu chỉ lưu thông tin đào tạo
// }

// const DialogThemSuaDaoTao = ({ open, onClose, onSubmit, initialData }: DialogDaoTaoProps) => {
// const [formData, setFormData] = useState<Partial<QuaTrinhDaoTaoEntryType>>(initialData || { TrangThaiDaoTao: 'Đang học' }); // Mặc định trạng thái

// useEffect(() => {
// setFormData(initialData || { TrangThaiDaoTao: 'Đang học' });
// }, [initialData, open]);

// const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | SelectChangeEvent<string>) => {
// const { name, value } = e.target;
// setFormData(prev => ({ ...prev, [name]: value }));
// };

// // const handleDateChange = (name: string, date: Date | null) => {
// // setFormData(prev => ({ ...prev, [name]: date ? date.toISOString() : undefined }));
// // };

// const handleSubmit = () => {
// onSubmit(formData as QuaTrinhDaoTaoEntryType); // Cần validate
// };

// return (
// // <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={vi}>
// <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
// <DialogTitle>{initialData ? 'Sửa Thông tin Đào tạo' : 'Thêm Thông tin Đào tạo'}</DialogTitle>
// <DialogContent>
// <Grid container spacing={3} sx={{mt:1}}>
// <Grid item xs={12}>
// <TextField name="TenKhoaHoc" label="Tên khóa học/Chương trình" value={formData.TenKhoaHoc || ''} onChange={handleChange} fullWidth required />
// </Grid>
// {/_ Ví dụ: HinhThucDaoTaoID, ChuyenNganhDaoTaoID, CoSoDaoTaoID sẽ là Select components _/}
// {/_ Ví dụ cho ThoiGianBatDau _/}
// {/_ <Grid item xs={12} sm={6}>
// <DatePicker
// label="Thời gian bắt đầu"
// value={formData.ThoiGianBatDau ? new Date(formData.ThoiGianBatDau) : null}
// onChange={(date) => handleDateChange('ThoiGianBatDau', date)}
// renderInput={(params) => <TextField {...params} fullWidth required />}
// />
// </Grid> _/}
// {/_ ... Thêm các trường khác ... _/}
// <Grid item xs={12} sm={6}>
// <FormControl fullWidth>
// <InputLabel id="trang-thai-dao-tao-label">Trạng thái Đào tạo</InputLabel>
// <Select
// labelId="trang-thai-dao-tao-label"
// name="TrangThaiDaoTao"
// value={formData.TrangThaiDaoTao || ''}
// label="Trạng thái Đào tạo"
// onChange={handleChange}
// >
// {(Object.keys(trangThaiDaoTaoObj) as TrangThaiDaoTaoType[]).map((key) => (
// <MenuItem key={key} value={key}>{trangThaiDaoTaoObj[key].label}</MenuItem>
// ))}
// </Select>
// </FormControl>
// </Grid>
// </Grid>
// </DialogContent>
// <DialogActions>
// <Button onClick={onClose}>Hủy</Button>
// <Button onClick={handleSubmit} variant="contained">Lưu</Button>
// </DialogActions>
// </Dialog>
// // </LocalizationProvider>
// );
// };
// export default DialogThemSuaDaoTao;
(Lưu ý: Component Dialog trên là sơ bộ, cần hoàn thiện với đầy đủ các trường, validation, và các component Select/Autocomplete cho các ID tham chiếu đến danh mục.)

Bước 18: Cập nhật KhuVucTabsChiTiet.tsx để sử dụng TabQuaTrinhDaoTao
Chỉnh sửa file src\views\quan-ly-quan-nhan\chi-tiet\components\KhuVucTabsChiTiet.tsx:

TypeScript

// src\views\quan-ly-quan-nhan\chi-tiet\components\KhuVucTabsChiTiet.tsx
// ... (các imports khác)
import TabQuaTrinhDaoTao from './tabs/TabQuaTrinhDaoTao';
// ... (import các tab khác khi tạo xong)

// interface TabsProps { ... }

const KhuVucTabsChiTiet = ({ quanNhanData, activeTab, handleTabChange }: TabsProps) => {
const tabContentList: { [key: string]: React.ReactNode } = {
'thong-tin-chung': <TabThongTinChung data={quanNhanData.baseInfo} />,
'ly-lich-ca-nhan': <TabLyLichCaNhan data={quanNhanData.lyLichCaNhan} />,
'qua-trinh-cong-tac': <TabQuaTrinhCongTac initialData={quanNhanData.quaTrinhCongTac} idQuanNhan={quanNhanData.baseInfo.SoHieuQuanNhan} />,
'qua-trinh-dao-tao': <TabQuaTrinhDaoTao initialData={quanNhanData.quaTrinhDaoTao} idQuanNhan={quanNhanData.baseInfo.SoHieuQuanNhan} />,
'khen-thuong': <div>Nội dung Tab Khen thưởng</div>,
// ... các tab khác
};

// ... (phần còn lại của component giữ nguyên)
return (
<TabContext value={activeTab}>
<Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
<TabList /_ ...props... _/ >
{/_ ...Tabs... _/}
</TabList>
</Box>
{Object.keys(tabContentList).map(tabValue => (
<TabPanel key={tabValue} value={tabValue} sx={{ p: 0 }}>
<CardContent>
{tabContentList[tabValue]}
</CardContent>
</TabPanel>
))}
</TabContext>
);
};

export default KhuVucTabsChiTiet;
