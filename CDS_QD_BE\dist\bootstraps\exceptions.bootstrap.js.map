{"version": 3, "sources": ["../../src/bootstraps/exceptions.bootstrap.ts"], "sourcesContent": ["import { INestApplication } from '@nestjs/common';\r\nimport { AllExceptionsFilter } from '~/common/filters/exception.filter';\r\nimport { TypeOrmFilter } from '~/common/filters/typeorm.filter';\r\n\r\nexport function bootstrapExceptions(app: INestApplication): void {\r\n    // const { httpAdapter } = app.get(HttpAdapterHost);\r\n    app.useGlobalFilters(new AllExceptionsFilter(), new TypeOrmFilter());\r\n}\r\n"], "names": ["bootstrapExceptions", "app", "useGlobalFilters", "AllExceptionsFilter", "TypeOrmFilter"], "mappings": "oGAIgBA,6DAAAA,sDAHoB,mEACN,oCAEvB,SAASA,oBAAoBC,GAAqB,EAErDA,IAAIC,gBAAgB,CAAC,IAAIC,oCAAmB,CAAI,IAAIC,4BAAa,CACrE"}