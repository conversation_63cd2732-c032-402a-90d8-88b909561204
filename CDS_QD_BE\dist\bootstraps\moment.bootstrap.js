"use strict";Object.defineProperty(exports,"__esModule",{value:true});Object.defineProperty(exports,"bootstrapMoment",{enumerable:true,get:function(){return bootstrapMoment}});const _moment=/*#__PURE__*/_interop_require_default(require("moment"));function _interop_require_default(obj){return obj&&obj.__esModule?obj:{default:obj}}function initMoment(){_moment.default.tz.setDefault(process.env.TZ);_moment.default.updateLocale("en",{week:{dow:1}});console.log("LOG:: initMoment:",(0,_moment.default)().format("YYYY-MM-DD HH:mm:ss"))}function bootstrapMoment(){initMoment()}
//# sourceMappingURL=moment.bootstrap.js.map