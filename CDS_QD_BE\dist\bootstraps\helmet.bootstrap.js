"use strict";Object.defineProperty(exports,"__esModule",{value:true});Object.defineProperty(exports,"bootstrapHelmet",{enumerable:true,get:function(){return bootstrapHelmet}});const _helmet=/*#__PURE__*/_interop_require_default(require("helmet"));function _interop_require_default(obj){return obj&&obj.__esModule?obj:{default:obj}}function bootstrapHelmet(app){if(process.env.NODE_ENV==="production"){app.use((0,_helmet.default)())}}
//# sourceMappingURL=helmet.bootstrap.js.map