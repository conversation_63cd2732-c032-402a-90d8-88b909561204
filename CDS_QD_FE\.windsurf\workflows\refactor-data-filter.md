---
description: <PERSON><PERSON> trình refactor page/component có sử dụng bảng dữ liệu và bộ lọc
---

# Quy trình Refactor Page/Component có Table và Filter

Quy trình này mô tả cách refactor các page/component có sử dụng bảng dữ liệu và bộ lọc, tương tự như đã thực hiện với trang Danh sách Đảng viên. Phương pháp này tách biệt logic xử lý và quản lý state vào các hook riêng, giúp code dễ bảo trì hơn và tuân thủ nguyên tắc separation of concerns.

## 1. Cấu trúc hook và component

### Hook hệ thống

Sử dụng các hook cơ bản sau:

- `useDataTable`: Quản lý trạng thái bảng dữ liệu
- `useFilter`: Quản lý trạng thái bộ lọc
- `useTableManager`: <PERSON>ế<PERSON> hợp các hook và cung cấp API thống nhất

### Shared Components

- `FilterPanel`: Container cho các bộ lọc
- `SelectFilter`, `TextFilter`: Các loại filter cung cấp giao diện người dùng
- `DataTable`: Component bảng dữ liệu với phân trang, sắp xếp
- `RowActions`: Các nút hành động trên hàng

### Hook riêng cho module

Mỗi module sẽ có hook riêng, ví dụ:

- `useDangVienTable`: Hook quản lý danh sách đảng viên
- `useKeHoachTable`: Hook quản lý kế hoạch sinh hoạt đảng

## 2. Quy trình Refactor

### Bước 1: Phân tích component hiện tại

- Xác định loại dữ liệu hiển thị trong bảng
- Xác định các bộ lọc và cấu trúc của chúng
- Xác định các dialog/modal đang sử dụng
- Phân tích trạng thái nào cần được đưa vào hook

### Bước 2: Tạo hook riêng cho module

1. **Tạo thư mục và file**
   ```bash
   mkdir -p src/hooks/modules/[đường-dẫn-module]
   touch src/hooks/modules/[đường-dẫn-module]/useModuleTable.ts
   touch src/hooks/modules/[đường-dẫn-module]/index.ts
   ```

2. **Định nghĩa các interface**
   ```typescript
   // Interface cho filter value
   export interface ModuleFilterValues {
     searchTerm: string
     // Thêm các trường khác tùy vào yêu cầu
   }
   
   // Interface cho options của hook
   export interface UseModuleTableOptions {
     initialData: ModuleDataType[]
     defaultPageSize?: number
     onDataChange?: (data: ModuleDataType[]) => void
   }
   
   // Interface cho kết quả của hook
   export interface UseModuleTableResult {
     // Dữ liệu và trạng thái
     dataList: ModuleDataType[]
     filteredDataList: ModuleDataType[]
     filters: ModuleFilterValues
     setFilters: (values: Partial<ModuleFilterValues>) => void
     pagination: {
       pageIndex: number
       pageSize: number
     }
     
     // Các hàm xử lý bảng
     handlePageChange: (page: number) => void
     handlePageSizeChange: (pageSize: number) => void
     handleApplyFilters: (filters: ModuleFilterValues) => void
     handleResetFilters: () => void
     handleSortChange: (sortModel: any) => void
     handleExport: () => void
     
     // Các trạng thái và hàm xử lý dialog
     // ... Thêm tùy theo yêu cầu
   }
   ```

3. **Triển khai hook**
   ```typescript
   export function useModuleTable({
     initialData,
     defaultPageSize = 10,
     onDataChange
   }: UseModuleTableOptions): UseModuleTableResult {
     // State cho dữ liệu
     const [dataList, setDataList] = useState<ModuleDataType[]>(initialData)
     const [filteredDataList, setFilteredDataList] = useState<ModuleDataType[]>(initialData)
     
     // State cho dialog
     // ... Tùy theo yêu cầu
     
     // Sử dụng tableManager
     const tableManager = useTableManager({
       data: dataList,
       defaultPageSize,
       filterConfig: {
         defaultValues: {
           searchTerm: '',
           // Thêm các giá trị mặc định khác
         }
       },
       // Các cấu hình khác
     })
     
     // Xử lý áp dụng bộ lọc
     const handleApplyFilters = useCallback((filters: ModuleFilterValues) => {
       tableManager.setFilters(filters)
       
       // Tạo hàm lọc tùy chỉnh
       const customFilter = (item: ModuleDataType) => {
         // Logic lọc ở đây
         // Ví dụ:
         if (filters.searchTerm) {
           const searchTerm = filters.searchTerm.toLowerCase()
           // Tìm kiếm trong các trường
           // ...
         }
         
         return true // Mặc định
       }
       
       // Lọc dữ liệu và cập nhật state
       const filteredList = dataList.filter(customFilter)
       setFilteredDataList(filteredList)
       
       // Thông báo thay đổi nếu cần
       if (onDataChange) {
         onDataChange(filteredList)
       }
     }, [tableManager, dataList, onDataChange])
     
     // Xử lý các dialog và hành động khác
     // ...
     
     // Trả về kết quả
     return {
       dataList,
       filteredDataList,
       filters: tableManager.filters,
       setFilters: tableManager.setFilters,
       pagination: tableManager.pagination,
       
       // Các hàm xử lý
       handlePageChange: tableManager.onPageChange,
       handlePageSizeChange: tableManager.onPageSizeChange,
       handleApplyFilters,
       handleResetFilters: tableManager.resetFilters,
       handleSortChange: tableManager.onSortChange,
       handleExport: tableManager.exportData,
       
       // Các trạng thái và hàm xử lý dialog
       // ...
     }
   }
   ```

4. **Cập nhật file index.ts**
   ```typescript
   export * from './useModuleTable'
   ```

### Bước 3: Refactor Page component

```tsx
// ModulePage.tsx
import { useModuleTable } from '@/hooks/modules/[đường-dẫn-module]'

const ModulePage = () => {
  // Khởi tạo router và params
  const router = useRouter()
  const { lang: locale } = useParams()
  
  // Sử dụng hook
  const {
    filteredDataList,
    pagination,
    filters,
    handlePageChange,
    handlePageSizeChange,
    handleApplyFilters,
    handleResetFilters,
    handleSortChange,
    handleExport,
    setFilters,
    // ... Các thuộc tính và hàm khác
  } = useModuleTable({
    initialData: mockData,
    defaultPageSize: 10
  })
  
  // Các hàm xử lý tương tác
  const handleViewDetails = (id: number) => {
    router.push(getLocalizedUrl(`/path-to-details/${id}`, locale as Locale))
  }
  
  return (
    <Grid container spacing={6}>
      <Grid item xs={12}>
        <Typography variant='h4'>Tiêu đề trang</Typography>
      </Grid>
      <Grid item xs={12}>
        <BangDuLieu
          rows={filteredDataList}
          onViewDetails={handleViewDetails}
          // ... Các prop khác
          onApplyFilters={handleApplyFilters}
          onResetFilters={handleResetFilters}
          pageSize={pagination.pageSize}
          onPageChange={handlePageChange}
          onPageSizeChange={handlePageSizeChange}
          onSortModelChange={handleSortChange}
          filters={filters}
          onFilterChange={(name, value) => {
            setFilters({
              [name]: value
            })
          }}
        />
      </Grid>
      
      {/* Dialog Components */}
    </Grid>
  )
}
```

### Bước 4: Refactor BangDuLieu component

```tsx
// Cập nhật interface
interface BangDuLieuProps {
  rows: ModuleDataType[]
  // ... Các prop hiện tại
  filters: FilterValues
  onFilterChange: (name: string, value: any) => void
}

// Component
const BangDuLieu = ({
  rows,
  // ... Các prop hiện tại
  filters,
  onFilterChange
}: BangDuLieuProps) => {
  // Không còn cần state filters cục bộ nữa
  
  // Render filter form
  const renderFilterForm = () => (
    <>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <TextFilter
            name='searchTerm'
            value={filters.searchTerm}
            onChange={onFilterChange}
            placeholder='Tìm kiếm...'
            startAdornment={<i className='tabler-search text-xl' />}
            fullWidth
          />
        </Grid>
        {/* Các filter khác */}
      </Grid>
    </>
  )
  
  return (
    <>
      <FilterPanel
        title='Bộ lọc'
        defaultValues={{
          searchTerm: '',
          // Các giá trị mặc định khác
        }}
        values={filters}
        onChange={(newValues) => {
          // Xử lý thay đổi filter
          Object.keys(newValues).forEach((key) => {
            onFilterChange(key, newValues[key as keyof typeof newValues])
          })
        }}
        onApply={() => onApplyFilters(filters)}
        onReset={onResetFilters}
      >
        {renderFilterForm}
      </FilterPanel>

      <DataTable
        // ... Các prop hiện tại
      />
    </>
  )
}
```

## 3. Ví dụ thực tế: Refactor Kế hoạch sinh hoạt Đảng

### Cấu trúc thư mục

```
src/hooks/modules/cong-tac-dang/sinh-hoat-dang/ke-hoach/
├── index.ts
└── useKeHoachTable.ts
```

### Các bước triển khai

1. **Tạo hook useKeHoachTable.ts** với các interface và impl
   - KeHoachFilterValues (searchTerm, trangThaiPheDuyet, loaiKeHoach)
   - UseKeHoachTableOptions và UseKeHoachTableResult
   - Logic lọc dữ liệu theo các tiêu chí

2. **Refactor KeHoachSinhHoatDangPage.tsx**
   - Sử dụng useKeHoachTable thay vì state cục bộ
   - Truyền filters và các hàm xử lý xuống BangDuLieu

3. **Refactor BangDuLieu.tsx**
   - Loại bỏ state filters cục bộ, sử dụng từ props
   - Cập nhật FilterPanel để sử dụng filters từ props

## 4. Checklist refactor

- [ ] Phân tích trang hiện tại
- [ ] Xác định cấu trúc dữ liệu, bộ lọc, dialog cần quản lý
- [ ] Tạo hook riêng cho module
  - [ ] Xác định các interface
  - [ ] Triển khai hook với đầy đủ state và handlers
  - [ ] Cập nhật các file index.ts
- [ ] Refactor Page component để sử dụng hook
- [ ] Refactor BangDuLieu component
  - [ ] Cập nhật interface
  - [ ] Loại bỏ state cục bộ
  - [ ] Cập nhật FilterPanel
- [ ] Refactor Dialog components (nếu cần)
- [ ] Kiểm thử
  - [ ] Kiểm tra filters
  - [ ] Kiểm tra phân trang, sắp xếp
  - [ ] Kiểm tra dialog

## 5. Lưu ý và best practices

1. **Naming conventions**
   - Tên hook: `use[Module]Table` hoặc `use[Tên-Module]`
   - Tên interface: `[Module]Type`, `[Module]FilterValues`
   - Tên state: `[module]List`, `filtered[Module]List`

2. **Optimize performance**
   - Sử dụng `useMemo` cho columns để tránh tái tạo
   - Sử dụng `useCallback` cho các hàm xử lý

3. **Single source of truth**
   - Quản lý state tập trung tại hook
   - Component chỉ lấy dữ liệu từ props, không duy trì state riêng

4. **Sử dụng TypeScript đầy đủ**
   - Định nghĩa type cho mọi state, props và function
   - Dùng `keyof typeof` để truy cập an toàn property

Lưu ý rằng, quy trình này có thể áp dụng cho bất kỳ trang danh sách/bảng dữ liệu nào có tính năng tương tự trong dự án.
