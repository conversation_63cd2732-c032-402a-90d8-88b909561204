"use strict";Object.defineProperty(exports,"__esModule",{value:true});Object.defineProperty(exports,"bootstrapSwagger",{enumerable:true,get:function(){return bootstrapSwagger}});const _swagger=require("@nestjs/swagger");const _nodefs=require("node:fs");function bootstrapSwagger(app,appVersion){if(process.env.NODE_ENV==="development"){const config=new _swagger.DocumentBuilder().setTitle(`${process.env.APP_NAME} Swagger`).setDescription(`The ${process.env.APP_NAME} API documents`).setVersion(appVersion).addBearerAuth().setLicense("CDS-QD.dev","https://cdsqd.dev").build();const document=_swagger.SwaggerModule.createDocument(app,config);_swagger.SwaggerModule.setup("docs",app,document,{swaggerOptions:{tagsSorter:"alpha",persistAuthorization:true,docExpansion:"none"}});(0,_nodefs.writeFileSync)("./swagger-spec.json",JSON.stringify(document,null,2))}}
//# sourceMappingURL=swagger.bootstrap.js.map