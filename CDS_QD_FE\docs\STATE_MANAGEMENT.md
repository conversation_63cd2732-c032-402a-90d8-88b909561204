# 🐻 Zustand - Quản lý state đơn giản và hiệu quả

## 1. T<PERSON><PERSON> quan
- <PERSON><PERSON><PERSON> là thư viện quản lý state siêu nhẹ (chỉ ~1KB gzipped)
- <PERSON><PERSON><PERSON><PERSON> c<PERSON>n Provider, d<PERSON> dàng tích hợp với React
- Hỗ trợ TypeScript đầy đủ
- Tích hợp sẵn middleware (devtools, persist, v.v.)

## 2. Cài đặt

```bash
pnpm add zustand
# Hoặc
npm install zustand
yarn add zustand
```

## 3. <PERSON><PERSON><PERSON> tr<PERSON><PERSON> thư mục

```
src/
└── store/
    ├── slices/           # Các store con theo từng domain
    │   ├── auth.store.ts    # Store cho xác thực
    │   ├── ui.store.ts      # Store cho UI state
    │   └── [module].store.ts # Store cho từng module
    └── index.ts            # Export tất cả các store
```

## 4. Tạo store cơ bản

```typescript
// store/slices/counter.store.ts
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

interface CounterState {
  count: number;
  increment: () => void;
  decrement: () => void;
  reset: () => void;
}

export const useCounterStore = create<CounterState>()(
  devtools(
    (set) => ({
      count: 0,
      increment: () => set((state) => ({ count: state.count + 1 })),
      decrement: () => set((state) => ({ count: state.count - 1 })),
      reset: () => set({ count: 0 }),
    }),
    { name: 'counter-storage' }
  )
);
```

## 5. Sử dụng trong component

```typescript
'use client';

import { useCounterStore } from '@/store/slices/counter.store';

export function Counter() {
  const { count, increment, decrement, reset } = useCounterStore();

  return (
    <div>
      <span>Count: {count}</span>
      <button onClick={increment}>+</button>
      <button onClick={decrement}>-</button>
      <button onClick={reset}>Reset</button>
    </div>
  );
}
```

## 6. Best Practices

### 6.1. Tách biệt store theo domain
- Mỗi domain (auth, UI, module) nên có store riêng
- Tránh tạo store quá lớn, khó bảo trì

### 6.2. Selector để tối ưu hiệu năng
```typescript
// Thay vì
const { user } = useAuthStore();

// Nên dùng selector
const user = useAuthStore((state) => state.user);
```

### 6.3. Sử dụng middleware
- `devtools`: Debug dễ dàng hơn
- `persist`: Lưu trữ state vào localStorage
- `immer`: Làm việc với nested state dễ dàng hơn

```typescript
import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

const useStore = create(
  devtools(
    persist(
      (set) => ({
        // state & actions
      }),
      {
        name: 'storage-key',
      }
    )
  )
);
```

### 6.4. Type Safety
- Luôn định nghĩa rõ ràng interface cho state
- Sử dụng `create<T>()` để có type inference tốt hơn

## 7. Kết hợp với React Query

```typescript
// store/slices/users.store.ts
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { User } from '@/types';

interface UsersState {
  users: User[];
  selectedUser: User | null;
  setUsers: (users: User[]) => void;
  selectUser: (user: User) => void;
  clearSelection: () => void;
}

export const useUsersStore = create<UsersState>()(
  devtools(
    (set) => ({
      users: [],
      selectedUser: null,
      setUsers: (users) => set({ users }),
      selectUser: (user) => set({ selectedUser: user }),
      clearSelection: () => set({ selectedUser: null }),
    }),
    { name: 'users-storage' }
  )
);
```

## 8. Debugging
- Cài đặt Redux DevTools Extension cho trình duyệt
- Sử dụng `devtools` middleware để theo dõi state changes
- Đặt tên cho store để dễ phân biệt khi debug

## 9. Tài liệu tham khảo
- [Zustand Documentation](https://docs.pmnd.rs/zustand/getting-started/introduction)
- [Zustand GitHub](https://github.com/pmndrs/zustand)
- [Zustand with TypeScript](https://docs.pmnd.rs/zustand/guides/typescript)

## 10. Lưu ý khi sử dụng với Next.js
- Đánh dấu component sử dụng store với 'use client'
- Sử dụng `persist` middleware cẩn thận để tránh lỗi hydration
- Khởi tạo store ở cấp độ phù hợp trong component tree
