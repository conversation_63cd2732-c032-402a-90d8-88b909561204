/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { YeuCauPheDuyetEntity } from '~/database/typeorm/entities/yeuCauPheDuyet.entity';

@Injectable()
export class YeuCauPheDuyetRepository extends Repository<YeuCauPheDuyetEntity> {
    constructor(private dataSource: DataSource) {
        super(YeuCauPheDuyetEntity, dataSource.createEntityManager());
    }
}
