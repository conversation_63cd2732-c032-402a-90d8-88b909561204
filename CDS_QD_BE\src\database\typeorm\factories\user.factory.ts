import moment from 'moment';
import { setSeederFactory } from 'typeorm-extension';
import { UserEntity } from '~/database/typeorm/entities/user.entity';

export default setSeederFactory(UserEntity, (faker) => {
    const entity = new UserEntity();

    entity.hoTen = faker.person.fullName();
    entity.email = faker.internet.email();
    entity.soDienThoai = faker.string.numeric(9);

    return entity;
});
