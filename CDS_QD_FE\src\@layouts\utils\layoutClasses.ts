// Classes for vertical layout
export const verticalLayoutClasses = {
  root: 'ts-vertical-layout',
  contentWrapper: 'ts-vertical-layout-content-wrapper',
  header: 'ts-vertical-layout-header',
  headerFixed: 'ts-vertical-layout-header-fixed',
  headerStatic: 'ts-vertical-layout-header-static',
  headerFloating: 'ts-vertical-layout-header-floating',
  headerDetached: 'ts-vertical-layout-header-detached',
  headerAttached: 'ts-vertical-layout-header-attached',
  headerContentCompact: 'ts-vertical-layout-header-content-compact',
  headerContentWide: 'ts-vertical-layout-header-content-wide',
  headerBlur: 'ts-vertical-layout-header-blur',
  navbar: 'ts-vertical-layout-navbar',
  navbarContent: 'ts-vertical-layout-navbar-content',
  content: 'ts-vertical-layout-content',
  contentCompact: 'ts-vertical-layout-content-compact',
  contentWide: 'ts-vertical-layout-content-wide',
  footer: 'ts-vertical-layout-footer',
  footerStatic: 'ts-vertical-layout-footer-static',
  footerFixed: 'ts-vertical-layout-footer-fixed',
  footerDetached: 'ts-vertical-layout-footer-detached',
  footerAttached: 'ts-vertical-layout-footer-attached',
  footerContentWrapper: 'ts-vertical-layout-footer-content-wrapper',
  footerContent: 'ts-vertical-layout-footer-content',
  footerContentCompact: 'ts-vertical-layout-footer-content-compact',
  footerContentWide: 'ts-vertical-layout-footer-content-wide'
}

// Classes for horizontal layout
export const horizontalLayoutClasses = {
  root: 'ts-horizontal-layout',
  contentWrapper: 'ts-horizontal-layout-content-wrapper',
  header: 'ts-horizontal-layout-header',
  headerFixed: 'ts-horizontal-layout-header-fixed',
  headerStatic: 'ts-horizontal-layout-header-static',
  headerContentCompact: 'ts-horizontal-layout-header-content-compact',
  headerContentWide: 'ts-horizontal-layout-header-content-wide',
  headerBlur: 'ts-horizontal-layout-header-blur',
  navbar: 'ts-horizontal-layout-navbar',
  navbarContent: 'ts-horizontal-layout-navbar-content',
  navigation: 'ts-horizontal-layout-navigation',
  navigationContentWrapper: 'ts-horizontal-layout-navigation-content-wrapper',
  content: 'ts-horizontal-layout-content',
  contentCompact: 'ts-horizontal-layout-content-compact',
  contentWide: 'ts-horizontal-layout-content-wide',
  footer: 'ts-horizontal-layout-footer',
  footerStatic: 'ts-horizontal-layout-footer-static',
  footerFixed: 'ts-horizontal-layout-footer-fixed',
  footerContentWrapper: 'ts-horizontal-layout-footer-content-wrapper',
  footerContent: 'ts-horizontal-layout-footer-content',
  footerContentCompact: 'ts-horizontal-layout-footer-content-compact',
  footerContentWide: 'ts-horizontal-layout-footer-content-wide'
}

// Classes for blank layout
export const blankLayoutClasses = {
  root: 'ts-blank-layout'
}
