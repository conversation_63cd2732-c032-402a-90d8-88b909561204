/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 *  Swagger
 * The  API documents
 * OpenAPI spec version: 1.0
 */
import { useInfiniteQuery, useMutation, useQuery } from '@tanstack/react-query'
import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseInfiniteQueryResult,
  DefinedUseQueryResult,
  InfiniteData,
  MutationFunction,
  QueryClient,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseInfiniteQueryOptions,
  UseInfiniteQueryResult,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult
} from '@tanstack/react-query'

import type { CreatePermissionDto, PermissionControllerFindAllParams, UpdatePermissionDto } from '.././model'

import { customInstance } from '../../mutator/custom-instance'
import type { ApiResponse } from '../../types'

export const permissionControllerCreate = (createPermissionDto: CreatePermissionDto, signal?: AbortSignal) => {
  return customInstance<ApiResponse<void>>({
    url: `/permission`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: createPermissionDto,
    signal
  })
}

export const getPermissionControllerCreateMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof permissionControllerCreate>>,
    TError,
    { data: CreatePermissionDto },
    TContext
  >
}): UseMutationOptions<
  Awaited<ReturnType<typeof permissionControllerCreate>>,
  TError,
  { data: CreatePermissionDto },
  TContext
> => {
  const mutationKey = ['permissionControllerCreate']
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } }

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof permissionControllerCreate>>,
    { data: CreatePermissionDto }
  > = props => {
    const { data } = props ?? {}

    return permissionControllerCreate(data)
  }

  return { mutationFn, ...mutationOptions }
}

export type PermissionControllerCreateMutationResult = NonNullable<
  Awaited<ReturnType<typeof permissionControllerCreate>>
>
export type PermissionControllerCreateMutationBody = CreatePermissionDto
export type PermissionControllerCreateMutationError = unknown

export const usePermissionControllerCreate = <TError = unknown, TContext = unknown>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof permissionControllerCreate>>,
      TError,
      { data: CreatePermissionDto },
      TContext
    >
  },
  queryClient?: QueryClient
): UseMutationResult<
  Awaited<ReturnType<typeof permissionControllerCreate>>,
  TError,
  { data: CreatePermissionDto },
  TContext
> => {
  const mutationOptions = getPermissionControllerCreateMutationOptions(options)

  return useMutation(mutationOptions, queryClient)
}
export const permissionControllerFindAll = (params?: PermissionControllerFindAllParams, signal?: AbortSignal) => {
  return customInstance<ApiResponse<void>>({ url: `/permission`, method: 'GET', params, signal })
}

export const getPermissionControllerFindAllQueryKey = (params?: PermissionControllerFindAllParams) => {
  return [`/permission`, ...(params ? [params] : [])] as const
}

export const getPermissionControllerFindAllInfiniteQueryOptions = <
  TData = InfiniteData<
    Awaited<ReturnType<typeof permissionControllerFindAll>>,
    PermissionControllerFindAllParams['page']
  >,
  TError = unknown
>(
  params?: PermissionControllerFindAllParams,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof permissionControllerFindAll>>,
        TError,
        TData,
        Awaited<ReturnType<typeof permissionControllerFindAll>>,
        QueryKey,
        PermissionControllerFindAllParams['page']
      >
    >
  }
) => {
  const { query: queryOptions } = options ?? {}

  const queryKey = queryOptions?.queryKey ?? getPermissionControllerFindAllQueryKey(params)

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof permissionControllerFindAll>>,
    QueryKey,
    PermissionControllerFindAllParams['page']
  > = ({ signal, pageParam }) => permissionControllerFindAll({ ...params, page: pageParam || params?.['page'] }, signal)

  return { queryKey, queryFn, staleTime: 10000, ...queryOptions } as UseInfiniteQueryOptions<
    Awaited<ReturnType<typeof permissionControllerFindAll>>,
    TError,
    TData,
    Awaited<ReturnType<typeof permissionControllerFindAll>>,
    QueryKey,
    PermissionControllerFindAllParams['page']
  > & { queryKey: DataTag<QueryKey, TData, TError> }
}

export type PermissionControllerFindAllInfiniteQueryResult = NonNullable<
  Awaited<ReturnType<typeof permissionControllerFindAll>>
>
export type PermissionControllerFindAllInfiniteQueryError = unknown

export function usePermissionControllerFindAllInfinite<
  TData = InfiniteData<
    Awaited<ReturnType<typeof permissionControllerFindAll>>,
    PermissionControllerFindAllParams['page']
  >,
  TError = unknown
>(
  params: undefined | PermissionControllerFindAllParams,
  options: {
    query: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof permissionControllerFindAll>>,
        TError,
        TData,
        Awaited<ReturnType<typeof permissionControllerFindAll>>,
        QueryKey,
        PermissionControllerFindAllParams['page']
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof permissionControllerFindAll>>,
          TError,
          Awaited<ReturnType<typeof permissionControllerFindAll>>,
          QueryKey
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): DefinedUseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function usePermissionControllerFindAllInfinite<
  TData = InfiniteData<
    Awaited<ReturnType<typeof permissionControllerFindAll>>,
    PermissionControllerFindAllParams['page']
  >,
  TError = unknown
>(
  params?: PermissionControllerFindAllParams,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof permissionControllerFindAll>>,
        TError,
        TData,
        Awaited<ReturnType<typeof permissionControllerFindAll>>,
        QueryKey,
        PermissionControllerFindAllParams['page']
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof permissionControllerFindAll>>,
          TError,
          Awaited<ReturnType<typeof permissionControllerFindAll>>,
          QueryKey
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function usePermissionControllerFindAllInfinite<
  TData = InfiniteData<
    Awaited<ReturnType<typeof permissionControllerFindAll>>,
    PermissionControllerFindAllParams['page']
  >,
  TError = unknown
>(
  params?: PermissionControllerFindAllParams,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof permissionControllerFindAll>>,
        TError,
        TData,
        Awaited<ReturnType<typeof permissionControllerFindAll>>,
        QueryKey,
        PermissionControllerFindAllParams['page']
      >
    >
  },
  queryClient?: QueryClient
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }

export function usePermissionControllerFindAllInfinite<
  TData = InfiniteData<
    Awaited<ReturnType<typeof permissionControllerFindAll>>,
    PermissionControllerFindAllParams['page']
  >,
  TError = unknown
>(
  params?: PermissionControllerFindAllParams,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof permissionControllerFindAll>>,
        TError,
        TData,
        Awaited<ReturnType<typeof permissionControllerFindAll>>,
        QueryKey,
        PermissionControllerFindAllParams['page']
      >
    >
  },
  queryClient?: QueryClient
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
  const queryOptions = getPermissionControllerFindAllInfiniteQueryOptions(params, options)

  const query = useInfiniteQuery(queryOptions, queryClient) as UseInfiniteQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>
  }

  query.queryKey = queryOptions.queryKey

  return query
}

export const getPermissionControllerFindAllQueryOptions = <
  TData = Awaited<ReturnType<typeof permissionControllerFindAll>>,
  TError = unknown
>(
  params?: PermissionControllerFindAllParams,
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof permissionControllerFindAll>>, TError, TData>> }
) => {
  const { query: queryOptions } = options ?? {}

  const queryKey = queryOptions?.queryKey ?? getPermissionControllerFindAllQueryKey(params)

  const queryFn: QueryFunction<Awaited<ReturnType<typeof permissionControllerFindAll>>> = ({ signal }) =>
    permissionControllerFindAll(params, signal)

  return { queryKey, queryFn, staleTime: 10000, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof permissionControllerFindAll>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> }
}

export type PermissionControllerFindAllQueryResult = NonNullable<
  Awaited<ReturnType<typeof permissionControllerFindAll>>
>
export type PermissionControllerFindAllQueryError = unknown

export function usePermissionControllerFindAll<
  TData = Awaited<ReturnType<typeof permissionControllerFindAll>>,
  TError = unknown
>(
  params: undefined | PermissionControllerFindAllParams,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof permissionControllerFindAll>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof permissionControllerFindAll>>,
          TError,
          Awaited<ReturnType<typeof permissionControllerFindAll>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function usePermissionControllerFindAll<
  TData = Awaited<ReturnType<typeof permissionControllerFindAll>>,
  TError = unknown
>(
  params?: PermissionControllerFindAllParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof permissionControllerFindAll>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof permissionControllerFindAll>>,
          TError,
          Awaited<ReturnType<typeof permissionControllerFindAll>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function usePermissionControllerFindAll<
  TData = Awaited<ReturnType<typeof permissionControllerFindAll>>,
  TError = unknown
>(
  params?: PermissionControllerFindAllParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof permissionControllerFindAll>>, TError, TData>>
  },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }

export function usePermissionControllerFindAll<
  TData = Awaited<ReturnType<typeof permissionControllerFindAll>>,
  TError = unknown
>(
  params?: PermissionControllerFindAllParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof permissionControllerFindAll>>, TError, TData>>
  },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
  const queryOptions = getPermissionControllerFindAllQueryOptions(params, options)

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>
  }

  query.queryKey = queryOptions.queryKey

  return query
}

export const permissionControllerFindOne = (id: string, signal?: AbortSignal) => {
  return customInstance<ApiResponse<ApiResponse<void>>>({ url: `/permission/${id}`, method: 'GET', signal })
}

export const getPermissionControllerFindOneQueryKey = (id: string) => {
  return [`/permission/${id}`] as const
}

export const getPermissionControllerFindOneInfiniteQueryOptions = <
  TData = InfiniteData<Awaited<ReturnType<typeof permissionControllerFindOne>>>,
  TError = unknown
>(
  id: string,
  options?: {
    query?: Partial<UseInfiniteQueryOptions<Awaited<ReturnType<typeof permissionControllerFindOne>>, TError, TData>>
  }
) => {
  const { query: queryOptions } = options ?? {}

  const queryKey = queryOptions?.queryKey ?? getPermissionControllerFindOneQueryKey(id)

  const queryFn: QueryFunction<Awaited<ReturnType<typeof permissionControllerFindOne>>> = ({ signal }) =>
    permissionControllerFindOne(id, signal)

  return { queryKey, queryFn, enabled: !!id, staleTime: 10000, ...queryOptions } as UseInfiniteQueryOptions<
    Awaited<ReturnType<typeof permissionControllerFindOne>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> }
}

export type PermissionControllerFindOneInfiniteQueryResult = NonNullable<
  Awaited<ReturnType<typeof permissionControllerFindOne>>
>
export type PermissionControllerFindOneInfiniteQueryError = unknown

export function usePermissionControllerFindOneInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof permissionControllerFindOne>>>,
  TError = unknown
>(
  id: string,
  options: {
    query: Partial<UseInfiniteQueryOptions<Awaited<ReturnType<typeof permissionControllerFindOne>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof permissionControllerFindOne>>,
          TError,
          Awaited<ReturnType<typeof permissionControllerFindOne>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): DefinedUseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function usePermissionControllerFindOneInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof permissionControllerFindOne>>>,
  TError = unknown
>(
  id: string,
  options?: {
    query?: Partial<UseInfiniteQueryOptions<Awaited<ReturnType<typeof permissionControllerFindOne>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof permissionControllerFindOne>>,
          TError,
          Awaited<ReturnType<typeof permissionControllerFindOne>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function usePermissionControllerFindOneInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof permissionControllerFindOne>>>,
  TError = unknown
>(
  id: string,
  options?: {
    query?: Partial<UseInfiniteQueryOptions<Awaited<ReturnType<typeof permissionControllerFindOne>>, TError, TData>>
  },
  queryClient?: QueryClient
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }

export function usePermissionControllerFindOneInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof permissionControllerFindOne>>>,
  TError = unknown
>(
  id: string,
  options?: {
    query?: Partial<UseInfiniteQueryOptions<Awaited<ReturnType<typeof permissionControllerFindOne>>, TError, TData>>
  },
  queryClient?: QueryClient
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
  const queryOptions = getPermissionControllerFindOneInfiniteQueryOptions(id, options)

  const query = useInfiniteQuery(queryOptions, queryClient) as UseInfiniteQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>
  }

  query.queryKey = queryOptions.queryKey

  return query
}

export const getPermissionControllerFindOneQueryOptions = <
  TData = Awaited<ReturnType<typeof permissionControllerFindOne>>,
  TError = unknown
>(
  id: string,
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof permissionControllerFindOne>>, TError, TData>> }
) => {
  const { query: queryOptions } = options ?? {}

  const queryKey = queryOptions?.queryKey ?? getPermissionControllerFindOneQueryKey(id)

  const queryFn: QueryFunction<Awaited<ReturnType<typeof permissionControllerFindOne>>> = ({ signal }) =>
    permissionControllerFindOne(id, signal)

  return { queryKey, queryFn, enabled: !!id, staleTime: 10000, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof permissionControllerFindOne>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> }
}

export type PermissionControllerFindOneQueryResult = NonNullable<
  Awaited<ReturnType<typeof permissionControllerFindOne>>
>
export type PermissionControllerFindOneQueryError = unknown

export function usePermissionControllerFindOne<
  TData = Awaited<ReturnType<typeof permissionControllerFindOne>>,
  TError = unknown
>(
  id: string,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof permissionControllerFindOne>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof permissionControllerFindOne>>,
          TError,
          Awaited<ReturnType<typeof permissionControllerFindOne>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function usePermissionControllerFindOne<
  TData = Awaited<ReturnType<typeof permissionControllerFindOne>>,
  TError = unknown
>(
  id: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof permissionControllerFindOne>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof permissionControllerFindOne>>,
          TError,
          Awaited<ReturnType<typeof permissionControllerFindOne>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function usePermissionControllerFindOne<
  TData = Awaited<ReturnType<typeof permissionControllerFindOne>>,
  TError = unknown
>(
  id: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof permissionControllerFindOne>>, TError, TData>>
  },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }

export function usePermissionControllerFindOne<
  TData = Awaited<ReturnType<typeof permissionControllerFindOne>>,
  TError = unknown
>(
  id: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof permissionControllerFindOne>>, TError, TData>>
  },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
  const queryOptions = getPermissionControllerFindOneQueryOptions(id, options)

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>
  }

  query.queryKey = queryOptions.queryKey

  return query
}

export const permissionControllerUpdate = (id: string, updatePermissionDto: UpdatePermissionDto) => {
  return customInstance<ApiResponse<ApiResponse<void>>>({
    url: `/permission/${id}`,
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json' },
    data: updatePermissionDto
  })
}

export const getPermissionControllerUpdateMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof permissionControllerUpdate>>,
    TError,
    { id: string; data: UpdatePermissionDto },
    TContext
  >
}): UseMutationOptions<
  Awaited<ReturnType<typeof permissionControllerUpdate>>,
  TError,
  { id: string; data: UpdatePermissionDto },
  TContext
> => {
  const mutationKey = ['permissionControllerUpdate']
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } }

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof permissionControllerUpdate>>,
    { id: string; data: UpdatePermissionDto }
  > = props => {
    const { id, data } = props ?? {}

    return permissionControllerUpdate(id, data)
  }

  return { mutationFn, ...mutationOptions }
}

export type PermissionControllerUpdateMutationResult = NonNullable<
  Awaited<ReturnType<typeof permissionControllerUpdate>>
>
export type PermissionControllerUpdateMutationBody = UpdatePermissionDto
export type PermissionControllerUpdateMutationError = unknown

export const usePermissionControllerUpdate = <TError = unknown, TContext = unknown>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof permissionControllerUpdate>>,
      TError,
      { id: string; data: UpdatePermissionDto },
      TContext
    >
  },
  queryClient?: QueryClient
): UseMutationResult<
  Awaited<ReturnType<typeof permissionControllerUpdate>>,
  TError,
  { id: string; data: UpdatePermissionDto },
  TContext
> => {
  const mutationOptions = getPermissionControllerUpdateMutationOptions(options)

  return useMutation(mutationOptions, queryClient)
}
export const permissionControllerRemove = (id: string) => {
  return customInstance<ApiResponse<void>>({ url: `/permission/${id}`, method: 'DELETE' })
}

export const getPermissionControllerRemoveMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof permissionControllerRemove>>,
    TError,
    { id: string },
    TContext
  >
}): UseMutationOptions<Awaited<ReturnType<typeof permissionControllerRemove>>, TError, { id: string }, TContext> => {
  const mutationKey = ['permissionControllerRemove']
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } }

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof permissionControllerRemove>>,
    { id: string }
  > = props => {
    const { id } = props ?? {}

    return permissionControllerRemove(id)
  }

  return { mutationFn, ...mutationOptions }
}

export type PermissionControllerRemoveMutationResult = NonNullable<
  Awaited<ReturnType<typeof permissionControllerRemove>>
>

export type PermissionControllerRemoveMutationError = unknown

export const usePermissionControllerRemove = <TError = unknown, TContext = unknown>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof permissionControllerRemove>>,
      TError,
      { id: string },
      TContext
    >
  },
  queryClient?: QueryClient
): UseMutationResult<Awaited<ReturnType<typeof permissionControllerRemove>>, TError, { id: string }, TContext> => {
  const mutationOptions = getPermissionControllerRemoveMutationOptions(options)

  return useMutation(mutationOptions, queryClient)
}
