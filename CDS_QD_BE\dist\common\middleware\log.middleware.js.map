{"version": 3, "sources": ["../../../src/common/middleware/log.middleware.ts"], "sourcesContent": ["import { Injectable, Logger, NestMiddleware } from '@nestjs/common';\nimport { Request, Response } from 'express';\nimport { DatabaseService } from '~/database/typeorm/database.service';\nimport { UtilService } from '~/shared/services';\n\n@Injectable()\nexport class LogMiddleware implements NestMiddleware {\n    constructor(private database: DatabaseService, private utilService: UtilService) {}\n    // eslint-disable-next-line @typescript-eslint/ban-types\n    use(req: Request, res: Response, next: Function) {\n        const { ip, method, originalUrl } = req;\n        const userAgent = req.get('user-agent') || '';\n\n        res.on('finish', () => {\n            const { statusCode } = res;\n            Logger.log(`[${statusCode}] ${originalUrl}, ${method} - ${userAgent} - ${ip}`);\n        });\n\n        next();\n    }\n}\n"], "names": ["LogMiddleware", "use", "req", "res", "next", "ip", "method", "originalUrl", "userAgent", "get", "on", "statusCode", "<PERSON><PERSON>", "log", "constructor", "database", "utilService"], "mappings": "oGAMaA,uDAAAA,uCANsC,iDAEnB,mEACJ,skBAGrB,IAAA,AAAMA,cAAN,MAAMA,cAGTC,IAAIC,GAAY,CAAEC,GAAa,CAAEC,IAAc,CAAE,CAC7C,KAAM,CAAEC,EAAE,CAAEC,MAAM,CAAEC,WAAW,CAAE,CAAGL,IACpC,MAAMM,UAAYN,IAAIO,GAAG,CAAC,eAAiB,GAE3CN,IAAIO,EAAE,CAAC,SAAU,KACb,KAAM,CAAEC,UAAU,CAAE,CAAGR,IACvBS,cAAM,CAACC,GAAG,CAAC,CAAC,CAAC,EAAEF,WAAW,EAAE,EAAEJ,YAAY,EAAE,EAAED,OAAO,GAAG,EAAEE,UAAU,GAAG,EAAEH,GAAG,CAAC,CACjF,GAEAD,MACJ,CAZAU,YAAY,AAAQC,QAAyB,CAAE,AAAQC,WAAwB,CAAE,MAA7DD,SAAAA,cAAmCC,YAAAA,WAA2B,CAatF"}