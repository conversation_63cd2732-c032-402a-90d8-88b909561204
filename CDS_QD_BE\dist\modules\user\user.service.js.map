{"version": 3, "sources": ["../../../src/modules/user/user.service.ts"], "sourcesContent": ["import { HttpException, Injectable } from '@nestjs/common';\nimport { DatabaseService } from '~/database/typeorm/database.service';\nimport { MediaService } from '~/modules/media/media.service';\nimport { TokenService, UtilService } from '~/shared/services';\nimport { CreateUserDto } from './dto/create-user.dto';\nimport { UpdateUserDto } from './dto/update-user.dto';\n\n@Injectable()\nexport class UserService {\n    constructor(\n        private readonly tokenService: TokenService,\n        private readonly utilService: UtilService,\n        private readonly mediaService: MediaService,\n        private readonly database: DatabaseService,\n    ) {}\n\n    async create(createUserDto: CreateUserDto) {\n        const { username, password, ...rest } = createUserDto;\n        const { salt, hash } = this.tokenService.hashPassword(createUserDto.password);\n        const account = await this.database.account.save(\n            this.database.account.create({\n                username: createUserDto.username,\n                password: hash,\n                salt,\n            }),\n        );\n\n        if (!account) {\n            throw new HttpException('Cannot create account', 400);\n        }\n\n        const user = await this.database.user.save(this.database.user.create({ ...rest, accountId: account.id }));\n        if (!user) {\n            throw new HttpException('Cannot create user', 400);\n        }\n\n        return {\n            data: {\n                account,\n                user,\n            },\n        };\n    }\n\n    async findAll(queries: { page: number; perPage: number; search: string; sortBy: string }) {\n        const { builder, take, pagination } = this.utilService.getQueryBuilderAndPagination(this.database.user, queries);\n\n        if (!this.utilService.isEmpty(queries.search)) {\n            builder.andWhere('(entity.fullName ILIKE :search OR entity.email ILIKE :search)', { search: `%${queries.search}%` });\n        }\n\n        builder.leftJoinAndSelect('entity.role', 'role');\n        builder.leftJoinAndSelect('entity.avatar', 'avatar');\n        builder.select(['entity', 'role.id', 'role.name', 'avatar.id']);\n\n        const [result, total] = await builder.getManyAndCount();\n        const totalPages = Math.ceil(total / take);\n        return {\n            data: result,\n            pagination: {\n                ...pagination,\n                totalRecords: total,\n                totalPages: totalPages,\n            },\n        };\n    }\n\n    findOne(id: number) {\n        return this.database.user.findOneUserWithAllRelationsById(id);\n    }\n\n    async update(id: number, updateUserDto: UpdateUserDto) {\n        const { username, password, ...rest } = updateUserDto;\n        const user = await this.database.user.findOneBy({ id });\n        if (!user) {\n            throw new HttpException('Không tìm thấy người dùng', 404);\n        }\n\n        if (password) {\n            const { salt, hash } = this.tokenService.hashPassword(updateUserDto.password);\n            this.database.account.update({ id: user.accountId }, { password: hash, salt });\n        }\n\n        return await this.database.user.update({ id }, rest);\n    }\n\n    async remove(id: number) {\n        const user = await this.database.user.findOneBy({ id });\n        if (!user) {\n            throw new HttpException('Không tìm thấy người dùng', 404);\n        }\n\n        // remove user\n        await this.database.user.delete({ id });\n        // remove account\n        await this.database.account.delete({ id: user.accountId });\n        // remove media\n        if (user.avatar?.id) {\n            await this.mediaService.remove(user.avatar.id);\n        }\n\n        return true;\n    }\n}\n"], "names": ["UserService", "create", "createUserDto", "username", "password", "rest", "salt", "hash", "tokenService", "hashPassword", "account", "database", "save", "HttpException", "user", "accountId", "id", "data", "findAll", "queries", "builder", "take", "pagination", "utilService", "getQueryBuilderAndPagination", "isEmpty", "search", "andWhere", "leftJoinAndSelect", "select", "result", "total", "getManyAndCount", "totalPages", "Math", "ceil", "totalRecords", "findOne", "findOneUserWithAllRelationsById", "update", "updateUserDto", "findOneBy", "remove", "delete", "avatar", "mediaService", "constructor"], "mappings": "oGAQaA,qDAAAA,qCAR6B,iDACV,uEACH,kDACa,skBAKnC,IAAA,AAAMA,YAAN,MAAMA,YAQT,MAAMC,OAAOC,aAA4B,CAAE,CACvC,KAAM,CAAEC,QAAQ,CAAEC,QAAQ,CAAE,GAAGC,KAAM,CAAGH,cACxC,KAAM,CAAEI,IAAI,CAAEC,IAAI,CAAE,CAAG,IAAI,CAACC,YAAY,CAACC,YAAY,CAACP,cAAcE,QAAQ,EAC5E,MAAMM,QAAU,MAAM,IAAI,CAACC,QAAQ,CAACD,OAAO,CAACE,IAAI,CAC5C,IAAI,CAACD,QAAQ,CAACD,OAAO,CAACT,MAAM,CAAC,CACzBE,SAAUD,cAAcC,QAAQ,CAChCC,SAAUG,KACVD,IACJ,IAGJ,GAAI,CAACI,QAAS,CACV,MAAM,IAAIG,qBAAa,CAAC,wBAAyB,IACrD,CAEA,MAAMC,KAAO,MAAM,IAAI,CAACH,QAAQ,CAACG,IAAI,CAACF,IAAI,CAAC,IAAI,CAACD,QAAQ,CAACG,IAAI,CAACb,MAAM,CAAC,CAAE,GAAGI,IAAI,CAAEU,UAAWL,QAAQM,EAAE,AAAC,IACtG,GAAI,CAACF,KAAM,CACP,MAAM,IAAID,qBAAa,CAAC,qBAAsB,IAClD,CAEA,MAAO,CACHI,KAAM,CACFP,QACAI,IACJ,CACJ,CACJ,CAEA,MAAMI,QAAQC,OAA0E,CAAE,CACtF,KAAM,CAAEC,OAAO,CAAEC,IAAI,CAAEC,UAAU,CAAE,CAAG,IAAI,CAACC,WAAW,CAACC,4BAA4B,CAAC,IAAI,CAACb,QAAQ,CAACG,IAAI,CAAEK,SAExG,GAAI,CAAC,IAAI,CAACI,WAAW,CAACE,OAAO,CAACN,QAAQO,MAAM,EAAG,CAC3CN,QAAQO,QAAQ,CAAC,gEAAiE,CAAED,OAAQ,CAAC,CAAC,EAAEP,QAAQO,MAAM,CAAC,CAAC,CAAC,AAAC,EACtH,CAEAN,QAAQQ,iBAAiB,CAAC,cAAe,QACzCR,QAAQQ,iBAAiB,CAAC,gBAAiB,UAC3CR,QAAQS,MAAM,CAAC,CAAC,SAAU,UAAW,YAAa,YAAY,EAE9D,KAAM,CAACC,OAAQC,MAAM,CAAG,MAAMX,QAAQY,eAAe,GACrD,MAAMC,WAAaC,KAAKC,IAAI,CAACJ,MAAQV,MACrC,MAAO,CACHJ,KAAMa,OACNR,WAAY,CACR,GAAGA,UAAU,CACbc,aAAcL,MACdE,WAAYA,UAChB,CACJ,CACJ,CAEAI,QAAQrB,EAAU,CAAE,CAChB,OAAO,IAAI,CAACL,QAAQ,CAACG,IAAI,CAACwB,+BAA+B,CAACtB,GAC9D,CAEA,MAAMuB,OAAOvB,EAAU,CAAEwB,aAA4B,CAAE,CACnD,KAAM,CAAErC,QAAQ,CAAEC,QAAQ,CAAE,GAAGC,KAAM,CAAGmC,cACxC,MAAM1B,KAAO,MAAM,IAAI,CAACH,QAAQ,CAACG,IAAI,CAAC2B,SAAS,CAAC,CAAEzB,EAAG,GACrD,GAAI,CAACF,KAAM,CACP,MAAM,IAAID,qBAAa,CAAC,4BAA6B,IACzD,CAEA,GAAIT,SAAU,CACV,KAAM,CAAEE,IAAI,CAAEC,IAAI,CAAE,CAAG,IAAI,CAACC,YAAY,CAACC,YAAY,CAAC+B,cAAcpC,QAAQ,EAC5E,IAAI,CAACO,QAAQ,CAACD,OAAO,CAAC6B,MAAM,CAAC,CAAEvB,GAAIF,KAAKC,SAAS,AAAC,EAAG,CAAEX,SAAUG,KAAMD,IAAK,EAChF,CAEA,OAAO,MAAM,IAAI,CAACK,QAAQ,CAACG,IAAI,CAACyB,MAAM,CAAC,CAAEvB,EAAG,EAAGX,KACnD,CAEA,MAAMqC,OAAO1B,EAAU,CAAE,CACrB,MAAMF,KAAO,MAAM,IAAI,CAACH,QAAQ,CAACG,IAAI,CAAC2B,SAAS,CAAC,CAAEzB,EAAG,GACrD,GAAI,CAACF,KAAM,CACP,MAAM,IAAID,qBAAa,CAAC,4BAA6B,IACzD,CAGA,MAAM,IAAI,CAACF,QAAQ,CAACG,IAAI,CAAC6B,MAAM,CAAC,CAAE3B,EAAG,EAErC,OAAM,IAAI,CAACL,QAAQ,CAACD,OAAO,CAACiC,MAAM,CAAC,CAAE3B,GAAIF,KAAKC,SAAS,AAAC,GAExD,GAAID,KAAK8B,MAAM,EAAE5B,GAAI,CACjB,MAAM,IAAI,CAAC6B,YAAY,CAACH,MAAM,CAAC5B,KAAK8B,MAAM,CAAC5B,EAAE,CACjD,CAEA,OAAO,IACX,CA7FA8B,YACI,AAAiBtC,YAA0B,CAC3C,AAAiBe,WAAwB,CACzC,AAAiBsB,YAA0B,CAC3C,AAAiBlC,QAAyB,CAC5C,MAJmBH,aAAAA,kBACAe,YAAAA,iBACAsB,aAAAA,kBACAlC,SAAAA,QAClB,CAyFP"}