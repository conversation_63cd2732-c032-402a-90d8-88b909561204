// <PERSON><PERSON><PERSON> nghĩa các kiểu dữ liệu chung cho API

export interface ApiResponse<T> {
  data: T;
  message?: string;
  statusCode?: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    total: number;
    page: number;
    perPage: number;
    totalPages: number;
  };
}

export interface LoginResponse {
  accessToken: string;
  refreshToken: string;
  user: {
    id: number;
    username: string;
    email: string;
    fullName: string;
    role: {
      id: number;
      name: string;
      permissions: {
        id: number;
        name: string;
        code: string;
      }[];
    };
  };
}

export interface UserResponse {
  id: number;
  username: string;
  email: string;
  fullName: string;
  areaCode: string;
  phone: string;
  address: string;
  birthday: string;
  gender: string;
  role: {
    id: number;
    name: string;
  };
  department: {
    id: number;
    name: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface RoleResponse {
  id: number;
  name: string;
  description: string;
  permissions: {
    id: number;
    name: string;
    code: string;
  }[];
  createdAt: string;
  updatedAt: string;
}

export interface PermissionResponse {
  id: number;
  name: string;
  code: string;
  createdAt: string;
  updatedAt: string;
}

export interface DepartmentResponse {
  id: number;
  name: string;
  description: string;
  createdAt: string;
  updatedAt: string;
}

export interface ProfileResponse {
  id: number;
  username: string;
  email: string;
  fullName: string;
  areaCode: string;
  phone: string;
  address: string;
  birthday: string;
  gender: string;
  role: {
    id: number;
    name: string;
  };
  department: {
    id: number;
    name: string;
  };
}

export interface TokenResponse {
  accessToken: string;
  refreshToken: string;
}
