Bước 10: Cập nhật mockDataChiTiet.ts cho "Quá trình <PERSON> tác"
Định nghĩa QuaTrinhCongTacEntryType:

Thêm dữ liệu mẫu vào mockQuanNhanChiTietData.quaTrinhCongTac.

TypeScript

// src\views\quan-ly-quan-nhan\chi-tiet\mockDataChiTiet.ts (Thêm/Cập nhật)

export interface QuaTrinhCongTacEntryType {
id: string; // ID duy nhất cho mỗi dòng, cần cho DataGrid
ThoiGianBatDau: string; // ISO Date string
ThoiGianKetThuc?: string | null; // ISO Date string, null nếu "Đến nay"
DonViCongTacID: string; // Sẽ map sang tên
ChucVuDamNhiemID: string; // Sẽ map sang tên
CapBacKhiDamNhiemID: string; // Sẽ map sang tên
QuyetDinhSo?: string;
NgayQuyetDinh?: string; // ISO Date string
CoQuanRaQuyetDinhID?: string; // Sẽ map sang tên
LoaiQuyetDinhID?: string; // Sẽ map sang tên
MoTaCongViecChinh?: string;
FileDinhKemURL?: string; // URL tới file
GhiChu?: string;
}

// Trong mockQuanNhanChiTietData:
// ...
// quaTrinhCongTac: [
// {
// id: 'qtct_001',
// ThoiGianBatDau: '2012-09-10T00:00:00Z',
// ThoiGianKetThuc: '2014-08-31T00:00:00Z',
// DonViCongTacID: 'DV001A', // Học viên, Trường Sĩ quan Lục quân 1
// ChucVuDamNhiemID: 'CV_HV', // Học viên
// CapBacKhiDamNhiemID: 'CB_BS', // Binh sĩ
// QuyetDinhSo: 'QD123/TruongSQLQ1',
// NgayQuyetDinh: '2012-09-05T00:00:00Z',
// CoQuanRaQuyetDinhID: 'TruongSQLQ1',
// LoaiQuyetDinhID: 'LDQ_NhapHoc', // Nhập học
// MoTaCongViecChinh: 'Học tập và rèn luyện theo chương trình đào tạo sĩ quan.',
// FileDinhKemURL: '/files/quyetdinh/qd123.pdf',
// GhiChu: 'Hoàn thành tốt khóa học.'
// },
// {
// id: 'qtct_002',
// ThoiGianBatDau: '2014-09-01T00:00:00Z',
// ThoiGianKetThuc: '2017-07-31T00:00:00Z',
// DonViCongTacID: 'DV003B', // Trung đội 1, Đại đội 2, Tiểu đoàn 1, Trung đoàn X
// ChucVuDamNhiemID: 'CV_TDTruong', // Trung đội trưởng
// CapBacKhiDamNhiemID: 'CB_ThUy', // Thiếu úy
// QuyetDinhSo: 'QD456/BoQP',
// NgayQuyetDinh: '2014-08-20T00:00:00Z',
// CoQuanRaQuyetDinhID: 'BoQuocPhong',
// LoaiQuyetDinhID: 'LDQ_PhongChuc', // Phong chức
// MoTaCongViecChinh: 'Chỉ huy, quản lý trung đội huấn luyện chiến sĩ mới.',
// FileDinhKemURL: '/files/quyetdinh/qd456.pdf',
// GhiChu: 'Đơn vị đạt danh hiệu đơn vị Quyết thắng năm 2016.'
// },
// {
// id: 'qtct_003',
// ThoiGianBatDau: '2017-08-01T00:00:00Z',
// ThoiGianKetThuc: null, // Đến nay
// DonViCongTacID: 'DV003', // Tiểu đoàn 1, Trung đoàn 2, Sư đoàn 3 (Đơn vị hiện tại trong baseInfo)
// ChucVuDamNhiemID: 'CV002', // Phó Đại đội trưởng (Chức vụ hiện tại trong baseInfo)
// CapBacKhiDamNhiemID: 'CB001', // Trung úy (Cấp bậc hiện tại có thể đã thay đổi so với khi đảm nhiệm chức vụ này ban đầu)
// QuyetDinhSo: 'QD789/SuDoan3',
// NgayQuyetDinh: '2017-07-25T00:00:00Z',
// CoQuanRaQuyetDinhID: 'SuDoan3',
// LoaiQuyetDinhID: 'LDQ_DieuDong', // Điều động
// MoTaCongViecChinh: 'Phụ trách công tác tham mưu, huấn luyện của đại đội.',
// FileDinhKemURL: '/files/quyetdinh/qd789.pdf',
// GhiChu: 'Tham gia diễn tập MT-18 đạt kết quả Giỏi.'
// }
// ] as QuaTrinhCongTacEntryType[],
// ...
Bước 11: Xây dựng TabPanel cho "Quá trình Công tác"
Tạo file src\views\quan-ly-quan-nhan\chi-tiet\components\tabs\TabQuaTrinhCongTac.tsx

Mục đích: Hiển thị lịch sử công tác dưới dạng bảng, cho phép thêm, sửa, xóa.

Props:

data?: QuaTrinhCongTacEntryType[]
idQuanNhan: string (Để truyền vào dialog khi thêm/sửa, liên kết với quân nhân hiện tại).
State:

listData: QuaTrinhCongTacEntryType[] (Để quản lý dữ liệu hiển thị, có thể cập nhật sau khi thêm/sửa/xóa).
openDialog: boolean (Trạng thái mở/đóng dialog thêm/sửa).
editingData: QuaTrinhCongTacEntryType | null (Dữ liệu của dòng đang được sửa, null nếu là thêm mới).
openConfirmDelete: boolean
deletingId: string | null (ID của dòng sẽ bị xóa).
paginationModel: { page: number, pageSize: number } (Quản lý phân trang cho DataGrid).
Component Vuexy (MUI & @mui/x-data-grid):

Button (cho "Thêm giai đoạn Công tác").
DataGrid (từ @mui/x-data-grid).
IconButton, Menu, MenuItem (hoặc OptionMenu của Vuexy) cho cột hành động.
Dialog (Sẽ tạo component riêng cho Dialog: DialogThemSuaQuaTrinhCongTac).
Dialog (Sẽ tạo component riêng cho Dialog xác nhận xóa: DialogXacNhanXoaItem).
Icons từ tabler-icons-react.
Cấu hình cột cho DataGrid:

ThoiGianBatDau (định dạng dd/MM/yyyy)
ThoiGianKetThuc (định dạng dd/MM/yyyy, "Đến nay" nếu null)
DonViCongTacID (hiển thị tên đơn vị)
ChucVuDamNhiemID (hiển thị tên chức vụ)
CapBacKhiDamNhiemID (hiển thị tên cấp bậc)
QuyetDinhSo
NgayQuyetDinh (định dạng dd/MM/yyyy)
CoQuanRaQuyetDinhID (hiển thị tên)
LoaiQuyetDinhID (hiển thị tên)
MoTaCongViecChinh (có thể hiển thị rút gọn với tooltip)
FileDinhKemURL (Link tải/xem)
GhiChu
Hành động: Nút Sửa, Xóa, Xem file.
TypeScript

// src\views\quan-ly-quan-nhan\chi-tiet\components\tabs\TabQuaTrinhCongTac.tsx
'use client';

import React, { useState, useEffect } from 'react';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import { DataGrid, GridColDef, GridRenderCellParams } from '@mui/x-data-grid';
import IconButton from '@mui/material/IconButton';
import Link from '@mui/material/Link';
import Tooltip from '@mui/material/Tooltip';

import IconPlus from '@tabler/icons-react/dist/esm/icons/IconPlus';
import IconEdit from '@tabler/icons-react/dist/esm/icons/IconEdit';
import IconTrash from '@tabler/icons-react/dist/esm/icons/IconTrash';
import IconFileText from '@tabler/icons-react/dist/esm/icons/IconFileText';

import { QuaTrinhCongTacEntryType } from '../../mockDataChiTiet';
// Giả định có DialogThemSuaQuaTrinhCongTac và DialogXacNhanXoaItem
// import DialogThemSuaQuaTrinhCongTac from './DialogThemSuaQuaTrinhCongTac';
// import DialogXacNhanXoaItem from '../../../../components/DialogXacNhanXoaItem'; // Component dùng chung

interface TabQuaTrinhCongTacProps {
initialData?: QuaTrinhCongTacEntryType[];
idQuanNhan: string; // Để liên kết khi thêm mới
}

// Helper functions (formatDate, mapIdToName - cần được định nghĩa hoặc import)
const formatDateDatagrid = (dateString?: string | null): string => {
if (!dateString) return '';
try {
const date = new Date(dateString);
if (isNaN(date.getTime())) return 'Không hợp lệ';
return `<span class="math-inline">\{String\(date\.getDate\(\)\)\.padStart\(2, '0'\)\}/</span>{String(date.getMonth() + 1).padStart(2, '0')}/${date.getFullYear()}`;
} catch (e) { return 'Không hợp lệ'; }
};
const mapIdToString = (id?: string, type?: string) => id || 'N/A'; // Placeholder

const TabQuaTrinhCongTac = ({ initialData = [], idQuanNhan }: TabQuaTrinhCongTacProps) => {
const [listData, setListData] = useState<QuaTrinhCongTacEntryType[]>(initialData);
const [openDialog, setOpenDialog] = useState(false);
const [editingData, setEditingData] = useState<QuaTrinhCongTacEntryType | null>(null);
const [openConfirmDelete, setOpenConfirmDelete] = useState(false);
const [deletingId, setDeletingId] = useState<string | null>(null);
const [paginationModel, setPaginationModel] = useState({ page: 0, pageSize: 5 });

useEffect(() => {
setListData(initialData);
}, [initialData]);

const handleOpenAddDialog = () => {
setEditingData(null);
setOpenDialog(true);
};

const handleOpenEditDialog = (rowData: QuaTrinhCongTacEntryType) => {
setEditingData(rowData);
setOpenDialog(true);
};

const handleCloseDialog = () => {
setOpenDialog(false);
setEditingData(null);
};

const handleSaveData = (savedData: QuaTrinhCongTacEntryType) => {
// Trong thực tế: gọi API để lưu
console.log('Saving data (QTCT):', savedData, 'for QN ID:', idQuanNhan);
if (editingData) { // Sửa
setListData(prev => prev.map(item => (item.id === savedData.id ? savedData : item)));
} else { // Thêm
setListData(prev => [...prev, { ...savedData, id: `new_qtct_${Date.now()}` }]); // Tạo ID tạm
}
handleCloseDialog();
};

const handleOpenDeleteDialog = (id: string) => {
setDeletingId(id);
setOpenConfirmDelete(true);
};

const handleCloseConfirmDelete = () => {
setOpenConfirmDelete(false);
setDeletingId(null);
};

const handleConfirmDelete = () => {
if (deletingId) {
// Trong thực tế: gọi API để xóa
console.log('Deleting QTCT ID:', deletingId);
setListData(prev => prev.filter(item => item.id !== deletingId));
handleCloseConfirmDelete();
}
};

const columns: GridColDef[] = [
{
field: 'ThoiGianBatDau',
headerName: 'Bắt đầu',
width: 120,
valueFormatter: params => formatDateDatagrid(params.value)
},
{
field: 'ThoiGianKetThuc',
headerName: 'Kết thúc',
width: 120,
renderCell: (params: GridRenderCellParams) => params.value ? formatDateDatagrid(params.value) : 'Đến nay'
},
{
field: 'DonViCongTacID',
headerName: 'Đơn vị',
width: 200,
valueGetter: params => mapIdToString(params.value, 'donVi')
},
{
field: 'ChucVuDamNhiemID',
headerName: 'Chức vụ',
width: 180,
valueGetter: params => mapIdToString(params.value, 'chucVu')
},
{
field: 'CapBacKhiDamNhiemID',
headerName: 'Cấp bậc',
width: 130,
valueGetter: params => mapIdToString(params.value, 'capBac')
},
{ field: 'QuyetDinhSo', headerName: 'Số QĐ', width: 120 },
{
field: 'MoTaCongViecChinh',
headerName: 'Mô tả công việc',
width: 250,
renderCell: (params: GridRenderCellParams) => (
<Tooltip title={params.value || ''} placement="top-start">
<Typography noWrap variant="body2" sx={{overflow: 'hidden', textOverflow: 'ellipsis'}}>
{params.value || ''}
</Typography>
</Tooltip>
)
},
{
field: 'actions',
headerName: 'Hành động',
width: 150,
sortable: false,
filterable: false,
renderCell: (params: GridRenderCellParams) => (
<Box>
<Tooltip title="Xem File">
<span>
<IconButton
size="small"
href={params.row.FileDinhKemURL || '#'}
target="_blank"
disabled={!params.row.FileDinhKemURL}
onClick={(e) => { if (!params.row.FileDinhKemURL) e.preventDefault(); else console.log('Viewing file:', params.row.FileDinhKemURL);}}
>
<IconFileText size={20} />
</IconButton>
</span>
</Tooltip>
<Tooltip title="Sửa">
<IconButton size="small" onClick={() => handleOpenEditDialog(params.row as QuaTrinhCongTacEntryType)}>
<IconEdit size={20} />
</IconButton>
</Tooltip>
<Tooltip title="Xóa">
<IconButton size="small" onClick={() => handleOpenDeleteDialog(params.row.id as string)}>
<IconTrash size={20} />
</IconButton>
</Tooltip>
</Box>
)
}
// Thêm các cột khác nếu cần: NgayQuyetDinh, CoQuanRaQuyetDinhID, LoaiQuyetDinhID, GhiChu
];

return (
<Box>
<Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
<Typography variant="h6" sx={{ color: 'primary.main' }}>
Quá trình Công tác
</Typography>
<Button
variant="contained"
startIcon={<IconPlus />}
onClick={handleOpenAddDialog} >
Thêm giai đoạn
</Button>
</Box>

      <DataGrid
        autoHeight
        rows={listData}
        columns={columns}
        pageSizeOptions={[5, 10, 25]}
        paginationModel={paginationModel}
        onPaginationModelChange={setPaginationModel}
        getRowId={(row) => row.id} // Đảm bảo mỗi dòng có id duy nhất
        sx={{
            '& .MuiDataGrid-columnHeaders': { backgroundColor: 'customColors.tableHeaderBg' } // Style header theo Vuexy
        }}
      />

      {/* {openDialog && (
        <DialogThemSuaQuaTrinhCongTac
          open={openDialog}
          onClose={handleCloseDialog}
          onSubmit={handleSaveData}
          initialData={editingData}
          idQuanNhan={idQuanNhan}
        />
      )}

      {openConfirmDelete && (
        <DialogXacNhanXoaItem
          open={openConfirmDelete}
          onClose={handleCloseConfirmDelete}
          onConfirm={handleConfirmDelete}
          itemName="giai đoạn công tác này"
        />
      )} */}
      {/* Placeholder cho Dialogs, cần tạo component thực tế */}
      {openDialog && <Typography sx={{mt: 2, p:2, border: '1px dashed grey'}}>Dialog Thêm/Sửa Quá trình Công tác (Placeholder - ID Quân nhân: {idQuanNhan}, Data: {JSON.stringify(editingData)})</Typography>}
      {openConfirmDelete && <Typography sx={{mt: 2, p:2, border: '1px dashed red'}}>Dialog Xác nhận Xóa (Placeholder - ID: {deletingId})</Typography>}
    </Box>

);
};

export default TabQuaTrinhCongTac;
Bước 12: Tạo Component DialogThemSuaQuaTrinhCongTac.tsx (Sơ bộ)
Component này sẽ chứa form để nhập liệu cho một giai đoạn công tác. Sử dụng các component form của MUI (TextField, DatePicker từ @mui/x-date-pickers hoặc custom của Vuexy, Select cho các ID).

TypeScript

// src\views\quan-ly-quan-nhan\chi-tiet\components\tabs\DialogThemSuaQuaTrinhCongTac.tsx (Sơ bộ)
// 'use client';
// import React, { useState, useEffect } from 'react';
// import Dialog from '@mui/material/Dialog';
// import DialogTitle from '@mui/material/DialogTitle';
// import DialogContent from '@mui/material/DialogContent';
// import DialogActions from '@mui/material/DialogActions';
// import Button from '@mui/material/Button';
// import Grid from '@mui/material/Grid';
// import TextField from '@mui/material/TextField';
// // import { DatePicker } from '@mui/x-date-pickers/DatePicker'; // Cần Adapter tương ứng
// // import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
// // import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
// // import vi from 'date-fns/locale/vi';

// import { QuaTrinhCongTacEntryType } from '../../mockDataChiTiet';

// interface DialogProps {
// open: boolean;
// onClose: () => void;
// onSubmit: (data: QuaTrinhCongTacEntryType) => void;
// initialData: QuaTrinhCongTacEntryType | null;
// idQuanNhan: string;
// }

// const DialogThemSuaQuaTrinhCongTac = ({ open, onClose, onSubmit, initialData, idQuanNhan }: DialogProps) => {
// const [formData, setFormData] = useState<Partial<QuaTrinhCongTacEntryType>>(initialData || {});

// useEffect(() => {
// setFormData(initialData || {});
// }, [initialData, open]);

// const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
// const { name, value } = e.target;
// setFormData(prev => ({ ...prev, [name]: value }));
// };

// // const handleDateChange = (name: string, date: Date | null) => {
// // setFormData(prev => ({ ...prev, [name]: date ? date.toISOString() : null }));
// // };

// const handleSubmit = () => {
// // Thêm idQuanNhan vào formData nếu cần gửi lên server
// // Validate formData trước khi submit
// onSubmit(formData as QuaTrinhCongTacEntryType); // Cần đảm bảo formData hợp lệ
// };

// return (
// // <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={vi}>
// <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
// <DialogTitle>{initialData ? 'Sửa Giai đoạn Công tác' : 'Thêm Giai đoạn Công tác'}</DialogTitle>
// <DialogContent>
// <Grid container spacing={2} sx={{mt:1}}>
// {/_ Ví dụ một vài trường, cần thêm đầy đủ các trường _/}
// <Grid item xs={12} sm={6}>
// {/_ <DatePicker
// label="Thời gian bắt đầu"
// value={formData.ThoiGianBatDau ? new Date(formData.ThoiGianBatDau) : null}
// onChange={(date) => handleDateChange('ThoiGianBatDau', date)}
// renderInput={(params) => <TextField {...params} fullWidth />}
// /> _/}
// <TextField
// name="ThoiGianBatDau"
// label="Thời gian bắt đầu (YYYY-MM-DD)"
// type="date"
// value={formData.ThoiGianBatDau?.substring(0,10) || ''}
// onChange={handleChange}
// fullWidth
// InputLabelProps={{ shrink: true }}
// />
// </Grid>
// <Grid item xs={12} sm={6}>
// <TextField
// name="ThoiGianKetThuc"
// label="Thời gian kết thúc (YYYY-MM-DD)"
// type="date"
// value={formData.ThoiGianKetThuc?.substring(0,10) || ''}
// onChange={handleChange}
// fullWidth
// InputLabelProps={{ shrink: true }}
// helperText="Để trống nếu 'Đến nay'"
// />
// </Grid>
// <Grid item xs={12} sm={6}>
// <TextField name="DonViCongTacID" label="ID Đơn vị Công tác" value={formData.DonViCongTacID || ''} onChange={handleChange} fullWidth />
// </Grid>
// <Grid item xs={12} sm={6}>
// <TextField name="ChucVuDamNhiemID" label="ID Chức vụ Đảm nhiệm" value={formData.ChucVuDamNhiemID || ''} onChange={handleChange} fullWidth />
// </Grid>
// {/_ ... Thêm các trường khác của QuaTrinhCongTacEntryType ... _/}
// <Grid item xs={12}>
// <TextField name="MoTaCongViecChinh" label="Mô tả công việc chính" value={formData.MoTaCongViecChinh || ''} onChange={handleChange} fullWidth multiline rows={3}/>
// </Grid>
// </Grid>
// </DialogContent>
// <DialogActions>
// <Button onClick={onClose}>Hủy</Button>
// <Button onClick={handleSubmit} variant="contained">Lưu</Button>
// </DialogActions>
// </Dialog>
// // </LocalizationProvider>
// );
// };
// export default DialogThemSuaQuaTrinhCongTac;
(Lưu ý: Component Dialog trên là sơ bộ, cần hoàn thiện với đầy đủ các trường, validation, và có thể sử dụng các component Select cho các ID, DatePicker chuẩn của Vuexy/MUI X, FileUpload nếu cần.)

Bước 13: Tạo Component DialogXacNhanXoaItem.tsx (Nếu chưa có)
Đây là component có thể dùng chung cho việc xác nhận xóa nhiều loại item khác nhau.

TypeScript

// src\views\components\DialogXacNhanXoaItem.tsx (Ví dụ đường dẫn)
// 'use client';
// import React from 'react';
// import Dialog from '@mui/material/Dialog';
// import DialogTitle from '@mui/material/DialogTitle';
// import DialogContent from '@mui/material/DialogContent';
// import DialogContentText from '@mui/material/DialogContentText';
// import DialogActions from '@mui/material/DialogActions';
// import Button from '@mui/material/Button';

// interface DialogXacNhanXoaProps {
// open: boolean;
// onClose: () => void;
// onConfirm: () => void;
// itemName?: string; // Tên của item đang xóa, ví dụ: "giai đoạn công tác này"
// title?: string;
// message?: string;
// }

// const DialogXacNhanXoaItem = ({
// open,
// onClose,
// onConfirm,
// itemName = "mục này",
// title = "Xác nhận Xóa",
// message
// }: DialogXacNhanXoaProps) => {
// return (
// <Dialog open={open} onClose={onClose}>
// <DialogTitle>{title}</DialogTitle>
// <DialogContent>
// <DialogContentText>
// {message || `Bạn có chắc chắn muốn xóa ${itemName} không? Hành động này không thể hoàn tác.`}
// </DialogContentText>
// </DialogContent>
// <DialogActions>
// <Button onClick={onClose} color="secondary">Hủy</Button>
// <Button onClick={onConfirm} color="error" variant="contained">Xóa</Button>
// </DialogActions>
// </Dialog>
// );
// };
// export default DialogXacNhanXoaItem;
Bước 14: Cập nhật KhuVucTabsChiTiet.tsx để sử dụng TabQuaTrinhCongTac
Chỉnh sửa file src\views\quan-ly-quan-nhan\chi-tiet\components\KhuVucTabsChiTiet.tsx:

TypeScript

// src\views\quan-ly-quan-nhan\chi-tiet\components\KhuVucTabsChiTiet.tsx
// ... (các imports khác)
import TabQuaTrinhCongTac from './tabs/TabQuaTrinhCongTac';
// ... (import các tab khác khi tạo xong)

// interface TabsProps {
// quanNhanData: QuanNhanDetailType;
// activeTab: string;
// handleTabChange: (event: React.SyntheticEvent, newValue: string) => void;
// }

const KhuVucTabsChiTiet = ({ quanNhanData, activeTab, handleTabChange }: TabsProps) => {
const tabContentList: { [key: string]: React.ReactNode } = {
'thong-tin-chung': <TabThongTinChung data={quanNhanData.baseInfo} />,
'ly-lich-ca-nhan': <TabLyLichCaNhan data={quanNhanData.lyLichCaNhan} />,
'qua-trinh-cong-tac': <TabQuaTrinhCongTac initialData={quanNhanData.quaTrinhCongTac} idQuanNhan={quanNhanData.baseInfo.SoHieuQuanNhan} />,
'qua-trinh-dao-tao': <div>Nội dung Tab Quá trình Đào tạo</div>,
// ... các tab khác
};

// ... (phần còn lại của component giữ nguyên)
return (
<TabContext value={activeTab}>
<Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
<TabList /_ ...props... _/ >
{/_ ...Tabs... _/}
</TabList>
</Box>
{Object.keys(tabContentList).map(tabValue => (
<TabPanel key={tabValue} value={tabValue} sx={{ p: 0 }}>
<CardContent>
{tabContentList[tabValue]}
</CardContent>
</TabPanel>
))}
</TabContext>
);
};

export default KhuVucTabsChiTiet;
