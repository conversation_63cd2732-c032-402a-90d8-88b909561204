Bước 23: C<PERSON><PERSON> nhật mockDataChiTiet.ts cho "Kỷ luật"
Định nghĩa KyLuatEntryType và các type liên quan:

Thêm dữ liệu mẫu vào mockQuanNhanChiTietData.kyLuat.

TypeScript

// src\views\quan-ly-quan-nhan\chi-tiet\mockDataChiTiet.ts (Thêm/Cập nhật)

export type TinhTrangChapHanhKyLuatType = 'Đang chấp hành' | 'Đã hoàn thành' | 'Được giảm/xóa kỷ luật' | 'Chưa thi hành';

export const tinhTrangChapHanhKyLuatObj: Record<
TinhTrangChapHanhKyLuatType,
{ color: 'warning' | 'success' | 'info' | 'secondary'; label: string }

> = {
> 'Đang chấp hành': { color: 'warning', label: 'Đang chấp hành' },
> 'Đã hoàn thành': { color: 'success', label: 'Đã hoàn thành' },
> 'Được giảm/xóa kỷ luật': { color: 'info', label: 'Được giảm/xóa KL' },
> 'Chưa thi hành': {color: 'secondary', label: 'Chưa thi hành'}
> };

export interface KyLuatEntryType {
id: string; // ID duy nhất cho mỗi dòng
HoSoVuViecKyLuatID?: string; // Link đến chi tiết vụ việc nếu có
SoQuyetDinh: string;
NgayQuyetDinh: string; // ISO Date string
CapRaQuyetDinhID: string; // Sẽ map sang tên
HinhThucKyLuatID: string; // Sẽ map sang tên (Khiển trách, Cảnh cáo, Hạ bậc lương, Giáng chức, Cách chức, Giáng cấp bậc, Tước danh hiệu QN...)
LyDoKyLuat: string; // Nội dung vi phạm
ThoiHanKyLuat?: string; // Ví dụ: "6 tháng", "1 năm", hoặc để trống nếu không có thời hạn cụ thể
NgayBatDauThiHanh?: string; // ISO Date string
NgayKetThucKyLuatDuKien?: string; // ISO Date string
TinhTrangChapHanh: TinhTrangChapHanhKyLuatType;
FileDinhKemURL?: string; // URL tới file quyết định
GhiChu?: string;
}

// Trong mockQuanNhanChiTietData:
// ...
// kyLuat: [
// {
// id: 'kl_001',
// HoSoVuViecKyLuatID: 'HSVV0045',
// SoQuyetDinh: 'QD203/TD1',
// NgayQuyetDinh: '2019-05-10T00:00:00Z',
// CapRaQuyetDinhID: 'TD001', // Tiểu đoàn 1
// HinhThucKyLuatID: 'HTKL_KT', // Khiển trách
// LyDoKyLuat: 'Vi phạm quy định về trực ban, trực chiến, để xảy ra mất an toàn trong đơn vị.',
// ThoiHanKyLuat: 'Không', // Hoặc để trống
// NgayBatDauThiHanh: '2019-05-15T00:00:00Z',
// NgayKetThucKyLuatDuKien: undefined, // Không có ngày kết thúc cụ thể cho khiển trách
// TinhTrangChapHanh: 'Đã hoàn thành',
// FileDinhKemURL: '/files/kyluat/qd203_td1.pdf',
// GhiChu: 'Đã nghiêm túc kiểm điểm và khắc phục.'
// },
// {
// id: 'kl_002',
// HoSoVuViecKyLuatID: 'HSVV0091',
// SoQuyetDinh: 'QD05/TRD2',
// NgayQuyetDinh: '2021-11-01T00:00:00Z',
// CapRaQuyetDinhID: 'TRD002', // Trung đoàn 2
// HinhThucKyLuatID: 'HTKL_CC', // Cảnh cáo
// LyDoKyLuat: 'Thiếu trách nhiệm trong quản lý vật tư, trang bị, gây thất thoát nhỏ.',
// ThoiHanKyLuat: '6 tháng',
// NgayBatDauThiHanh: '2021-11-10T00:00:00Z',
// NgayKetThucKyLuatDuKien: '2022-05-09T00:00:00Z', // 6 tháng sau
// TinhTrangChapHanh: 'Đang chấp hành',
// FileDinhKemURL: '/files/kyluat/qd05_trd2.pdf',
// GhiChu: 'Cần theo dõi thêm.'
// }
// ] as KyLuatEntryType[],
// ...
Bước 24: Xây dựng TabPanel cho "Kỷ luật"
Tạo file src\views\quan-ly-quan-nhan\chi-tiet\components\tabs\TabKyLuat.tsx

Mục đích: Hiển thị danh sách các quyết định kỷ luật, cho phép thêm, sửa, xóa và cập nhật tình trạng chấp hành.

Props:

initialData?: KyLuatEntryType[]
idQuanNhan: string
State:

listData, openAddEditDialog, editingData, openConfirmDelete, deletingId, paginationModel.
openDetailDialog, viewingData (để xem chi tiết QĐ Kỷ luật).
openUpdateStatusDialog: boolean
updatingStatusRecord: KyLuatEntryType | null (dữ liệu của QĐ kỷ luật đang được cập nhật trạng thái).
Component Vuexy (MUI & @mui/x-data-grid): Tương tự.

Cấu hình cột cho DataGrid:

HoSoVuViecKyLuatID (Link đến chi tiết vụ việc nếu có)
SoQuyetDinh
NgayQuyetDinh (định dạng dd/MM/yyyy)
CapRaQuyetDinhID (hiển thị tên)
HinhThucKyLuatID (hiển thị tên)
LyDoKyLuat (rút gọn với tooltip)
ThoiHanKyLuat
NgayBatDauThiHanh (định dạng dd/MM/yyyy)
NgayKetThucKyLuatDuKien (định dạng dd/MM/yyyy)
TinhTrangChapHanh (hiển thị bằng CustomChip)
FileDinhKemURL (Link tải/xem)
GhiChu
Hành động: Xem chi tiết QĐ, Cập nhật Tình trạng chấp hành, Sửa (nếu được phép), Xóa (nếu được phép).
TypeScript

// src\views\quan-ly-quan-nhan\chi-tiet\components\tabs\TabKyLuat.tsx
'use client';

import React, { useState, useEffect } from 'react';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import { DataGrid, GridColDef, GridRenderCellParams } from '@mui/x-data-grid';
import IconButton from '@mui/material/IconButton';
import Tooltip from '@mui/material/Tooltip';
import Link from '@mui/material/Link'; // For HoSoVuViecKyLuatID

import IconPlus from '@tabler/icons-react/dist/esm/icons/IconPlus';
import IconEdit from '@tabler/icons-react/dist/esm/icons/IconEdit';
import IconTrash from '@tabler/icons-react/dist/esm/icons/IconTrash';
import IconEye from '@tabler/icons-react/dist/esm/icons/IconEye';
import IconFileText from '@tabler/icons-react/dist/esm/icons/IconFileText';
import IconChecks from '@tabler/icons-react/dist/esm/icons/IconChecks'; // For Update Status

import CustomChip from '@core/components/mui/chip';
import { KyLuatEntryType, tinhTrangChapHanhKyLuatObj, TinhTrangChapHanhKyLuatType } from '../../mockDataChiTiet';
// Giả định có các Dialogs:
// import DialogThemSuaKyLuat from './DialogThemSuaKyLuat';
// import DialogXemChiTietKyLuat from './DialogXemChiTietKyLuat';
// import DialogCapNhatTinhTrangChapHanh from './DialogCapNhatTinhTrangChapHanh';
// import DialogXacNhanXoaItem from '../../../../components/DialogXacNhanXoaItem';

interface TabKyLuatProps {
initialData?: KyLuatEntryType[];
idQuanNhan: string;
}

const formatDateDatagridKL = (dateString?: string | null): string => {
if (!dateString) return '';
// ... (implementation from previous tabs)
try {
const date = new Date(dateString);
if (isNaN(date.getTime())) return 'Không hợp lệ';
return `<span class="math-inline">\{String\(date\.getDate\(\)\)\.padStart\(2, '0'\)\}/</span>{String(date.getMonth() + 1).padStart(2, '0')}/${date.getFullYear()}`;
} catch (e) { return 'Không hợp lệ'; }
};
const mapIdToStringKL = (id?: string, type?: string) => id || 'N/A'; // Placeholder

const TabKyLuat = ({ initialData = [], idQuanNhan }: TabKyLuatProps) => {
const [listData, setListData] = useState<KyLuatEntryType[]>(initialData);
const [openAddEditDialog, setOpenAddEditDialog] = useState(false);
const [editingData, setEditingData] = useState<KyLuatEntryType | null>(null);
const [openDetailDialog, setOpenDetailDialog] = useState(false);
const [viewingData, setViewingData] = useState<KyLuatEntryType | null>(null);
const [openUpdateStatusDialog, setOpenUpdateStatusDialog] = useState(false);
const [updatingStatusRecord, setUpdatingStatusRecord] = useState<KyLuatEntryType | null>(null);
const [openConfirmDelete, setOpenConfirmDelete] = useState(false);
const [deletingId, setDeletingId] = useState<string | null>(null);
const [paginationModel, setPaginationModel] = useState({ page: 0, pageSize: 5 });

useEffect(() => {
setListData(initialData);
}, [initialData]);

const handleOpenAddDialog = () => { /_ ... _/ setEditingData(null); setOpenAddEditDialog(true); };
const handleOpenEditDialog = (rowData: KyLuatEntryType) => { /_ ... _/ setEditingData(rowData); setOpenAddEditDialog(true); };
const handleOpenDetailDialog = (rowData: KyLuatEntryType) => { /_ ... _/ setViewingData(rowData); setOpenDetailDialog(true); };
const handleOpenUpdateStatusDialog = (rowData: KyLuatEntryType) => { /_ ... _/ setUpdatingStatusRecord(rowData); setOpenUpdateStatusDialog(true); };

const handleCloseDialogs = () => {
setOpenAddEditDialog(false);
setOpenDetailDialog(false);
setOpenUpdateStatusDialog(false);
setEditingData(null);
setViewingData(null);
setUpdatingStatusRecord(null);
};

const handleSaveData = (savedData: KyLuatEntryType) => {
console.log('Saving data (Kỷ luật):', savedData, 'for QN ID:', idQuanNhan);
if (editingData) {
setListData(prev => prev.map(item => (item.id === savedData.id ? savedData : item)));
} else {
setListData(prev => [...prev, { ...savedData, id: `new_kl_${Date.now()}` }]);
}
handleCloseDialogs();
};

const handleSaveStatusUpdate = (updatedRecord: KyLuatEntryType) => {
console.log('Updating status (Kỷ luật):', updatedRecord);
setListData(prev => prev.map(item => (item.id === updatedRecord.id ? updatedRecord : item)));
handleCloseDialogs();
};

const handleOpenDeleteDialog = (id: string) => { /_ ... _/ setDeletingId(id); setOpenConfirmDelete(true); };
const handleCloseConfirmDelete = () => { /_ ... _/ setOpenConfirmDelete(false); setDeletingId(null);};
const handleConfirmDelete = () => {
if (deletingId) {
console.log('Deleting Kỷ luật ID:', deletingId);
setListData(prev => prev.filter(item => item.id !== deletingId));
handleCloseConfirmDelete();
}
};

const columns: GridColDef[] = [
{
field: 'HoSoVuViecKyLuatID',
headerName: 'Hồ sơ Vụ việc',
width: 150,
renderCell: (params: GridRenderCellParams) =>
params.value ? <Link href={`/path/to/hsvv/${params.value}`} target="\_blank">{params.value}</Link> : 'N/A'
},
{ field: 'SoQuyetDinh', headerName: 'Số QĐ', width: 130 },
{
field: 'NgayQuyetDinh',
headerName: 'Ngày QĐ',
width: 110,
valueFormatter: params => formatDateDatagridKL(params.value)
},
{
field: 'HinhThucKyLuatID',
headerName: 'Hình thức Kỷ luật',
width: 180,
valueGetter: params => mapIdToStringKL(params.value, 'hinhThucKL')
},
{
field: 'LyDoKyLuat',
headerName: 'Lý do/Nội dung vi phạm',
width: 250,
renderCell: (params: GridRenderCellParams) => (
<Tooltip title={params.value || ''} placement="top-start">
<Typography noWrap variant="body2" sx={{overflow: 'hidden', textOverflow: 'ellipsis'}}>
{params.value || ''}
</Typography>
</Tooltip>
)
},
{
field: 'TinhTrangChapHanh',
headerName: 'Tình trạng Chấp hành',
width: 180,
renderCell: (params: GridRenderCellParams) => {
const status = params.value as TinhTrangChapHanhKyLuatType;
const statusInfo = tinhTrangChapHanhKyLuatObj[status] || { label: status, color: 'default' };
return <CustomChip label={statusInfo.label} color={statusInfo.color as any} skin="light" size="small" rounded />;
}
},
{
field: 'actions',
headerName: 'Hành động',
width: 200, // Tăng chiều rộng cho nhiều nút hơn
sortable: false,
filterable: false,
renderCell: (params: GridRenderCellParams) => (
<Box>
<Tooltip title="Xem Chi tiết QĐ">
<IconButton size="small" onClick={() => handleOpenDetailDialog(params.row as KyLuatEntryType)}>
<IconEye size={20} />
</IconButton>
</Tooltip>
<Tooltip title="Cập nhật Tình trạng Chấp hành">
<IconButton size="small" onClick={() => handleOpenUpdateStatusDialog(params.row as KyLuatEntryType)}>
<IconChecks size={20} />
</IconButton>
</Tooltip>
<Tooltip title="Sửa">
<IconButton size="small" onClick={() => handleOpenEditDialog(params.row as KyLuatEntryType)}>
<IconEdit size={20} />
</IconButton>
</Tooltip>
<Tooltip title="Xóa">
<IconButton size="small" onClick={() => handleOpenDeleteDialog(params.row.id as string)}>
<IconTrash size={20} />
</IconButton>
</Tooltip>
</Box>
)
}
// Các cột khác: ThoiHanKyLuat, NgayBatDauThiHanh, NgayKetThucKyLuatDuKien, FileDinhKemURL, GhiChu
];

return (
<Box>
<Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
<Typography variant="h6" sx={{ color: 'primary.main' }}>
Kỷ luật
</Typography>
<Button
variant="contained"
startIcon={<IconPlus />}
onClick={handleOpenAddDialog} >
Thêm QĐ Kỷ luật
</Button>
</Box>

      <DataGrid
        autoHeight
        rows={listData}
        columns={columns}
        pageSizeOptions={[5, 10, 25]}
        paginationModel={paginationModel}
        onPaginationModelChange={setPaginationModel}
        getRowId={(row) => row.id}
        sx={{
            '& .MuiDataGrid-columnHeaders': { backgroundColor: 'customColors.tableHeaderBg' }
        }}
      />

      {/* Placeholders for Dialogs */}
      {openAddEditDialog && <Typography sx={{mt: 2, p:2, border: '1px dashed grey'}}>Dialog Thêm/Sửa Kỷ luật (Placeholder - Data: {JSON.stringify(editingData)})</Typography>}
      {openDetailDialog && viewingData && <Typography sx={{mt: 2, p:2, border: '1px dashed blue'}}>Dialog Xem Chi tiết Kỷ luật (Placeholder - Data: {JSON.stringify(viewingData)})</Typography>}
      {openUpdateStatusDialog && updatingStatusRecord && <Typography sx={{mt: 2, p:2, border: '1px dashed orange'}}>Dialog Cập nhật Tình trạng Chấp hành (Placeholder - Record: {JSON.stringify(updatingStatusRecord)})</Typography>}
      {openConfirmDelete && <Typography sx={{mt: 2, p:2, border: '1px dashed red'}}>Dialog Xác nhận Xóa (Placeholder - ID: {deletingId})</Typography>}
    </Box>

);
};

export default TabKyLuat;
Bước 25: Tạo Component Dialogs cho "Kỷ luật" (Sơ bộ)
DialogThemSuaKyLuat.tsx: Tương tự các dialog thêm/sửa trước, với các trường của KyLuatEntryType.

DialogXemChiTietKyLuat.tsx: Hiển thị thông tin chi tiết một QĐ Kỷ luật.

DialogCapNhatTinhTrangChapHanh.tsx: Cho phép chọn TinhTrangChapHanh mới từ Select, có thể kèm theo ngày tháng và ghi chú.

TypeScript

// src\views\quan-ly-quan-nhan\chi-tiet\components\tabs\DialogCapNhatTinhTrangChapHanh.tsx (Sơ bộ)
// 'use client';
// import React, { useState, useEffect } from 'react';
// import Dialog from '@mui/material/Dialog';
// import DialogTitle from '@mui/material/DialogTitle';
// import DialogContent from '@mui/material/DialogContent';
// import DialogActions from '@mui/material/DialogActions';
// import Button from '@mui/material/Button';
// import FormControl from '@mui/material/FormControl';
// import InputLabel from '@mui/material/InputLabel';
// import Select, { SelectChangeEvent } from '@mui/material/Select';
// import MenuItem from '@mui/material/MenuItem';
// import TextField from '@mui/material/TextField'; // For notes or new end date
// import Grid from '@mui/material/Grid';

// import { KyLuatEntryType, TinhTrangChapHanhKyLuatType, tinhTrangChapHanhKyLuatObj } from '../../mockDataChiTiet';

// interface DialogUpdateStatusProps {
// open: boolean;
// onClose: () => void;
// onSubmit: (updatedRecord: KyLuatEntryType) => void;
// currentRecord: KyLuatEntryType | null;
// }

// const DialogCapNhatTinhTrangChapHanh = ({ open, onClose, onSubmit, currentRecord }: DialogUpdateStatusProps) => {
// const [newStatus, setNewStatus] = useState<TinhTrangChapHanhKyLuatType | ''>('');
// const [ghiChuCapNhat, setGhiChuCapNhat] = useState('');
// // Có thể thêm state cho ngày hoàn thành mới nếu cần

// useEffect(() => {
// if (currentRecord) {
// setNewStatus(currentRecord.TinhTrangChapHanh); // Khởi tạo với trạng thái hiện tại
// setGhiChuCapNhat(currentRecord.GhiChu || ''); // Hoặc một trường ghi chú riêng cho việc cập nhật
// } else {
// setNewStatus('');
// setGhiChuCapNhat('');
// }
// }, [currentRecord, open]);

// const handleStatusChange = (event: SelectChangeEvent<TinhTrangChapHanhKyLuatType>) => {
// setNewStatus(event.target.value as TinhTrangChapHanhKyLuatType);
// };

// const handleSubmitUpdate = () => {
// if (currentRecord && newStatus) {
// const updatedRecord = {
// ...currentRecord,
// TinhTrangChapHanh: newStatus,
// GhiChu: `${currentRecord.GhiChu || ''}\n[Cập nhật ngày ${new Date().toLocaleDateString()}]: ${ghiChuCapNhat}`.trim()
// // Cập nhật NgayKetThucKyLuat nếu newStatus là 'Đã hoàn thành' và có ngày cụ thể
// };
// onSubmit(updatedRecord);
// }
// };

// if (!currentRecord) return null;

// return (
// <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
// <DialogTitle>Cập nhật Tình trạng Chấp hành Kỷ luật</DialogTitle>
// <DialogContent>
// <Typography variant="subtitle1" gutterBottom>Quyết định số: {currentRecord.SoQuyetDinh}</Typography>
// <Grid container spacing={2} sx={{mt: 1}}>
// <Grid item xs={12}>
// <FormControl fullWidth>
// <InputLabel id="update-status-kl-label">Trạng thái Chấp hành Mới</InputLabel>
// <Select
// labelId="update-status-kl-label"
// value={newStatus}
// label="Trạng thái Chấp hành Mới"
// onChange={handleStatusChange}
// >
// {(Object.keys(tinhTrangChapHanhKyLuatObj) as TinhTrangChapHanhKyLuatType[]).map((key) => (
// <MenuItem key={key} value={key}>{tinhTrangChapHanhKyLuatObj[key].label}</MenuItem>
// ))}
// </Select>
// </FormControl>
// </Grid>
// {/_ Có thể thêm DatePicker cho "Ngày hoàn thành thực tế" nếu trạng thái là "Đã hoàn thành" _/}
// <Grid item xs={12}>
// <TextField
// label="Ghi chú cập nhật"
// multiline
// rows={3}
// fullWidth
// value={ghiChuCapNhat}
// onChange={(e) => setGhiChuCapNhat(e.target.value)}
// variant="outlined"
// />
// </Grid>
// </Grid>
// </DialogContent>
// <DialogActions>
// <Button onClick={onClose}>Hủy</Button>
// <Button onClick={handleSubmitUpdate} variant="contained">Cập nhật</Button>
// </DialogActions>
// </Dialog>
// );
// };
// export default DialogCapNhatTinhTrangChapHanh;
Bước 26: Cập nhật KhuVucTabsChiTiet.tsx để sử dụng TabKyLuat
Chỉnh sửa file src\views\quan-ly-quan-nhan\chi-tiet\components\KhuVucTabsChiTiet.tsx:

TypeScript

// src\views\quan-ly-quan-nhan\chi-tiet\components\KhuVucTabsChiTiet.tsx
// ... (các imports khác)
import TabKyLuat from './tabs/TabKyLuat';
// ... (import các tab khác khi tạo xong)

// interface TabsProps { ... }

const KhuVucTabsChiTiet = ({ quanNhanData, activeTab, handleTabChange }: TabsProps) => {
const tabContentList: { [key: string]: React.ReactNode } = {
'thong-tin-chung': <TabThongTinChung data={quanNhanData.baseInfo} />,
'ly-lich-ca-nhan': <TabLyLichCaNhan data={quanNhanData.lyLichCaNhan} />,
'qua-trinh-cong-tac': <TabQuaTrinhCongTac initialData={quanNhanData.quaTrinhCongTac} idQuanNhan={quanNhanData.baseInfo.SoHieuQuanNhan} />,
'qua-trinh-dao-tao': <TabQuaTrinhDaoTao initialData={quanNhanData.quaTrinhDaoTao} idQuanNhan={quanNhanData.baseInfo.SoHieuQuanNhan} />,
'khen-thuong': <TabKhenThuong initialData={quanNhanData.khenThuong} idQuanNhan={quanNhanData.baseInfo.SoHieuQuanNhan} />,
'ky-luat': <TabKyLuat initialData={quanNhanData.kyLuat} idQuanNhan={quanNhanData.baseInfo.SoHieuQuanNhan} />,
'suc-khoe': <div>Nội dung Tab Sức khỏe</div>,
// ... các tab khác
};

// ... (phần còn lại của component giữ nguyên)
return (
<TabContext value={activeTab}>
<Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
<TabList /_ ...props... _/ >
{/_ ...Tabs... _/}
</TabList>
</Box>
{Object.keys(tabContentList).map(tabValue => (
<TabPanel key={tabValue} value={tabValue} sx={{ p: 0 }}>
<CardContent>
{tabContentList[tabValue]}
</CardContent>
</TabPanel>
))}
</TabContext>
);
};

export default KhuVucTabsChiTiet;
