/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { QuanHeGiaDinhEntity } from '~/database/typeorm/entities/quanHeGiaDinh.entity';

@Injectable()
export class QuanHeGiaDinhRepository extends Repository<QuanHeGiaDinhEntity> {
    constructor(private dataSource: DataSource) {
        super(QuanHeGiaDinhEntity, dataSource.createEntityManager());
    }
}
