# Quy trình xây dựng giao diện trang "Quản lý Đảng phí"

---

**Trang: Quản lý Đảng phí (`/${locale}/cong-tac-dang/dang-phi`)**

- **<PERSON><PERSON><PERSON> (Tiêu đề hiển thị):** `<PERSON>uản lý Thu - Nộp - Chi Đảng phí`
- **<PERSON><PERSON><PERSON> đích chính của Trang:** <PERSON><PERSON> cấp công cụ cho người có trách nhiệm (ví dụ: <PERSON><PERSON>ư <PERSON>, người được phân công quản lý quỹ Đảng của Tổ chức Đảng) đ<PERSON> ghi nhận, theo dõi và báo cáo các giao dịch liên quan đến đảng phí.

---

#### I. Chức năng trang `QuanLyDangPhiPage`

1.  **<PERSON><PERSON><PERSON> năng "Chọn Tổ chức Đảng để <PERSON>ả<PERSON> l<PERSON>"**

    - **Tổng quan:** <PERSON><PERSON><PERSON><PERSON> dùng cần chọn một Tổ chức <PERSON> cụ thể (thường là Chi bộ hoặc Đảng bộ cơ sở mà họ có quyền quản lý đảng phí) để xem và thực hiện các giao dịch.
    - **Chi tiết:**
      - Ở vị trí đầu trang, có một trường lựa chọn "Tổ chức Đảng".
      - Người dùng chọn một Tổ chức Đảng từ danh sách (danh sách này được giới hạn theo quyền quản lý của người dùng).
      - Sau khi chọn, tất cả các thông tin và chức năng trên trang (danh sách giao dịch, số dư, các nút hành động) sẽ được áp dụng cho Tổ chức Đảng đã chọn.

2.  **Chức năng "Hiển thị Danh sách Giao dịch Đảng phí"**

    - **Tổng quan:** Sau khi chọn Tổ chức Đảng, trang hiển thị một bảng liệt kê tất cả các giao dịch thu, nộp, chi đảng phí của Tổ chức Đảng đó.
    - **Chi tiết:**
      - Thông tin được trình bày dưới dạng bảng, mỗi hàng là một giao dịch.
      - **Các cột thông tin cho mỗi giao dịch:** "Ngày Giao dịch", "Kỳ Thu nộp" (YYYY-MM), "Loại Giao dịch" (Thu của đảng viên, Nộp cấp trên, Chi tại chi bộ), "Đảng viên Nộp" (nếu là giao dịch thu), "Số Tiền", "Nội dung Giao dịch", "Người thực hiện Ghi nhận".
      - Hỗ trợ phân trang nếu số lượng giao dịch lớn.
      - Có thể có các bộ lọc bổ sung cho danh sách giao dịch.

3.  **Chức năng "Lọc và Tìm kiếm Giao dịch"**

    - **Tổng quan:** Cho phép người dùng lọc và tìm kiếm các giao dịch trong sổ quỹ của Tổ chức Đảng đã chọn.
    - **Chi tiết:**
      - **Lọc theo "Kỳ Thu nộp":** Trường nhập liệu/chọn (YYYY-MM).
      - **Lọc theo "Loại Giao dịch":** Trường lựa chọn từ danh mục `DmLoaiGiaoDichDangPhi`.
      - **Lọc theo "Ngày Giao dịch":** Chọn khoảng ngày (Từ ngày - Đến ngày).
      - **Tìm kiếm theo "Đảng viên Nộp" (nếu là giao dịch thu):** Ô nhập liệu tên hoặc mã đảng viên.
      - **Tìm kiếm theo "Nội dung Giao dịch":** Ô nhập liệu từ khóa.
      - Nút "Áp dụng bộ lọc" và "Đặt lại".

4.  **Chức năng "Ghi nhận Giao dịch Thu Đảng phí từ Đảng viên"**

    - **Tổng quan:** Cho phép ghi nhận việc đảng viên đã đóng đảng phí cho một kỳ cụ thể.
    - **Chi tiết:**
      - Có nút "Ghi nhận Thu Đảng phí".
      - Khi nhấn, mở giao diện (form/dialog) để nhập thông tin:
        - `DangVienNopID`: Lựa chọn Đảng viên (từ danh sách đảng viên thuộc Tổ chức Đảng đang quản lý).
        - `KyThuNop`: Nhập Kỳ thu nộp (YYYY-MM, bắt buộc).
        - `NgayGiaoDich`: Chọn Ngày thực tế giao dịch (bắt buộc).
        - `SoTien`: Nhập Số tiền đã thu (bắt buộc, số dương).
        - `NoiDungGiaoDich`: Nhập nội dung (ví dụ: "Đóng đảng phí tháng X/YYYY", có thể tự gợi ý).
        - `NguoiThuNguoiChiNguoiNop`: (Tên người trực tiếp thu, có thể là người dùng hiện tại).
        - `FileChungTuKemTheoURL`: (Tùy chọn) Tải lên file minh chứng (ví dụ: phiếu thu nếu có).
        - `GhiChu`: Ghi chú thêm.
      - Nút "Lưu Giao dịch Thu".
      - Sau khi lưu, giao dịch mới sẽ xuất hiện trong bảng danh sách, và số dư (nếu có) sẽ được cập nhật.

5.  **Chức năng "Ghi nhận Giao dịch Nộp Đảng phí lên Cấp trên"**

    - **Tổng quan:** Cho phép ghi nhận việc Tổ chức Đảng đã nộp số đảng phí thu được lên cấp ủy cấp trên.
    - **Chi tiết:**
      - Có nút "Ghi nhận Nộp Cấp trên".
      - Khi nhấn, mở giao diện (form/dialog) để nhập thông tin:
        - `KyThuNop`: Nhập Kỳ nộp (YYYY-MM, có thể là nhiều kỳ, bắt buộc).
        - `NgayGiaoDich`: Chọn Ngày thực tế giao dịch (bắt buộc).
        - `SoTien`: Nhập Số tiền đã nộp (bắt buộc, số dương).
        - `NoiDungGiaoDich`: Nhập nội dung (ví dụ: "Nộp đảng phí quý X/YYYY lên Đảng ủy Y").
        - `NguoiThuNguoiChiNguoiNop`: (Tên người thực hiện nộp).
        - `FileChungTuKemTheoURL`: (Tùy chọn) Tải lên file minh chứng (ví dụ: giấy nộp tiền, biên lai).
        - `GhiChu`: Ghi chú thêm.
      - Nút "Lưu Giao dịch Nộp".
      - Sau khi lưu, giao dịch mới sẽ xuất hiện trong bảng danh sách, và số dư (nếu có) sẽ được cập nhật.

6.  **Chức năng "Ghi nhận Giao dịch Chi Đảng phí tại Chi bộ/Đảng bộ"**

    - **Tổng quan:** Cho phép ghi nhận các khoản chi từ quỹ đảng phí của Tổ chức Đảng (theo quy định).
    - **Chi tiết:**
      - Có nút "Ghi nhận Chi tại Đơn vị".
      - Khi nhấn, mở giao diện (form/dialog) để nhập thông tin:
        - `NgayGiaoDich`: Chọn Ngày thực tế giao dịch (bắt buộc).
        - `SoTien`: Nhập Số tiền đã chi (bắt buộc, số dương).
        - `NoiDungGiaoDich`: Nhập nội dung chi (ví dụ: "Chi mua văn phòng phẩm cho sinh hoạt chi bộ", bắt buộc).
        - `NguoiThuNguoiChiNguoiNop`: (Tên người thực hiện chi/người nhận nếu có).
        - `FileChungTuKemTheoURL`: (Tùy chọn) Tải lên file minh chứng (ví dụ: hóa đơn).
        - `GhiChu`: Ghi chú thêm.
      - Nút "Lưu Giao dịch Chi".
      - Sau khi lưu, giao dịch mới sẽ xuất hiện trong bảng danh sách, và số dư (nếu có) sẽ được cập nhật.

7.  **Chức năng "Xem Số dư Quỹ Đảng phí" (Nếu hệ thống quản lý chi tiết số dư)**

    - **Tổng quan:** Hiển thị số dư hiện tại của quỹ đảng phí của Tổ chức Đảng đã chọn.
    - **Chi tiết:**
      - Một khu vực trên trang hiển thị: "Số dư hiện tại: [Số tiền]".
      - Số dư này sẽ được tính toán tự động dựa trên tổng thu trừ tổng chi (bao gồm cả nộp cấp trên).
      - Mỗi khi có giao dịch mới được ghi nhận, số dư cần được cập nhật lại.
      - _(Lưu ý: Việc quản lý số dư chi tiết (`SoDuSauGiaoDich` trong từng giao dịch) sẽ phức tạp hơn và cần đảm bảo tính chính xác của các phép tính lũy kế. Nếu chỉ quản lý thu-nộp-chi mà không cần số dư tức thời, có thể bỏ qua trường này trong từng giao dịch và chỉ tính tổng khi báo cáo)._

8.  **Chức năng "Báo cáo Tình hình Thu - Nộp - Chi Đảng phí"**

    - **Tổng quan:** Cho phép tạo và xem các báo cáo về tình hình quản lý đảng phí.
    - **Chi tiết:**
      - Có nút "Xem Báo cáo" hoặc một mục riêng cho báo cáo.
      - Giao diện chọn loại báo cáo (ví dụ: Báo cáo Thu đảng phí theo kỳ, Báo cáo Nộp cấp trên, Báo cáo Chi tại chi bộ, Báo cáo Tổng hợp Thu-Nộp-Chi-Tồn).
      - Chọn kỳ báo cáo (Tháng, Quý, Năm).
      - Hiển thị kết quả báo cáo dưới dạng bảng.
      - Chức năng "Xuất báo cáo" ra file (ví dụ: Excel, PDF).

9.  **Chức năng "Sửa/Xóa Giao dịch" (Hạn chế, cần phân quyền chặt chẽ)**
    - **Tổng quan:** Cho phép chỉnh sửa hoặc xóa các giao dịch đã ghi nhận (thường chỉ áp dụng cho các giao dịch mới nhập sai và cần được thực hiện bởi người có quyền cao).
    - **Chi tiết:**
      - Trong bảng danh sách giao dịch, mỗi dòng có thể có hành động "Sửa" và "Xóa".
      - **Sửa:** Mở lại form nhập liệu tương ứng với loại giao dịch đó, cho phép chỉnh sửa các thông tin.
      - **Xóa:** Cần có hộp thoại xác nhận. Việc xóa giao dịch sẽ ảnh hưởng đến số dư.
      - Mọi hành động sửa/xóa phải được ghi lại chi tiết trong nhật ký hệ thống (Audit Log).

---

#### II. Bảng dữ liệu được sử dụng trên trang `QuanLyDangPhiPage`

- **Bảng `ThuChiDangPhi` (PartyFeeTransactions)**

  - `ID: number` (BIGINT, PK, AUTO_INCREMENT)
  - `DangVienNopID: string` (VARCHAR(20), FK NULL REFERENCES DangVien(MaDangVien)) - Đảng viên nộp (chỉ áp dụng cho giao dịch Thu)
  - `ToChucDangQuanLyID: number` (BIGINT, NOT NULL, FK REFERENCES ToChucDang(ID)) - Chi bộ/Đảng bộ quản lý quỹ này
  - `LoaiGiaoDichID: number` (INT, NOT NULL, FK REFERENCES DmLoaiGiaoDichDangPhi(ID)) - Loại giao dịch (Thu của ĐV, Nộp cấp trên, Chi tại CB)
  - `KyThuNop: string` (VARCHAR(7) NOT NULL) - Kỳ thu nộp (YYYY-MM) (áp dụng cho Thu và Nộp)
  - `NgayGiaoDich: date` (DATE, NOT NULL) - Ngày thực tế giao dịch
  - `SoTien: number` (DECIMAL(15,2), NOT NULL) - Số tiền
  - `NoiDungGiaoDich: string` (NVARCHAR(500)) - Nội dung/Diễn giải giao dịch
  - `NguoiThuNguoiChiNguoiNop: string` (NVARCHAR(100) NULL) - Tên người liên quan trực tiếp (người thu, người chi, người nộp)
  - `FileChungTuKemTheoURL: string` (VARCHAR(255) NULL) - Đường dẫn file chứng từ đính kèm
  - `SoDuSauGiaoDich: number` (DECIMAL(15,2) NULL) - Số dư quỹ của chi bộ sau giao dịch (nếu quản lý chi tiết)
  - `GhiChu: string` (NTEXT)

- **Bảng `DangVien` (PartyMembers)** (Để chọn Đảng viên khi ghi nhận Thu Đảng phí)

  - `MaDangVien: string` (PK)
  - `HoVaTen: string`
  - `ToChucDangSinhHoatID: number` (Để lọc đảng viên theo Tổ chức Đảng đang quản lý quỹ)

- **Bảng `ToChucDang` (PartyOrganizations)** (Để chọn Tổ chức Đảng quản lý quỹ và hiển thị tên)

  - `ID: number` (PK)
  - `TenToChucDang: string`

- **Danh mục `DmLoaiGiaoDichDangPhi`**
  - `ID: number` (PK)
  - `TenLoaiGiaoDich: string` (Ví dụ: "Thu Đảng phí từ Đảng viên", "Nộp Đảng phí lên Cấp trên", "Chi hoạt động tại Chi bộ")

---

#### III. Liên kết với page khác

1.  **Trang Chi tiết Hồ sơ Đảng viên:**
    - Tab "Đảng phí" trong chi tiết hồ sơ đảng viên sẽ hiển thị lịch sử đóng đảng phí của cá nhân đảng viên đó, lấy dữ liệu từ bảng `ThuChiDangPhi` (lọc theo `DangVienNopID`).
2.  **Module Báo cáo Tổng hợp (Nếu có):**
    - Dữ liệu từ quản lý đảng phí có thể được sử dụng để tổng hợp vào các báo cáo tài chính chung của Đảng bộ (nếu có).

---
