Bước 35: C<PERSON><PERSON> nhật mockDataChiTiet.ts cho "Chế độ Chính sách"
Định nghĩa CheDoChinhSachQuanNhanEntryType và các type liên quan (ví dụ: TrangThaiGiaiQuyetCheDoType):

Thêm dữ liệu mẫu vào mockQuanNhanChiTietData.cheDoChinhSach.

TypeScript

// src\views\quan-ly-quan-nhan\chi-tiet\mockDataChiTiet.ts (Thêm/Cập nhật)

export type TrangThaiGiaiQuyetCheDoType = 'Đã giải quyết' | 'Đang xử lý' | 'Chưa giải quyết' | 'Từ chối';

export const trangThaiGiaiQuyetCheDoObj: Record<
TrangThaiGiaiQuyetCheDoType,
{ color: 'success' | 'info' | 'secondary' | 'error'; label: string }

> = {
> 'Đã giải quyết': { color: 'success', label: 'Đã giải quyết' },
> 'Đang xử lý': { color: 'info', label: 'Đang xử lý' },
> 'Chưa giải quyết': { color: 'secondary', label: 'Chưa giải quyết' },
> 'Từ chối': { color: 'error', label: 'Từ chối' }
> };

export interface CheDoChinhSachQuanNhanEntryType {
id: string; // ID duy nhất cho mỗi chế độ
LoaiCheDoID: string; // Sẽ map sang tên (BHYT, BHXH, Chính sách nhà ở, Nghỉ phép, Trợ cấp khó khăn...)
NgayBatDauHuong: string; // ISO Date string
NgayKetThucHuong?: string; // ISO Date string, có thể không có
SoQuyetDinhLienQuan?: string;
CoQuanGiaiQuyet?: string; // Tên cơ quan
ChiTietNoiDung: string; // Mô tả chi tiết về chế độ, chính sách được hưởng
SoTienHuong?: number; // Số tiền, nếu có
TrangThaiGiaiQuyet: TrangThaiGiaiQuyetCheDoType;
NgayGiaiQuyet?: string; // ISO Date string
FileDinhKemURL?: string; // URL tới file liên quan (quyết định, đơn...)
GhiChu?: string;
}

// Trong mockQuanNhanChiTietData:
// ...
// cheDoChinhSach: [
// {
// id: 'cdcs_001',
// LoaiCheDoID: 'CD_BHYT', // Bảo hiểm y tế
// NgayBatDauHuong: '2012-09-10T00:00:00Z', // Từ ngày nhập ngũ
// // NgayKetThucHuong: null, // Vẫn còn hiệu lực
// ChiTietNoiDung: 'Bảo hiểm y tế theo đối tượng quân nhân tại ngũ.',
// CoQuanGiaiQuyet: 'Bảo hiểm Xã hội Bộ Quốc phòng',
// TrangThaiGiaiQuyet: 'Đã giải quyết', // Hoặc 'Đang hưởng'
// NgayGiaiQuyet: '2012-09-10T00:00:00Z',
// FileDinhKemURL: '/files/chedo/bhyt_qn.pdf',
// GhiChu: 'Mã thẻ BHYT: QN123456789'
// },
// {
// id: 'cdcs_002',
// LoaiCheDoID: 'CD_NP2023', // Nghỉ phép năm 2023
// NgayBatDauHuong: '2023-08-01T00:00:00Z',
// NgayKetThucHuong: '2023-08-15T00:00:00Z',
// SoQuyetDinhLienQuan: 'QDNP/2023/DV003',
// ChiTietNoiDung: 'Nghỉ phép năm theo quy định, 15 ngày.',
// CoQuanGiaiQuyet: 'Đơn vị DV003',
// TrangThaiGiaiQuyet: 'Đã giải quyết',
// NgayGiaiQuyet: '2023-07-20T00:00:00Z',
// GhiChu: 'Đã thanh toán tiền tàu xe.'
// },
// {
// id: 'cdcs_003',
// LoaiCheDoID: 'CD_TCKK2024', // Trợ cấp khó khăn đột xuất 2024
// NgayBatDauHuong: '2024-03-01T00:00:00Z', // Ngày đề nghị/xét duyệt
// ChiTietNoiDung: 'Hỗ trợ gia đình gặp khó khăn do thiên tai.',
// SoTienHuong: 5000000,
// CoQuanGiaiQuyet: 'Cục Chính sách',
// TrangThaiGiaiQuyet: 'Đang xử lý',
// // FileDinhKemURL: '/files/chedo/dexuat_tckk_2024.pdf',
// GhiChu: 'Chờ duyệt cấp trên.'
// }
// ] as CheDoChinhSachQuanNhanEntryType[],
// ...
Bước 36: Xây dựng TabPanel cho "Chế độ Chính sách"
Tạo file src\views\quan-ly-quan-nhan\chi-tiet\components\tabs\TabCheDoChinhSach.tsx

Mục đích: Hiển thị danh sách các chế độ chính sách, cho phép thêm, sửa, xóa.

Props:

initialData?: CheDoChinhSachQuanNhanEntryType[]
idQuanNhan: string
State: Tương tự các tab DataGrid trước.

Component Vuexy (MUI & @mui/x-data-grid): Tương tự.

Cấu hình cột cho DataGrid:

LoaiCheDoID (hiển thị tên)
NgayBatDauHuong (định dạng dd/MM/yyyy)
NgayKetThucHuong (định dạng dd/MM/yyyy hoặc "N/A")
ChiTietNoiDung (rút gọn với tooltip)
SoTienHuong (định dạng tiền tệ)
TrangThaiGiaiQuyet (hiển thị bằng CustomChip)
FileDinhKemURL (Link tải/xem)
Hành động: Sửa, Xóa, Xem file (và có thể có "Xem chi tiết" để xem đầy đủ thông tin QĐ, cơ quan giải quyết...).
TypeScript

// src\views\quan-ly-quan-nhan\chi-tiet\components\tabs\TabCheDoChinhSach.tsx
'use client';

import React, { useState, useEffect } from 'react';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import { DataGrid, GridColDef, GridRenderCellParams, GridValueFormatterParams } from '@mui/x-data-grid';
import IconButton from '@mui/material/IconButton';
import Tooltip from '@mui/material/Tooltip';

import IconPlus from '@tabler/icons-react/dist/esm/icons/IconPlus';
import IconEdit from '@tabler/icons-react/dist/esm/icons/IconEdit';
import IconTrash from '@tabler/icons-react/dist/esm/icons/IconTrash';
import IconFileText from '@tabler/icons-react/dist/esm/icons/IconFileText';
import IconEye from '@tabler/icons-react/dist/esm/icons/IconEye'; // For View Details

import CustomChip from '@core/components/mui/chip';
import { CheDoChinhSachQuanNhanEntryType, trangThaiGiaiQuyetCheDoObj, TrangThaiGiaiQuyetCheDoType } from '../../mockDataChiTiet';
// Giả định có DialogThemSuaCheDoChinhSach và DialogXacNhanXoaItem
// import DialogThemSuaCheDoChinhSach from './DialogThemSuaCheDoChinhSach';
// import DialogXemChiTietCheDoChinhSach from './DialogXemChiTietCheDoChinhSach'; // Optional
// import DialogXacNhanXoaItem from '../../../../components/DialogXacNhanXoaItem';

interface TabCheDoChinhSachProps {
initialData?: CheDoChinhSachQuanNhanEntryType[];
idQuanNhan: string;
}

const formatDateDatagridCDCS = (dateString?: string | null): string => {
if (!dateString) return '';
try {
const date = new Date(dateString);
if (isNaN(date.getTime())) return 'Không hợp lệ';
return `<span class="math-inline">\{String\(date\.getDate\(\)\)\.padStart\(2, '0'\)\}/</span>{String(date.getMonth() + 1).padStart(2, '0')}/${date.getFullYear()}`;
} catch (e) { return 'Không hợp lệ'; }
};

const formatCurrency = (value?: number): string => {
if (value === undefined || value === null) return 'N/A';
return new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(value);
};

const mapIdToStringCDCS = (id?: string, type?: string) => id || 'N/A'; // Placeholder

const TabCheDoChinhSach = ({ initialData = [], idQuanNhan }: TabCheDoChinhSachProps) => {
const [listData, setListData] = useState<CheDoChinhSachQuanNhanEntryType[]>(initialData);
const [openAddEditDialog, setOpenAddEditDialog] = useState(false);
const [editingData, setEditingData] = useState<CheDoChinhSachQuanNhanEntryType | null>(null);
const [openDetailDialog, setOpenDetailDialog] = useState(false); // Optional
const [viewingData, setViewingData] = useState<CheDoChinhSachQuanNhanEntryType | null>(null); // Optional
const [openConfirmDelete, setOpenConfirmDelete] = useState(false);
const [deletingId, setDeletingId] = useState<string | null>(null);
const [paginationModel, setPaginationModel] = useState({ page: 0, pageSize: 5 });

useEffect(() => {
setListData(initialData);
}, [initialData]);

const handleOpenAddDialog = () => { setEditingData(null); setOpenAddEditDialog(true); };
const handleOpenEditDialog = (rowData: CheDoChinhSachQuanNhanEntryType) => { setEditingData(rowData); setOpenAddEditDialog(true); };
const handleOpenDetailDialog = (rowData: CheDoChinhSachQuanNhanEntryType) => { setViewingData(rowData); setOpenDetailDialog(true); };

const handleCloseDialogs = () => {
setOpenAddEditDialog(false);
setOpenDetailDialog(false);
setEditingData(null);
setViewingData(null);
};

const handleSaveData = (savedData: CheDoChinhSachQuanNhanEntryType) => {
console.log('Saving data (Chế độ CS):', savedData, 'for QN ID:', idQuanNhan);
if (editingData) {
setListData(prev => prev.map(item => (item.id === savedData.id ? savedData : item)));
} else {
setListData(prev => [...prev, { ...savedData, id: `new_cdcs_${Date.now()}` }]);
}
handleCloseDialogs();
};

const handleOpenDeleteDialog = (id: string) => { setDeletingId(id); setOpenConfirmDelete(true); };
const handleCloseConfirmDelete = () => { setOpenConfirmDelete(false); setDeletingId(null);};
const handleConfirmDelete = () => {
if (deletingId) {
console.log('Deleting Chế độ CS ID:', deletingId);
setListData(prev => prev.filter(item => item.id !== deletingId));
handleCloseConfirmDelete();
}
};

const columns: GridColDef[] = [
{
field: 'LoaiCheDoID',
headerName: 'Loại Chế độ/Chính sách',
width: 220,
valueGetter: params => mapIdToStringCDCS(params.value, 'loaiCheDo')
},
{
field: 'NgayBatDauHuong',
headerName: 'Ngày bắt đầu',
width: 120,
valueFormatter: (params: GridValueFormatterParams<string | undefined>) => formatDateDatagridCDCS(params.value)
},
{
field: 'ChiTietNoiDung',
headerName: 'Chi tiết Nội dung',
width: 300,
renderCell: (params: GridRenderCellParams) => (
<Tooltip title={params.value || ''} placement="top-start">
<Typography noWrap variant="body2" sx={{overflow: 'hidden', textOverflow: 'ellipsis'}}>
{params.value || ''}
</Typography>
</Tooltip>
)
},
{
field: 'SoTienHuong',
headerName: 'Số tiền hưởng',
width: 150,
type: 'number',
valueFormatter: (params: GridValueFormatterParams<number | undefined>) => formatCurrency(params.value)
},
{
field: 'TrangThaiGiaiQuyet',
headerName: 'Trạng thái Giải quyết',
width: 180,
renderCell: (params: GridRenderCellParams) => {
const status = params.value as TrangThaiGiaiQuyetCheDoType;
const statusInfo = trangThaiGiaiQuyetCheDoObj[status] || { label: status, color: 'default' };
return <CustomChip label={statusInfo.label} color={statusInfo.color as any} skin="light" size="small" rounded />;
}
},
{
field: 'actions',
headerName: 'Hành động',
width: 180,
sortable: false,
filterable: false,
renderCell: (params: GridRenderCellParams) => (
<Box>
<Tooltip title="Xem Chi tiết">
<IconButton size="small" onClick={() => handleOpenDetailDialog(params.row as CheDoChinhSachQuanNhanEntryType)}>
<IconEye size={20} />
</IconButton>
</Tooltip>
<Tooltip title="Xem File đính kèm">
<span>
<IconButton
size="small"
href={params.row.FileDinhKemURL || '#'}
target="\_blank"
disabled={!params.row.FileDinhKemURL}
onClick={(e) => { if (!params.row.FileDinhKemURL) e.preventDefault(); else console.log('Viewing file:', params.row.FileDinhKemURL);}} >
<IconFileText size={20} />
</IconButton>
</span>
</Tooltip>
<Tooltip title="Sửa">
<IconButton size="small" onClick={() => handleOpenEditDialog(params.row as CheDoChinhSachQuanNhanEntryType)}>
<IconEdit size={20} />
</IconButton>
</Tooltip>
<Tooltip title="Xóa">
<IconButton size="small" onClick={() => handleOpenDeleteDialog(params.row.id as string)}>
<IconTrash size={20} />
</IconButton>
</Tooltip>
</Box>
)
}
// Các cột khác có thể xem trong chi tiết: NgayKetThucHuong, SoQuyetDinhLienQuan, CoQuanGiaiQuyet, NgayGiaiQuyet, GhiChu
];

return (
<Box>
<Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
<Typography variant="h6" sx={{ color: 'primary.main' }}>
Chế độ Chính sách
</Typography>
<Button
variant="contained"
startIcon={<IconPlus />}
onClick={handleOpenAddDialog} >
Thêm Chế độ/Chính sách
</Button>
</Box>

      <DataGrid
        autoHeight
        rows={listData}
        columns={columns}
        pageSizeOptions={[5, 10, 25]}
        paginationModel={paginationModel}
        onPaginationModelChange={setPaginationModel}
        getRowId={(row) => row.id}
        sx={{
            '& .MuiDataGrid-columnHeaders': { backgroundColor: 'customColors.tableHeaderBg' }
        }}
      />

      {/* Placeholders for Dialogs */}
      {openAddEditDialog && <Typography sx={{mt: 2, p:2, border: '1px dashed grey'}}>Dialog Thêm/Sửa Chế độ Chính sách (Placeholder - Data: {JSON.stringify(editingData)})</Typography>}
      {openDetailDialog && viewingData && <Typography sx={{mt: 2, p:2, border: '1px dashed blue'}}>Dialog Xem Chi tiết Chế độ Chính sách (Placeholder - Data: {JSON.stringify(viewingData)})</Typography>}
      {openConfirmDelete && <Typography sx={{mt: 2, p:2, border: '1px dashed red'}}>Dialog Xác nhận Xóa (Placeholder - ID: {deletingId})</Typography>}
    </Box>

);
};

export default TabCheDoChinhSach;
Bước 37: Tạo Component DialogThemSuaCheDoChinhSach.tsx (Sơ bộ)
Form này sẽ bao gồm các trường của CheDoChinhSachQuanNhanEntryType.

TypeScript

// src\views\quan-ly-quan-nhan\chi-tiet\components\tabs\DialogThemSuaCheDoChinhSach.tsx (Sơ bộ)
// 'use client';
// import React, { useState, useEffect } from 'react';
// import Dialog from '@mui/material/Dialog';
// import DialogTitle from '@mui/material/DialogTitle';
// // ... (các imports khác)
// import { CheDoChinhSachQuanNhanEntryType, TrangThaiGiaiQuyetCheDoType, trangThaiGiaiQuyetCheDoObj } from '../../mockDataChiTiet';
// import Grid from '@mui/material/Grid';
// import TextField from '@mui/material/TextField';
// import FormControl from '@mui/material/FormControl';
// import InputLabel from '@mui/material/InputLabel';
// import Select, { SelectChangeEvent } from '@mui/material/Select';
// import MenuItem from '@mui/material/MenuItem';
// // import CurrencyTextField from '@unicef/material-ui-currency-textfield' // Ví dụ cho input tiền tệ

// interface DialogCheDoChinhSachProps {
// open: boolean;
// onClose: () => void;
// onSubmit: (data: CheDoChinhSachQuanNhanEntryType) => void;
// initialData: CheDoChinhSachQuanNhanEntryType | null;
// }

// const DialogThemSuaCheDoChinhSach = ({ open, onClose, onSubmit, initialData }: DialogCheDoChinhSachProps) => {
// const [formData, setFormData] = useState<Partial<CheDoChinhSachQuanNhanEntryType>>(initialData || { TrangThaiGiaiQuyet: 'Chưa giải quyết'});

// useEffect(() => {
// setFormData(initialData || { TrangThaiGiaiQuyet: 'Chưa giải quyết'});
// }, [initialData, open]);

// const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | SelectChangeEvent<string>) => { /_ ... _/ };
// // const handleDateChange = (name: string, date: Date | null) => { /_ ... _/ };
// // const handleCurrencyChange = (event: React.ChangeEvent<HTMLInputElement>, value: number | string) => {
// // setFormData(prev => ({ ...prev, SoTienHuong: typeof value === 'number' ? value : parseFloat(value) || undefined }));
// // };

// const handleSubmit = () => {
// onSubmit(formData as CheDoChinhSachQuanNhanEntryType); // Cần validate
// };

// return (
// <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
// <DialogTitle>{initialData ? 'Sửa Chế độ/Chính sách' : 'Thêm Chế độ/Chính sách'}</DialogTitle>
// <DialogContent>
// <Grid container spacing={3} sx={{mt:1}}>
// <Grid item xs={12} sm={6}>
// <TextField name="LoaiCheDoID" label="ID Loại Chế độ/Chính sách" value={formData.LoaiCheDoID || ''} onChange={handleChange} fullWidth required />
// </Grid>
// {/_ <Grid item xs={12} sm={6}>
// <DatePicker label="Ngày bắt đầu hưởng" ... required/>
// </Grid> _/}
// <Grid item xs={12} sm={6}>
// <TextField name="NgayBatDauHuong" label="Ngày bắt đầu hưởng (YYYY-MM-DD)" type="date" InputLabelProps={{ shrink: true }} value={formData.NgayBatDauHuong?.substring(0,10) || ''} onChange={handleChange} fullWidth required />
// </Grid>
// <Grid item xs={12} sm={6}>
// <TextField name="NgayKetThucHuong" label="Ngày kết thúc hưởng (YYYY-MM-DD)" type="date" InputLabelProps={{ shrink: true }} value={formData.NgayKetThucHuong?.substring(0,10) || ''} onChange={handleChange} fullWidth />
// </Grid>
// <Grid item xs={12}>
// <TextField name="ChiTietNoiDung" label="Chi tiết Nội dung" value={formData.ChiTietNoiDung || ''} onChange={handleChange} fullWidth multiline rows={3} required />
// </Grid>
// {/_ <Grid item xs={12} sm={6}>
// <CurrencyTextField
// label="Số tiền hưởng"
// variant="outlined"
// value={formData.SoTienHuong || null}
// currencySymbol="VNĐ"
// outputFormat="string"
// decimalCharacter=","
// digitGroupSeparator="."
// onChange={handleCurrencyChange}
// fullWidth
// />
// </Grid> _/}
// <Grid item xs={12} sm={6}>
// <TextField name="SoTienHuong" label="Số tiền hưởng" type="number" value={formData.SoTienHuong || ''} onChange={handleChange} fullWidth />
// </Grid>
// <Grid item xs={12} sm={6}>
// <FormControl fullWidth>
// <InputLabel id="trang-thai-gq-cdcs-label">Trạng thái Giải quyết</InputLabel>
// <Select
// labelId="trang-thai-gq-cdcs-label"
// name="TrangThaiGiaiQuyet"
// value={formData.TrangThaiGiaiQuyet || ''}
// label="Trạng thái Giải quyết"
// onChange={handleChange}
// >
// {(Object.keys(trangThaiGiaiQuyetCheDoObj) as TrangThaiGiaiQuyetCheDoType[]).map((key) => (
// <MenuItem key={key} value={key}>{trangThaiGiaiQuyetCheDoObj[key].label}</MenuItem>
// ))}
// </Select>
// </FormControl>
// </Grid>
// {/_ ... Thêm các trường: SoQuyetDinhLienQuan, CoQuanGiaiQuyet, NgayGiaiQuyet, FileDinhKemURL, GhiChu ... _/}
// </Grid>
// </DialogContent>
// <DialogActions>
// <Button onClick={onClose}>Hủy</Button>
// <Button onClick={handleSubmit} variant="contained">Lưu</Button>
// </DialogActions>
// </Dialog>
// );
// };
// export default DialogThemSuaCheDoChinhSach;
Bước 38: Cập nhật KhuVucTabsChiTiet.tsx để sử dụng TabCheDoChinhSach
Chỉnh sửa file src\views\quan-ly-quan-nhan\chi-tiet\components\KhuVucTabsChiTiet.tsx:

TypeScript

// src\views\quan-ly-quan-nhan\chi-tiet\components\KhuVucTabsChiTiet.tsx
// ... (các imports khác)
import TabCheDoChinhSach from './tabs/TabCheDoChinhSach';
// ... (import các tab khác khi tạo xong)

// interface TabsProps { ... }

const KhuVucTabsChiTiet = ({ quanNhanData, activeTab, handleTabChange }: TabsProps) => {
const tabContentList: { [key: string]: React.ReactNode } = {
'thong-tin-chung': <TabThongTinChung data={quanNhanData.baseInfo} />,
'ly-lich-ca-nhan': <TabLyLichCaNhan data={quanNhanData.lyLichCaNhan} />,
'qua-trinh-cong-tac': <TabQuaTrinhCongTac initialData={quanNhanData.quaTrinhCongTac} idQuanNhan={quanNhanData.baseInfo.SoHieuQuanNhan} />,
'qua-trinh-dao-tao': <TabQuaTrinhDaoTao initialData={quanNhanData.quaTrinhDaoTao} idQuanNhan={quanNhanData.baseInfo.SoHieuQuanNhan} />,
'khen-thuong': <TabKhenThuong initialData={quanNhanData.khenThuong} idQuanNhan={quanNhanData.baseInfo.SoHieuQuanNhan} />,
'ky-luat': <TabKyLuat initialData={quanNhanData.kyLuat} idQuanNhan={quanNhanData.baseInfo.SoHieuQuanNhan} />,
'suc-khoe': <TabSucKhoe initialData={quanNhanData.sucKhoe} idQuanNhan={quanNhanData.baseInfo.SoHieuQuanNhan} />,
'quan-he-gia-dinh': <TabQuanHeGiaDinh initialData={quanNhanData.thanNhan} idQuanNhan={quanNhanData.baseInfo.SoHieuQuanNhan} />,
'che-do-chinh-sach': <TabCheDoChinhSach initialData={quanNhanData.cheDoChinhSach} idQuanNhan={quanNhanData.baseInfo.SoHieuQuanNhan} />,
'lich-su-thay-doi': <div>Nội dung Tab Lịch sử Thay đổi Hồ sơ</div>, // (Tùy chọn)
};

// ... (phần còn lại của component giữ nguyên)
return (
<TabContext value={activeTab}>
<Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
<TabList /_ ...props... _/ >
{/_ ...Tabs... _/}
</TabList>
</Box>
{Object.keys(tabContentList).map(tabValue => (
<TabPanel key={tabValue} value={tabValue} sx={{ p: 0 }}>
<CardContent>
{tabContentList[tabValue]}
</CardContent>
</TabPanel>
))}
</TabContext>
);
};

export default KhuVucTabsChiTiet;
Đã hoàn thành Tab 9: "Chế độ Chính sách". Tab này cho phép quản lý các chế độ, chính sách mà quân nhân được hưởng.
