{"version": 3, "sources": ["../../src/config/redis.config.ts"], "sourcesContent": ["export default () => {\n    return {\n        redis: {\n            host: process.env.REDIS_HOST,\n            port: parseInt(process.env.REDIS_PORT),\n            // db: parseInt(process.env.REDIS_DB),\n            password: process.env.REDIS_PASSWORD,\n            // keyPrefix: process.env.REDIS_PREFIX,\n            // maxRetriesPerRequest: process.env.REDIS_MAXRETRIESPERREQUEST,\n            // tls: {\n            //   host: process.env.REDIS_HOST\n            // },\n            connectTimeout: 10000,\n        },\n    };\n};\n"], "names": ["redis", "host", "process", "env", "REDIS_HOST", "port", "parseInt", "REDIS_PORT", "password", "REDIS_PASSWORD", "connectTimeout"], "mappings": "oGAAA,iDAAA,kBAAA,SAAe,KACX,MAAO,CACHA,MAAO,CACHC,KAAMC,QAAQC,GAAG,CAACC,UAAU,CAC5BC,KAAMC,SAASJ,QAAQC,GAAG,CAACI,UAAU,EAErCC,SAAUN,QAAQC,GAAG,CAACM,cAAc,CAMpCC,eAAgB,GACpB,CACJ,CACJ"}