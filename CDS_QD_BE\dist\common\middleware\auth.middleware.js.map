{"version": 3, "sources": ["../../../src/common/middleware/auth.middleware.ts"], "sourcesContent": ["import { Injectable, NestMiddleware, UnauthorizedException } from '@nestjs/common';\nimport { Request, Response } from 'express';\nimport { UtilService } from '~/shared/services';\nimport { TokenService } from '../../shared/services/token.service';\n\n@Injectable()\nexport class AuthMiddleware implements NestMiddleware {\n    constructor(private tokenService: TokenService, private utilService: UtilService) {}\n\n    async use(req: Request, res: Response, next) {\n        try {\n            if (!req.headers['authorization'] && !req.headers['passcode']) {\n                console.log('LOG:: AuthMiddleware - Missing authorization header');\n                throw new UnauthorizedException('Request Forbidden');\n            }\n\n            const authHeader = req.headers['authorization'];\n            let authToken = authHeader;\n\n            // Hỗ trợ cả \"Bearer token\" và \"token\" thuần túy\n            if (authHeader?.startsWith('Bearer ')) {\n                authToken = authHeader.substring(7); // Cắt bỏ \"Bearer \"\n                console.log('LOG:: AuthMiddleware - Bearer token detected, token length:', authToken?.length || 0);\n            }\n\n            const authData = await this.tokenService.verifyAuthToken({ authToken: authToken });\n\n            if (!authData?.id || !authData?.user?.id) {\n                console.log('LOG:: AuthMiddleware - Invalid token or user data not found');\n                throw new UnauthorizedException('Error: Invalid token data');\n            }\n\n            req.headers['_accountId'] = authData.id;\n            req.headers['_userId'] = authData.user.id.toString();\n            req['user'] = authData.user;\n            console.log('LOG:: AuthMiddleware - Token verified successfully, user ID:', authData.user.id);\n            next();\n        } catch (err) {\n            console.log('LOG:: AuthMiddleware Error:', err.message);\n            throw new UnauthorizedException('Error: Request Forbidden [Token Invalid]');\n        }\n    }\n}\n"], "names": ["AuthMiddleware", "use", "req", "res", "next", "headers", "console", "log", "UnauthorizedException", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "authToken", "startsWith", "substring", "length", "authData", "tokenService", "verifyAuthToken", "id", "user", "toString", "err", "message", "constructor", "utilService"], "mappings": "oGAMaA,wDAAAA,wCANqD,0CAEtC,qDACC,olBAGtB,IAAA,AAAMA,eAAN,MAAMA,eAGT,MAAMC,IAAIC,GAAY,CAAEC,GAAa,CAAEC,IAAI,CAAE,CACzC,GAAI,CACA,GAAI,CAACF,IAAIG,OAAO,CAAC,gBAAgB,EAAI,CAACH,IAAIG,OAAO,CAAC,WAAW,CAAE,CAC3DC,QAAQC,GAAG,CAAC,sDACZ,OAAM,IAAIC,6BAAqB,CAAC,oBACpC,CAEA,MAAMC,WAAaP,IAAIG,OAAO,CAAC,gBAAgB,CAC/C,IAAIK,UAAYD,WAGhB,GAAIA,YAAYE,WAAW,WAAY,CACnCD,UAAYD,WAAWG,SAAS,CAAC,GACjCN,QAAQC,GAAG,CAAC,8DAA+DG,WAAWG,QAAU,EACpG,CAEA,MAAMC,SAAW,MAAM,IAAI,CAACC,YAAY,CAACC,eAAe,CAAC,CAAEN,UAAWA,SAAU,GAEhF,GAAI,CAACI,UAAUG,IAAM,CAACH,UAAUI,MAAMD,GAAI,CACtCX,QAAQC,GAAG,CAAC,8DACZ,OAAM,IAAIC,6BAAqB,CAAC,4BACpC,CAEAN,IAAIG,OAAO,CAAC,aAAa,CAAGS,SAASG,EAAE,AACvCf,CAAAA,IAAIG,OAAO,CAAC,UAAU,CAAGS,SAASI,IAAI,CAACD,EAAE,CAACE,QAAQ,EAClDjB,CAAAA,GAAG,CAAC,OAAO,CAAGY,SAASI,IAAI,CAC3BZ,QAAQC,GAAG,CAAC,+DAAgEO,SAASI,IAAI,CAACD,EAAE,EAC5Fb,MACJ,CAAE,MAAOgB,IAAK,CACVd,QAAQC,GAAG,CAAC,8BAA+Ba,IAAIC,OAAO,CACtD,OAAM,IAAIb,6BAAqB,CAAC,2CACpC,CACJ,CAlCAc,YAAY,AAAQP,YAA0B,CAAE,AAAQQ,WAAwB,CAAE,MAA9DR,aAAAA,kBAAoCQ,YAAAA,WAA2B,CAmCvF"}