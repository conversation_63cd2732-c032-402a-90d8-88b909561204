module.exports = {
    parser: '@typescript-eslint/parser',
    parserOptions: {
        project: 'tsconfig.json',
        sourceType: 'module',
    },
    plugins: ['@typescript-eslint/eslint-plugin'],
    extends: ['plugin:@typescript-eslint/recommended', 'plugin:prettier/recommended'],
    root: true,
    env: {
        node: true,
        jest: true,
    },
    ignorePatterns: ['.eslintrc.js'],
    rules: {
        '@typescript-eslint/interface-name-prefix': 'off',
        '@typescript-eslint/explicit-function-return-type': 'off',
        '@typescript-eslint/explicit-module-boundary-types': 'off',
        '@typescript-eslint/no-explicit-any': 'off',
        '@typescript-eslint/no-unused-vars': 0,
        '@typescript-eslint/no-empty-function': 'off',
        'prettier/prettier': [
            'error',
            {
                trailingComma: 'all',
                semi: true,
                tabWidth: 4,
                singleQuote: true,
                bracketSpacing: true,
                jsxBracketSameLine: true,
                endOfLine: 'auto',
                printWidth: 150,
                code: 150,
            },
        ],
        strict: 1,
        noUnusedLocals: 0,
        noUnusedParameters: 0,
    },
};
