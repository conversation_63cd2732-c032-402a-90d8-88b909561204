/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { HoatDongTuyenTruyenVanHoaEntity } from '~/database/typeorm/entities/hoatDongTuyenTruyenVanHoa.entity';

@Injectable()
export class HoatDongTuyenTruyenVanHoaRepository extends Repository<HoatDongTuyenTruyenVanHoaEntity> {
    constructor(private dataSource: DataSource) {
        super(HoatDongTuyenTruyenVanHoaEntity, dataSource.createEntityManager());
    }
}
