/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { QuyetDinhKhenThuongEntity } from '~/database/typeorm/entities/quyetDinhKhenThuong.entity';

@Injectable()
export class QuyetDinhKhenThuongRepository extends Repository<QuyetDinhKhenThuongEntity> {
    constructor(private dataSource: DataSource) {
        super(QuyetDinhKhenThuongEntity, dataSource.createEntityManager());
    }
}
