<PERSON><PERSON><PERSON> vậ<PERSON>, đ<PERSON> là một câu hỏi quan trọng về UX cần làm rõ!

Đối với **Buổi Sinh hoạt Đảng**, do khối lượng thông tin cần quản lý cho một buổi sinh hoạt (thông tin chung, danh sách đảng viên tham gia, nội dung diễn biến, kết qu<PERSON> biểu quyết, file biên bản, tài li<PERSON>u...) là tương đối lớn và phức tạp, việc **tách thành các trang riêng cho "Xem chi tiết" và "Tạo mới/Sửa" sẽ mang lại trải nghiệm tốt hơn** so với việc sử dụng Dialog.

Lý do tương tự như khi chúng ta thảo luận về <PERSON><PERSON> sơ Quân nhân/Đảng viên:

- **Không gian hiển thị:** Trang riêng cung cấp đủ không gian để trình bày rõ ràng, chi tiết.
- **<PERSON><PERSON> chức thông tin:** Dễ dàng sử dụng các tab, section để nhóm thông tin.
- **URL tường minh:** Tiện cho việc điều hướng, chia sẻ.
- **Tráchnhiệm người dùng tốt hơn:** Cho các tác vụ phức tạp như chuẩn bị, ghi biên bản một buổi sinh hoạt.

Do đó, tôi sẽ mô tả chức năng theo hướng có các trang riêng biệt.

---

**Mục Menu: Sinh hoạt Đảng (Tập thể)**

**II. Trang Buổi Sinh hoạt (`/${locale}/cong-tac-dang/sinh-hoat-dang/buoi-hop`)**

- **Tên Trang (Tiêu đề hiển thị):** `Quản lý Buổi Sinh hoạt Đảng`
- **Mục đích chính của Trang:** Cho phép người dùng (thường là Bí thư Chi bộ, Cấp ủy viên được phân công) tạo, quản lý, theo dõi và cập nhật thông tin chi tiết về các buổi sinh hoạt của tổ chức Đảng.

---

#### A. Chức năng trang `DanhSachBuoiSinhHoatDangPage`

1.  **Chức năng "Hiển thị Danh sách Buổi Sinh hoạt Đảng"**

    - **Tổng quan:** Trang hiển thị một bảng liệt kê các buổi sinh hoạt Đảng đã, đang và sẽ diễn ra.
    - **Chi tiết:**
      - Khi tải trang, hệ thống truy vấn và hiển thị danh sách các buổi sinh hoạt Đảng thuộc phạm vi quản lý của người dùng hoặc của Tổ chức Đảng được chọn.
      - Thông tin được trình bày dưới dạng bảng, mỗi hàng là một buổi sinh hoạt.
      - Hỗ trợ phân trang.

2.  **Chức năng "Tìm kiếm và Lọc Buổi Sinh hoạt"**

    - **Tổng quan:** Cung cấp công cụ để người dùng nhanh chóng tìm thấy buổi sinh hoạt mong muốn.
    - **Chi tiết:**
      - **Tìm kiếm theo Tên/Nội dung buổi sinh hoạt:** Ô nhập liệu.
      - **Lọc theo Tổ chức Đảng:** Trường lựa chọn (có thể dạng cây).
      - **Lọc theo Thời gian diễn ra:** Chọn khoảng ngày (Từ ngày - Đến ngày).
      - **Lọc theo Loại Sinh hoạt:** Trường lựa chọn (ví dụ: Thường kỳ, Chuyên đề, Bất thường) - dữ liệu từ danh mục `DmLoaiSinhHoatDang`.
      - **Lọc theo Trạng thái Buổi họp:** Trường lựa chọn (ví dụ: Đã lên kế hoạch, Đang diễn ra, Đã kết thúc/Chờ biên bản, Đã hoàn thành biên bản, Bị hoãn) - dữ liệu từ danh mục `DmTrangThaiBuoiHopDang`.
      - **Lọc theo Kế hoạch Sinh hoạt liên kết (nếu có):** Trường lựa chọn các kế hoạch đã được duyệt.
      - Nút "Áp dụng bộ lọc" và "Đặt lại".

3.  **Chức năng "Tạo Buổi Sinh hoạt mới"**

    - **Tổng quan:** Cho phép người dùng lên kế hoạch và tạo một buổi sinh hoạt Đảng mới.
    - **Chi tiết:**
      - Nhấn nút "Tạo Buổi sinh hoạt mới" sẽ điều hướng đến trang tạo mới (`/${locale}/cong-tac-dang/sinh-hoat-dang/them-moi-buoi-hop`).
      - **(Tùy chọn khi tạo):** Có thể cho phép chọn một "Kế hoạch Sinh hoạt Đảng" đã được duyệt để kế thừa một số thông tin (như Tổ chức Đảng, một phần nội dung dự kiến).

4.  **Chức năng "Xem chi tiết Buổi Sinh hoạt" (Từ bảng danh sách)**

    - **Tổng quan:** Cho phép xem đầy đủ thông tin của một buổi sinh hoạt đã chọn.
    - **Chi tiết:** Khi người dùng chọn hành động "Xem chi tiết" từ bảng danh sách, hệ thống điều hướng đến trang xem chi tiết buổi sinh hoạt (`/${locale}/cong-tac-dang/sinh-hoat-dang/chi-tiet-buoi-hop/{idBuoiHop}`).

5.  **Chức năng "Sửa Buổi Sinh hoạt" (Từ bảng danh sách)**

    - **Tổng quan:** Cho phép chỉnh sửa thông tin của một buổi sinh hoạt (nếu buổi sinh hoạt chưa diễn ra hoặc người dùng có quyền).
    - **Chi tiết:** Điều hướng đến trang sửa buổi sinh hoạt (`/${locale}/cong-tac-dang/sinh-hoat-dang/chinh-sua-buoi-hop/{idBuoiHop}`).

6.  **Chức năng "Thay đổi Trạng thái Buổi Sinh hoạt" (Từ bảng danh sách hoặc trang chi tiết)**

    - **Tổng quan:** Cho phép cập nhật trạng thái của buổi sinh hoạt (ví dụ: từ "Đã lên kế hoạch" sang "Đang diễn ra", hoặc "Bị hoãn", "Đã kết thúc").
    - **Chi tiết:** Có thể là một hành động nhanh trên bảng danh sách (ví dụ: nút "Bắt đầu họp") hoặc một chức năng trong trang chi tiết. Việc thay đổi trạng thái sẽ ảnh hưởng đến các hành động tiếp theo có thể thực hiện (ví dụ: không thể điểm danh nếu chưa "Bắt đầu họp").

7.  **Chức năng "Xóa Buổi Sinh hoạt" (Từ bảng danh sách)**
    - **Tổng quan:** Cho phép xóa một buổi sinh hoạt (thường chỉ khi buổi đó chưa diễn ra và không có dữ liệu phát sinh quan trọng).
    - **Chi tiết:** Cần có hộp thoại xác nhận.

---

#### B. Trang Tạo mới / Chỉnh sửa Buổi Sinh hoạt Đảng (`/.../them-moi-buoi-hop` hoặc `/.../chinh-sua-buoi-hop/{idBuoiHop}`)

- **Tên Trang:** `Tạo mới Buổi Sinh hoạt Đảng` / `Chỉnh sửa Buổi Sinh hoạt Đảng: [Tên/Nội dung buổi họp]`
- **Bố cục:** Một form lớn, có thể chia thành các section/tab để quản lý thông tin.
- **Các trường dữ liệu và chức năng:**

  1.  **Thông tin chung Buổi Sinh hoạt:**

      - `ToChucDangID`: Lựa chọn Tổ chức Đảng tổ chức sinh hoạt (bắt buộc).
      - `KeHoachSinhHoatID`: (Tùy chọn) Lựa chọn Kế hoạch Sinh hoạt Đảng liên kết (chỉ hiển thị các kế hoạch đã duyệt của Tổ chức Đảng đã chọn).
      - `LoaiSinhHoatID`: Lựa chọn Loại sinh hoạt (Thường kỳ, Chuyên đề...) (bắt buộc).
      - `ThoiGianBatDau`: Chọn Ngày và Giờ bắt đầu (bắt buộc).
      - `ThoiGianKetThuc`: (Tùy chọn) Chọn Ngày và Giờ kết thúc dự kiến.
      - `DiaDiem`: Nhập địa điểm tổ chức.
      - `ChuTriID`: Lựa chọn Người chủ trì (là Đảng viên trong Tổ chức Đảng đó).
      - `ThuKyID`: Lựa chọn Thư ký cuộc họp (là Đảng viên trong Tổ chức Đảng đó).
      - `NoiDungChinhDuKien`: Nhập nội dung chính dự kiến của buổi sinh hoạt (có thể dùng trình soạn thảo văn bản phong phú - RichTextEditor).
      - `FileTaiLieuChuanBiURL`: Cho phép tải lên các file tài liệu chuẩn bị cho buổi họp (ví dụ: dự thảo nghị quyết, báo cáo tham khảo). Có thể cho upload nhiều file.
      - `TrangThaiBuoiHopID`: (Thường hệ thống tự đặt là "Đã lên kế hoạch" khi tạo mới).

  2.  **Nút "Lưu thông tin Buổi Sinh hoạt" và "Hủy".**

---

#### C. Trang Xem chi tiết Buổi Sinh hoạt Đảng (`/.../chi-tiet-buoi-hop/{idBuoiHop}`)

- **Tên Trang:** `Chi tiết Buổi Sinh hoạt Đảng: [Tên/Nội dung buổi họp]`
- **Bố cục:** Hiển thị thông tin tổng quan, và các tab/section chi tiết.
- **Khu vực Header Thông tin Tổng quan:**

  - Tên/Nội dung buổi họp, Tổ chức Đảng, Thời gian, Địa điểm, Chủ trì, Trạng thái.
  - **Nút "Chỉnh sửa Buổi Sinh hoạt"** (nếu được phép).
  - **Nút hành động theo trạng thái:**
    - Nếu trạng thái là "Đã lên kế hoạch": Nút "Bắt đầu Họp".
    - Nếu trạng thái là "Đang diễn ra": Nút "Kết thúc Họp", Nút "Ghi Biên bản & Điểm danh".
    - Nếu trạng thái là "Đã kết thúc/Chờ biên bản": Nút "Hoàn thành Biên bản".
    - Nút "Hoãn họp" (cần nhập lý do).

- **Các Tab/Section chi tiết:**

  1.  **Tab "Thông tin Chung":** Hiển thị các thông tin đã nhập khi tạo/sửa buổi sinh hoạt (read-only).
  2.  **Tab "Danh sách Tham gia & Điểm danh" (từ bảng `TheoDoiSinhHoatCaNhan`):**

      - **Mục đích:** Quản lý việc tham gia của các đảng viên thuộc Tổ chức Đảng.
      - Hiển thị danh sách đảng viên của `ToChucDangID` đã chọn.
      - Với mỗi đảng viên:
        - Checkbox/Lựa chọn "Có mặt".
        - Nếu vắng: Lựa chọn "Lý do vắng mặt" (từ `DmLyDoVangMat`) hoặc ô nhập lý do khác.
      - **Nút "Lưu Điểm danh"** (chỉ kích hoạt khi buổi họp "Đang diễn ra").
      - Hiển thị thống kê nhanh: Số lượng có mặt, vắng có phép, vắng không phép.

  3.  **Tab "Diễn biến & Biên bản" (từ bảng `BienBanSinhHoatDang`):**

      - **Mục đích:** Ghi nhận nội dung diễn biến, thảo luận, ý kiến và kết quả của buổi sinh hoạt.
      - **Form nhập/hiển thị (cho phép sửa nếu đang trong quá trình ghi biên bản):**
        - `BuoiSinhHoatID`: (ẩn, tự động liên kết).
        - `SoDangVienCoMat`: (có thể tự động tính từ tab điểm danh).
        - `SoDangVienVangMatCoPhep`: (tự động tính).
        - `SoDangVienVangMatKhongPhep`: (tự động tính).
        - `DanhSachVangMatChiTiet`: Trường text ghi rõ tên và lý do vắng (nếu cần chi tiết hơn điểm danh).
        - `NoiDungDienBienCuocHop`: Trường nhập liệu lớn (RichTextEditor) để ghi toàn bộ nội dung diễn biến, thảo luận, ý kiến phát biểu.
        - `NoiDungTuPheBinhVaPheBinh`: Trường nhập liệu lớn (RichTextEditor) cho nội dung tự phê bình và phê bình.
        - `KetQuaBieuQuyet`: Trường nhập liệu lớn (RichTextEditor) ghi kết quả biểu quyết các vấn đề (nếu có).
        - `FileBienBanChinhThucURL`: Cho phép upload file biên bản đã ký (Word/PDF đã được mã hóa nếu cần).
        - `TrangThaiBienBanID`: Trạng thái của biên bản (Dự thảo, Chờ duyệt, Đã duyệt/Hoàn thành) - từ `DmTrangThaiBienBan`.
        - `NguoiDuyetBienBanID`: Người duyệt biên bản (Bí thư).
        - `NgayHoanThanhBienBan`: Ngày hoàn thành/phê duyệt biên bản.
      - **Nút "Lưu Dự thảo Biên bản", "Gửi duyệt Biên bản", "Duyệt Biên bản".**

  4.  **Tab "Nghị quyết" (nếu buổi sinh hoạt này ra Nghị quyết - từ bảng `NghiQuyetDang`):**

      - Hiển thị danh sách các nghị quyết được ban hành từ buổi sinh hoạt này (nếu có).
      - Nút "Ban hành Nghị quyết mới từ Buổi sinh hoạt này" -> điều hướng/mở form tạo Nghị quyết với `BuoiSinhHoatID` đã liên kết.

  5.  **Tab "Tài liệu Chuẩn bị":**
      - Hiển thị danh sách các file đã upload ở bước tạo/sửa buổi sinh hoạt (`FileTaiLieuChuanBiURL`).
      - Cho phép Xem/Tải file.

---

#### D. Bảng dữ liệu sử dụng trên các trang Buổi Sinh hoạt

- **Bảng `BuoiSinhHoatDang` (PartyMeetings)**

  - `ID: number` (BIGINT, PK, AUTO_INCREMENT)
  - `ToChucDangID: number` (BIGINT, NOT NULL, FK REFERENCES ToChucDang(ID))
  - `KeHoachSinhHoatID: number` (BIGINT, FK NULL REFERENCES KeHoachSinhHoatDang(ID))
  - `LoaiSinhHoatID: number` (INT, NOT NULL, FK REFERENCES DmLoaiSinhHoatDang(ID))
  - `ThoiGianBatDau: datetime` (TIMESTAMP NOT NULL)
  - `ThoiGianKetThuc: datetime` (TIMESTAMP NULL)
  - `DiaDiem: string` (NVARCHAR(255))
  - `ChuTriID: string` (VARCHAR(20), FK REFERENCES DangVien(MaDangVien)) - Người chủ trì
  - `ThuKyID: string` (VARCHAR(20), FK REFERENCES DangVien(MaDangVien)) - Thư ký cuộc họp
  - `NoiDungChinhDuKien: string` (NTEXT)
  - `FileTaiLieuChuanBiURL: string` (VARCHAR(255))
  - `TrangThaiBuoiHopID: number` (INT, FK REFERENCES DmTrangThaiBuoiHopDang(ID))

- **Bảng `BienBanSinhHoatDang` (PartyMeetingMinutes)** (Liên kết 1-1 với `BuoiSinhHoatDang`)

  - `BuoiSinhHoatID: number` (BIGINT, PK, FK REFERENCES BuoiSinhHoatDang(ID))
  - `SoDangVienCoMat: number` (INT)
  - `SoDangVienVangMatCoPhep: number` (INT)
  - `SoDangVienVangMatKhongPhep: number` (INT)
  - `DanhSachVangMatChiTiet: string` (NTEXT)
  - `NoiDungDienBienCuocHop: string` (NTEXT)
  - `NoiDungTuPheBinhVaPheBinh: string` (NTEXT)
  - `KetQuaBieuQuyet: string` (NTEXT)
  - `FileBienBanChinhThucURL: string` (VARCHAR(255))
  - `TrangThaiBienBanID: number` (INT, FK REFERENCES DmTrangThaiBienBan(ID))
  - `NguoiDuyetBienBanID: string` (VARCHAR(20), FK NULL REFERENCES DangVien(MaDangVien))
  - `NgayHoanThanhBienBan: date` (DATE)

- **Bảng `TheoDoiSinhHoatCaNhan` (IndividualPartyActivityTracking)** (Đã liệt kê ở phần chi tiết Hồ sơ Đảng viên, Tab 3)

  - (Sử dụng để hiển thị và cập nhật danh sách tham gia, lý do vắng)

- **Bảng `DangVien` (PartyMembers)** (Để chọn Chủ trì, Thư ký và hiển thị danh sách đảng viên điểm danh)

  - `MaDangVien: string` (PK)
  - `HoVaTen: string`
  - `ToChucDangSinhHoatID: number` (Để lọc đảng viên theo Tổ chức Đảng của buổi họp)

- **Bảng `KeHoachSinhHoatDang` (PartyActivityPlans)** (Để liên kết khi tạo buổi họp từ kế hoạch)

  - `ID: number` (PK)
  - `TenKeHoach: string`

- **Danh mục `DmLoaiSinhHoatDang`**

  - `ID: number` (PK)
  - `TenLoaiSinhHoat: string` (Thường kỳ, Chuyên đề...)

- **Danh mục `DmTrangThaiBuoiHopDang`**

  - `ID: number` (PK)
  - `TenTrangThai: string` (Đã lên kế hoạch, Đang diễn ra, Đã kết thúc/Chờ biên bản, Đã hoàn thành biên bản, Bị hoãn)

- **Danh mục `DmTrangThaiBienBan`**

  - `ID: number` (PK)
  - `TenTrangThai: string` (Dự thảo, Chờ duyệt, Đã duyệt/Hoàn thành)

- **Danh mục `DmLyDoVangMat`** (Đã liệt kê)

---

#### E. Liên kết với page khác

1.  **Trang Kế hoạch Sinh hoạt Đảng:** Có thể tạo Buổi sinh hoạt từ một Kế hoạch.
2.  **Trang Danh sách Nghị quyết Đảng:** Nghị quyết có thể được tạo từ một Buổi sinh hoạt.
3.  **Trang Chi tiết Hồ sơ Đảng viên (Tab "Theo dõi Sinh hoạt"):** Hiển thị lịch sử tham gia các buổi sinh hoạt của đảng viên.

---

Phần này đã mô tả chi tiết cho các trang liên quan đến "Buổi Sinh hoạt Đảng". Tiếp theo, chúng ta sẽ đến với "Nghị quyết Đảng".
