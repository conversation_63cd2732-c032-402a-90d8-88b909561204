{"version": 3, "sources": ["../../../src/common/filters/typeorm.filter.ts"], "sourcesContent": ["import { ArgumentsHost, Catch, ExceptionFilter, HttpException, HttpStatus, Logger } from '@nestjs/common';\nimport { TypeORMError } from 'typeorm';\n\n@Catch(TypeORMError)\nexport class TypeOrmFilter implements ExceptionFilter {\n    catch(exception: TypeORMError, host: ArgumentsHost) {\n        const ctx = host.switchToHttp();\n        const response = ctx.getResponse();\n        const request = ctx.getRequest();\n        const message: string = exception.message;\n        const code: number = (exception as any).code;\n        const status = exception instanceof HttpException ? exception.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR;\n\n        const error = this.handleError(exception);\n        Logger.error(`[${status}] {${request.url}, ${response?.req?.route?.path}, ${request.method}}: ${exception?.message}`, 'ExceptionFilter');\n        response.status(error.code).json({\n            result: false,\n            message: error.message,\n            data: { code: code, message: message },\n            statusCode: error.code,\n            // errors: [{ code: code, message: message }],\n            // errorCode: 300,\n        });\n    }\n\n    private handleError(error) {\n        const field = this.parseField(error?.detail?.match(/Key \\((\\w+)\\)=/), error.detail);\n        /**\n         * Note:\n         * this is just error code of postgresql\n         * not implement mysql yet\n         * */\n        switch (error.code) {\n            case '23505':\n                return { code: 409, message: `Trùng ${field}` };\n            case '23503':\n                return { code: 409, message: `Không tồn tại (${error.detail})` };\n            case '22P02':\n                return { code: 400, message: `Sai kiểu dữ liệu` };\n            case '22001':\n                return { code: 400, message: `Dữ liệu quá dài` };\n            case '23502':\n                return { code: 400, message: `Thiếu trường bắt buộc` };\n            case '42703':\n                return { code: 500, message: `Trường không tồn tại` };\n            case 'ER_DATA_TOO_LONG':\n                return { code: 400, message: `Dữ liệu quá dài` };\n            case 'ER_DUP_ENTRY':\n                return { code: 409, message: `Trùng dữ liệu` };\n            case 'ER_NO_REFERENCED_ROW_2':\n                return { code: 409, message: `Không tồn tại các dữ liệu liên quan` };\n            case 'ER_BAD_NULL_ERROR':\n                return { code: 400, message: `Thiếu trường bắt buộc` };\n            case 'ER_BAD_FIELD_ERROR':\n                return { code: 500, message: `Trường không tồn tại` };\n            case 'ER_NO_DEFAULT_FOR_FIELD':\n                return { code: 400, message: `Thiếu trường bắt buộc` };\n            default:\n                return { code: 500, message: `Lỗi hệ thống` };\n        }\n    }\n\n    private parseField(field: string, detail: string) {\n        if (!field) return `dữ liệu (${detail?.replace('Key ', '')?.replace(' already exists.', '')})`;\n        switch (field[1]) {\n            case 'registration_no':\n                return 'Số đăng bộ';\n            case 'citizen_id':\n                return 'Số CCCD/CMND';\n            default:\n                return field[1] || 'dữ liệu';\n        }\n    }\n}\n"], "names": ["TypeOrmFilter", "catch", "exception", "host", "ctx", "switchToHttp", "response", "getResponse", "request", "getRequest", "message", "code", "status", "HttpException", "getStatus", "HttpStatus", "INTERNAL_SERVER_ERROR", "error", "handleError", "<PERSON><PERSON>", "url", "req", "route", "path", "method", "json", "result", "data", "statusCode", "field", "parseField", "detail", "match", "replace"], "mappings": "oGAIaA,uDAAAA,uCAJ4E,yCAC5D,6bAGtB,IAAA,AAAMA,cAAN,MAAMA,cACTC,MAAMC,SAAuB,CAAEC,IAAmB,CAAE,CAChD,MAAMC,IAAMD,KAAKE,YAAY,GAC7B,MAAMC,SAAWF,IAAIG,WAAW,GAChC,MAAMC,QAAUJ,IAAIK,UAAU,GAC9B,MAAMC,QAAkBR,UAAUQ,OAAO,CACzC,MAAMC,KAAe,AAACT,UAAkBS,IAAI,CAC5C,MAAMC,OAASV,qBAAqBW,qBAAa,CAAGX,UAAUY,SAAS,GAAKC,kBAAU,CAACC,qBAAqB,CAE5G,MAAMC,MAAQ,IAAI,CAACC,WAAW,CAAChB,WAC/BiB,cAAM,CAACF,KAAK,CAAC,CAAC,CAAC,EAAEL,OAAO,GAAG,EAAEJ,QAAQY,GAAG,CAAC,EAAE,EAAEd,UAAUe,KAAKC,OAAOC,KAAK,EAAE,EAAEf,QAAQgB,MAAM,CAAC,GAAG,EAAEtB,WAAWQ,QAAQ,CAAC,CAAE,mBACtHJ,SAASM,MAAM,CAACK,MAAMN,IAAI,EAAEc,IAAI,CAAC,CAC7BC,OAAQ,MACRhB,QAASO,MAAMP,OAAO,CACtBiB,KAAM,CAAEhB,KAAMA,KAAMD,QAASA,OAAQ,EACrCkB,WAAYX,MAAMN,IAAI,AAG1B,EACJ,CAEA,AAAQO,YAAYD,KAAK,CAAE,CACvB,MAAMY,MAAQ,IAAI,CAACC,UAAU,CAACb,OAAOc,QAAQC,MAAM,kBAAmBf,MAAMc,MAAM,EAMlF,OAAQd,MAAMN,IAAI,EACd,IAAK,QACD,MAAO,CAAEA,KAAM,IAAKD,QAAS,CAAC,SAAM,EAAEmB,MAAM,CAAC,AAAC,CAClD,KAAK,QACD,MAAO,CAAElB,KAAM,IAAKD,QAAS,CAAC,kBAAe,EAAEO,MAAMc,MAAM,CAAC,CAAC,CAAC,AAAC,CACnE,KAAK,QACD,MAAO,CAAEpB,KAAM,IAAKD,QAAS,CAAC,gBAAgB,CAAC,AAAC,CACpD,KAAK,QACD,MAAO,CAAEC,KAAM,IAAKD,QAAS,CAAC,qBAAe,CAAC,AAAC,CACnD,KAAK,QACD,MAAO,CAAEC,KAAM,IAAKD,QAAS,CAAC,qBAAqB,CAAC,AAAC,CACzD,KAAK,QACD,MAAO,CAAEC,KAAM,IAAKD,QAAS,CAAC,uBAAoB,CAAC,AAAC,CACxD,KAAK,mBACD,MAAO,CAAEC,KAAM,IAAKD,QAAS,CAAC,qBAAe,CAAC,AAAC,CACnD,KAAK,eACD,MAAO,CAAEC,KAAM,IAAKD,QAAS,CAAC,gBAAa,CAAC,AAAC,CACjD,KAAK,yBACD,MAAO,CAAEC,KAAM,IAAKD,QAAS,CAAC,4CAAmC,CAAC,AAAC,CACvE,KAAK,oBACD,MAAO,CAAEC,KAAM,IAAKD,QAAS,CAAC,qBAAqB,CAAC,AAAC,CACzD,KAAK,qBACD,MAAO,CAAEC,KAAM,IAAKD,QAAS,CAAC,uBAAoB,CAAC,AAAC,CACxD,KAAK,0BACD,MAAO,CAAEC,KAAM,IAAKD,QAAS,CAAC,qBAAqB,CAAC,AAAC,CACzD,SACI,MAAO,CAAEC,KAAM,IAAKD,QAAS,CAAC,YAAY,CAAC,AAAC,CACpD,CACJ,CAEA,AAAQoB,WAAWD,KAAa,CAAEE,MAAc,CAAE,CAC9C,GAAI,CAACF,MAAO,MAAO,CAAC,SAAS,EAAEE,QAAQE,QAAQ,OAAQ,KAAKA,QAAQ,mBAAoB,IAAI,CAAC,CAAC,CAC9F,OAAQJ,KAAK,CAAC,EAAE,EACZ,IAAK,kBACD,MAAO,YACX,KAAK,aACD,MAAO,cACX,SACI,OAAOA,KAAK,CAAC,EAAE,EAAI,SAC3B,CACJ,CACJ"}