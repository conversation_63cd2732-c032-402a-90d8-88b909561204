Tên Page (Tiêu đề hiển thị): Chỉnh sửa <PERSON><PERSON> sơ Quân nhân: [Họ tên Quân nhân]
Mục đích: Cho phép cập nhật thông tin chi tiết của một quân nhân.
Layout: T<PERSON><PERSON><PERSON> tự trang "Thêm mới Hồ sơ Quân nhân" nhưng các trường đã được điền sẵn dữ liệu hiện tại của quân nhân đó. Các Tab và Section cũng tương tự như trang "Xem chi tiết" nhưng ở chế độ form nhập liệu.
Các Thành phần Gia<PERSON> (Components) và Vị trí:
Breadcrumbs: Trang chủ / Quản lý Quân nhân / Danh sách Quân nhân / Chi tiết: [Họ tên] / Chỉnh sửa
Tiêu đề Trang: "Chỉnh sửa <PERSON>ồ sơ: [<PERSON>ọ tên Quân nhân]"
Form nhập liệu đa Tab/Section:
Mỗi tab/section sẽ chứa các <PERSON>, DatePicker, Select, Autocomplete, FileUploader, Textarea tương ứng với các trường dữ liệu.
Các danh sách (Quá trình công tác, Đào tạo, Khen thưởng, Kỷ luật, Gia đình, Sức khỏe, Chế độ) sẽ cho phép Thêm/Sửa/Xóa từng mục (có thể bằng cách mở Dialog con hoặc inline editing nếu đơn giản).
Validation dữ liệu.
Khu vực Hành động của Form:
Nút "Lưu thay đổi": Button (variant="contained", color="primary").
Nút "Hủy": Button (variant="outlined"). Điều hướng về trang Chi tiết.
