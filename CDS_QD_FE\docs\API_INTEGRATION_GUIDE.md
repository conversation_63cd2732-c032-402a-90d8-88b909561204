# Hướng dẫn tích hợp API với React Query

## 1. Tổng quan
- React Query giúp quản lý server state hiệu quả
- Hỗ trợ caching, background updates, và stale-while-revalidate
- Tích hợp tốt với TypeScript và Next.js

## 2. Cài đặt

```bash
pnpm add @tanstack/react-query
```

## 3. Cấu hình Provider

```tsx
// app/providers.tsx
'use client';

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

export function QueryProvider({ children }: { children: React.ReactNode }) {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        staleTime: 5 * 60 * 1000, // 5 phút
        retry: 1,
      },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
}
```

## 4. Service mẫu

```typescript
// lib/services/api.service.ts
import { ENDPOINTS } from '@/lib/api/endpoints';
import { httpClient } from '@/lib/api/http-client';

export class ApiService {
  // GET
  static async getUsers(params?: any) {
    return httpClient.get(ENDPOINTS.USERS, { params });
  }

  // GET by ID
  static async getUserById(id: string) {
    return httpClient.get(`${ENDPOINTS.USERS}/${id}`);
  }

  // POST
  static async createUser(data: any) {
    return httpClient.post(ENDPOINTS.USERS, data);
  }

  // PUT
  static async updateUser(id: string, data: any) {
    return httpClient.put(`${ENDPOINTS.USERS}/${id}`, data);
  }

  // DELETE
  static async deleteUser(id: string) {
    return httpClient.delete(`${ENDPOINTS.USERS}/${id}`);
  }
}
```

## 5. Custom Hooks

```typescript
// hooks/use-users.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { ApiService } from '@/lib/services/api.service';

export function useUsers(params?: any) {
  return useQuery({
    queryKey: ['users', params],
    queryFn: () => ApiService.getUsers(params),
  });
}

export function useUser(id: string) {
  return useQuery({
    queryKey: ['user', id],
    queryFn: () => ApiService.getUserById(id),
    enabled: !!id,
  });
}

export function useCreateUser() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: any) => ApiService.createUser(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
    },
  });
}
```

## 6. Sử dụng trong Component

```tsx
// components/users/user-list.tsx
'use client';

import { useUsers } from '@/hooks/use-users';
import { DataGrid } from '@mui/x-data-grid';

export function UserList() {
  const { data, isLoading, error } = useUsers();

  if (isLoading) return <div>Đang tải...</div>;
  if (error) return <div>Đã xảy ra lỗi</div>;

  return (
    <DataGrid
      rows={data?.data || []}
      columns={[
        { field: 'id', headerName: 'ID', width: 90 },
        { field: 'name', headerName: 'Tên', flex: 1 },
        { field: 'email', headerName: 'Email', flex: 1 },
      ]}
      pageSizeOptions={[5, 10, 25]}
      checkboxSelection
      disableRowSelectionOnClick
    />
  );
}
```

## 7. Xử lý lỗi toàn cục

```typescript
// lib/api/http-client.ts
import axios from 'axios';
import { toast } from 'react-toastify';

const httpClient = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL,
  timeout: 10000,
});

httpClient.interceptors.response.use(
  (response) => response.data,
  (error) => {
    const message = error.response?.data?.message || 'Đã xảy ra lỗi';
    toast.error(message);
    return Promise.reject(error);
  }
);

export { httpClient };
```

## 8. Best Practices
- Sử dụng query keys có cấu trúc rõ ràng
- Tách biệt API logic với components
- Sử dụng TypeScript để đảm bảo type safety
- Xử lý loading và error states một cách nhất quán
