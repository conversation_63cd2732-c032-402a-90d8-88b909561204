/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { DanhGiaXepLoaiDangVienEntity } from '~/database/typeorm/entities/danhGiaXepLoaiDangVien.entity';

@Injectable()
export class DanhGiaXepLoaiDangVienRepository extends Repository<DanhGiaXepLoaiDangVienEntity> {
    constructor(private dataSource: DataSource) {
        super(DanhGiaXepLoaiDangVienEntity, dataSource.createEntityManager());
    }
}
