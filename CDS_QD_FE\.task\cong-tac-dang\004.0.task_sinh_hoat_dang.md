# Quy trình xây dựng giao diện trang "Quản lý Kế hoạch Sinh hoạt Đảng"

---

**<PERSON><PERSON><PERSON>: <PERSON>h hoạt <PERSON> (Tập thể)**

**I. Trang Kế hoạch Sinh hoạt (`/${locale}/cong-tac-dang/sinh-hoat-dang/ke-hoach`)**

- **<PERSON><PERSON><PERSON> (Tiêu đề hiển thị):** `<PERSON>u<PERSON>n lý Kế hoạch Sinh hoạt Đảng`
- **M<PERSON><PERSON> đích chính của Trang:** Cho phép người dùng (thư<PERSON><PERSON> l<PERSON> c<PERSON>, người phụ trách công tác Đảng của đơn vị) tạo, xem, cập nhật và quản lý các kế hoạch sinh hoạt Đảng theo năm, quý, hoặc tháng cho tổ chức Đảng của mình.

---

#### A. <PERSON><PERSON><PERSON> năng trang `DanhSachKeHoachSinhHoatDangPage`

1.  **<PERSON><PERSON><PERSON> nă<PERSON> "Hiển thị Danh sách Kế hoạch Sinh hoạt Đảng"**

    - **Tổng quan:** Trang hiển thị một bảng liệt kê các kế hoạch sinh hoạt Đảng đã được tạo, cho phép người dùng nắm bắt tổng quan các kế hoạch.
    - **Chi tiết:**
      - Khi tải trang, hệ thống truy vấn và hiển thị danh sách các kế hoạch sinh hoạt Đảng thuộc phạm vi quản lý của người dùng (hoặc toàn bộ nếu có quyền).
      - Thông tin được trình bày dưới dạng bảng, mỗi hàng là một kế hoạch.
      - Hỗ trợ phân trang nếu số lượng kế hoạch lớn.

2.  **Chức năng "Tìm kiếm và Lọc Kế hoạch"**

    - **Tổng quan:** Cung cấp công cụ để người dùng nhanh chóng tìm thấy kế hoạch mong muốn.
    - **Chi tiết:**
      - **Tìm kiếm theo Tên Kế hoạch/Chủ đề:** Ô nhập liệu để tìm kiếm theo từ khóa.
      - **Lọc theo Loại Kế hoạch:** Trường lựa chọn (ví dụ: Năm, Quý, Tháng, Đợt, Chuyên đề) - dữ liệu từ danh mục `DmLoaiKeHoachGDCT` (tên bảng này cần được chuẩn hóa cho Công tác Đảng, ví dụ `DmLoaiKeHoachSinhHoat`).
      - **Lọc theo Tổ chức Đảng:** Trường lựa chọn (có thể dạng cây) để chọn Tổ chức Đảng lập kế hoạch.
      - **Lọc theo Thời gian Kế hoạch:** Chọn Năm, hoặc khoảng thời gian cụ thể.
      - **Lọc theo Trạng thái Phê duyệt:** Trường lựa chọn (ví dụ: Mới tạo, Chờ duyệt, Đã duyệt, Từ chối) - dữ liệu từ danh mục `DmTrangThaiPheDuyet`.
      - Nút "Áp dụng bộ lọc" và "Đặt lại".

3.  **Chức năng "Tạo Kế hoạch Sinh hoạt mới"**

    - **Tổng quan:** Cho phép người dùng tạo một kế hoạch sinh hoạt Đảng mới.
    - **Chi tiết:**
      - Nhấn nút "Tạo Kế hoạch mới" sẽ mở ra một giao diện (trang riêng hoặc một cửa sổ/dialog lớn) để nhập thông tin.
      - **Form tạo mới bao gồm các trường:**
        - `ToChucDangID`: Lựa chọn Tổ chức Đảng lập kế hoạch (thường là đơn vị của người dùng).
        - `LoaiKeHoachID`: Lựa chọn Loại kế hoạch (Năm, Quý, Tháng...).
        - `NamKeHoach` (nếu Loại là Năm, Quý, Tháng): Nhập năm.
        - `QuyKeHoach` (nếu Loại là Quý): Lựa chọn Quý.
        - `ThangKeHoach` (nếu Loại là Tháng): Lựa chọn Tháng.
        - `TenKeHoach`: Tên/Chủ đề kế hoạch (bắt buộc).
        - `NoiDungKeHoach_URL`: Cho phép tải lên file đính kèm chứa nội dung chi tiết của kế hoạch (ví dụ: file Word, PDF).
      - Nút "Lưu Kế hoạch" và "Hủy".
      - Sau khi lưu, kế hoạch mới sẽ ở trạng thái "Mới tạo" hoặc "Chờ duyệt" tùy theo quy trình.

4.  **Chức năng "Xem chi tiết Kế hoạch"**

    - **Tổng quan:** Cho phép xem đầy đủ thông tin của một kế hoạch đã chọn.
    - **Chi tiết:** Khi người dùng chọn hành động "Xem chi tiết" từ bảng danh sách, hệ thống hiển thị đầy đủ các thông tin của kế hoạch đó (có thể trong một cửa sổ/dialog hoặc điều hướng sang trang chi tiết riêng nếu thông tin phức tạp). Bao gồm tất cả các trường đã nhập khi tạo/sửa và trạng thái hiện tại.

5.  **Chức năng "Sửa Kế hoạch"**

    - **Tổng quan:** Cho phép chỉnh sửa thông tin của một kế hoạch đã có (nếu kế hoạch chưa được phê duyệt hoặc người dùng có quyền).
    - **Chi tiết:** Tương tự như form "Tạo Kế hoạch mới", nhưng các trường đã được điền sẵn thông tin của kế hoạch đang sửa.

6.  **Chức năng "Xóa Kế hoạch"**

    - **Tổng quan:** Cho phép xóa một kế hoạch (thường chỉ khi kế hoạch chưa được phê duyệt hoặc không có buổi sinh hoạt nào liên kết).
    - **Chi tiết:** Cần có hộp thoại xác nhận trước khi xóa.

7.  **Chức năng "Quản lý Phê duyệt Kế hoạch" (Dành cho người có thẩm quyền)**
    - **Tổng quan:** Cho phép người có thẩm quyền xem xét và phê duyệt hoặc từ chối các kế hoạch được gửi lên.
    - **Chi tiết:**
      - Đối với các kế hoạch ở trạng thái "Chờ duyệt", người có thẩm quyền sẽ thấy các nút hành động như "Phê duyệt", "Từ chối".
      - Khi "Từ chối", có thể yêu cầu nhập lý do.
      - Trạng thái của kế hoạch (`TrangThaiPheDuyetID`) sẽ được cập nhật tương ứng.
      - Hệ thống có thể gửi thông báo cho người tạo kế hoạch về kết quả phê duyệt.

---

#### B. Bảng dữ liệu sử dụng trên trang `DanhSachKeHoachSinhHoatDangPage` và Form Tạo/Sửa

- **Bảng `KeHoachSinhHoatDang` (PartyActivityPlans)** (Tên bảng trong tài liệu là `KeHoachGiaoDucCT`, cần đổi tên cho phù hợp ngữ cảnh Công tác Đảng)

  - `ID: number` (BIGINT, PK, AUTO_INCREMENT) - Mã kế hoạch
  - `ToChucDangID: number` (BIGINT, NOT NULL, FK REFERENCES ToChucDang(ID)) - Tổ chức Đảng lập kế hoạch
  - `NamKeHoach: number` (INT NOT NULL) - Năm của kế hoạch
  - `QuyKeHoach: number` (INT NULL) - Quý của kế hoạch (1, 2, 3, 4)
  - `ThangKeHoach: number` (INT NULL) - Tháng của kế hoạch (1-12)
  - `TenKeHoach: string` (NVARCHAR(500) NOT NULL) - Tên/Chủ đề kế hoạch
  - `NoiDungKeHoach_URL: string` (VARCHAR(255) NULL) - Đường dẫn file nội dung chi tiết
  - `LoaiKeHoachID: number` (INT, FK REFERENCES DmLoaiKeHoachSinhHoat(ID)) - Loại kế hoạch (Năm, Quý, Tháng, Đợt, Chuyên đề)
  - `MaKeHoach: string` (VARCHAR(50), UNIQUE) - Mã kế hoạch (nếu có, tùy chọn)
  - `ThoiGianBatDauKeHoach: date` (DATE) - Thời gian bắt đầu dự kiến của kế hoạch (tính cho cả chu kỳ, ví dụ KH năm thì là ngày đầu năm)
  - `ThoiGianKetThucKeHoach: date` (DATE) - Thời gian kết thúc dự kiến của kế hoạch
  - `NguoiPhuTrachID: string` (VARCHAR(20), FK REFERENCES DangVien(MaDangVien) hoặc QuanNhan(SoHieuQuanNhan)) - Người phụ trách kế hoạch
  - `TrangThaiPheDuyetID: number` (INT, FK REFERENCES DmTrangThaiPheDuyet(ID), DEFAULT 0) - Trạng thái phê duyệt
  - `NguoiTaoID: string` (VARCHAR(50), NOT NULL) - Người tạo
  - `NgayTao: datetime` (TIMESTAMP, DEFAULT CURRENT_TIMESTAMP) - Ngày tạo
  - `NguoiCapNhatID: string` (VARCHAR(50)) - Người cập nhật
  - `NgayCapNhat: datetime` (TIMESTAMP, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP) - Ngày cập nhật

- **Bảng `ToChucDang` (PartyOrganizations)** (Để chọn và hiển thị tên Tổ chức Đảng)

  - `ID: number` (BIGINT, PK)
  - `TenToChucDang: string` (NVARCHAR(255), NOT NULL)
  - (Các trường khác như `CapToChucDangID`, `ToChucDangCapTrenID` để hiển thị dạng cây nếu cần)

- **Danh mục `DmLoaiKeHoachSinhHoat`** (Tên gợi ý, trong tài liệu là `DmLoaiKeHoachGDCT`)

  - `ID: number` (PK)
  - `TenLoaiKeHoach: string` (Tên loại: Năm, Quý, Tháng, Chuyên đề...)

- **Danh mục `DmTrangThaiPheDuyet`**

  - `ID: number` (PK)
  - `TenTrangThai: string` (Tên trạng thái: Mới tạo, Chờ duyệt, Đã duyệt, Từ chối)

- **Bảng `DangVien` / `QuanNhan`** (Để chọn người phụ trách)
  - `MaDangVien` / `SoHieuQuanNhan: string` (PK)
  - `HoVaTen: string`

---

#### C. Liên kết với page khác

1.  **Trang Danh sách Buổi Sinh hoạt Đảng:**
    - Từ một kế hoạch đã được phê duyệt, có thể có chức năng "Tạo Buổi sinh hoạt từ Kế hoạch này", điều hướng hoặc mở form tạo buổi sinh hoạt với thông tin kế hoạch được liên kết sẵn.
2.  **Trang Chi tiết Buổi Sinh hoạt Đảng:**
    - Một buổi sinh hoạt có thể liên kết ngược lại với kế hoạch mà nó thuộc về.

_(Các trang Buổi Sinh hoạt và Nghị quyết Đảng sẽ được mô tả chi tiết ở các phản hồi tiếp theo.)_
