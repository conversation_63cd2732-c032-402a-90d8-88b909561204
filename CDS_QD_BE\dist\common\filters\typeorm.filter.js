"use strict";Object.defineProperty(exports,"__esModule",{value:true});Object.defineProperty(exports,"TypeOrmFilter",{enumerable:true,get:function(){return TypeOrmFilter}});const _common=require("@nestjs/common");const _typeorm=require("typeorm");function _ts_decorate(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect==="object"&&typeof Reflect.decorate==="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)if(d=decorators[i])r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r;return c>3&&r&&Object.defineProperty(target,key,r),r}let TypeOrmFilter=class TypeOrmFilter{catch(exception,host){const ctx=host.switchToHttp();const response=ctx.getResponse();const request=ctx.getRequest();const message=exception.message;const code=exception.code;const status=exception instanceof _common.HttpException?exception.getStatus():_common.HttpStatus.INTERNAL_SERVER_ERROR;const error=this.handleError(exception);_common.Logger.error(`[${status}] {${request.url}, ${response?.req?.route?.path}, ${request.method}}: ${exception?.message}`,"ExceptionFilter");response.status(error.code).json({result:false,message:error.message,data:{code:code,message:message},statusCode:error.code})}handleError(error){const field=this.parseField(error?.detail?.match(/Key \((\w+)\)=/),error.detail);switch(error.code){case"23505":return{code:409,message:`Tr\xf9ng ${field}`};case"23503":return{code:409,message:`Kh\xf4ng tồn tại (${error.detail})`};case"22P02":return{code:400,message:`Sai kiểu dữ liệu`};case"22001":return{code:400,message:`Dữ liệu qu\xe1 d\xe0i`};case"23502":return{code:400,message:`Thiếu trường bắt buộc`};case"42703":return{code:500,message:`Trường kh\xf4ng tồn tại`};case"ER_DATA_TOO_LONG":return{code:400,message:`Dữ liệu qu\xe1 d\xe0i`};case"ER_DUP_ENTRY":return{code:409,message:`Tr\xf9ng dữ liệu`};case"ER_NO_REFERENCED_ROW_2":return{code:409,message:`Kh\xf4ng tồn tại c\xe1c dữ liệu li\xean quan`};case"ER_BAD_NULL_ERROR":return{code:400,message:`Thiếu trường bắt buộc`};case"ER_BAD_FIELD_ERROR":return{code:500,message:`Trường kh\xf4ng tồn tại`};case"ER_NO_DEFAULT_FOR_FIELD":return{code:400,message:`Thiếu trường bắt buộc`};default:return{code:500,message:`Lỗi hệ thống`}}}parseField(field,detail){if(!field)return`dữ liệu (${detail?.replace("Key ","")?.replace(" already exists.","")})`;switch(field[1]){case"registration_no":return"Số đăng bộ";case"citizen_id":return"Số CCCD/CMND";default:return field[1]||"dữ liệu"}}};TypeOrmFilter=_ts_decorate([(0,_common.Catch)(_typeorm.TypeORMError)],TypeOrmFilter);
//# sourceMappingURL=typeorm.filter.js.map