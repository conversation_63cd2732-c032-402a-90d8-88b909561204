/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { BienPhapTacDongTuTuongEntity } from '~/database/typeorm/entities/bienPhapTacDongTuTuong.entity';

@Injectable()
export class BienPhapTacDongTuTuongRepository extends Repository<BienPhapTacDongTuTuongEntity> {
    constructor(private dataSource: DataSource) {
        super(BienPhapTacDongTuTuongEntity, dataSource.createEntityManager());
    }
}
