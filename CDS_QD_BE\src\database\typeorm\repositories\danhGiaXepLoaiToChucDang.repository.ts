/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { DanhGiaXepLoaiToChucDangEntity } from '~/database/typeorm/entities/danhGiaXepLoaiToChucDang.entity';

@Injectable()
export class DanhGiaXepLoaiToChucDangRepository extends Repository<DanhGiaXepLoaiToChucDangEntity> {
    constructor(private dataSource: DataSource) {
        super(DanhGiaXepLoaiToChucDangEntity, dataSource.createEntityManager());
    }
}
