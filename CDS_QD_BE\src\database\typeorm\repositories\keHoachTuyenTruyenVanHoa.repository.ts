/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { KeHoachTuyenTruyenVanHoaEntity } from '~/database/typeorm/entities/keHoachTuyenTruyenVanHoa.entity';

@Injectable()
export class KeHoachTuyenTruyenVanHoaRepository extends Repository<KeHoachTuyenTruyenVanHoaEntity> {
    constructor(private dataSource: DataSource) {
        super(KeHoachTuyenTruyenVanHoaEntity, dataSource.createEntityManager());
    }
}
