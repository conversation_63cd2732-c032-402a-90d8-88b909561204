/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { CuocKiemTraGiamSatDangEntity } from '~/database/typeorm/entities/cuocKiemTraGiamSatDang.entity';

@Injectable()
export class CuocKiemTraGiamSatDangRepository extends Repository<CuocKiemTraGiamSatDangEntity> {
    constructor(private dataSource: DataSource) {
        super(CuocKiemTraGiamSatDangEntity, dataSource.createEntityManager());
    }
}
