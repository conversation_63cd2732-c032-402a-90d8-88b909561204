{"version": 3, "sources": ["../../../src/common/decorators/permission.decorator.ts"], "sourcesContent": ["import { SetMetadata } from '@nestjs/common';\n\nexport const PERMISSION_KEY = 'permission';\nexport const Permission = (...args: string[]) => SetMetadata(PERMISSION_KEY, args);\n"], "names": ["PERMISSION_KEY", "Permission", "args", "SetMetadata"], "mappings": "mPAEaA,wBAAAA,oBACAC,oBAAAA,oCAHe,kBAErB,MAAMD,eAAiB,aACvB,MAAMC,WAAa,CAAC,GAAGC,OAAmBC,GAAAA,mBAAW,EAACH,eAAgBE"}