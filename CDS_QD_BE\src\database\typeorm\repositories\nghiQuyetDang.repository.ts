/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { NghiQuyetDangEntity } from '~/database/typeorm/entities/nghiQuyetDang.entity';

@Injectable()
export class NghiQuyetDangRepository extends Repository<NghiQuyetDangEntity> {
    constructor(private dataSource: DataSource) {
        super(NghiQuyetDangEntity, dataSource.createEntityManager());
    }
}
