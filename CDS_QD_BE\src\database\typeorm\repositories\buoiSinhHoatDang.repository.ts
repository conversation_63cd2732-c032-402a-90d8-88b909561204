/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { BuoiSinhHoatDangEntity } from '~/database/typeorm/entities/buoiSinhHoatDang.entity';

@Injectable()
export class BuoiSinhHoatDangRepository extends Repository<BuoiSinhHoatDangEntity> {
    constructor(private dataSource: DataSource) {
        super(BuoiSinhHoatDangEntity, dataSource.createEntityManager());
    }
}
