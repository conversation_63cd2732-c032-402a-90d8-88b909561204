{"version": 3, "sources": ["../../../../src/database/typeorm/entities/usersRole.entity.ts"], "sourcesContent": ["import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>umn, Relation } from 'typeorm';\nimport { UserEntity } from './user.entity';\nimport { RoleEntity } from './role.entity';\n\n@Entity({ name: 'users_roles' })\nexport class UsersRoleEntity {\n    @PrimaryColumn({ name: 'user_id', type: 'bigint' })\n    userId: number;\n\n    @PrimaryColumn({ name: 'role_id', type: 'bigint' })\n    roleId: number;\n\n    /* RELATIONS */\n    @ManyToOne(() => UserEntity, (user) => user.userRoles, {\n        onDelete: 'CASCADE',\n        onUpdate: 'CASCADE',\n        createForeignKeyConstraints: false,\n    })\n    @JoinColumn({ name: 'user_id', referencedColumnName: 'id' })\n    user: Relation<UserEntity>;\n\n    @ManyToOne(() => RoleEntity, (role) => role.userRoles, {\n        onDelete: 'CASCADE',\n        onUpdate: 'CASCADE',\n        createForeignKeyConstraints: false,\n    })\n    @JoinColumn({ name: 'role_id', referencedColumnName: 'id' })\n    role: Relation<RoleEntity>;\n}\n"], "names": ["UsersRoleEntity", "name", "type", "UserEntity", "user", "userRoles", "onDelete", "onUpdate", "createForeignKeyConstraints", "referencedColumnName", "RoleEntity", "role"], "mappings": "oGAKaA,yDAAAA,0CALkE,qCACpD,2CACA,8jBAGpB,IAAA,AAAMA,gBAAN,MAAMA,gBAuBb,4CAtBqBC,KAAM,UAAWC,KAAM,oIAGvBD,KAAM,UAAWC,KAAM,mIAIvBC,sBAAU,CAAGC,MAASA,KAAKC,SAAS,EACjDC,SAAU,UACVC,SAAU,UACVC,4BAA6B,iCAEnBP,KAAM,UAAWQ,qBAAsB,sLAGpCC,sBAAU,CAAGC,MAASA,KAAKN,SAAS,EACjDC,SAAU,UACVC,SAAU,UACVC,4BAA6B,iCAEnBP,KAAM,UAAWQ,qBAAsB,gMAtB/CR,KAAM"}