/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { QuanNhanEntity } from '~/database/typeorm/entities/quanNhan.entity';

@Injectable()
export class QuanNhanRepository extends Repository<QuanNhanEntity> {
    constructor(private dataSource: DataSource) {
        super(QuanNhanEntity, dataSource.createEntityManager());
    }
}
