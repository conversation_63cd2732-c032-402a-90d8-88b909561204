"use strict";Object.defineProperty(exports,"__esModule",{value:true});Object.defineProperty(exports,"UsersRoleRepository",{enumerable:true,get:function(){return UsersRoleRepository}});const _common=require("@nestjs/common");const _typeorm=require("typeorm");const _usersRoleentity=require("../entities/usersRole.entity");function _ts_decorate(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect==="object"&&typeof Reflect.decorate==="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)if(d=decorators[i])r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r;return c>3&&r&&Object.defineProperty(target,key,r),r}function _ts_metadata(k,v){if(typeof Reflect==="object"&&typeof Reflect.metadata==="function")return Reflect.metadata(k,v)}let UsersRoleRepository=class UsersRoleRepository extends _typeorm.Repository{async findRolesByUserId(userId){return this.find({where:{userId},relations:["role"]})}async findUsersByRoleId(roleId){return this.find({where:{roleId},relations:["user"]})}async addRoleToUser(userId,roleId){const existingUserRole=await this.findOne({where:{userId,roleId}});if(existingUserRole){return existingUserRole}const userRole=this.create({userId,roleId});return this.save(userRole)}async removeRoleFromUser(userId,roleId){return this.delete({userId,roleId})}async removeAllRolesFromUser(userId){return this.delete({userId})}async userHasRole(userId,roleId){const userRole=await this.findOne({where:{userId,roleId}});return!!userRole}constructor(dataSource){super(_usersRoleentity.UsersRoleEntity,dataSource.createEntityManager()),this.dataSource=dataSource}};UsersRoleRepository=_ts_decorate([(0,_common.Injectable)(),_ts_metadata("design:type",Function),_ts_metadata("design:paramtypes",[typeof _typeorm.DataSource==="undefined"?Object:_typeorm.DataSource])],UsersRoleRepository);
//# sourceMappingURL=usersRole.repository.js.map