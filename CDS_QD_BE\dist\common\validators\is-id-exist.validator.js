"use strict";Object.defineProperty(exports,"__esModule",{value:true});function _export(target,all){for(var name in all)Object.defineProperty(target,name,{enumerable:true,get:Object.getOwnPropertyDescriptor(all,name).get})}_export(exports,{get IsIdAlreadyExistConstraint(){return IsIdAlreadyExistConstraint},get IsIdExist(){return IsIdExist}});const _common=require("@nestjs/common");const _classvalidator=require("class-validator");const _databaseservice=require("../../database/typeorm/database.service");function _ts_decorate(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect==="object"&&typeof Reflect.decorate==="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)if(d=decorators[i])r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r;return c>3&&r&&Object.defineProperty(target,key,r),r}function _ts_metadata(k,v){if(typeof Reflect==="object"&&typeof Reflect.metadata==="function")return Reflect.metadata(k,v)}let IsIdAlreadyExistConstraint=class IsIdAlreadyExistConstraint{async validate(id,args){return!!await this.database[args.constraints[0]?.entity]?.countBy({id})}defaultMessage(validationArguments){return`ID ${validationArguments?.value} kh\xf4ng tồn tại`}constructor(database){this.database=database}};IsIdAlreadyExistConstraint=_ts_decorate([(0,_classvalidator.ValidatorConstraint)({name:"IsIdExist",async:true}),(0,_common.Injectable)(),_ts_metadata("design:type",Function),_ts_metadata("design:paramtypes",[typeof _databaseservice.DatabaseService==="undefined"?Object:_databaseservice.DatabaseService])],IsIdAlreadyExistConstraint);function IsIdExist(data,validationOptions){return function(object,propertyName){(0,_classvalidator.registerDecorator)({target:object.constructor,propertyName:propertyName,options:validationOptions,constraints:[data],validator:IsIdAlreadyExistConstraint})}}
//# sourceMappingURL=is-id-exist.validator.js.map