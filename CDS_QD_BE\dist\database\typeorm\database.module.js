"use strict";Object.defineProperty(exports,"__esModule",{value:true});Object.defineProperty(exports,"DatabaseModule",{enumerable:true,get:function(){return DatabaseModule}});const _common=require("@nestjs/common");const _config=require("@nestjs/config");const _typeorm=require("@nestjs/typeorm");const _typeorm1=require("typeorm");const _databaseservice=require("./database.service");const _accountentity=require("./entities/account.entity");const _departmententity=require("./entities/department.entity");const _mediaentity=require("./entities/media.entity");const _permissionentity=require("./entities/permission.entity");const _providerentity=require("./entities/provider.entity");const _roleentity=require("./entities/role.entity");const _userentity=require("./entities/user.entity");const _userLogentity=require("./entities/userLog.entity");const _warehouseentity=require("./entities/warehouse.entity");const _warehouseTypeentity=require("./entities/warehouseType.entity");const _accountrepository=require("./repositories/account.repository");const _departmentrepository=require("./repositories/department.repository");const _mediarepository=require("./repositories/media.repository");const _permissionrepository=require("./repositories/permission.repository");const _providerrepository=require("./repositories/provider.repository");const _rolerepository=require("./repositories/role.repository");const _userrepository=require("./repositories/user.repository");const _userLogrepository=require("./repositories/userLog.repository");const _warehouserepository=require("./repositories/warehouse.repository");const _warehouseTyperepository=require("./repositories/warehouseType.repository");const _loaiDanhMucentity=require("./entities/loaiDanhMuc.entity");const _loaiDanhMucrepository=require("./repositories/loaiDanhMuc.repository");const _giaTriDanhMucentity=require("./entities/giaTriDanhMuc.entity");const _giaTriDanhMucrepository=require("./repositories/giaTriDanhMuc.repository");const _thamSoHeThongentity=require("./entities/thamSoHeThong.entity");const _thamSoHeThongrepository=require("./repositories/thamSoHeThong.repository");const _quanNhanentity=require("./entities/quanNhan.entity");const _quanNhanrepository=require("./repositories/quanNhan.repository");const _lyLichCanNhanentity=require("./entities/lyLichCanNhan.entity");const _lyLichCanNhanrepository=require("./repositories/lyLichCanNhan.repository");const _quaTrinhCongTacentity=require("./entities/quaTrinhCongTac.entity");const _quaTrinhCongTacrepository=require("./repositories/quaTrinhCongTac.repository");const _quaTrinhDaoTaoentity=require("./entities/quaTrinhDaoTao.entity");const _quaTrinhDaoTaorepository=require("./repositories/quaTrinhDaoTao.repository");const _deXuatKhenThuongentity=require("./entities/deXuatKhenThuong.entity");const _deXuatKhenThuongrepository=require("./repositories/deXuatKhenThuong.repository");const _quyetDinhKhenThuongentity=require("./entities/quyetDinhKhenThuong.entity");const _quyetDinhKhenThuongrepository=require("./repositories/quyetDinhKhenThuong.repository");const _hoSoVuViecKyLuatentity=require("./entities/hoSoVuViecKyLuat.entity");const _hoSoVuViecKyLuatrepository=require("./repositories/hoSoVuViecKyLuat.repository");const _bienBanHoiDongKyLuatentity=require("./entities/bienBanHoiDongKyLuat.entity");const _bienBanHoiDongKyLuatrepository=require("./repositories/bienBanHoiDongKyLuat.repository");const _taiLieuVuViecKyLuatentity=require("./entities/taiLieuVuViecKyLuat.entity");const _taiLieuVuViecKyLuatrepository=require("./repositories/taiLieuVuViecKyLuat.repository");const _quyetDinhKyLuatentity=require("./entities/quyetDinhKyLuat.entity");const _quyetDinhKyLuatrepository=require("./repositories/quyetDinhKyLuat.repository");const _hoSoSucKhoeentity=require("./entities/hoSoSucKhoe.entity");const _hoSoSucKhoerepository=require("./repositories/hoSoSucKhoe.repository");const _quanHeGiaDinhentity=require("./entities/quanHeGiaDinh.entity");const _quanHeGiaDinhrepository=require("./repositories/quanHeGiaDinh.repository");const _theoDoiCheDoChinhSachentity=require("./entities/theoDoiCheDoChinhSach.entity");const _theoDoiCheDoChinhSachrepository=require("./repositories/theoDoiCheDoChinhSach.repository");const _dangVienentity=require("./entities/dangVien.entity");const _dangVienrepository=require("./repositories/dangVien.repository");const _hoSoDangVienentity=require("./entities/hoSoDangVien.entity");const _hoSoDangVienrepository=require("./repositories/hoSoDangVien.repository");const _deXuatKhenThuongDangVienentity=require("./entities/deXuatKhenThuongDangVien.entity");const _deXuatKhenThuongDangVienrepository=require("./repositories/deXuatKhenThuongDangVien.repository");const _quyetDinhKhenThuongDangVienentity=require("./entities/quyetDinhKhenThuongDangVien.entity");const _quyetDinhKhenThuongDangVienrepository=require("./repositories/quyetDinhKhenThuongDangVien.repository");const _banKiemDiemDangVienentity=require("./entities/banKiemDiemDangVien.entity");const _banKiemDiemDangVienrepository=require("./repositories/banKiemDiemDangVien.repository");const _hoSoVuViecKyLuatDangVienentity=require("./entities/hoSoVuViecKyLuatDangVien.entity");const _hoSoVuViecKyLuatDangVienrepository=require("./repositories/hoSoVuViecKyLuatDangVien.repository");const _bienBanHoiDongKyLuatDangentity=require("./entities/bienBanHoiDongKyLuatDang.entity");const _bienBanHoiDongKyLuatDangrepository=require("./repositories/bienBanHoiDongKyLuatDang.repository");const _quyetDinhKyLuatDangVienentity=require("./entities/quyetDinhKyLuatDangVien.entity");const _quyetDinhKyLuatDangVienrepository=require("./repositories/quyetDinhKyLuatDangVien.repository");const _capUyNhiemKyentity=require("./entities/capUyNhiemKy.entity");const _capUyNhiemKyrepository=require("./repositories/capUyNhiemKy.repository");const _thanhVienCapUyentity=require("./entities/thanhVienCapUy.entity");const _thanhVienCapUyrepository=require("./repositories/thanhVienCapUy.repository");const _keHoachSinhHoatDangentity=require("./entities/keHoachSinhHoatDang.entity");const _keHoachSinhHoatDangrepository=require("./repositories/keHoachSinhHoatDang.repository");const _buoiSinhHoatDangentity=require("./entities/buoiSinhHoatDang.entity");const _buoiSinhHoatDangrepository=require("./repositories/buoiSinhHoatDang.repository");const _nghiQuyetDangentity=require("./entities/nghiQuyetDang.entity");const _nghiQuyetDangrepository=require("./repositories/nghiQuyetDang.repository");const _hoSoPhatTrienDangVienentity=require("./entities/hoSoPhatTrienDangVien.entity");const _hoSoPhatTrienDangVienrepository=require("./repositories/hoSoPhatTrienDangVien.repository");const _danhGiaXepLoaiDangVienentity=require("./entities/danhGiaXepLoaiDangVien.entity");const _danhGiaXepLoaiDangVienrepository=require("./repositories/danhGiaXepLoaiDangVien.repository");const _danhGiaXepLoaiToChucDangentity=require("./entities/danhGiaXepLoaiToChucDang.entity");const _danhGiaXepLoaiToChucDangrepository=require("./repositories/danhGiaXepLoaiToChucDang.repository");const _thuChiDangPhientity=require("./entities/thuChiDangPhi.entity");const _thuChiDangPhirepository=require("./repositories/thuChiDangPhi.repository");const _keHoachKiemTraGiamSatDangentity=require("./entities/keHoachKiemTraGiamSatDang.entity");const _keHoachKiemTraGiamSatDangrepository=require("./repositories/keHoachKiemTraGiamSatDang.repository");const _cuocKiemTraGiamSatDangentity=require("./entities/cuocKiemTraGiamSatDang.entity");const _cuocKiemTraGiamSatDangrepository=require("./repositories/cuocKiemTraGiamSatDang.repository");const _keHoachGiaoDucChinhTrientity=require("./entities/keHoachGiaoDucChinhTri.entity");const _keHoachGiaoDucChinhTrirepository=require("./repositories/keHoachGiaoDucChinhTri.repository");const _hoatDongGiaoDucChinhTrientity=require("./entities/hoatDongGiaoDucChinhTri.entity");const _hoatDongGiaoDucChinhTrirepository=require("./repositories/hoatDongGiaoDucChinhTri.repository");const _thamGiaGiaoDucChinhTrientity=require("./entities/thamGiaGiaoDucChinhTri.entity");const _thamGiaGiaoDucChinhTrirepository=require("./repositories/thamGiaGiaoDucChinhTri.repository");const _ketQuaHocTapChinhTrientity=require("./entities/ketQuaHocTapChinhTri.entity");const _ketQuaHocTapChinhTrirepository=require("./repositories/ketQuaHocTapChinhTri.repository");const _ghiNhanTinhHinhTuTuongentity=require("./entities/ghiNhanTinhHinhTuTuong.entity");const _ghiNhanTinhHinhTuTuongrepository=require("./repositories/ghiNhanTinhHinhTuTuong.repository");const _bienPhapTacDongTuTuongentity=require("./entities/bienPhapTacDongTuTuong.entity");const _bienPhapTacDongTuTuongrepository=require("./repositories/bienPhapTacDongTuTuong.repository");const _keHoachTuyenTruyenVanHoaentity=require("./entities/keHoachTuyenTruyenVanHoa.entity");const _keHoachTuyenTruyenVanHoarepository=require("./repositories/keHoachTuyenTruyenVanHoa.repository");const _hoatDongTuyenTruyenVanHoaentity=require("./entities/hoatDongTuyenTruyenVanHoa.entity");const _hoatDongTuyenTruyenVanHoarepository=require("./repositories/hoatDongTuyenTruyenVanHoa.repository");const _phongTraoThiDuaChinhTrientity=require("./entities/phongTraoThiDuaChinhTri.entity");const _phongTraoThiDuaChinhTrirepository=require("./repositories/phongTraoThiDuaChinhTri.repository");const _chinhSachHauPhuongentity=require("./entities/chinhSachHauPhuong.entity");const _chinhSachHauPhuongrepository=require("./repositories/chinhSachHauPhuong.repository");const _doiTuongChinhSachHauPhuongentity=require("./entities/doiTuongChinhSachHauPhuong.entity");const _doiTuongChinhSachHauPhuongrepository=require("./repositories/doiTuongChinhSachHauPhuong.repository");const _thongBaoentity=require("./entities/thongBao.entity");const _thongBaorepository=require("./repositories/thongBao.repository");const _quyTrinhPheDuyetentity=require("./entities/quyTrinhPheDuyet.entity");const _quyTrinhPheDuyetrepository=require("./repositories/quyTrinhPheDuyet.repository");const _yeuCauPheDuyetentity=require("./entities/yeuCauPheDuyet.entity");const _yeuCauPheDuyetrepository=require("./repositories/yeuCauPheDuyet.repository");const _buocPheDuyetentity=require("./entities/buocPheDuyet.entity");const _buocPheDuyetrepository=require("./repositories/buocPheDuyet.repository");const _lichSuPheDuyetentity=require("./entities/lichSuPheDuyet.entity");const _lichSuPheDuyetrepository=require("./repositories/lichSuPheDuyet.repository");const _nhatKyHeThongentity=require("./entities/nhatKyHeThong.entity");const _nhatKyHeThongrepository=require("./repositories/nhatKyHeThong.repository");const _duLieuTepTinentity=require("./entities/duLieuTepTin.entity");const _duLieuTepTinrepository=require("./repositories/duLieuTepTin.repository");const _usersRoleentity=require("./entities/usersRole.entity");const _usersRolerepository=require("./repositories/usersRole.repository");function _ts_decorate(decorators,target,key,desc){var c=arguments.length,r=c<3?target:desc===null?desc=Object.getOwnPropertyDescriptor(target,key):desc,d;if(typeof Reflect==="object"&&typeof Reflect.decorate==="function")r=Reflect.decorate(decorators,target,key,desc);else for(var i=decorators.length-1;i>=0;i--)if(d=decorators[i])r=(c<3?d(r):c>3?d(target,key,r):d(target,key))||r;return c>3&&r&&Object.defineProperty(target,key,r),r}const entities=[_roleentity.RoleEntity,_userentity.UserEntity,_permissionentity.PermissionEntity,_mediaentity.MediaEntity,_accountentity.AccountEntity,_departmententity.DepartmentEntity,_warehouseentity.WarehouseEntity,_userLogentity.UserLogEntity,_warehouseTypeentity.WarehouseTypeEntity,_providerentity.ProviderEntity,_loaiDanhMucentity.LoaiDanhMucEntity,_giaTriDanhMucentity.GiaTriDanhMucEntity,_thamSoHeThongentity.ThamSoHeThongEntity,_quanNhanentity.QuanNhanEntity,_lyLichCanNhanentity.LyLichCanNhanEntity,_quaTrinhCongTacentity.QuaTrinhCongTacEntity,_quaTrinhDaoTaoentity.QuaTrinhDaoTaoEntity,_deXuatKhenThuongentity.DeXuatKhenThuongEntity,_quyetDinhKhenThuongentity.QuyetDinhKhenThuongEntity,_hoSoVuViecKyLuatentity.HoSoVuViecKyLuatEntity,_bienBanHoiDongKyLuatentity.BienBanHoiDongKyLuatEntity,_taiLieuVuViecKyLuatentity.TaiLieuVuViecKyLuatEntity,_quyetDinhKyLuatentity.QuyetDinhKyLuatEntity,_hoSoSucKhoeentity.HoSoSucKhoeEntity,_quanHeGiaDinhentity.QuanHeGiaDinhEntity,_theoDoiCheDoChinhSachentity.TheoDoiCheDoChinhSachEntity,_dangVienentity.DangVienEntity,_hoSoDangVienentity.HoSoDangVienEntity,_deXuatKhenThuongDangVienentity.DeXuatKhenThuongDangVienEntity,_quyetDinhKhenThuongDangVienentity.QuyetDinhKhenThuongDangVienEntity,_banKiemDiemDangVienentity.BanKiemDiemDangVienEntity,_hoSoVuViecKyLuatDangVienentity.HoSoVuViecKyLuatDangVienEntity,_bienBanHoiDongKyLuatDangentity.BienBanHoiDongKyLuatDangEntity,_quyetDinhKyLuatDangVienentity.QuyetDinhKyLuatDangVienEntity,_capUyNhiemKyentity.CapUyNhiemKyEntity,_thanhVienCapUyentity.ThanhVienCapUyEntity,_keHoachSinhHoatDangentity.KeHoachSinhHoatDangEntity,_buoiSinhHoatDangentity.BuoiSinhHoatDangEntity,_nghiQuyetDangentity.NghiQuyetDangEntity,_hoSoPhatTrienDangVienentity.HoSoPhatTrienDangVienEntity,_danhGiaXepLoaiDangVienentity.DanhGiaXepLoaiDangVienEntity,_danhGiaXepLoaiToChucDangentity.DanhGiaXepLoaiToChucDangEntity,_thuChiDangPhientity.ThuChiDangPhiEntity,_keHoachKiemTraGiamSatDangentity.KeHoachKiemTraGiamSatDangEntity,_cuocKiemTraGiamSatDangentity.CuocKiemTraGiamSatDangEntity,_keHoachGiaoDucChinhTrientity.KeHoachGiaoDucChinhTriEntity,_hoatDongGiaoDucChinhTrientity.HoatDongGiaoDucChinhTriEntity,_thamGiaGiaoDucChinhTrientity.ThamGiaGiaoDucChinhTriEntity,_ketQuaHocTapChinhTrientity.KetQuaHocTapChinhTriEntity,_ghiNhanTinhHinhTuTuongentity.GhiNhanTinhHinhTuTuongEntity,_bienPhapTacDongTuTuongentity.BienPhapTacDongTuTuongEntity,_keHoachTuyenTruyenVanHoaentity.KeHoachTuyenTruyenVanHoaEntity,_hoatDongTuyenTruyenVanHoaentity.HoatDongTuyenTruyenVanHoaEntity,_phongTraoThiDuaChinhTrientity.PhongTraoThiDuaChinhTriEntity,_chinhSachHauPhuongentity.ChinhSachHauPhuongEntity,_doiTuongChinhSachHauPhuongentity.DoiTuongChinhSachHauPhuongEntity,_thongBaoentity.ThongBaoEntity,_quyTrinhPheDuyetentity.QuyTrinhPheDuyetEntity,_yeuCauPheDuyetentity.YeuCauPheDuyetEntity,_buocPheDuyetentity.BuocPheDuyetEntity,_lichSuPheDuyetentity.LichSuPheDuyetEntity,_nhatKyHeThongentity.NhatKyHeThongEntity,_duLieuTepTinentity.DuLieuTepTinEntity,_usersRoleentity.UsersRoleEntity];const repositories=[_departmentrepository.DepartmentRepository,_userrepository.UserRepository,_accountrepository.AccountRepository,_mediarepository.MediaRepository,_permissionrepository.PermissionRepository,_rolerepository.RoleRepository,_warehouserepository.WarehouseRepository,_userLogrepository.UserLogRepository,_warehouseTyperepository.WarehouseTypeRepository,_providerrepository.ProviderRepository,_loaiDanhMucrepository.LoaiDanhMucRepository,_giaTriDanhMucrepository.GiaTriDanhMucRepository,_thamSoHeThongrepository.ThamSoHeThongRepository,_quanNhanrepository.QuanNhanRepository,_lyLichCanNhanrepository.LyLichCanNhanRepository,_quaTrinhCongTacrepository.QuaTrinhCongTacRepository,_quaTrinhDaoTaorepository.QuaTrinhDaoTaoRepository,_deXuatKhenThuongrepository.DeXuatKhenThuongRepository,_quyetDinhKhenThuongrepository.QuyetDinhKhenThuongRepository,_hoSoVuViecKyLuatrepository.HoSoVuViecKyLuatRepository,_bienBanHoiDongKyLuatrepository.BienBanHoiDongKyLuatRepository,_taiLieuVuViecKyLuatrepository.TaiLieuVuViecKyLuatRepository,_quyetDinhKyLuatrepository.QuyetDinhKyLuatRepository,_hoSoSucKhoerepository.HoSoSucKhoeRepository,_quanHeGiaDinhrepository.QuanHeGiaDinhRepository,_theoDoiCheDoChinhSachrepository.TheoDoiCheDoChinhSachRepository,_dangVienrepository.DangVienRepository,_hoSoDangVienrepository.HoSoDangVienRepository,_deXuatKhenThuongDangVienrepository.DeXuatKhenThuongDangVienRepository,_quyetDinhKhenThuongDangVienrepository.QuyetDinhKhenThuongDangVienRepository,_banKiemDiemDangVienrepository.BanKiemDiemDangVienRepository,_hoSoVuViecKyLuatDangVienrepository.HoSoVuViecKyLuatDangVienRepository,_bienBanHoiDongKyLuatDangrepository.BienBanHoiDongKyLuatDangRepository,_quyetDinhKyLuatDangVienrepository.QuyetDinhKyLuatDangVienRepository,_capUyNhiemKyrepository.CapUyNhiemKyRepository,_thanhVienCapUyrepository.ThanhVienCapUyRepository,_keHoachSinhHoatDangrepository.KeHoachSinhHoatDangRepository,_buoiSinhHoatDangrepository.BuoiSinhHoatDangRepository,_nghiQuyetDangrepository.NghiQuyetDangRepository,_hoSoPhatTrienDangVienrepository.HoSoPhatTrienDangVienRepository,_danhGiaXepLoaiDangVienrepository.DanhGiaXepLoaiDangVienRepository,_danhGiaXepLoaiToChucDangrepository.DanhGiaXepLoaiToChucDangRepository,_thuChiDangPhirepository.ThuChiDangPhiRepository,_keHoachKiemTraGiamSatDangrepository.KeHoachKiemTraGiamSatDangRepository,_cuocKiemTraGiamSatDangrepository.CuocKiemTraGiamSatDangRepository,_keHoachGiaoDucChinhTrirepository.KeHoachGiaoDucChinhTriRepository,_hoatDongGiaoDucChinhTrirepository.HoatDongGiaoDucChinhTriRepository,_thamGiaGiaoDucChinhTrirepository.ThamGiaGiaoDucChinhTriRepository,_ketQuaHocTapChinhTrirepository.KetQuaHocTapChinhTriRepository,_ghiNhanTinhHinhTuTuongrepository.GhiNhanTinhHinhTuTuongRepository,_bienPhapTacDongTuTuongrepository.BienPhapTacDongTuTuongRepository,_keHoachTuyenTruyenVanHoarepository.KeHoachTuyenTruyenVanHoaRepository,_hoatDongTuyenTruyenVanHoarepository.HoatDongTuyenTruyenVanHoaRepository,_phongTraoThiDuaChinhTrirepository.PhongTraoThiDuaChinhTriRepository,_chinhSachHauPhuongrepository.ChinhSachHauPhuongRepository,_doiTuongChinhSachHauPhuongrepository.DoiTuongChinhSachHauPhuongRepository,_thongBaorepository.ThongBaoRepository,_quyTrinhPheDuyetrepository.QuyTrinhPheDuyetRepository,_yeuCauPheDuyetrepository.YeuCauPheDuyetRepository,_buocPheDuyetrepository.BuocPheDuyetRepository,_lichSuPheDuyetrepository.LichSuPheDuyetRepository,_nhatKyHeThongrepository.NhatKyHeThongRepository,_duLieuTepTinrepository.DuLieuTepTinRepository,_usersRolerepository.UsersRoleRepository];let DatabaseModule=class DatabaseModule{};DatabaseModule=_ts_decorate([(0,_common.Global)(),(0,_common.Module)({imports:[_typeorm.TypeOrmModule.forRootAsync({useFactory:configService=>({...configService.get("database"),entities}),inject:[_config.ConfigService],dataSourceFactory:async options=>{const dataSource=await new _typeorm1.DataSource(options).initialize();return dataSource}})],providers:[_databaseservice.DatabaseService,...repositories],exports:[_databaseservice.DatabaseService]})],DatabaseModule);
//# sourceMappingURL=database.module.js.map