{"version": 3, "sources": ["../../src/bootstraps/interceptor.bootstrap.ts"], "sourcesContent": ["import { INestApplication } from '@nestjs/common';\r\nimport { TransformInterceptor } from '~/common/interceptors/transform.interceptor';\r\n\r\nexport function bootstrapInterceptor(app: INestApplication): void {\r\n    app.useGlobalInterceptors(new TransformInterceptor());\r\n}\r\n"], "names": ["bootstrapInterceptor", "app", "useGlobalInterceptors", "TransformInterceptor"], "mappings": "oGAGgBA,8DAAAA,4DAFqB,gDAE9B,SAASA,qBAAqBC,GAAqB,EACtDA,IAAIC,qBAAqB,CAAC,IAAIC,0CAAoB,CACtD"}