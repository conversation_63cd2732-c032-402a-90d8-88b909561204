/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 *  Swagger
 * The  API documents
 * OpenAPI spec version: 1.0
 */
import { useMutation } from '@tanstack/react-query'
import type { MutationFunction, QueryClient, UseMutationOptions, UseMutationResult } from '@tanstack/react-query'

import type { ForgotPasswordDto, LoginDto, RenewTokenDto, ResetPasswordDto } from '.././model'

import { customInstance } from '../../mutator/custom-instance'
import type { ApiResponse } from '../../types'

export const authControllerLogin = (loginDto: LoginDto, signal?: AbortSignal) => {
  return customInstance<ApiResponse<void>>({
    url: `/auth/login`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: loginDto,
    signal
  })
}

export const getAuthControllerLoginMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<Awaited<ReturnType<typeof authControllerLogin>>, TError, { data: LoginDto }, TContext>
}): UseMutationOptions<Awaited<ReturnType<typeof authControllerLogin>>, TError, { data: LoginDto }, TContext> => {
  const mutationKey = ['authControllerLogin']
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } }

  const mutationFn: MutationFunction<Awaited<ReturnType<typeof authControllerLogin>>, { data: LoginDto }> = props => {
    const { data } = props ?? {}

    return authControllerLogin(data)
  }

  return { mutationFn, ...mutationOptions }
}

export type AuthControllerLoginMutationResult = NonNullable<Awaited<ReturnType<typeof authControllerLogin>>>
export type AuthControllerLoginMutationBody = LoginDto
export type AuthControllerLoginMutationError = unknown

export const useAuthControllerLogin = <TError = unknown, TContext = unknown>(
  options?: {
    mutation?: UseMutationOptions<Awaited<ReturnType<typeof authControllerLogin>>, TError, { data: LoginDto }, TContext>
  },
  queryClient?: QueryClient
): UseMutationResult<Awaited<ReturnType<typeof authControllerLogin>>, TError, { data: LoginDto }, TContext> => {
  const mutationOptions = getAuthControllerLoginMutationOptions(options)

  return useMutation(mutationOptions, queryClient)
}
export const authControllerLogout = (signal?: AbortSignal) => {
  return customInstance<ApiResponse<void>>({ url: `/auth/logout`, method: 'POST', signal })
}

export const getAuthControllerLogoutMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<Awaited<ReturnType<typeof authControllerLogout>>, TError, void, TContext>
}): UseMutationOptions<Awaited<ReturnType<typeof authControllerLogout>>, TError, void, TContext> => {
  const mutationKey = ['authControllerLogout']
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } }

  const mutationFn: MutationFunction<Awaited<ReturnType<typeof authControllerLogout>>, void> = () => {
    return authControllerLogout()
  }

  return { mutationFn, ...mutationOptions }
}

export type AuthControllerLogoutMutationResult = NonNullable<Awaited<ReturnType<typeof authControllerLogout>>>

export type AuthControllerLogoutMutationError = unknown

export const useAuthControllerLogout = <TError = unknown, TContext = unknown>(
  options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof authControllerLogout>>, TError, void, TContext> },
  queryClient?: QueryClient
): UseMutationResult<Awaited<ReturnType<typeof authControllerLogout>>, TError, void, TContext> => {
  const mutationOptions = getAuthControllerLogoutMutationOptions(options)

  return useMutation(mutationOptions, queryClient)
}
export const authControllerForgotPassword = (forgotPasswordDto: ForgotPasswordDto, signal?: AbortSignal) => {
  return customInstance<ApiResponse<void>>({
    url: `/auth/forgot-password`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: forgotPasswordDto,
    signal
  })
}

export const getAuthControllerForgotPasswordMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof authControllerForgotPassword>>,
    TError,
    { data: ForgotPasswordDto },
    TContext
  >
}): UseMutationOptions<
  Awaited<ReturnType<typeof authControllerForgotPassword>>,
  TError,
  { data: ForgotPasswordDto },
  TContext
> => {
  const mutationKey = ['authControllerForgotPassword']
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } }

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof authControllerForgotPassword>>,
    { data: ForgotPasswordDto }
  > = props => {
    const { data } = props ?? {}

    return authControllerForgotPassword(data)
  }

  return { mutationFn, ...mutationOptions }
}

export type AuthControllerForgotPasswordMutationResult = NonNullable<
  Awaited<ReturnType<typeof authControllerForgotPassword>>
>
export type AuthControllerForgotPasswordMutationBody = ForgotPasswordDto
export type AuthControllerForgotPasswordMutationError = unknown

export const useAuthControllerForgotPassword = <TError = unknown, TContext = unknown>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof authControllerForgotPassword>>,
      TError,
      { data: ForgotPasswordDto },
      TContext
    >
  },
  queryClient?: QueryClient
): UseMutationResult<
  Awaited<ReturnType<typeof authControllerForgotPassword>>,
  TError,
  { data: ForgotPasswordDto },
  TContext
> => {
  const mutationOptions = getAuthControllerForgotPasswordMutationOptions(options)

  return useMutation(mutationOptions, queryClient)
}
export const authControllerResetPassword = (resetPasswordDto: ResetPasswordDto, signal?: AbortSignal) => {
  return customInstance<ApiResponse<void>>({
    url: `/auth/reset-password`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: resetPasswordDto,
    signal
  })
}

export const getAuthControllerResetPasswordMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof authControllerResetPassword>>,
    TError,
    { data: ResetPasswordDto },
    TContext
  >
}): UseMutationOptions<
  Awaited<ReturnType<typeof authControllerResetPassword>>,
  TError,
  { data: ResetPasswordDto },
  TContext
> => {
  const mutationKey = ['authControllerResetPassword']
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } }

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof authControllerResetPassword>>,
    { data: ResetPasswordDto }
  > = props => {
    const { data } = props ?? {}

    return authControllerResetPassword(data)
  }

  return { mutationFn, ...mutationOptions }
}

export type AuthControllerResetPasswordMutationResult = NonNullable<
  Awaited<ReturnType<typeof authControllerResetPassword>>
>
export type AuthControllerResetPasswordMutationBody = ResetPasswordDto
export type AuthControllerResetPasswordMutationError = unknown

export const useAuthControllerResetPassword = <TError = unknown, TContext = unknown>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof authControllerResetPassword>>,
      TError,
      { data: ResetPasswordDto },
      TContext
    >
  },
  queryClient?: QueryClient
): UseMutationResult<
  Awaited<ReturnType<typeof authControllerResetPassword>>,
  TError,
  { data: ResetPasswordDto },
  TContext
> => {
  const mutationOptions = getAuthControllerResetPasswordMutationOptions(options)

  return useMutation(mutationOptions, queryClient)
}
export const authControllerRenewToken = (renewTokenDto: RenewTokenDto, signal?: AbortSignal) => {
  return customInstance<ApiResponse<void>>({
    url: `/auth/renew-token`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: renewTokenDto,
    signal
  })
}

export const getAuthControllerRenewTokenMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof authControllerRenewToken>>,
    TError,
    { data: RenewTokenDto },
    TContext
  >
}): UseMutationOptions<
  Awaited<ReturnType<typeof authControllerRenewToken>>,
  TError,
  { data: RenewTokenDto },
  TContext
> => {
  const mutationKey = ['authControllerRenewToken']
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } }

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof authControllerRenewToken>>,
    { data: RenewTokenDto }
  > = props => {
    const { data } = props ?? {}

    return authControllerRenewToken(data)
  }

  return { mutationFn, ...mutationOptions }
}

export type AuthControllerRenewTokenMutationResult = NonNullable<Awaited<ReturnType<typeof authControllerRenewToken>>>
export type AuthControllerRenewTokenMutationBody = RenewTokenDto
export type AuthControllerRenewTokenMutationError = unknown

export const useAuthControllerRenewToken = <TError = unknown, TContext = unknown>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof authControllerRenewToken>>,
      TError,
      { data: RenewTokenDto },
      TContext
    >
  },
  queryClient?: QueryClient
): UseMutationResult<
  Awaited<ReturnType<typeof authControllerRenewToken>>,
  TError,
  { data: RenewTokenDto },
  TContext
> => {
  const mutationOptions = getAuthControllerRenewTokenMutationOptions(options)

  return useMutation(mutationOptions, queryClient)
}
