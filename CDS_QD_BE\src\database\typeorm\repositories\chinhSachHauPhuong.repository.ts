/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { ChinhSachHauPhuongEntity } from '~/database/typeorm/entities/chinhSachHauPhuong.entity';

@Injectable()
export class ChinhSachHauPhuongRepository extends Repository<ChinhSachHauPhuongEntity> {
    constructor(private dataSource: DataSource) {
        super(ChinhSachHauPhuongEntity, dataSource.createEntityManager());
    }
}
