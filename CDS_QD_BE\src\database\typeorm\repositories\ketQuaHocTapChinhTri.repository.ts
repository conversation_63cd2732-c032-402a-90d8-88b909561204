/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { KetQuaHocTapChinhTriEntity } from '~/database/typeorm/entities/ketQuaHocTapChinhTri.entity';

@Injectable()
export class KetQuaHocTapChinhTriRepository extends Repository<KetQuaHocTapChinhTriEntity> {
    constructor(private dataSource: DataSource) {
        super(KetQuaHocTapChinhTriEntity, dataSource.createEntityManager());
    }
}
