/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { HoSoDangVienEntity } from '~/database/typeorm/entities/hoSoDangVien.entity';

@Injectable()
export class HoSoDangVienRepository extends Repository<HoSoDangVienEntity> {
    constructor(private dataSource: DataSource) {
        super(HoSoDangVienEntity, dataSource.createEntityManager());
    }
}
