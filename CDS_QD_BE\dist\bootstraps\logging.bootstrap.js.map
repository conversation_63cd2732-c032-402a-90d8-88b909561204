{"version": 3, "sources": ["../../src/bootstraps/logging.bootstrap.ts"], "sourcesContent": ["import { INestApplication } from '@nestjs/common';\r\nimport { Logger, LoggerErrorInterceptor } from 'nestjs-pino';\r\n\r\nexport function bootstrapLogging(app: INestApplication): void {\r\n    app.useLogger(app.get(Logger));\r\n    app.useGlobalInterceptors(new LoggerErrorInterceptor());\r\n}\r\n"], "names": ["bootstrapLogging", "app", "useLogger", "get", "<PERSON><PERSON>", "useGlobalInterceptors", "LoggerErrorInterceptor"], "mappings": "oGAGgBA,0DAAAA,8CAF+B,eAExC,SAASA,iBAAiBC,GAAqB,EAClDA,IAAIC,SAAS,CAACD,IAAIE,GAAG,CAACC,kBAAM,GAC5BH,IAAII,qBAAqB,CAAC,IAAIC,kCAAsB,CACxD"}