/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Request } from 'express';
import { Observable } from 'rxjs';
import { DataSource } from 'typeorm';
import { BYPASS_PERMISSION } from '~/common/constants/constant';
import { PERMISSION_KEY } from '~/common/decorators/permission.decorator';
import { CACHE_TIME, USER_ROLE } from '~/common/enums/enum';
import { UserEntity } from '~/database/typeorm/entities/user.entity';
import { CacheService } from '~/shared/services/cache.service';

const URLs = ['auth', 'docs'];

@Injectable()
export class PermissionGuard implements CanActivate {
    constructor(private reflector: Reflector, private dataSource: DataSource, private readonly cacheService: CacheService) {}

    canActivate(context: ExecutionContext): boolean | Promise<boolean> | Observable<boolean> {
        const req = context.switchToHttp().getRequest();
        if (URLs.some((url) => req.originalUrl.includes(url))) return true;
        const permission = this.reflector.getAllAndOverride<string>(PERMISSION_KEY, [context.getHandler(), context.getClass()]);
        if (!permission) return false;

        return this.verifyPermission({
            req: req,
            permission: permission[0],
            params: req.params,
        });
    }

    private async verifyPermission(data: { req: Request; permission: string; params: any }) {
        try {
            if (data.permission === BYPASS_PERMISSION) return true;

            const userId = data.req.headers['_userId']; // sau khi qua authMiddleware thì đã add _userId vào headers
            const user = await this.getUser(+userId || 0);
            if (!user) return false;

            // Check if user has admin role (either single or multiple)
            if (this.hasAdminRole(user)) {
                // Set primary role ID for backward compatibility
                const primaryRoleId = user.roleId || user.userRoles?.[0]?.role?.id;
                data.req.headers['_roleId'] = primaryRoleId?.toString() || '';
                data.req.headers['_fullName'] = user.hoTen;
                return true;
            }

            // Get all role IDs for the user
            const roleIds = this.getAllUserRoleIds(user);
            if (roleIds.length === 0) return false;

            // Get permissions from all roles
            const permissions = await this.getPermissionsFromMultipleRoles(roleIds);
            if (permissions.length === 0) return false;

            // Check if permission is in permissions
            if (!permissions.some((p) => p.action === data.permission)) return false;

            // Set primary role ID for backward compatibility
            const primaryRoleId = user.roleId || roleIds[0];
            data.req.headers['_roleId'] = primaryRoleId?.toString() || '';
            data.req.headers['_fullName'] = user.hoTen;
            data.req.headers['_allRoleIds'] = roleIds.join(','); // New header for multiple roles

            return true;
        } catch (error) {
            console.log('LOG:: error:', error.stack);
            console.log('LOG:: PermissionGuard:', error.message);
            return false;
        }
    }

    private async getPermissions(roleId: number) {
        const key = `permissions:${roleId}`;
        const cached = await this.cacheService.getJson(key);
        if (cached) return cached;

        const entityManager = this.dataSource.manager;
        const permissions = await entityManager.query(`
            SELECT p.action
            FROM roles_permissions as rp, permissions as p
            WHERE rp.role_id = ${roleId}
                AND rp.permission_id = p.id
        `);
        this.cacheService.setJson(key, permissions, CACHE_TIME.ONE_MONTH);

        return permissions;
    }

    // Get permissions from multiple roles
    private async getPermissionsFromMultipleRoles(roleIds: number[]) {
        if (!roleIds || roleIds.length === 0) return [];

        const key = `permissions:multiple:${roleIds.sort().join(',')}`;
        const cached = await this.cacheService.getJson(key);
        if (cached) return cached;

        const entityManager = this.dataSource.manager;
        const permissions = await entityManager.query(`
            SELECT DISTINCT p.action
            FROM roles_permissions as rp, permissions as p
            WHERE rp.role_id IN (${roleIds.join(',')})
                AND rp.permission_id = p.id
        `);

        this.cacheService.setJson(key, permissions, CACHE_TIME.ONE_MONTH);
        return permissions;
    }

    // Check if user has admin role (either single or multiple)
    private hasAdminRole(user: UserEntity): boolean {
        // Check single role
        if (user.roleId === USER_ROLE.ADMIN) {
            return true;
        }

        // Check multiple roles
        if (user.userRoles && user.userRoles.length > 0) {
            return user.userRoles.some((userRole) => userRole.role?.id === USER_ROLE.ADMIN);
        }

        return false;
    }

    // Get all role IDs for a user (both single and multiple)
    private getAllUserRoleIds(user: UserEntity): number[] {
        const roleIds = [];

        // Add single role if exists
        if (user.roleId) {
            roleIds.push(user.roleId);
        }

        // Add multiple roles if exists
        if (user.userRoles && user.userRoles.length > 0) {
            user.userRoles.forEach((userRole) => {
                if (userRole.role?.id) {
                    roleIds.push(userRole.role.id);
                }
            });
        }

        // Remove duplicates
        return [...new Set(roleIds)];
    }

    private async getUser(id: number): Promise<UserEntity> {
        const key = `userData:${id}`;
        const cached = await this.cacheService.getJson(key);
        if (cached) return cached;

        const entityManager = this.dataSource.manager;
        const user = await entityManager.findOne(UserEntity, {
            where: { id: id },
            select: ['id', 'roleId', 'hoTen'],
            relations: ['role', 'userRoles', 'userRoles.role'],
        });

        // Cache user data for 1 week
        if (user) {
            this.cacheService.setJson(key, user, CACHE_TIME.ONE_WEEK);
        }

        return user;
    }
}
