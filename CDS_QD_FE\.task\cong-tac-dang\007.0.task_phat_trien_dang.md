# Quy trình xây dựng giao diện trang "Quản lý <PERSON>ồ sơ Phát triển Đảng viên"

---

**Ch<PERSON><PERSON> năng: Phát triển Đảng viên (Cập nhật - Chỉ dành cho đối tượng Quân nhân)**

**I. <PERSON>rang <PERSON> sách Hồ sơ Phát triển Đảng viên (`/${locale}/cong-tac-dang/phat-trien-dang-vien`)**

*   **T<PERSON><PERSON> (Tiêu đề hiển thị):** `Quản lý Hồ sơ Phát triển Đảng viên (Quân nhân)`
*   **M<PERSON><PERSON> đích chính của Trang:** Cung cấp giao diện để theo dõi và quản lý tất cả các hồ sơ của Quân nhân đang trong quá trình được xem xét, bồi dưỡng để kết nạp vào Đảng hoặc chuyển Đảng chính thức.
*   **<PERSON><PERSON> cục tổng quan của Trang:**
    1.  **<PERSON><PERSON> điều hướng phân cấp (Breadcrumbs):** Hiển thị đường dẫn hiện tại.
    2.  **Tiêu đề trang:** Ví dụ "Quản lý Hồ sơ Phát triển Đảng viên".
    3.  **Nút hành động chính:** "Tạo Hồ sơ Phát triển mới".
    4.  **Khu vực Lọc và Tìm kiếm:** Các trường để thu hẹp danh sách hồ sơ.
    5.  **Khu vực Hiển thị Danh sách (Dạng bảng):** Liệt kê các hồ sơ phát triển.
    6.  **Khu vực Phân trang:** Điều hướng qua các trang kết quả.

---

#### A. Chức năng trang `DanhSachPhatTrienDangVienPage`

1.  **Chức năng "Hiển thị Danh sách Hồ sơ Phát triển Đảng viên"**
    *   **Tổng quan:** Trang hiển thị một bảng liệt kê các hồ sơ phát triển của Quân nhân.
    *   **Chi tiết (Bố cục hiển thị):**
        *   Khi tải trang, hệ thống truy vấn và hiển thị danh sách các hồ sơ phát triển.
        *   **Khu vực chính hiển thị dạng bảng:** Mỗi hàng đại diện cho một hồ sơ phát triển, bao gồm các cột thông tin tóm tắt.
            *   **Các cột trong bảng ví dụ:** "Số hiệu QN Đối tượng", "Họ tên Đối tượng", "Đơn vị Công tác", "Ngày tạo Hồ sơ", "Tổ chức Đảng đề nghị", "Trạng thái Quy trình hiện tại", "Hành động".
        *   Hỗ trợ phân trang ở cuối bảng nếu số lượng hồ sơ lớn.

2.  **Chức năng "Tìm kiếm và Lọc Hồ sơ Phát triển"**
    *   **Tổng quan:** Cho phép người dùng tìm kiếm và lọc danh sách hồ sơ.
    *   **Chi tiết (Bố cục khu vực lọc):**
        *   Một khu vực phía trên bảng danh sách, có thể ẩn/hiện.
        *   **Ô nhập liệu "Tìm kiếm Quân nhân":** Cho phép nhập Số hiệu QN hoặc Họ tên để tìm kiếm.
        *   **Trường lựa chọn "Đơn vị Công tác":** Lọc theo đơn vị của Quân nhân.
        *   **Trường lựa chọn "Tổ chức Đảng đề nghị/quản lý":** Lọc theo Tổ chức Đảng liên quan.
        *   **Trường lựa chọn "Trạng thái Quy trình":** Lọc theo các trạng thái trong `DmTrangThaiPhatTrienDang`.
        *   **Trường chọn khoảng ngày "Ngày tạo Hồ sơ":** Lọc theo khoảng thời gian tạo hồ sơ.
        *   Nút "Áp dụng bộ lọc" và "Đặt lại".

3.  **Chức năng "Tạo Hồ sơ Phát triển Đảng viên mới"**
    *   **Tổng quan:** Cho phép người dùng (có quyền) khởi tạo một hồ sơ phát triển mới cho một Quân nhân ưu tú.
    *   **Chi tiết (Luồng và giao diện tạo mới):**
        *   Nhấn nút "Tạo Hồ sơ Phát triển mới" trên trang danh sách.
        *   Hệ thống mở ra một **giao diện nhập liệu** (có thể là một trang riêng hoặc một cửa sổ/dialog lớn).
        *   **Các trường thông tin cần nhập/chọn trên giao diện tạo mới:**
            *   **"Chọn Quân nhân":** Một trường tìm kiếm có gợi ý (autocomplete) hoặc nút "Chọn" để mở một cửa sổ/dialog danh sách Quân nhân cho phép người dùng tìm và chọn một Quân nhân. Sau khi chọn, các thông tin cơ bản (Số hiệu QN, Họ tên, Ngày sinh, Đơn vị) của Quân nhân đó sẽ được hiển thị để xác nhận. Hệ thống cần kiểm tra xem Quân nhân này đã là Đảng viên hoặc đã có hồ sơ phát triển Đảng khác đang hoạt động hay chưa.
            *   **"Ngày tạo Hồ sơ":** Trường chọn ngày, mặc định là ngày hiện tại.
            *   **"Tổ chức Đảng đề nghị kết nạp":** Trường lựa chọn (dạng cây hoặc danh sách) từ bảng `ToChucDang`.
            *   **"Đảng viên hướng dẫn 1 (nếu có)":** Trường tìm kiếm và lựa chọn Đảng viên.
            *   **"Đảng viên hướng dẫn 2 (nếu có)":** Trường tìm kiếm và lựa chọn Đảng viên.
            *   **"Ghi chú ban đầu (nếu có)":** Ô nhập liệu văn bản.
        *   Nút "Lưu Hồ sơ" để tạo mới và nút "Hủy".
        *   Sau khi lưu thành công, hồ sơ mới sẽ xuất hiện trong bảng danh sách, với trạng thái quy trình ban đầu (ví dụ: "Tạo nguồn" hoặc "Chờ thẩm tra").

4.  **Chức năng "Xem chi tiết Quy trình Phát triển" (hành động trên dòng trong bảng danh sách)**
    *   **Tổng quan:** Cho phép người dùng xem toàn bộ quá trình của một hồ sơ phát triển cụ thể.
    *   **Chi tiết:**
        *   Mỗi hàng trong bảng danh sách hồ sơ sẽ có một nút/biểu tượng "Xem chi tiết".
        *   Khi nhấn vào, hệ thống sẽ điều hướng người dùng đến **Trang Chi tiết Quy trình Phát triển Đảng viên** (`/${locale}/cong-tac-dang/phat-trien-dang-vien/chi-tiet/{idHoSoPhatTrien}`).

---

#### B. Trang Chi tiết Quy trình Phát triển Đảng viên (`/${locale}/cong-tac-dang/phat-trien-dang-vien/chi-tiet/{idHoSoPhatTrien}`)

*   **Tên Trang:** `Chi tiết Quy trình Phát triển Đảng viên: [Họ tên Quân nhân]`
*   **Mục đích chính của Trang:** Hiển thị toàn bộ thông tin và diễn biến của một hồ sơ phát triển Đảng viên, cho phép người có quyền thực hiện các hành động tương ứng với từng bước của quy trình.
*   **Bố cục tổng quan của Trang:**
    1.  **Thanh điều hướng phân cấp (Breadcrumbs).**
    2.  **Tiêu đề trang:** Ví dụ "Chi tiết Quy trình Phát triển Đảng viên: Nguyễn Văn A".
    3.  **Khu vực "Thông tin chung Đối tượng Phát triển":** Hiển thị thông tin cố định của Quân nhân đang được phát triển.
    4.  **Khu vực "Tiến trình Quy trình":** Một thành phần trực quan (ví dụ: dạng các bước nối tiếp - stepper) hiển thị các bước chính của quy trình phát triển và trạng thái của từng bước (đã hoàn thành, đang thực hiện, chưa tới). Bước hiện tại của hồ sơ sẽ được làm nổi bật.
    5.  **Khu vực "Chi tiết Bước hiện tại/Được chọn":** Khi người dùng chọn một bước trong "Tiến trình Quy trình", khu vực này sẽ hiển thị thông tin chi tiết, tài liệu liên quan và các hành động có thể thực hiện cho bước đó.

*   **Chi tiết các khu vực và chức năng:**

    1.  **Khu vực "Thông tin chung Đối tượng Phát triển (Quân nhân)":**
        *   **Bố cục:** Một khu vực riêng biệt, có thể là một thẻ (card) ở đầu trang.
        *   **Hiển thị các thông tin (read-only):**
            *   **Ảnh chân dung Quân nhân** (nếu có).
            *   **Số hiệu Quân nhân:** Từ `HoSoPhatTrienDangVien.DoiTuongQuanNhanID`.
            *   **Họ và tên:** Lấy từ `QuanNhan.HoVaTenKhaiSinh`.
            *   **Ngày sinh:** Lấy từ `QuanNhan.NgaySinh`.
            *   **Đơn vị công tác hiện tại:** Lấy từ `QuanNhan.DonViID` (hiển thị tên đơn vị).
            *   **Cấp bậc/Chức vụ hiện tại:** Lấy từ `QuanNhan`.
            *   **Ngày tạo Hồ sơ Phát triển:** `HoSoPhatTrienDangVien.NgayTaoHoSo`.
            *   **Tổ chức Đảng đề nghị kết nạp (ban đầu):** Tên từ `ToChucDang` (liên kết qua `HoSoPhatTrienDangVien.ToChucDangDeNghiKetNapID`).
            *   **Đảng viên hướng dẫn 1:** Tên từ `DangVien` (liên kết qua `HoSoPhatTrienDangVien.NguoiHuongDan1ID`).
            *   **Đảng viên hướng dẫn 2:** Tên từ `DangVien` (liên kết qua `HoSoPhatTrienDangVien.NguoiHuongDan2ID`).
            *   **Trạng thái Quy trình Hiện tại:** Tên trạng thái từ `DmTrangThaiPhatTrienDang`.

    2.  **Khu vực "Tiến trình Quy trình Phát triển Đảng":**
        *   **Bố cục:** Một dãy các bước được sắp xếp theo thứ tự (`DmTrangThaiPhatTrienDang.ThuTuBuoc`), có thể hiển thị theo chiều ngang hoặc dọc.
        *   **Mỗi bước trong tiến trình hiển thị:**
            *   **Tên bước:** Lấy từ `DmTrangThaiPhatTrienDang.TenTrangThai`.
            *   **Biểu thị trạng thái:** Ví dụ, bước đã hoàn thành có dấu tích, bước đang thực hiện được tô màu khác, bước chưa tới có màu xám.
        *   **Chức năng:** Người dùng có thể nhấn vào một bước để xem chi tiết của bước đó trong "Khu vực Chi tiết Bước".

    3.  **Khu vực "Chi tiết Bước hiện tại/Được chọn":**
        *   **Bố cục:** Nội dung khu vực này sẽ thay đổi tùy theo bước quy trình được chọn.
        *   **Nội dung chung cho mỗi bước:**
            *   **Tiêu đề bước:** Tên của bước quy trình.
            *   **Mô tả/Hướng dẫn (nếu có):** Thông tin về những việc cần làm trong bước này.
            *   **Thông tin cụ thể của bước:** Các trường dữ liệu liên quan đến bước đó (ví dụ: Ngày Quyết định, Số Quyết định, kết quả bỏ phiếu...).
            *   **Danh sách "Tài liệu của Bước":**
                *   Hiển thị dưới dạng danh sách hoặc bảng các tài liệu liên quan đến bước này (lọc từ `TaiLieuPhatTrienDangVien` theo `HoSoPhatTrienID` và `LoaiTaiLieuPhatTrienID` phù hợp với bước).
                *   **Thông tin mỗi tài liệu:** Tên tài liệu, Loại tài liệu, Ngày nộp/phát hành.
                *   **Hành động "Xem/Tải file":** Cho phép truy cập file tài liệu.
                *   **Hành động "Nộp/Upload Tài liệu mới cho bước này":** (Chỉ hiển thị nếu bước đó cho phép và người dùng có quyền). Mở giao diện cho phép chọn loại tài liệu (phù hợp với bước) và tải lên file.
        *   **Các Nút Hành động của Bước (hiển thị tùy theo bước hiện tại của hồ sơ và quyền của người dùng):**
            *   **Ví dụ các nút hành động có thể có:**
                *   "Lưu thông tin Bước" (nếu có các trường nhập liệu cho bước).
                *   "Hoàn thành Bước & Chuyển tiếp".
                *   "Gửi Yêu cầu Thẩm tra Lý lịch".
                *   "Xác nhận Đã học Lớp Nhận thức" (có thể kèm upload giấy chứng nhận).
                *   "Tạo Giấy giới thiệu vào Đảng".
                *   "Ghi nhận Kết quả Họp Chi bộ" (nhập kết quả, số phiếu, upload nghị quyết chi bộ).
                *   "Đề nghị [Cấp trên] Xem xét".
                *   "Ra Quyết định Kết nạp" (nhập số QĐ, ngày QĐ, upload file QĐ).
                *   "Xác nhận Đã Tổ chức Lễ Kết nạp".
                *   "Chuyển Đảng viên sang Giai đoạn Dự bị".
                *   "Đề nghị Chuyển Đảng Chính thức".
                *   "Ra Quyết định Công nhận Chính thức".
                *   "Trả lại Hồ sơ" (yêu cầu nhập lý do).
                *   "Yêu cầu Bổ sung Thông tin/Tài liệu" (yêu cầu nhập nội dung cần bổ sung).
                *   "Tạm dừng Quy trình" (yêu cầu nhập lý do).
        *   Các hành động này sẽ cập nhật `HoSoPhatTrienDangVien.TrangThaiQuyTrinhID` và các trường dữ liệu liên quan khác.

---

#### II. Bảng dữ liệu được sử dụng trên các trang Phát triển Đảng viên (Không thay đổi so với mô tả trước, chỉ nhấn mạnh lại các trường chính liên quan đến việc đối tượng là Quân nhân)

*   **Bảng `HoSoPhatTrienDangVien` (PartyAdmissionDossiers)**
    *   `ID: number`
    *   `DoiTuongQuanNhanID: string` (NOT NULL, FK REFERENCES QuanNhan(SoHieuQuanNhan)) - Trường chính để xác định đối tượng.
    *   `NgayTaoHoSo: date`
    *   `TrangThaiQuyTrinhID: number` (FK REFERENCES DmTrangThaiPhatTrienDang(ID))
    *   `NguoiHuongDan1ID: string` (FK REFERENCES DangVien(MaDangVien))
    *   `NguoiHuongDan2ID: string` (FK REFERENCES DangVien(MaDangVien))
    *   `ToChucDangDeNghiKetNapID: number` (FK REFERENCES ToChucDang(ID))
    *   `NgayQuyetDinhKetNap: date`
    *   `SoQuyetDinhKetNap: string`
    *   `NgayCongNhanChinhThucDuKien: date`
    *   `NgayQuyetDinhChinhThuc: date`
    *   `SoQuyetDinhChinhThuc: string`
    *   `LyDoTamDungTraLai: string`
    *   `GhiChuQuyTrinh: string`

*   **Bảng `TaiLieuPhatTrienDangVien` (PartyAdmissionDocuments)** (Không thay đổi)
    *   `ID: number`
    *   `HoSoPhatTrienID: number` (FK REFERENCES HoSoPhatTrienDangVien(ID))
    *   `TenTaiLieu: string`
    *   `LoaiTaiLieuPhatTrienID: number` (FK REFERENCES DmLoaiTaiLieuPhatTrienDang(ID))
    *   `FileURL: string`
    *   `NgayPhatHanhTaiLieu: date`
    *   `NgayNopTaiLieu: date`
    *   `GhiChu: string`

*   **Bảng `QuanNhan` (Servicemen)**
    *   `SoHieuQuanNhan: string` (PK)
    *   `HoVaTenKhaiSinh: string`
    *   `NgaySinh: date`
    *   `DonViID: number`
    *   (Các trường liên quan khác để hiển thị thông tin quân nhân)

*   **Bảng `DangVien` (PartyMembers)**
    *   `MaDangVien: string` (PK)
    *   `HoVaTen: string`

*   **Bảng `ToChucDang` (PartyOrganizations)**
    *   `ID: number` (PK)
    *   `TenToChucDang: string`

*   **Danh mục `DmTrangThaiPhatTrienDang`**
    *   `ID: number` (PK)
    *   `TenTrangThai: string`
    *   `ThuTuBuoc: number`

*   **Danh mục `DmLoaiTaiLieuPhatTrienDang`**
    *   `ID: number` (PK)
    *   `TenLoaiTaiLieu: string`
    *   `BuocQuyTrinhApDungID: number`

---

#### III. Liên kết với page khác (Không thay đổi)

1.  **Trang Danh sách Hồ sơ Đảng viên / Chi tiết Hồ sơ Đảng viên.**
2.  **Module Quản lý Quân nhân.**
3.  **Module Quản lý Sinh hoạt Đảng (Buổi Sinh hoạt).**

---

Mô tả này đã làm rõ hơn về bố cục và các thành phần chức năng của hai trang chính trong quy trình Phát triển Đảng viên, với việc xác định đối tượng chỉ là Quân nhân.
