Bước 7: <PERSON><PERSON><PERSON> dựng Tab<PERSON><PERSON><PERSON> cho "Thông tin Chung"
Tạo file src\views\quan-ly-quan-nhan\chi-tiet\components\tabs\TabThongTinChung.tsx

Mục đích: Hi<PERSON><PERSON> thị các thông tin cơ bản nhất của quân nhân.

Props:

data: QuanNhanBaseType (Thông tin cơ bản của quân nhân từ quanNhanData.baseInfo).
Component Vuexy (MUI): Grid, Typography, Box (để tạo layout key-value).

Layout: Dạng danh sách key-value, sử dụng Grid 2 cột (hoặc tùy chỉnh cho responsive).

Các trường dữ liệu hiển thị (read-only):

SoHieuQuanNhan: Số hiệu Quân nhân
HoVaTenKhaiSinh: Họ và tên <PERSON>hai sinh
TenThuongDung: Tên thường dùng
NgaySinh: <PERSON><PERSON><PERSON> (Định dạng: dd/MM/yyyy)
GioiTinh: <PERSON><PERSON><PERSON><PERSON> (Hiển thị tên: Nam, Nữ, Khác)
SoCCCD_CMTQD: Số CCCD/CMT Quân đội
NgayNhapNgu: Ngày nhập ngũ (Định dạng: dd/MM/yyyy)
DonViID: Đơn vị hiện tại (Hiển thị tên đơn vị)
CapBacHienTaiID: Cấp bậc hiện tại (Hiển thị tên cấp bậc)
ChucVuHienTaiID: Chức vụ hiện tại (Hiển thị tên chức vụ)
TrangThaiHoSo: Trạng thái Hồ sơ (Hiển thị tên trạng thái - có thể dùng CustomChip nhỏ nếu muốn)
Email: Địa chỉ email
SoDienThoai: Số điện thoại
NgayTaoHSCN: Ngày tạo hồ sơ (Định dạng: dd/MM/yyyy HH:mm:ss)
NguoiTaoHSCNID: Người tạo hồ sơ (Hiển thị tên người tạo/username)
NgayCapNhatHSCN: Ngày cập nhật lần cuối (Định dạng: dd/MM/yyyy HH:mm:ss)
NguoiCapNhatHSCNID: Người cập nhật lần cuối (Hiển thị tên người cập nhật/username)
TypeScript

// src\views\quan-ly-quan-nhan\chi-tiet\components\tabs\TabThongTinChung.tsx
'use client';

import React from 'react';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import Divider from '@mui/material/Divider'; // Tùy chọn để phân cách rõ hơn

import { QuanNhanBaseType, quanNhanStatusChiTietObj } from '../../mockDataChiTiet'; // Hoặc từ types chung
import CustomChip from '@core/components/mui/chip'; // Để hiển thị trạng thái hồ sơ

interface TabThongTinChungProps {
data: QuanNhanBaseType;
}

// Helper function để định dạng ngày tháng (Ví dụ, cần 1 thư viện như date-fns cho việc này)
const formatDate = (dateString?: string, includeTime: boolean = false): string => {
if (!dateString) return 'N/A';
try {
const date = new Date(dateString);
if (isNaN(date.getTime())) return 'Ngày không hợp lệ';

    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0'); // Tháng trong JS từ 0-11
    const year = date.getFullYear();

    if (includeTime) {
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      return `<span class="math-inline">\{day\}/</span>{month}/${year} <span class="math-inline">\{hours\}\:</span>{minutes}:${seconds}`;
    }
    return `<span class="math-inline">\{day\}/</span>{month}/${year}`;

} catch (error) {
return 'Ngày không hợp lệ';
}
};

// Hàm giả định map ID sang tên - Sẽ cần logic thực tế hoặc dữ liệu đầy đủ hơn từ danh mục
const mapIdToNameForDisplay = (id?: string, type?: 'donVi' | 'capBac' | 'chucVu' | 'nguoiDung'): string => {
if (!id) return 'N/A';
if (type === 'donVi') return `Tên Đơn vị ${id}`;
if (type === 'capBac') return `Tên Cấp bậc ${id}`;
if (type === 'chucVu') return `Tên Chức vụ ${id}`;
if (type === 'nguoiDung') return `Người dùng ${id}`; // Hoặc tên đầy đủ nếu có
return id;
};

const DataRow: React.FC<{ label: string; value?: string | React.ReactNode }> = ({ label, value }) => (
<Grid item xs={12} sm={6}>
<Box sx={{ display: 'flex', mb: 1.5 }}>
<Typography variant="subtitle2" sx={{ minWidth: '180px', color: 'text.secondary', mr: 2 }}>
{label}:
</Typography>
{typeof value === 'string' ? (
<Typography variant="body2" sx={{ color: 'text.primary' }}>
{value || 'N/A'}
</Typography>
) : (
value || <Typography variant="body2" sx={{ color: 'text.primary' }}>N/A</Typography>
)}
</Box>
</Grid>
);

const TabThongTinChung = ({ data }: TabThongTinChungProps) => {
const statusDisplay = quanNhanStatusChiTietObj[data.TrangThaiHoSo] || {
label: data.TrangThaiHoSo,
color: 'default'
};

return (
<Box>
<Typography variant="h6" sx={{ mb: 3, color: 'primary.main' }}>
Thông tin Chung về Quân nhân
</Typography>
<Grid container spacing={2}>
<DataRow label="Số hiệu Quân nhân" value={data.SoHieuQuanNhan} />
<DataRow label="Họ và tên Khai sinh" value={data.HoVaTenKhaiSinh} />
<DataRow label="Tên thường dùng" value={data.TenThuongDung} />
<DataRow label="Ngày sinh" value={formatDate(data.NgaySinh)} />
<DataRow label="Giới tính" value={data.GioiTinh} />
<DataRow label="Số CCCD/CMT Quân đội" value={data.SoCCCD_CMTQD} />
<DataRow label="Ngày nhập ngũ" value={formatDate(data.NgayNhapNgu)} />
<DataRow label="Đơn vị hiện tại" value={mapIdToNameForDisplay(data.DonViID, 'donVi')} />
<DataRow label="Cấp bậc hiện tại" value={mapIdToNameForDisplay(data.CapBacHienTaiID, 'capBac')} />
<DataRow label="Chức vụ hiện tại" value={mapIdToNameForDisplay(data.ChucVuHienTaiID, 'chucVu')} />
<DataRow
label="Trạng thái Hồ sơ"
value={
<CustomChip
                    label={statusDisplay.label}
                    color={statusDisplay.color}
                    skin="light"
                    size="small"
                    rounded
                />
}
/>
<DataRow label="Địa chỉ Email" value={data.Email} />
<DataRow label="Số điện thoại" value={data.SoDienThoai} />
</Grid>

      <Divider sx={{ my: 3 }} />

      <Typography variant="h6" sx={{ mb: 3, color: 'primary.main' }}>
        Thông tin Quản lý Hồ sơ
      </Typography>
      <Grid container spacing={2}>
        <DataRow label="Ngày tạo Hồ sơ" value={formatDate(data.NgayTaoHSCN, true)} />
        <DataRow label="Người tạo Hồ sơ" value={mapIdToNameForDisplay(data.NguoiTaoHSCNID, 'nguoiDung')} />
        <DataRow label="Ngày cập nhật cuối" value={formatDate(data.NgayCapNhatHSCN, true)} />
        <DataRow label="Người cập nhật cuối" value={mapIdToNameForDisplay(data.NguoiCapNhatHSCNID, 'nguoiDung')} />
      </Grid>
    </Box>

);
};

export default TabThongTinChung;
Bước 8: Xây dựng TabPanel cho "Lý lịch Cá nhân"
Cập nhật mockDataChiTiet.ts: Thêm cấu trúc và dữ liệu mẫu cho LyLichCaNhanType.

TypeScript

// src\views\quan-ly-quan-nhan\chi-tiet\mockDataChiTiet.ts (Thêm/Cập nhật)

export interface DiaChiType {
XaID?: string; // Sẽ map sang tên
HuyenID?: string; // Sẽ map sang tên
TinhID?: string; // Sẽ map sang tên
DiaChiChiTiet?: string; // Thêm nếu có địa chỉ chi tiết hơn
}

export interface LyLichCaNhanType {
QueQuan?: DiaChiType;
NoiOHienNay?: DiaChiType;
DanTocID?: string; // Sẽ map sang tên
TonGiaoID?: string; // Sẽ map sang tên
ThanhPhanGiaDinhID?: string; // Sẽ map sang tên
NgheNghiepTruocNhapNguID?: string; // Sẽ map sang tên
TrinhDoVanHoaPhoThong?: string; // Ví dụ: "12/12"
TrinhDoLyLuanChinhTriID?: string; // Sẽ map sang tên
TrinhDoNgoaiNgu?: string; // Ví dụ: "Tiếng Anh B1, Tiếng Nga A2"
TrinhDoTinHoc?: string; // Ví dụ: "MOS Master, Chứng chỉ Tin học VP"
NangKhieuSoTruong?: string;
TinhHinhChinhTriLichSuBanThan?: string; // Text dài
NgayVaoDangChinhThuc?: string; // ISO Date string
NgayVaoDoan?: string; // ISO Date string
SoHoKhau?: string;
TinhTrangHonNhanID?: string; // Sẽ map sang tên
GhiChuLyLich?: string;
}

// Trong mockQuanNhanChiTietData:
// ...
// lyLichCaNhan: {
// QueQuan: { TinhID: 'T01', HuyenID: 'H0101', XaID: 'X010101', DiaChiChiTiet: 'Thôn A, Xã B' },
// NoiOHienNay: { TinhID: 'T02', HuyenID: 'H0201', XaID: 'X020101', DiaChiChiTiet: 'Số nhà 123, Đường XYZ' },
// DanTocID: 'DT01', // Kinh
// TonGiaoID: 'TG01', // Không
// ThanhPhanGiaDinhID: 'TPGD02', // Nông dân
// NgheNghiepTruocNhapNguID: 'NN05', // Học sinh, Sinh viên
// TrinhDoVanHoaPhoThong: '12/12',
// TrinhDoLyLuanChinhTriID: 'LLCT01', // Sơ cấp
// TrinhDoNgoaiNgu: 'Tiếng Anh - B1',
// TrinhDoTinHoc: 'Ứng dụng CNTT cơ bản',
// NangKhieuSoTruong: 'Bóng đá, Ca hát',
// TinhHinhChinhTriLichSuBanThan: 'Không có vấn đề gì đặc biệt về lịch sử chính trị bản thân. Chấp hành tốt chủ trương, đường lối của Đảng, chính sách, pháp luật của Nhà nước.',
// NgayVaoDangChinhThuc: '2018-05-19T00:00:00Z',
// NgayVaoDoan: '2008-03-26T00:00:00Z',
// SoHoKhau: 'SHK12345678',
// TinhTrangHonNhanID: 'TTHN02', // Đã kết hôn
// GhiChuLyLich: 'Không có ghi chú đặc biệt.'
// } as LyLichCaNhanType, // Ép kiểu để TS không báo lỗi khi mock
// ...
(Bạn cần định nghĩa LyLichCaNhanType đầy đủ và điền dữ liệu mẫu tương ứng trong mockQuanNhanChiTietData)

Tạo file src\views\quan-ly-quan-nhan\chi-tiet\components\tabs\TabLyLichCaNhan.tsx

Mục đích: Hiển thị chi tiết thông tin lý lịch.

Props:

data?: LyLichCaNhanType (Lưu ý data có thể là undefined nếu quân nhân chưa có thông tin này).
Component Vuexy (MUI): Grid, Typography, Box, Divider.

Layout: Dạng danh sách key-value hoặc grid 2 cột, có thể nhóm các trường liên quan (ví dụ: Quê quán, Nơi ở hiện tại là các nhóm riêng).

Các trường dữ liệu hiển thị (read-only):

Quê quán:
QueQuan_XaID: Xã/Phường (Hiển thị tên)
QueQuan_HuyenID: Quận/Huyện (Hiển thị tên)
QueQuan_TinhID: Tỉnh/Thành phố (Hiển thị tên)
(QueQuan_DiaChiChiTiet nếu có)
Nơi ở hiện tại:
NoiOHienNay_XaID: Xã/Phường (Hiển thị tên)
NoiOHienNay_HuyenID: Quận/Huyện (Hiển thị tên)
NoiOHienNay_TinhID: Tỉnh/Thành phố (Hiển thị tên)
(NoiOHienNay_DiaChiChiTiet nếu có)
DanTocID: Dân tộc (Hiển thị tên dân tộc)
TonGiaoID: Tôn giáo (Hiển thị tên tôn giáo)
ThanhPhanGiaDinhID: Thành phần gia đình khi nhập ngũ (Hiển thị tên thành phần)
NgheNghiepTruocNhapNguID: Nghề nghiệp trước khi nhập ngũ (Hiển thị tên nghề nghiệp)
TrinhDoVanHoaPhoThong: Trình độ văn hóa phổ thông
TrinhDoLyLuanChinhTriID: Trình độ lý luận chính trị (Hiển thị tên trình độ)
TrinhDoNgoaiNgu: Trình độ ngoại ngữ
TrinhDoTinHoc: Trình độ tin học
NangKhieuSoTruong: Năng khiếu, sở trường
TinhHinhChinhTriLichSuBanThan: Đặc điểm lịch sử bản thân (hiển thị dạng text dài, có thể dùng Typography với whiteSpace: 'pre-line')
NgayVaoDangChinhThuc: Ngày vào Đảng chính thức (Định dạng dd/MM/yyyy)
NgayVaoDoan: Ngày vào Đoàn TNCS Hồ Chí Minh (Định dạng dd/MM/yyyy)
SoHoKhau: Số sổ hộ khẩu
TinhTrangHonNhanID: Tình trạng hôn nhân (Hiển thị tên tình trạng)
GhiChuLyLich: Ghi chú thêm về lý lịch
TypeScript

// src\views\quan-ly-quan-nhan\chi-tiet\components\tabs\TabLyLichCaNhan.tsx
'use client';

import React from 'react';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import Divider from '@mui/material/Divider';

import { LyLichCaNhanType, DiaChiType } from '../../mockDataChiTiet'; // Hoặc từ types chung

interface TabLyLichCaNhanProps {
data?: LyLichCaNhanType;
}

// Helper function (tương tự TabThongTinChung)
const formatDateForLyLich = (dateString?: string): string => {
if (!dateString) return 'N/A';
try {
const date = new Date(dateString);
if (isNaN(date.getTime())) return 'Ngày không hợp lệ';
const day = String(date.getDate()).padStart(2, '0');
const month = String(date.getMonth() + 1).padStart(2, '0');
const year = date.getFullYear();
return `<span class="math-inline">\{day\}/</span>{month}/${year}`;
} catch (error) {
return 'Ngày không hợp lệ';
}
};

const mapIdToNameForLyLich = (id?: string, type?: 'diaGioi' | 'danToc' | 'tonGiao' | 'thanhPhanGD' | 'ngheNghiep' | 'trinhDoLLCT' | 'tinhTrangHonNhan'): string => {
if (!id) return 'N/A';
// Đây là ví dụ, bạn cần có logic map thực tế từ danh mục
if (type === 'diaGioi') return `Tên ${id}`;
if (type === 'danToc') return `Dân tộc ${id}`;
if (type === 'tonGiao') return `Tôn giáo ${id}`;
// ... các type khác
return id;
};

const displayDiaChi = (diaChi?: DiaChiType, labelPrefix: string = ""): React.ReactNode => {
if (!diaChi) return <Typography variant="body2">N/A</Typography>;
const { XaID, HuyenID, TinhID, DiaChiChiTiet } = diaChi;
let fullAddress = '';
if (DiaChiChiTiet) fullAddress += `${DiaChiChiTiet}, `;
if (XaID) fullAddress += `${mapIdToNameForLyLich(XaID, 'diaGioi')}, `;
if (HuyenID) fullAddress += `${mapIdToNameForLyLich(HuyenID, 'diaGioi')}, `;
if (TinhID) fullAddress += `${mapIdToNameForLyLich(TinhID, 'diaGioi')}`;
return fullAddress.trim().replace(/,$/, '') || 'N/A';
};

const DataRowLyLich: React.FC<{ label: string; value?: string | React.ReactNode; fullWidthValue?: boolean }> = ({ label, value, fullWidthValue }) => (
<Grid item xs={12} sm={fullWidthValue ? 12 : 6}>
<Box sx={{ display: 'flex', flexDirection: fullWidthValue ? 'column' : 'row', mb: 1.5 }}>
<Typography variant="subtitle2" sx={{ minWidth: fullWidthValue ? 'auto' : '200px', color: 'text.secondary', mr: fullWidthValue ? 0 : 2, mb: fullWidthValue ? 0.5 : 0 }}>
{label}:
</Typography>
{typeof value === 'string' ? (
<Typography variant="body2" sx={{ color: 'text.primary', whiteSpace: fullWidthValue ? 'pre-line' : 'normal' }}>
{value || 'N/A'}
</Typography>
) : (
value || <Typography variant="body2" sx={{ color: 'text.primary' }}>N/A</Typography>
)}
</Box>
</Grid>
);

const TabLyLichCaNhan = ({ data }: TabLyLichCaNhanProps) => {
if (!data) {
return <Typography sx={{p: 2}}>Chưa có thông tin Lý lịch Cá nhân.</Typography>;
}

return (
<Box>
<Typography variant="h6" sx={{ mb: 3, color: 'primary.main' }}>
Thông tin Lý lịch Cá nhân
</Typography>
<Grid container spacing={2}>
<DataRowLyLich label="Quê quán" value={displayDiaChi(data.QueQuan)} />
<DataRowLyLich label="Nơi ở hiện tại" value={displayDiaChi(data.NoiOHienNay)} />
<DataRowLyLich label="Dân tộc" value={mapIdToNameForLyLich(data.DanTocID, 'danToc')} />
<DataRowLyLich label="Tôn giáo" value={mapIdToNameForLyLich(data.TonGiaoID, 'tonGiao')} />
<DataRowLyLich label="Thành phần gia đình khi nhập ngũ" value={mapIdToNameForLyLich(data.ThanhPhanGiaDinhID, 'thanhPhanGD')} />
<DataRowLyLich label="Nghề nghiệp trước khi nhập ngũ" value={mapIdToNameForLyLich(data.NgheNghiepTruocNhapNguID, 'ngheNghiep')} />
<DataRowLyLich label="Trình độ văn hóa phổ thông" value={data.TrinhDoVanHoaPhoThong} />
<DataRowLyLich label="Trình độ Lý luận Chính trị" value={mapIdToNameForLyLich(data.TrinhDoLyLuanChinhTriID, 'trinhDoLLCT')} />
<DataRowLyLich label="Trình độ Ngoại ngữ" value={data.TrinhDoNgoaiNgu} />
<DataRowLyLich label="Trình độ Tin học" value={data.TrinhDoTinHoc} />
<DataRowLyLich label="Năng khiếu, sở trường" value={data.NangKhieuSoTruong} />
<DataRowLyLich label="Ngày vào Đảng chính thức" value={formatDateForLyLich(data.NgayVaoDangChinhThuc)} />
<DataRowLyLich label="Ngày vào Đoàn TNCS HCM" value={formatDateForLyLich(data.NgayVaoDoan)} />
<DataRowLyLich label="Số sổ Hộ khẩu" value={data.SoHoKhau} />
<DataRowLyLich label="Tình trạng Hôn nhân" value={mapIdToNameForLyLich(data.TinhTrangHonNhanID, 'tinhTrangHonNhan')} />
</Grid>

      <Divider sx={{ my: 3 }} />
      <Typography variant="subtitle1" sx={{ mb: 1, color: 'text.primary', fontWeight: 'medium' }}>
        Đặc điểm Lịch sử bản thân:
      </Typography>
      <Typography variant="body2" sx={{ color: 'text.primary', whiteSpace: 'pre-line', mb: 2, p:1, border: '1px dashed', borderColor: 'divider', borderRadius: 1}}>
        {data.TinhHinhChinhTriLichSuBanThan || 'N/A'}
      </Typography>

      <Typography variant="subtitle1" sx={{ mb: 1, color: 'text.primary', fontWeight: 'medium' }}>
        Ghi chú thêm về Lý lịch:
      </Typography>
      <Typography variant="body2" sx={{ color: 'text.primary', whiteSpace: 'pre-line', p:1, border: '1px dashed', borderColor: 'divider', borderRadius: 1}}>
        {data.GhiChuLyLich || 'N/A'}
      </Typography>
    </Box>

);
};

export default TabLyLichCaNhan;
Bước 9: Cập nhật KhuVucTabsChiTiet.tsx để sử dụng các TabPanel mới
Chỉnh sửa file src\views\quan-ly-quan-nhan\chi-tiet\components\KhuVucTabsChiTiet.tsx để import và sử dụng TabThongTinChung và TabLyLichCaNhan.

TypeScript

// src\views\quan-ly-quan-nhan\chi-tiet\components\KhuVucTabsChiTiet.tsx
// ... (các imports khác)
import TabThongTinChung from './tabs/TabThongTinChung';
import TabLyLichCaNhan from './tabs/TabLyLichCaNhan';
// ... (import các tab khác khi tạo xong)

// ... (interface TabsProps)

const KhuVucTabsChiTiet = ({ quanNhanData, activeTab, handleTabChange }: TabsProps) => {
const tabContentList: { [key: string]: React.ReactNode } = {
'thong-tin-chung': <TabThongTinChung data={quanNhanData.baseInfo} />,
'ly-lich-ca-nhan': <TabLyLichCaNhan data={quanNhanData.lyLichCaNhan} />, // Truyền lyLichCaNhan từ quanNhanData
'qua-trinh-cong-tac': <div>Nội dung Tab Quá trình Công tác</div>,
'qua-trinh-dao-tao': <div>Nội dung Tab Quá trình Đào tạo</div>,
'khen-thuong': <div>Nội dung Tab Khen thưởng</div>,
'ky-luat': <div>Nội dung Tab Kỷ luật</div>,
'suc-khoe': <div>Nội dung Tab Sức khỏe</div>,
'quan-he-gia-dinh': <div>Nội dung Tab Quan hệ Gia đình</div>,
'che-do-chinh-sach': <div>Nội dung Tab Chế độ Chính sách</div>,
'lich-su-thay-doi': <div>Nội dung Tab Lịch sử Thay đổi</div>,
};

return (
<TabContext value={activeTab}>
<Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
<TabList
          onChange={handleTabChange}
          aria-label="Chi tiết hồ sơ quân nhân"
          variant="scrollable"
          scrollButtons="auto"
        >
<Tab label="Thông tin Chung" value="thong-tin-chung" />
<Tab label="Lý lịch Cá nhân" value="ly-lich-ca-nhan" />
<Tab label="Quá trình Công tác" value="qua-trinh-cong-tac" />
<Tab label="Quá trình Đào tạo" value="qua-trinh-dao-tao" />
<Tab label="Khen thưởng" value="khen-thuong" />
<Tab label="Kỷ luật" value="ky-luat" />
<Tab label="Sức khỏe" value="suc-khoe" />
<Tab label="Quan hệ Gia đình" value="quan-he-gia-dinh" />
<Tab label="Chế độ Chính sách" value="che-do-chinh-sach" />
<Tab label="Lịch sử Thay đổi" value="lich-su-thay-doi" />
</TabList>
</Box>
{Object.keys(tabContentList).map(tabValue => (
<TabPanel key={tabValue} value={tabValue} sx={{ p: 0 }}>
<CardContent> {/_ Giữ CardContent để nhất quán với Vuexy _/}
{tabContentList[tabValue]}
</CardContent>
</TabPanel>
))}
</TabContext>
);
};

export default KhuVucTabsChiTiet;
