---
trigger: always_on
---

# Project Structure
- Pages follow path structure src\app\[lang]\(dashboard)\page-name with existing folders for bang-dieu-khien, quan-ly-quan-nhan, and cong-tac-chinh-tri with various subfolders.
- Component names should be in Vietnamese without diacritics, following parent component naming style.
- When implementing new features, strictly clone existing component structures and only modify specific data, maintaining the same component organization and patterns.

# UI Design Preferences
- User prefers interfaces with minimal repetition, aesthetically pleasing layouts, and good content organization.
- Form field labels should be positioned inside input fields and elements aligned in a straight line.
- User prefers high contrast text colors for better readability and white-colored action buttons.
- Tables should have fewer visible columns to avoid horizontal scrolling with compact layouts and shorter field widths.
- Dialog components should maintain consistent structure, layout, colors, spacing and typography across the application.
- Use Chip components for status displays in tables and lists.
- User prefers horizontal layouts for filter components with 'Duyệt theo cấu trúc' taking 30% width and 'Bộ lọc' taking 70%, with data tables placed below using full width to maximize visible data without horizontal scrolling.
- User prefers flexible, balanced layouts using relative CSS units instead of fixed calculations like calc(100vh - 300px), ensuring no wasted space while maintaining proper functionality.

# Component Implementation Guidelines
- Use AppReactDatepicker with format 'dd/MM/yyyy' for date fields instead of basic date inputs or dropdown selects.
- Use TablePaginationComponent from src\components\shared\TablePagination.tsx for all data tables to ensure UI consistency.
- Use DialogXacNhanXoaItem for confirmation dialogs instead of creating new ones.
- User prefers react-toastify for notifications instead of react-hot-toast.
- TreeView component from @mui/lab is deprecated and should be migrated to @mui/x-tree-view package.
- User prefers gộp chức năng thêm mới và chỉnh sửa vào cùng một component dialog (e.g., DialogThemSuaVanKien.tsx).

# Implementation Workflow
- Research existing components in related modules before implementation.
- Analyze similar list/table/dialog components for reference.
- Prioritize reusing existing UI components rather than creating new ones.
- Maintain naming conventions and file structure consistency.
- Build basic files first and mark them as "đang phát triển" before completing each module step-by-step.
- Create reusable shared components in src\components\shared directory to ensure UI consistency.

# Military Personnel Management
- The listing page should include a data table with search/filter capabilities, pagination, and action buttons.
- Data entry forms should include multiple tabs/sections with validation and action buttons.
- Portrait image sections should avoid excessive empty space; position image in corner with proper aspect ratio (2:3).
- The edit page should match the layout of the add new page with basic information at top and personal background below.

# Party Organization Management
- Implement TreeView components with proper expand/collapse functionality and visual hierarchy indicators.
- Dialog components for adding/editing/deleting terms and committee members should maintain consistency with existing patterns.
- When implementing the 'Add New Party Organization' feature, create a dialog component with necessary form fields.

# Party Document Management
- Implement document list with search/filter functionality before other features.
- Create dialog components for uploading new documents, editing metadata, and managing document status.
- User wants to implement a 'Browse by folder structure' feature using TreeView components.
- Data tables should not have row click events that open detail dialogs; use dedicated action buttons instead.

# Party Meeting Management
- When implementing the 'Chi tiết Buổi Sinh hoạt Đảng' page, focus on header, tab list, general info tab, and placeholder tabs.
- Implement tabs for 'Diễn biến & Biên bản' and 'Nghị quyết' with appropriate fields and components.
- User prefers using dialogs for adding/editing records instead of navigating to separate pages.
