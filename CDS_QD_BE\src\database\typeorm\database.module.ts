import { Global, Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import { DatabaseService } from '~/database/typeorm/database.service';
import { AccountEntity } from '~/database/typeorm/entities/account.entity';
import { DepartmentEntity } from '~/database/typeorm/entities/department.entity';
import { MediaEntity } from '~/database/typeorm/entities/media.entity';
import { PermissionEntity } from '~/database/typeorm/entities/permission.entity';
import { ProviderEntity } from '~/database/typeorm/entities/provider.entity';
import { RoleEntity } from '~/database/typeorm/entities/role.entity';
import { UserEntity } from '~/database/typeorm/entities/user.entity';
import { UserLogEntity } from '~/database/typeorm/entities/userLog.entity';
import { WarehouseEntity } from '~/database/typeorm/entities/warehouse.entity';
import { WarehouseTypeEntity } from '~/database/typeorm/entities/warehouseType.entity';
import { AccountRepository } from '~/database/typeorm/repositories/account.repository';
import { DepartmentRepository } from '~/database/typeorm/repositories/department.repository';
import { MediaRepository } from '~/database/typeorm/repositories/media.repository';
import { PermissionRepository } from '~/database/typeorm/repositories/permission.repository';
import { ProviderRepository } from '~/database/typeorm/repositories/provider.repository';
import { RoleRepository } from '~/database/typeorm/repositories/role.repository';
import { UserRepository } from '~/database/typeorm/repositories/user.repository';
import { UserLogRepository } from '~/database/typeorm/repositories/userLog.repository';
import { WarehouseRepository } from '~/database/typeorm/repositories/warehouse.repository';
import { WarehouseTypeRepository } from '~/database/typeorm/repositories/warehouseType.repository';
import { LoaiDanhMucEntity } from '~/database/typeorm/entities/loaiDanhMuc.entity';
import { LoaiDanhMucRepository } from '~/database/typeorm/repositories/loaiDanhMuc.repository';
import { GiaTriDanhMucEntity } from '~/database/typeorm/entities/giaTriDanhMuc.entity';
import { GiaTriDanhMucRepository } from '~/database/typeorm/repositories/giaTriDanhMuc.repository';
import { ThamSoHeThongEntity } from '~/database/typeorm/entities/thamSoHeThong.entity';
import { ThamSoHeThongRepository } from '~/database/typeorm/repositories/thamSoHeThong.repository';
import { RolesPermissionEntity } from '~/database/typeorm/entities/rolesPermission.entity';
import { RolesPermissionRepository } from '~/database/typeorm/repositories/rolesPermission.repository';
import { QuanNhanEntity } from '~/database/typeorm/entities/quanNhan.entity';
import { QuanNhanRepository } from '~/database/typeorm/repositories/quanNhan.repository';
import { LyLichCanNhanEntity } from '~/database/typeorm/entities/lyLichCanNhan.entity';
import { LyLichCanNhanRepository } from '~/database/typeorm/repositories/lyLichCanNhan.repository';
import { QuaTrinhCongTacEntity } from '~/database/typeorm/entities/quaTrinhCongTac.entity';
import { QuaTrinhCongTacRepository } from '~/database/typeorm/repositories/quaTrinhCongTac.repository';
import { QuaTrinhDaoTaoEntity } from '~/database/typeorm/entities/quaTrinhDaoTao.entity';
import { QuaTrinhDaoTaoRepository } from '~/database/typeorm/repositories/quaTrinhDaoTao.repository';
import { DeXuatKhenThuongEntity } from '~/database/typeorm/entities/deXuatKhenThuong.entity';
import { DeXuatKhenThuongRepository } from '~/database/typeorm/repositories/deXuatKhenThuong.repository';
import { QuyetDinhKhenThuongEntity } from '~/database/typeorm/entities/quyetDinhKhenThuong.entity';
import { QuyetDinhKhenThuongRepository } from '~/database/typeorm/repositories/quyetDinhKhenThuong.repository';
import { HoSoVuViecKyLuatEntity } from '~/database/typeorm/entities/hoSoVuViecKyLuat.entity';
import { HoSoVuViecKyLuatRepository } from '~/database/typeorm/repositories/hoSoVuViecKyLuat.repository';
import { BienBanHoiDongKyLuatEntity } from '~/database/typeorm/entities/bienBanHoiDongKyLuat.entity';
import { BienBanHoiDongKyLuatRepository } from '~/database/typeorm/repositories/bienBanHoiDongKyLuat.repository';
import { TaiLieuVuViecKyLuatEntity } from '~/database/typeorm/entities/taiLieuVuViecKyLuat.entity';
import { TaiLieuVuViecKyLuatRepository } from '~/database/typeorm/repositories/taiLieuVuViecKyLuat.repository';
import { QuyetDinhKyLuatEntity } from '~/database/typeorm/entities/quyetDinhKyLuat.entity';
import { QuyetDinhKyLuatRepository } from '~/database/typeorm/repositories/quyetDinhKyLuat.repository';
import { HoSoSucKhoeEntity } from '~/database/typeorm/entities/hoSoSucKhoe.entity';
import { HoSoSucKhoeRepository } from '~/database/typeorm/repositories/hoSoSucKhoe.repository';
import { QuanHeGiaDinhEntity } from '~/database/typeorm/entities/quanHeGiaDinh.entity';
import { QuanHeGiaDinhRepository } from '~/database/typeorm/repositories/quanHeGiaDinh.repository';
import { TheoDoiCheDoChinhSachEntity } from '~/database/typeorm/entities/theoDoiCheDoChinhSach.entity';
import { TheoDoiCheDoChinhSachRepository } from '~/database/typeorm/repositories/theoDoiCheDoChinhSach.repository';
import { DangVienEntity } from '~/database/typeorm/entities/dangVien.entity';
import { DangVienRepository } from '~/database/typeorm/repositories/dangVien.repository';
import { HoSoDangVienEntity } from '~/database/typeorm/entities/hoSoDangVien.entity';
import { HoSoDangVienRepository } from '~/database/typeorm/repositories/hoSoDangVien.repository';
import { DeXuatKhenThuongDangVienEntity } from '~/database/typeorm/entities/deXuatKhenThuongDangVien.entity';
import { DeXuatKhenThuongDangVienRepository } from '~/database/typeorm/repositories/deXuatKhenThuongDangVien.repository';
import { QuyetDinhKhenThuongDangVienEntity } from '~/database/typeorm/entities/quyetDinhKhenThuongDangVien.entity';
import { QuyetDinhKhenThuongDangVienRepository } from '~/database/typeorm/repositories/quyetDinhKhenThuongDangVien.repository';
import { BanKiemDiemDangVienEntity } from '~/database/typeorm/entities/banKiemDiemDangVien.entity';
import { BanKiemDiemDangVienRepository } from '~/database/typeorm/repositories/banKiemDiemDangVien.repository';
import { HoSoVuViecKyLuatDangVienEntity } from '~/database/typeorm/entities/hoSoVuViecKyLuatDangVien.entity';
import { HoSoVuViecKyLuatDangVienRepository } from '~/database/typeorm/repositories/hoSoVuViecKyLuatDangVien.repository';
import { BienBanHoiDongKyLuatDangEntity } from '~/database/typeorm/entities/bienBanHoiDongKyLuatDang.entity';
import { BienBanHoiDongKyLuatDangRepository } from '~/database/typeorm/repositories/bienBanHoiDongKyLuatDang.repository';
import { QuyetDinhKyLuatDangVienEntity } from '~/database/typeorm/entities/quyetDinhKyLuatDangVien.entity';
import { QuyetDinhKyLuatDangVienRepository } from '~/database/typeorm/repositories/quyetDinhKyLuatDangVien.repository';
import { CapUyNhiemKyEntity } from '~/database/typeorm/entities/capUyNhiemKy.entity';
import { CapUyNhiemKyRepository } from '~/database/typeorm/repositories/capUyNhiemKy.repository';
import { ThanhVienCapUyEntity } from '~/database/typeorm/entities/thanhVienCapUy.entity';
import { ThanhVienCapUyRepository } from '~/database/typeorm/repositories/thanhVienCapUy.repository';
import { KeHoachSinhHoatDangEntity } from '~/database/typeorm/entities/keHoachSinhHoatDang.entity';
import { KeHoachSinhHoatDangRepository } from '~/database/typeorm/repositories/keHoachSinhHoatDang.repository';
import { BuoiSinhHoatDangEntity } from '~/database/typeorm/entities/buoiSinhHoatDang.entity';
import { BuoiSinhHoatDangRepository } from '~/database/typeorm/repositories/buoiSinhHoatDang.repository';
import { NghiQuyetDangEntity } from '~/database/typeorm/entities/nghiQuyetDang.entity';
import { NghiQuyetDangRepository } from '~/database/typeorm/repositories/nghiQuyetDang.repository';
import { HoSoPhatTrienDangVienEntity } from '~/database/typeorm/entities/hoSoPhatTrienDangVien.entity';
import { HoSoPhatTrienDangVienRepository } from '~/database/typeorm/repositories/hoSoPhatTrienDangVien.repository';
import { DanhGiaXepLoaiDangVienEntity } from '~/database/typeorm/entities/danhGiaXepLoaiDangVien.entity';
import { DanhGiaXepLoaiDangVienRepository } from '~/database/typeorm/repositories/danhGiaXepLoaiDangVien.repository';
import { DanhGiaXepLoaiToChucDangEntity } from '~/database/typeorm/entities/danhGiaXepLoaiToChucDang.entity';
import { DanhGiaXepLoaiToChucDangRepository } from '~/database/typeorm/repositories/danhGiaXepLoaiToChucDang.repository';
import { ThuChiDangPhiEntity } from '~/database/typeorm/entities/thuChiDangPhi.entity';
import { ThuChiDangPhiRepository } from '~/database/typeorm/repositories/thuChiDangPhi.repository';
import { KeHoachKiemTraGiamSatDangEntity } from '~/database/typeorm/entities/keHoachKiemTraGiamSatDang.entity';
import { KeHoachKiemTraGiamSatDangRepository } from '~/database/typeorm/repositories/keHoachKiemTraGiamSatDang.repository';
import { CuocKiemTraGiamSatDangEntity } from '~/database/typeorm/entities/cuocKiemTraGiamSatDang.entity';
import { CuocKiemTraGiamSatDangRepository } from '~/database/typeorm/repositories/cuocKiemTraGiamSatDang.repository';
import { KeHoachGiaoDucChinhTriEntity } from '~/database/typeorm/entities/keHoachGiaoDucChinhTri.entity';
import { KeHoachGiaoDucChinhTriRepository } from '~/database/typeorm/repositories/keHoachGiaoDucChinhTri.repository';
import { HoatDongGiaoDucChinhTriEntity } from '~/database/typeorm/entities/hoatDongGiaoDucChinhTri.entity';
import { HoatDongGiaoDucChinhTriRepository } from '~/database/typeorm/repositories/hoatDongGiaoDucChinhTri.repository';
import { ThamGiaGiaoDucChinhTriEntity } from '~/database/typeorm/entities/thamGiaGiaoDucChinhTri.entity';
import { ThamGiaGiaoDucChinhTriRepository } from '~/database/typeorm/repositories/thamGiaGiaoDucChinhTri.repository';
import { KetQuaHocTapChinhTriEntity } from '~/database/typeorm/entities/ketQuaHocTapChinhTri.entity';
import { KetQuaHocTapChinhTriRepository } from '~/database/typeorm/repositories/ketQuaHocTapChinhTri.repository';
import { GhiNhanTinhHinhTuTuongEntity } from '~/database/typeorm/entities/ghiNhanTinhHinhTuTuong.entity';
import { GhiNhanTinhHinhTuTuongRepository } from '~/database/typeorm/repositories/ghiNhanTinhHinhTuTuong.repository';
import { BienPhapTacDongTuTuongEntity } from '~/database/typeorm/entities/bienPhapTacDongTuTuong.entity';
import { BienPhapTacDongTuTuongRepository } from '~/database/typeorm/repositories/bienPhapTacDongTuTuong.repository';
import { KeHoachTuyenTruyenVanHoaEntity } from '~/database/typeorm/entities/keHoachTuyenTruyenVanHoa.entity';
import { KeHoachTuyenTruyenVanHoaRepository } from '~/database/typeorm/repositories/keHoachTuyenTruyenVanHoa.repository';
import { HoatDongTuyenTruyenVanHoaEntity } from '~/database/typeorm/entities/hoatDongTuyenTruyenVanHoa.entity';
import { HoatDongTuyenTruyenVanHoaRepository } from '~/database/typeorm/repositories/hoatDongTuyenTruyenVanHoa.repository';
import { PhongTraoThiDuaChinhTriEntity } from '~/database/typeorm/entities/phongTraoThiDuaChinhTri.entity';
import { PhongTraoThiDuaChinhTriRepository } from '~/database/typeorm/repositories/phongTraoThiDuaChinhTri.repository';
import { ChinhSachHauPhuongEntity } from '~/database/typeorm/entities/chinhSachHauPhuong.entity';
import { ChinhSachHauPhuongRepository } from '~/database/typeorm/repositories/chinhSachHauPhuong.repository';
import { DoiTuongChinhSachHauPhuongEntity } from '~/database/typeorm/entities/doiTuongChinhSachHauPhuong.entity';
import { DoiTuongChinhSachHauPhuongRepository } from '~/database/typeorm/repositories/doiTuongChinhSachHauPhuong.repository';
import { ThongBaoEntity } from '~/database/typeorm/entities/thongBao.entity';
import { ThongBaoRepository } from '~/database/typeorm/repositories/thongBao.repository';
import { QuyTrinhPheDuyetEntity } from '~/database/typeorm/entities/quyTrinhPheDuyet.entity';
import { QuyTrinhPheDuyetRepository } from '~/database/typeorm/repositories/quyTrinhPheDuyet.repository';
import { YeuCauPheDuyetEntity } from '~/database/typeorm/entities/yeuCauPheDuyet.entity';
import { YeuCauPheDuyetRepository } from '~/database/typeorm/repositories/yeuCauPheDuyet.repository';
import { BuocPheDuyetEntity } from '~/database/typeorm/entities/buocPheDuyet.entity';
import { BuocPheDuyetRepository } from '~/database/typeorm/repositories/buocPheDuyet.repository';
import { LichSuPheDuyetEntity } from '~/database/typeorm/entities/lichSuPheDuyet.entity';
import { LichSuPheDuyetRepository } from '~/database/typeorm/repositories/lichSuPheDuyet.repository';
import { NhatKyHeThongEntity } from '~/database/typeorm/entities/nhatKyHeThong.entity';
import { NhatKyHeThongRepository } from '~/database/typeorm/repositories/nhatKyHeThong.repository';
import { DuLieuTepTinEntity } from '~/database/typeorm/entities/duLieuTepTin.entity';
import { DuLieuTepTinRepository } from '~/database/typeorm/repositories/duLieuTepTin.repository';

const entities = [
    RoleEntity,
    UserEntity,
    PermissionEntity,
    MediaEntity,
    AccountEntity,
    DepartmentEntity,
    WarehouseEntity,
    UserLogEntity,
    WarehouseTypeEntity,
    ProviderEntity,
    LoaiDanhMucEntity,
    GiaTriDanhMucEntity,
    ThamSoHeThongEntity,
    RolesPermissionEntity,
    QuanNhanEntity,
    LyLichCanNhanEntity,
    QuaTrinhCongTacEntity,
    QuaTrinhDaoTaoEntity,
    DeXuatKhenThuongEntity,
    QuyetDinhKhenThuongEntity,
    HoSoVuViecKyLuatEntity,
    BienBanHoiDongKyLuatEntity,
    TaiLieuVuViecKyLuatEntity,
    QuyetDinhKyLuatEntity,
    HoSoSucKhoeEntity,
    QuanHeGiaDinhEntity,
    TheoDoiCheDoChinhSachEntity,
    DangVienEntity,
    HoSoDangVienEntity,
    DeXuatKhenThuongDangVienEntity,
    QuyetDinhKhenThuongDangVienEntity,
    BanKiemDiemDangVienEntity,
    HoSoVuViecKyLuatDangVienEntity,
    BienBanHoiDongKyLuatDangEntity,
    QuyetDinhKyLuatDangVienEntity,
    CapUyNhiemKyEntity,
    ThanhVienCapUyEntity,
    KeHoachSinhHoatDangEntity,
    BuoiSinhHoatDangEntity,
    NghiQuyetDangEntity,
    HoSoPhatTrienDangVienEntity,
    DanhGiaXepLoaiDangVienEntity,
    DanhGiaXepLoaiToChucDangEntity,
    ThuChiDangPhiEntity,
    KeHoachKiemTraGiamSatDangEntity,
    CuocKiemTraGiamSatDangEntity,
    KeHoachGiaoDucChinhTriEntity,
    HoatDongGiaoDucChinhTriEntity,
    ThamGiaGiaoDucChinhTriEntity,
    KetQuaHocTapChinhTriEntity,
    GhiNhanTinhHinhTuTuongEntity,
    BienPhapTacDongTuTuongEntity,
    KeHoachTuyenTruyenVanHoaEntity,
    HoatDongTuyenTruyenVanHoaEntity,
    PhongTraoThiDuaChinhTriEntity,
    ChinhSachHauPhuongEntity,
    DoiTuongChinhSachHauPhuongEntity,
    ThongBaoEntity,
    QuyTrinhPheDuyetEntity,
    YeuCauPheDuyetEntity,
    BuocPheDuyetEntity,
    LichSuPheDuyetEntity,
    NhatKyHeThongEntity,
    DuLieuTepTinEntity,
];

const repositories = [
    DepartmentRepository,
    UserRepository,
    AccountRepository,
    MediaRepository,
    PermissionRepository,
    RoleRepository,
    WarehouseRepository,
    UserLogRepository,
    WarehouseTypeRepository,
    ProviderRepository,
    LoaiDanhMucRepository,
    GiaTriDanhMucRepository,
    ThamSoHeThongRepository,
    RolesPermissionRepository,
    QuanNhanRepository,
    LyLichCanNhanRepository,
    QuaTrinhCongTacRepository,
    QuaTrinhDaoTaoRepository,
    DeXuatKhenThuongRepository,
    QuyetDinhKhenThuongRepository,
    HoSoVuViecKyLuatRepository,
    BienBanHoiDongKyLuatRepository,
    TaiLieuVuViecKyLuatRepository,
    QuyetDinhKyLuatRepository,
    HoSoSucKhoeRepository,
    QuanHeGiaDinhRepository,
    TheoDoiCheDoChinhSachRepository,
    DangVienRepository,
    HoSoDangVienRepository,
    DeXuatKhenThuongDangVienRepository,
    QuyetDinhKhenThuongDangVienRepository,
    BanKiemDiemDangVienRepository,
    HoSoVuViecKyLuatDangVienRepository,
    BienBanHoiDongKyLuatDangRepository,
    QuyetDinhKyLuatDangVienRepository,
    CapUyNhiemKyRepository,
    ThanhVienCapUyRepository,
    KeHoachSinhHoatDangRepository,
    BuoiSinhHoatDangRepository,
    NghiQuyetDangRepository,
    HoSoPhatTrienDangVienRepository,
    DanhGiaXepLoaiDangVienRepository,
    DanhGiaXepLoaiToChucDangRepository,
    ThuChiDangPhiRepository,
    KeHoachKiemTraGiamSatDangRepository,
    CuocKiemTraGiamSatDangRepository,
    KeHoachGiaoDucChinhTriRepository,
    HoatDongGiaoDucChinhTriRepository,
    ThamGiaGiaoDucChinhTriRepository,
    KetQuaHocTapChinhTriRepository,
    GhiNhanTinhHinhTuTuongRepository,
    BienPhapTacDongTuTuongRepository,
    KeHoachTuyenTruyenVanHoaRepository,
    HoatDongTuyenTruyenVanHoaRepository,
    PhongTraoThiDuaChinhTriRepository,
    ChinhSachHauPhuongRepository,
    DoiTuongChinhSachHauPhuongRepository,
    ThongBaoRepository,
    QuyTrinhPheDuyetRepository,
    YeuCauPheDuyetRepository,
    BuocPheDuyetRepository,
    LichSuPheDuyetRepository,
    NhatKyHeThongRepository,
    DuLieuTepTinRepository,
];

@Global()
@Module({
    imports: [
        TypeOrmModule.forRootAsync({
            useFactory: (configService: ConfigService) => ({
                ...configService.get('database'),
                entities,
            }),
            inject: [ConfigService],
            // dataSource receives the configured DataSourceOptions
            // and returns a Promise<DataSource>.
            dataSourceFactory: async (options) => {
                const dataSource = await new DataSource(options).initialize();
                return dataSource;
            },
        }),
        // TypeOrmModule.forFeature(entities),
    ],
    providers: [DatabaseService, ...repositories],
    exports: [DatabaseService],
})
export class DatabaseModule {}
