Bước 39: <PERSON><PERSON><PERSON> nhật mockDataChiTiet.ts cho "Lịch sử Thay đổi Hồ sơ"
Định nghĩa AuditLogEntryType:

Thêm dữ liệu mẫu vào mockQuanNhanChiTietData.auditLog. Trong thực tế, dữ liệu này thường được lấy từ một hệ thống logging riêng hoặc một bảng audit log chung, được lọc theo ID của quân nhân.

TypeScript

// src\views\quan-ly-quan-nhan\chi-tiet\mockDataChiTiet.ts (Thêm/Cập nhật)

export interface AuditLogEntryType {
id: string; // ID của log entry
ThoiGianThayDoi: string; // ISO DateTime string
NguoiThucHienID: string; // ID của người dùng thực hiện, sẽ map sang tên người dùng
TaiKhoanThucHien?: string; // Tên tài khoản thực hiện
HanhDong: 'Tạo mới' | 'Cậ<PERSON> nhật' | 'Xóa' | 'Khôi phục' | string; // Hoặc các hành động cụ thể khác
DoiTuongBiTacDong?: string; // Ví dụ: "Hồ sơ Quân nhân", "Quá trình công tác", "Lý lịch cá nhân"
TruongThongTinThayDoi?: string; // Tên trường cụ thể bị thay đổi, ví dụ: "Số điện thoại", "Cấp bậc"
MoTaThayDoi?: string; // Mô tả chi tiết hơn về thay đổi (có thể tự động tạo hoặc nhập tay)
GiaTriCu?: string; // Dưới dạng JSON string hoặc text mô tả
GiaTriMoi?: string; // Dưới dạng JSON string hoặc text mô tả
DiaChiIP?: string; // IP của người thực hiện (nếu cần theo dõi)
}

// Trong mockQuanNhanChiTietData:
// ...
// auditLog: [
// {
// id: 'log_001',
// ThoiGianThayDoi: '2024-01-15T10:30:00Z',
// NguoiThucHienID: 'user_admin01',
// TaiKhoanThucHien: 'admin_qn',
// HanhDong: 'Cập nhật',
// DoiTuongBiTacDong: 'Thông tin Chung Quân nhân',
// TruongThongTinThayDoi: 'Số điện thoại',
// MoTaThayDoi: 'Cập nhật số điện thoại liên hệ.',
// GiaTriCu: '0905123455',
// GiaTriMoi: '0905123456',
// DiaChiIP: '************'
// },
// {
// id: 'log_002',
// ThoiGianThayDoi: '2024-02-20T14:00:00Z',
// NguoiThucHienID: 'user_cbcs02',
// TaiKhoanThucHien: 'nguyenvana_cbcs',
// HanhDong: 'Thêm mới',
// DoiTuongBiTacDong: 'Quá trình Công tác',
// MoTaThayDoi: 'Thêm giai đoạn công tác mới tại Đơn vị XYZ.',
// GiaTriMoi: '{ "DonViCongTacID": "DV_XYZ", "ChucVuDamNhiemID": "CV_ABC", ... }',
// DiaChiIP: '********'
// },
// {
// id: 'log_003',
// ThoiGianThayDoi: '2023-11-05T09:15:00Z',
// NguoiThucHienID: 'user_admin01',
// TaiKhoanThucHien: 'admin_qn',
// HanhDong: 'Tạo mới',
// DoiTuongBiTacDong: 'Hồ sơ Quân nhân',
// MoTaThayDoi: `Tạo mới Hồ sơ Quân nhân: Trần Văn Chiến Thắng (QN123456)`,
// GiaTriMoi: '{ "SoHieuQuanNhan": "QN123456", "HoVaTenKhaiSinh": "Trần Văn Chiến Thắng", ... }',
// DiaChiIP: '************'
// }
// ] as AuditLogEntryType[],
// ...
Bước 40: Xây dựng TabPanel cho "Lịch sử Thay đổi Hồ sơ"
Tạo file src\views\quan-ly-quan-nhan\chi-tiet\components\tabs\TabLichSuThayDoi.tsx

Mục đích: Hiển thị log các thay đổi trên hồ sơ của quân nhân.

Props:

initialData?: AuditLogEntryType[] (Dữ liệu log)
idQuanNhan: string (Để có thể fetch log nếu không được truyền qua initialData)
State:

logData: AuditLogEntryType[] (Danh sách log hiển thị).
isLoading: boolean (Nếu fetch dữ liệu log riêng).
paginationModel.
openDetailLogDialog: boolean (Trạng thái mở dialog xem chi tiết log).
viewingLogEntry: AuditLogEntryType | null (Log entry đang được xem chi tiết).
Component Vuexy (MUI & @mui/x-data-grid):

DataGrid.
IconButton (trong cột Hành động để xem chi tiết).
Dialog (sẽ tạo component DialogXemChiTietLog.tsx để hiển thị GiaTriCu và GiaTriMoi).
Cấu hình cột cho DataGrid:

ThoiGianThayDoi (định dạng dd/MM/yyyy HH:mm:ss)
NguoiThucHienID (hiển thị Tên người dùng/Tài khoản)
HanhDong
DoiTuongBiTacDong (Hoặc MoTaThayDoi nếu nó đủ súc tích)
TruongThongTinThayDoi (Tùy chọn, nếu có)
Hành động: Nút "Xem chi tiết thay đổi" (mở Dialog hiển thị Giá trị cũ/Giá trị mới).
TypeScript

// src\views\quan-ly-quan-nhan\chi-tiet\components\tabs\TabLichSuThayDoi.tsx
'use client';

import React, { useState, useEffect } from 'react';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import { DataGrid, GridColDef, GridRenderCellParams, GridValueFormatterParams } from '@mui/x-data-grid';
import IconButton from '@mui/material/IconButton';
import Tooltip from '@mui/material/Tooltip';
import CircularProgress from '@mui/material/CircularProgress';

import IconEye from '@tabler/icons-react/dist/esm/icons/IconEye';

import { AuditLogEntryType } from '../../mockDataChiTiet';
// Giả định có DialogXemChiTietLog
// import DialogXemChiTietLog from './DialogXemChiTietLog';

interface TabLichSuThayDoiProps {
initialData?: AuditLogEntryType[];
idQuanNhan: string; // Dùng để fetch nếu initialData không có hoặc cần refresh
}

const formatDateTimeAuditLog = (dateString?: string | null): string => {
if (!dateString) return '';
try {
const date = new Date(dateString);
if (isNaN(date.getTime())) return 'Không hợp lệ';
const day = String(date.getDate()).padStart(2, '0');
const month = String(date.getMonth() + 1).padStart(2, '0');
const year = date.getFullYear();
const hours = String(date.getHours()).padStart(2, '0');
const minutes = String(date.getMinutes()).padStart(2, '0');
const seconds = String(date.getSeconds()).padStart(2, '0');
return `<span class="math-inline">\{day\}/</span>{month}/${year} <span class="math-inline">\{hours\}\:</span>{minutes}:${seconds}`;
} catch (e) { return 'Không hợp lệ'; }
};

const mapUserIdToName = (userId?: string, taiKhoan?: string) => {
if (taiKhoan) return taiKhoan;
if (userId) return `User ID: ${userId}`;
return 'N/A';
}

const TabLichSuThayDoi = ({ initialData = [], idQuanNhan }: TabLichSuThayDoiProps) => {
const [logData, setLogData] = useState<AuditLogEntryType[]>(initialData);
const [isLoading, setIsLoading] = useState<boolean>(false); // Sẽ true nếu fetch API
const [openDetailLogDialog, setOpenDetailLogDialog] = useState(false);
const [viewingLogEntry, setViewingLogEntry] = useState<AuditLogEntryType | null>(null);
const [paginationModel, setPaginationModel] = useState({ page: 0, pageSize: 10 });

// Ví dụ fetch dữ liệu log nếu không có initialData hoặc khi idQuanNhan thay đổi
useEffect(() => {
const fetchAuditLog = async () => {
// if (!initialData || initialData.length === 0) { // Hoặc luôn fetch để có dữ liệu mới nhất
// setIsLoading(true);
// console.log(`Workspaceing audit log for QN ID: ${idQuanNhan}`);
// // Giả lập API call
// await new Promise(resolve => setTimeout(resolve, 1500));
// // Trong thực tế: const response = await YourApiService.getAuditLog(idQuanNhan, paginationModel);
// // setLogData(response.data); // Giả sử response.data là mảng AuditLogEntryType
// // setRowCount(response.totalCount); // Nếu có server-side pagination
// setIsLoading(false);
// } else {
setLogData(initialData); // Sử dụng initialData nếu có
// }
};
fetchAuditLog();
}, [idQuanNhan, initialData]); // Thêm paginationModel nếu fetch theo trang

const handleOpenDetailLogDialog = (logEntry: AuditLogEntryType) => {
setViewingLogEntry(logEntry);
setOpenDetailLogDialog(true);
};

const handleCloseDetailLogDialog = () => {
setOpenDetailLogDialog(false);
setViewingLogEntry(null);
};

const columns: GridColDef[] = [
{
field: 'ThoiGianThayDoi',
headerName: 'Thời gian',
width: 180,
valueFormatter: (params: GridValueFormatterParams<string | undefined>) => formatDateTimeAuditLog(params.value)
},
{
field: 'NguoiThucHien', // Tạo một trường ảo để hiển thị
headerName: 'Người thực hiện',
width: 180,
valueGetter: params => mapUserIdToName(params.row.NguoiThucHienID, params.row.TaiKhoanThucHien)
},
{ field: 'HanhDong', headerName: 'Hành động', width: 120 },
{
field: 'MoTaThayDoi',
headerName: 'Mô tả thay đổi',
width: 350,
valueGetter: params => params.row.MoTaThayDoi || `<span class="math-inline">\{params\.row\.DoiTuongBiTacDong \|\| ''\}</span>{params.row.TruongThongTinThayDoi ? ' - ' + params.row.TruongThongTinThayDoi : ''}`,
renderCell: (params: GridRenderCellParams) => (
<Tooltip title={params.value || ''} placement="top-start">
<Typography noWrap variant="body2" sx={{overflow: 'hidden', textOverflow: 'ellipsis'}}>
{params.value || ''}
</Typography>
</Tooltip>
)
},
{
field: 'actions',
headerName: 'Chi tiết',
width: 100,
sortable: false,
filterable: false,
renderCell: (params: GridRenderCellParams) => (
<Tooltip title="Xem Chi tiết Thay đổi">
<IconButton size="small" onClick={() => handleOpenDetailLogDialog(params.row as AuditLogEntryType)}>
<IconEye size={20} />
</IconButton>
</Tooltip>
)
}
];

if (isLoading) {
return <Box sx={{display: 'flex', justifyContent: 'center', alignItems: 'center', p: 3}}><CircularProgress /></Box>;
}

return (
<Box>
<Typography variant="h6" sx={{ color: 'primary.main', mb: 3 }}>
Lịch sử Thay đổi Hồ sơ
</Typography>

      <DataGrid
        autoHeight
        rows={logData}
        columns={columns}
        pageSizeOptions={[10, 25, 50]}
        paginationModel={paginationModel}
        onPaginationModelChange={setPaginationModel}
        // rowCount={rowCountState} // Nếu có server-side pagination
        // paginationMode="server" // Nếu có server-side pagination
        loading={isLoading}
        getRowId={(row) => row.id}
        sx={{
            '& .MuiDataGrid-columnHeaders': { backgroundColor: 'customColors.tableHeaderBg' }
        }}
      />
      {/* Placeholder cho DialogXemChiTietLog */}
      {openDetailLogDialog && viewingLogEntry && (
        <Typography sx={{mt: 2, p:2, border: '1px dashed blue'}}>
          Dialog Xem Chi tiết Log (Placeholder)
          <pre style={{whiteSpace: 'pre-wrap', wordBreak: 'break-all', maxHeight: '300px', overflowY: 'auto', background: '#f5f5f5', padding: '10px', borderRadius: '4px'}}>
            ID: {viewingLogEntry.id}<br/>
            Thời gian: {formatDateTimeAuditLog(viewingLogEntry.ThoiGianThayDoi)}<br/>
            Người thực hiện: {mapUserIdToName(viewingLogEntry.NguoiThucHienID, viewingLogEntry.TaiKhoanThucHien)}<br/>
            Hành động: {viewingLogEntry.HanhDong}<br/>
            Đối tượng: {viewingLogEntry.DoiTuongBiTacDong}<br/>
            Trường: {viewingLogEntry.TruongThongTinThayDoi || 'N/A'}<br/>
            Mô tả: {viewingLogEntry.MoTaThayDoi}<br/>
            Giá trị cũ: {viewingLogEntry.GiaTriCu || 'N/A'}<br/>
            Giá trị mới: {viewingLogEntry.GiaTriMoi || 'N/A'}<br/>
            IP: {viewingLogEntry.DiaChiIP || 'N/A'}
          </pre>
          <Button onClick={handleCloseDetailLogDialog}>Đóng</Button>
        </Typography>
      )}
      {/* {openDetailLogDialog && viewingLogEntry && (
        <DialogXemChiTietLog
          open={openDetailLogDialog}
          onClose={handleCloseDetailLogDialog}
          logEntry={viewingLogEntry}
        />
      )} */}
    </Box>

);
};

export default TabLichSuThayDoi;
Bước 41: Tạo Component DialogXemChiTietLog.tsx (Sơ bộ)
Component này sẽ hiển thị chi tiết một log entry, đặc biệt là GiaTriCu và GiaTriMoi.

TypeScript

// src\views\quan-ly-quan-nhan\chi-tiet\components\tabs\DialogXemChiTietLog.tsx (Sơ bộ)
// 'use client';
// import React from 'react';
// import Dialog from '@mui/material/Dialog';
// import DialogTitle from '@mui/material/DialogTitle';
// import DialogContent from '@mui/material/DialogContent';
// import DialogActions from '@mui/material/DialogActions';
// import Button from '@mui/material/Button';
// import Typography from '@mui/material/Typography';
// import Grid from '@mui/material/Grid';
// import Box from '@mui/material/Box';
// import { AuditLogEntryType } from '../../mockDataChiTiet';

// interface DialogXemChiTietLogProps {
// open: boolean;
// onClose: () => void;
// logEntry: AuditLogEntryType | null;
// }

// const formatDisplayValue = (value?: string) => {
// if (!value) return 'N/A';
// try {
// // Thử parse JSON nếu là object/array
// const parsed = JSON.parse(value);
// return <pre style={{whiteSpace: 'pre-wrap', wordBreak: 'break-all', background: '#f9f9f9', padding: '8px', borderRadius: '4px', margin:0}}>{JSON.stringify(parsed, null, 2)}</pre>;
// } catch (e) {
// // Nếu không phải JSON, hiển thị text thường
// return <Typography variant="body2" component="span" sx={{whiteSpace: 'pre-wrap', wordBreak: 'break-all'}}>{value}</Typography>;
// }
// }

// const DetailRowLog: React.FC<{ label: string; value?: string | React.ReactNode }> = ({ label, value }) => (
// <Grid item xs={12} sx={{ display: 'flex', flexDirection: {xs: 'column', sm: 'row'}, mb: 1.5 }}>
// <Typography variant="subtitle2" sx={{ minWidth: '180px', color: 'text.secondary', mr: 2, mb: {xs: 0.5, sm: 0} }}>
// {label}:
// </Typography>
// <Box sx={{flexGrow: 1}}>{typeof value === 'string' ? formatDisplayValue(value) : value || 'N/A'}</Box>
// </Grid>
// );

// const DialogXemChiTietLog = ({ open, onClose, logEntry }: DialogXemChiTietLogProps) => {
// if (!logEntry) return null;

// return (
// <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
// <DialogTitle>Chi tiết Lịch sử Thay đổi</DialogTitle>
// <DialogContent dividers>
// <Grid container spacing={1}>
// <DetailRowLog label="ID Log" value={logEntry.id} />
// <DetailRowLog label="Thời gian" value={formatDateTimeAuditLog(logEntry.ThoiGianThayDoi)} />
// <DetailRowLog label="Người thực hiện" value={mapUserIdToName(logEntry.NguoiThucHienID, logEntry.TaiKhoanThucHien)} />
// <DetailRowLog label="Địa chỉ IP" value={logEntry.DiaChiIP} />
// <DetailRowLog label="Hành động" value={logEntry.HanhDong} />
// <DetailRowLog label="Đối tượng bị tác động" value={logEntry.DoiTuongBiTacDong} />
// <DetailRowLog label="Trường thông tin thay đổi" value={logEntry.TruongThongTinThayDoi} />
// <DetailRowLog label="Mô tả chi tiết" value={logEntry.MoTaThayDoi} />
// <Grid item xs={12} sx={{mt:1}}><Typography variant="subtitle1">Giá trị thay đổi:</Typography></Grid>
// <Grid item xs={12} sm={6}>
// <Typography variant="subtitle2" color="text.secondary">Giá trị cũ:</Typography>
// {formatDisplayValue(logEntry.GiaTriCu)}
// </Grid>
// <Grid item xs={12} sm={6}>
// <Typography variant="subtitle2" color="text.secondary">Giá trị mới:</Typography>
// {formatDisplayValue(logEntry.GiaTriMoi)}
// </Grid>
// </Grid>
// </DialogContent>
// <DialogActions>
// <Button onClick={onClose}>Đóng</Button>
// </DialogActions>
// </Dialog>
// );
// };
// export default DialogXemChiTietLog;
Bước 42: Cập nhật KhuVucTabsChiTiet.tsx để sử dụng TabLichSuThayDoi
Chỉnh sửa file src\views\quan-ly-quan-nhan\chi-tiet\components\KhuVucTabsChiTiet.tsx:

TypeScript

// src\views\quan-ly-quan-nhan\chi-tiet\components\KhuVucTabsChiTiet.tsx
// ... (các imports khác)
import TabLichSuThayDoi from './tabs/TabLichSuThayDoi';

// interface TabsProps { ... }

const KhuVucTabsChiTiet = ({ quanNhanData, activeTab, handleTabChange }: TabsProps) => {
const tabContentList: { [key: string]: React.ReactNode } = {
'thong-tin-chung': <TabThongTinChung data={quanNhanData.baseInfo} />,
'ly-lich-ca-nhan': <TabLyLichCaNhan data={quanNhanData.lyLichCaNhan} />,
'qua-trinh-cong-tac': <TabQuaTrinhCongTac initialData={quanNhanData.quaTrinhCongTac} idQuanNhan={quanNhanData.baseInfo.SoHieuQuanNhan} />,
'qua-trinh-dao-tao': <TabQuaTrinhDaoTao initialData={quanNhanData.quaTrinhDaoTao} idQuanNhan={quanNhanData.baseInfo.SoHieuQuanNhan} />,
'khen-thuong': <TabKhenThuong initialData={quanNhanData.khenThuong} idQuanNhan={quanNhanData.baseInfo.SoHieuQuanNhan} />,
'ky-luat': <TabKyLuat initialData={quanNhanData.kyLuat} idQuanNhan={quanNhanData.baseInfo.SoHieuQuanNhan} />,
'suc-khoe': <TabSucKhoe initialData={quanNhanData.sucKhoe} idQuanNhan={quanNhanData.baseInfo.SoHieuQuanNhan} />,
'quan-he-gia-dinh': <TabQuanHeGiaDinh initialData={quanNhanData.thanNhan} idQuanNhan={quanNhanData.baseInfo.SoHieuQuanNhan} />,
'che-do-chinh-sach': <TabCheDoChinhSach initialData={quanNhanData.cheDoChinhSach} idQuanNhan={quanNhanData.baseInfo.SoHieuQuanNhan} />,
'lich-su-thay-doi': <TabLichSuThayDoi initialData={quanNhanData.auditLog} idQuanNhan={quanNhanData.baseInfo.SoHieuQuanNhan} />,
};

const tabConfigurations = [
{ label: "Thông tin Chung", value: "thong-tin-chung" },
{ label: "Lý lịch Cá nhân", value: "ly-lich-ca-nhan" },
{ label: "Quá trình Công tác", value: "qua-trinh-cong-tac" },
{ label: "Quá trình Đào tạo", value: "qua-trinh-dao-tao" },
{ label: "Khen thưởng", value: "khen-thuong" },
{ label: "Kỷ luật", value: "ky-luat" },
{ label: "Sức khỏe", value: "suc-khoe" },
{ label: "Quan hệ Gia đình", value: "quan-he-gia-dinh" },
{ label: "Chế độ Chính sách", value: "che-do-chinh-sach" },
{ label: "Lịch sử Thay đổi", value: "lich-su-thay-doi", isOptional: true } // Đánh dấu tab tùy chọn
];

return (
<TabContext value={activeTab}>
<Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
<TabList
            onChange={handleTabChange}
            aria-label="Chi tiết hồ sơ quân nhân"
            variant="scrollable"
            scrollButtons="auto"
        >
{tabConfigurations.map(tab => {
// Chỉ render tab "Lịch sử Thay đổi" nếu có dữ liệu auditLog hoặc có logic fetch riêng
// Hoặc luôn render nếu isOptional chỉ mang tính chất ghi chú
if (tab.value === 'lich-su-thay-doi' && !(quanNhanData.auditLog && quanNhanData.auditLog.length > 0) && !tab.isOptional /_&& some_other_condition_to_show_even_if_empty_/) {
return null; // Bỏ qua tab này nếu không có dữ liệu và không phải là bắt buộc hiển thị
}
return <Tab key={tab.value} label={tab.label} value={tab.value} />;
})}
</TabList>
</Box>
{tabConfigurations.map(tab => {
if (tab.value === 'lich-su-thay-doi' && !(quanNhanData.auditLog && quanNhanData.auditLog.length > 0) && !tab.isOptional) {
return null;
}
return (
<TabPanel key={tab.value} value={tab.value} sx={{ p: 0 }}>
<CardContent>
{tabContentList[tab.value]}
</CardContent>
</TabPanel>
)
})}
</TabContext>
);
};

export default KhuVucTabsChiTiet;
