## CẤU TRÚC TRANG DANH SÁCH MẪU

### 1. <PERSON><PERSON><PERSON> trúc thư mục
```
[module]/
  ├── list/
  │   ├── components/
  │   │   ├── [TenModule]Filters.tsx    # Component bộ lọc
  │   │   ├── [TenModule]Table.tsx      # Component bảng dữ liệu
  │   │   └── [TenModule]Toolbar.tsx    # Thanh công cụ (tìm kiếm, thêm mới, xuất file)
  │   ├── dialogs/                     # Các dialog (nếu có)
  │   ├── DanhSach[TenModule]Page.tsx  # Page chính
  │   └── models/                      # Các interface, type
  └── [id]/                            # Chi tiết (nếu có)
```

### 2. Quy tắc chung
- **Tên file**: Đặt tên theo quy ước PascalCase
- **Component**: Sử dụng TypeScript với interface rõ ràng
- **State management**: Sử dụng React hooks (useState, useCallback, useMemo)
- **Styling**: Sử dụng Material-UI với theme chung
- **<PERSON>ân trang**: Sử dụng TablePaginationComponent dùng chung
- **Hành động**: Tối đa 3 icon hiển thị trực tiếp, các hành động khác đưa vào dropdown

### 3. Các component bắt buộc
1. **Toolbar**: Chứa nút thêm mới, xuất file và tìm kiếm
2. **Filters**: Bộ lọc dữ liệu (có thể thu gọn/mở rộng)
3. **Table**: Hiển thị dữ liệu dạng bảng với phân trang
4. **Pagination**: Sử dụng component chung

### 4. Quy ước đặt tên
- Props: camelCase
- Event handlers: handle[Action] (ví dụ: handleSearch, handleFilterChange)
- State: [tênState, setTênState] (ví dụ: [filters, setFilters])

### 5. File mẫu tham khảo
- `src/views/cong-tac-dang/phat-trien-dang-vien/list/`
- `src/views/cong-tac-dang/danh-sach-dang-vien/`

Khi tạo mới một trang danh sách, cần tuân thủ đúng cấu trúc và quy ước này để đảm bảo tính nhất quán của dự án.
