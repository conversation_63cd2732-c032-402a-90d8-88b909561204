import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany, OneToOne, PrimaryColumn, Relation } from 'typeorm';
import { AbstractEntity } from './abstract.entity';
import { GiaTriDanhMucEntity } from './giaTriDanhMuc.entity';
import { DuLieuTepTinEntity } from './duLieuTepTin.entity';
import { LyLichCanNhanEntity } from './lyLichCanNhan.entity';

@Entity({ name: 'quan_nhans' })
export class QuanNhanEntity extends AbstractEntity {
    @PrimaryColumn({ name: 'so_hieu_quan_nhan', type: 'varchar', length: 20 })
    soHieuQuanNhan: string;

    @Column({ name: 'ho_ten_khai_sinh', type: 'varchar', length: 100, nullable: false })
    hoTenKhaiSinh: string;

    @Column({ name: 'ten_thuong_dung', type: 'varchar', length: 100, nullable: true })
    tenThuongD<PERSON>?: string;

    @Column({ name: 'ngay_sinh', type: 'date', nullable: false })
    ngaySinh: Date;

    @Column({ name: 'gioi_tinh_id', type: 'int', unsigned: true, nullable: false })
    gioiTinhId: number;

    @Column({ name: 'so_cccd_cmtd', type: 'varchar', length: 20, unique: true, nullable: true })
    soCCCD_CMTQ?: string;

    @Column({ name: 'ngay_nhap_ngu', type: 'date', nullable: true })
    ngayNhapNgu?: Date;

    @Column({ name: 'don_vi_id', type: 'int', unsigned: true, nullable: false })
    donViId: number;

    @Column({ name: 'cap_bac_hien_tai_id', type: 'int', unsigned: true, nullable: true })
    capBacHienTaiId?: number;

    @Column({ name: 'chuc_vu_hien_tai_id', type: 'int', unsigned: true, nullable: true })
    chucVuHienTaiId?: number;

    @Column({ name: 'trang_thai_ho_so_id', type: 'int', unsigned: true, nullable: false })
    trangThaiHoSoId: number;

    @Column({ name: 'anh_chan_dung_tep_tin_id', type: 'uuid', nullable: true })
    anhChanDungTepTinId?: string;

    @Column({ name: 'email', type: 'varchar', length: 100, unique: true, nullable: true })
    email?: string;

    @Column({ name: 'so_dien_thoai', type: 'varchar', length: 15, nullable: true })
    soDienThoai?: string;

    /* RELATION */
    @ManyToOne(() => GiaTriDanhMucEntity, { createForeignKeyConstraints: false })
    @JoinColumn({ name: 'gioi_tinh_id', referencedColumnName: 'id' })
    gioiTinh: Relation<GiaTriDanhMucEntity>;

    @ManyToOne(() => GiaTriDanhMucEntity, { createForeignKeyConstraints: false })
    @JoinColumn({ name: 'don_vi_id', referencedColumnName: 'id' })
    donVi: Relation<GiaTriDanhMucEntity>;

    @ManyToOne(() => GiaTriDanhMucEntity, { createForeignKeyConstraints: false })
    @JoinColumn({ name: 'cap_bac_hien_tai_id', referencedColumnName: 'id' })
    capBacHienTai?: Relation<GiaTriDanhMucEntity>;

    @ManyToOne(() => GiaTriDanhMucEntity, { createForeignKeyConstraints: false })
    @JoinColumn({ name: 'chuc_vu_hien_tai_id', referencedColumnName: 'id' })
    chucVuHienTai?: Relation<GiaTriDanhMucEntity>;

    @ManyToOne(() => GiaTriDanhMucEntity, { createForeignKeyConstraints: false })
    @JoinColumn({ name: 'trang_thai_ho_so_id', referencedColumnName: 'id' })
    trangThaiHoSo: Relation<GiaTriDanhMucEntity>;

    @OneToOne(() => DuLieuTepTinEntity, { createForeignKeyConstraints: false })
    @JoinColumn({ name: 'anh_chan_dung_tep_tin_id', referencedColumnName: 'id' })
    anhChanDung?: Relation<DuLieuTepTinEntity>;

    @OneToOne(() => LyLichCanNhanEntity, (lyLichCanNhan) => lyLichCanNhan.quanNhan, {
        createForeignKeyConstraints: false,
    })
    lyLichCanNhan?: Relation<LyLichCanNhanEntity>;
}
