{"version": 3, "sources": ["../../src/config/token.config.ts"], "sourcesContent": ["export default () => ({\n    token: {\n        authTokenSecret: process.env.AUTH_TOKEN_SECRET,\n        authTokenName: process.env.AUTH_TOKEN_NAME,\n        authExpiresIn: Number(process.env.AUTH_TOKEN_EXP),\n        passcodeTokenSecret: process.env.PASSCODE_TOKEN_SECRET,\n        passcodeTokenName: process.env.PASSCODE_TOKEN_NAME,\n        passcodeExpiresIn: Number(process.env.PASSCODE_TOKEN_EXP),\n        refreshTokenSecret: process.env.REFRESH_TOKEN_SECRET,\n        refreshTokenName: process.env.REFRESH_TOKEN_NAME,\n        refreshExpiresIn: Number(process.env.REFRESH_TOKEN_EXP),\n    },\n});\n"], "names": ["token", "authTokenSecret", "process", "env", "AUTH_TOKEN_SECRET", "authTokenName", "AUTH_TOKEN_NAME", "authExpiresIn", "Number", "AUTH_TOKEN_EXP", "passcodeTokenSecret", "PASSCODE_TOKEN_SECRET", "passcodeTokenName", "PASSCODE_TOKEN_NAME", "passcodeExpiresIn", "PASSCODE_TOKEN_EXP", "refreshTokenSecret", "REFRESH_TOKEN_SECRET", "refreshTokenName", "REFRESH_TOKEN_NAME", "refreshExpiresIn", "REFRESH_TOKEN_EXP"], "mappings": "oGAAA,iDAAA,kBAAA,SAAe,IAAO,CAAA,CAClBA,MAAO,CACHC,gBAAiBC,QAAQC,GAAG,CAACC,iBAAiB,CAC9CC,cAAeH,QAAQC,GAAG,CAACG,eAAe,CAC1CC,cAAeC,OAAON,QAAQC,GAAG,CAACM,cAAc,EAChDC,oBAAqBR,QAAQC,GAAG,CAACQ,qBAAqB,CACtDC,kBAAmBV,QAAQC,GAAG,CAACU,mBAAmB,CAClDC,kBAAmBN,OAAON,QAAQC,GAAG,CAACY,kBAAkB,EACxDC,mBAAoBd,QAAQC,GAAG,CAACc,oBAAoB,CACpDC,iBAAkBhB,QAAQC,GAAG,CAACgB,kBAAkB,CAChDC,iBAAkBZ,OAAON,QAAQC,GAAG,CAACkB,iBAAiB,CAC1D,CACJ,CAAA"}