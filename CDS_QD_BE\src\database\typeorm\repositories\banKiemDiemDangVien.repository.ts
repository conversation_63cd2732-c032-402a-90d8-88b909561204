/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { BanKiemDiemDangVienEntity } from '~/database/typeorm/entities/banKiemDiemDangVien.entity';

@Injectable()
export class BanKiemDiemDangVienRepository extends Repository<BanKiemDiemDangVienEntity> {
    constructor(private dataSource: DataSource) {
        super(BanKiemDiemDangVienEntity, dataSource.createEntityManager());
    }
}
