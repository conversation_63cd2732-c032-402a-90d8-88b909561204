# Quy trình xây dựng giao diện trang "Chi tiết Quy trình Phát triển Đảng viên"

**<PERSON><PERSON><PERSON> định về Quy trình Phát triển Đảng viên (Happy Path - 12 bước cơ bản):**

Dựa trên mô tả trước đó và các quy trình phổ biến, chúng ta có thể tạm định nghĩa một quy trình "happy path" gồm các bước sau. Tên bước và thứ tự có thể cần điều chỉnh lại cho chính xác tuyệt đối với quy định cụ thể của đơn vị bạn, nhưng đây là một khung sườn tốt để bắt đầu:

1. Bước 1:Tạo nguồn
2. Bước 2:Th<PERSON><PERSON> tra lý lịch
3. Bước 3:<PERSON><PERSON><PERSON> nhận thức về Đả<PERSON>
4. Bước 4:<PERSON><PERSON><PERSON><PERSON> thi<PERSON>u vào <PERSON>
5. Bước 5:<PERSON><PERSON><PERSON> nạp <PERSON>
6. Bước 6:<PERSON><PERSON><PERSON> viên dự bị
7. Bước 7:Chuyển Đảng chính thức

---

**Trang: Chi tiết Quy trình Phát triển Đảng viên (`/${locale}/cong-tac-dang/phat-trien-dang-vien/chi-tiet/{idHoSoPhatTrien}`)**

**IV. Khu vực "Chi tiết Bước Hiện tại/Được chọn" (Cập nhật chi tiết)**

**Bước 1: Lập Danh sách Nguồn (Tạo Hồ sơ Ban đầu)**

- **Tên Bước Hiển thị:** `Lập Danh sách Nguồn` (hoặc `Tạo Hồ sơ Ban đầu`)
- **Mục đích của Bước:** Khởi tạo hồ sơ theo dõi cho một quần chúng ưu tú (Quân nhân) có tiềm năng phát triển Đảng. Ghi nhận các thông tin ban đầu và phân công người hướng dẫn.
- **Trạng thái Quy trình (`DmTrangThaiPhatTrienDang.ID`) tương ứng:** Ví dụ ID cho "Tạo nguồn" hoặc "Chờ Thẩm tra Lý lịch" (nếu thẩm tra là bước ngay sau).

- **Thông tin cụ thể hiển thị/nhập liệu cho Bước 1:**

  - **Thông tin Đối tượng (Quân nhân):** (Các trường này thường đã được nhập khi tạo hồ sơ ban đầu từ trang danh sách và hiển thị read-only ở đây)
    - Hiển thị: Số hiệu Quân nhân, Họ tên, Ngày sinh, Đơn vị công tác.
  - **Ngày tạo Hồ sơ Phát triển:**
    - Hiển thị: `HoSoPhatTrienDangVien.NgayTaoHoSo` (read-only).
  - **Tổ chức Đảng Đề nghị/Quản lý ban đầu:**
    - Hiển thị/Cho phép chọn (nếu chưa có): Tên Tổ chức Đảng từ `ToChucDang.TenToChucDang` (liên kết qua `HoSoPhatTrienDangVien.ToChucDangDeNghiKetNapID`).
  - **Đảng viên Chính thức Hướng dẫn:**
    - **Người Hướng dẫn 1:**
      - Hiển thị/Cho phép chọn: Tên Đảng viên từ `DangVien.HoVaTen` (liên kết qua `HoSoPhatTrienDangVien.NguoiHuongDan1ID`).
    - **Người Hướng dẫn 2:**
      - Hiển thị/Cho phép chọn: Tên Đảng viên từ `DangVien.HoVaTen` (liên kết qua `HoSoPhatTrienDangVien.NguoiHuongDan2ID`).
  - **Ghi chú ban đầu (nếu có):**
    - Hiển thị/Cho phép nhập/sửa: `HoSoPhatTrienDangVien.GhiChuQuyTrinh` (cho bước này).

- **Danh sách Tài liệu Yêu cầu/Đã nộp (Fix cứng cho Bước 1):**

  - _(Trong bước tạo nguồn ban đầu, có thể chưa yêu cầu tài liệu chính thức nào phải nộp ngay, hoặc có thể có các tài liệu mang tính chất sơ bộ nếu quy trình yêu cầu)._
  - **Ví dụ (nếu có):**
    1.  **Loại Tài liệu:** "Phiếu giới thiệu nguồn" (Tên từ `DmLoaiTaiLieuPhatTrienDang`)
        - **Trạng thái:** "Chưa nộp" / "Đã nộp" (ngày nộp)
        - **File đính kèm:** (Nếu đã nộp) Hiển thị tên file với nút "Xem/Tải".
        - **Hành động:** (Nếu chưa nộp và người dùng có quyền) Nút "Nộp tài liệu" (mở giao diện upload file, tự động gán `LoaiTaiLieuPhatTrienID` là "Phiếu giới thiệu nguồn").
  - **Lưu ý:** Danh sách tài liệu "fix cứng" ở đây nghĩa là giao diện sẽ luôn hiển thị các mục tài liệu này cho Bước 1, cho dù đã có file nộp hay chưa. Trạng thái và file đính kèm sẽ được lấy từ bảng `TaiLieuPhatTrienDangVien`.

- **Nút Hành động cho Bước 1 (Tùy theo vai trò người dùng và trạng thái hiện tại của hồ sơ):**
  - **Nút "Lưu thông tin Bước 1":**
    - **Hành động:** Lưu các thông tin đã nhập/chỉnh sửa ở trên (Tổ chức Đảng đề nghị, Người hướng dẫn, Ghi chú).
    - **Logic:** Cập nhật bản ghi `HoSoPhatTrienDangVien`.
  - **Nút "Hoàn thành Bước 1 & Chuyển sang Thẩm tra Lý lịch":**
    - **Điều kiện hiển thị:** Khi các thông tin bắt buộc của Bước 1 đã đầy đủ.
    - **Hành động:**
      - Lưu lại các thông tin của Bước 1 (nếu có thay đổi chưa lưu).
      - Cập nhật `HoSoPhatTrienDangVien.TrangThaiQuyTrinhID` sang ID của bước "Thẩm tra, Xác minh Lý lịch".
      - Hiển thị thông báo thành công.
      - Giao diện tự động chuyển sang hiển thị chi tiết của Bước 2.
      - (Có thể có logic gửi thông báo cho người/bộ phận chịu trách nhiệm thẩm tra lý lịch).

---

**Bảng Dữ liệu Liên quan Chính cho Bước 1:**

- **Bảng `HoSoPhatTrienDangVien`:**

  - `ID: number`
  - `DoiTuongQuanNhanID: string`
  - `NgayTaoHoSo: date`
  - `TrangThaiQuyTrinhID: number` (Sẽ được cập nhật khi chuyển bước)
  - `NguoiHuongDan1ID: string`
  - `NguoiHuongDan2ID: string`
  - `ToChucDangDeNghiKetNapID: number`
  - `GhiChuQuyTrinh: string` (Có thể lưu ghi chú chung cho toàn quy trình, hoặc từng bước sẽ có trường ghi chú riêng trong một bảng khác nếu cần chi tiết hơn. Hiện tại giả định là ghi chú chung).

- **Bảng `TaiLieuPhatTrienDangVien`:** (Để lưu các file tài liệu nếu Bước 1 có yêu cầu)

  - `HoSoPhatTrienID: number` (Liên kết với `HoSoPhatTrienDangVien.ID`)
  - `LoaiTaiLieuPhatTrienID: number` (Ví dụ: ID của "Phiếu giới thiệu nguồn" từ `DmLoaiTaiLieuPhatTrienDang`)
  - `FileURL: string`
  - `TenTaiLieu: string` (Có thể tự sinh từ tên file upload hoặc cho người dùng nhập)
  - `NgayNopTaiLieu: date`

- **Bảng `QuanNhan`:** (Để hiển thị thông tin đối tượng và chọn người hướng dẫn nếu người hướng dẫn cũng là QN)

  - `SoHieuQuanNhan: string`
  - `HoVaTenKhaiSinh: string`
  - `DonViID: number` (Để biết đơn vị của QN)

- **Bảng `DangVien`:** (Để chọn người hướng dẫn là Đảng viên)

  - `MaDangVien: string`
  - `HoVaTen: string`

- **Bảng `ToChucDang`:** (Để chọn Tổ chức Đảng đề nghị)

  - `ID: number`
  - `TenToChucDang: string`

- **Danh mục `DmTrangThaiPhatTrienDang`:** (Để xác định ID của bước tiếp theo)

  - `ID: number`
  - `TenTrangThai: string`

- **Danh mục `DmLoaiTaiLieuPhatTrienDang`:** (Để xác định loại tài liệu "Phiếu giới thiệu nguồn")
  - `ID: number`
  - `TenLoaiTaiLieu: string`

---

Đây là chi tiết cho Bước 1. Chúng ta sẽ tiếp tục tuần tự với các bước tiếp theo, làm rõ các tài liệu "fix cứng" và hành động cụ thể cho từng bước trong luồng "happy path"
