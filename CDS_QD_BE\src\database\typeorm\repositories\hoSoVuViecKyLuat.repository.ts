/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { HoSoVuViecKyLuatEntity } from '~/database/typeorm/entities/hoSoVuViecKyLuat.entity';

@Injectable()
export class HoSoVuViecKyLuatRepository extends Repository<HoSoVuViecKyLuatEntity> {
    constructor(private dataSource: DataSource) {
        super(HoSoVuViecKyLuatEntity, dataSource.createEntityManager());
    }
}
