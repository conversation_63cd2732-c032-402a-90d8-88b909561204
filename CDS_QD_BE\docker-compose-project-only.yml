version: '3.3'
services:
    redis:
        image: 'bitnami/redis:latest'
        container_name: redis
        environment:
            - REDIS_PASSWORD=${REDIS_PASSWORD}
            - ENABLE_OVERCOMMIT_MEMORY=true
            - REDIS_AOF_ENABLED=no
        networks:
            - myNetwork
        ports:
            - '6379:6379'
        volumes:
            - redis_data:/bitnami/redis/data
        restart: always

    db:
        image: postgres:15
        container_name: db
        restart: always
        environment:
            POSTGRES_PASSWORD: ${DATABASE_PASSWORD}
            POSTGRES_USER: ${DATABASE_USERNAME}
            POSTGRES_DB: ${DATABASE_DB_NAME}
        networks:
            - myNetwork
        ports:
            - '5433:5432'
        volumes:
            - db_data:/var/lib/postgresql/data

networks:
    myNetwork:

volumes:
    redis_data:
    db_data:
