/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { ThamSoHeThongEntity } from '~/database/typeorm/entities/thamSoHeThong.entity';

@Injectable()
export class ThamSoHeThongRepository extends Repository<ThamSoHeThongEntity> {
    constructor(private dataSource: DataSource) {
        super(ThamSoHeThongEntity, dataSource.createEntityManager());
    }
}
