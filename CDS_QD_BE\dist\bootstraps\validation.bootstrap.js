"use strict";Object.defineProperty(exports,"__esModule",{value:true});Object.defineProperty(exports,"bootstrapValidation",{enumerable:true,get:function(){return bootstrapValidation}});const _common=require("@nestjs/common");const _classvalidator=require("class-validator");const _appmodule=require("../app.module");function bootstrapValidation(app){(0,_classvalidator.useContainer)(app.select(_appmodule.AppModule),{fallbackOnErrors:true});app.useGlobalPipes(new _common.ValidationPipe({whitelist:true,transform:true,exceptionFactory:(validationErrors=[])=>{return new _common.BadRequestException(validationErrors.map(error=>({field:error.property,error:Object.values(error.constraints).join(", ")})))}}))}
//# sourceMappingURL=validation.bootstrap.js.map