/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 *  Swagger
 * The  API documents
 * OpenAPI spec version: 1.0
 */
import { useInfiniteQuery, useMutation, useQuery } from '@tanstack/react-query'
import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseInfiniteQueryResult,
  DefinedUseQueryResult,
  InfiniteData,
  MutationFunction,
  QueryClient,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseInfiniteQueryOptions,
  UseInfiniteQueryResult,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult
} from '@tanstack/react-query'

import type {
  CreateWarehouseDto,
  CreateWarehouseTypeDto,
  UpdateWarehouseDto,
  UpdateWarehouseTypeDto,
  WarehouseControllerFindAllParams,
  WarehouseControllerFindAllTypeParams
} from '.././model'

import { customInstance } from '../../mutator/custom-instance'
import type { ApiResponse } from '../../types'

export const warehouseControllerCreateType = (createWarehouseTypeDto: CreateWarehouseTypeDto, signal?: AbortSignal) => {
  return customInstance<ApiResponse<void>>({
    url: `/warehouse/type`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: createWarehouseTypeDto,
    signal
  })
}

export const getWarehouseControllerCreateTypeMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof warehouseControllerCreateType>>,
    TError,
    { data: CreateWarehouseTypeDto },
    TContext
  >
}): UseMutationOptions<
  Awaited<ReturnType<typeof warehouseControllerCreateType>>,
  TError,
  { data: CreateWarehouseTypeDto },
  TContext
> => {
  const mutationKey = ['warehouseControllerCreateType']
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } }

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof warehouseControllerCreateType>>,
    { data: CreateWarehouseTypeDto }
  > = props => {
    const { data } = props ?? {}

    return warehouseControllerCreateType(data)
  }

  return { mutationFn, ...mutationOptions }
}

export type WarehouseControllerCreateTypeMutationResult = NonNullable<
  Awaited<ReturnType<typeof warehouseControllerCreateType>>
>
export type WarehouseControllerCreateTypeMutationBody = CreateWarehouseTypeDto
export type WarehouseControllerCreateTypeMutationError = unknown

export const useWarehouseControllerCreateType = <TError = unknown, TContext = unknown>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof warehouseControllerCreateType>>,
      TError,
      { data: CreateWarehouseTypeDto },
      TContext
    >
  },
  queryClient?: QueryClient
): UseMutationResult<
  Awaited<ReturnType<typeof warehouseControllerCreateType>>,
  TError,
  { data: CreateWarehouseTypeDto },
  TContext
> => {
  const mutationOptions = getWarehouseControllerCreateTypeMutationOptions(options)

  return useMutation(mutationOptions, queryClient)
}
export const warehouseControllerFindAllType = (params?: WarehouseControllerFindAllTypeParams, signal?: AbortSignal) => {
  return customInstance<ApiResponse<void>>({ url: `/warehouse/type`, method: 'GET', params, signal })
}

export const getWarehouseControllerFindAllTypeQueryKey = (params?: WarehouseControllerFindAllTypeParams) => {
  return [`/warehouse/type`, ...(params ? [params] : [])] as const
}

export const getWarehouseControllerFindAllTypeInfiniteQueryOptions = <
  TData = InfiniteData<
    Awaited<ReturnType<typeof warehouseControllerFindAllType>>,
    WarehouseControllerFindAllTypeParams['page']
  >,
  TError = unknown
>(
  params?: WarehouseControllerFindAllTypeParams,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof warehouseControllerFindAllType>>,
        TError,
        TData,
        Awaited<ReturnType<typeof warehouseControllerFindAllType>>,
        QueryKey,
        WarehouseControllerFindAllTypeParams['page']
      >
    >
  }
) => {
  const { query: queryOptions } = options ?? {}

  const queryKey = queryOptions?.queryKey ?? getWarehouseControllerFindAllTypeQueryKey(params)

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof warehouseControllerFindAllType>>,
    QueryKey,
    WarehouseControllerFindAllTypeParams['page']
  > = ({ signal, pageParam }) =>
    warehouseControllerFindAllType({ ...params, page: pageParam || params?.['page'] }, signal)

  return { queryKey, queryFn, staleTime: 10000, ...queryOptions } as UseInfiniteQueryOptions<
    Awaited<ReturnType<typeof warehouseControllerFindAllType>>,
    TError,
    TData,
    Awaited<ReturnType<typeof warehouseControllerFindAllType>>,
    QueryKey,
    WarehouseControllerFindAllTypeParams['page']
  > & { queryKey: DataTag<QueryKey, TData, TError> }
}

export type WarehouseControllerFindAllTypeInfiniteQueryResult = NonNullable<
  Awaited<ReturnType<typeof warehouseControllerFindAllType>>
>
export type WarehouseControllerFindAllTypeInfiniteQueryError = unknown

export function useWarehouseControllerFindAllTypeInfinite<
  TData = InfiniteData<
    Awaited<ReturnType<typeof warehouseControllerFindAllType>>,
    WarehouseControllerFindAllTypeParams['page']
  >,
  TError = unknown
>(
  params: undefined | WarehouseControllerFindAllTypeParams,
  options: {
    query: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof warehouseControllerFindAllType>>,
        TError,
        TData,
        Awaited<ReturnType<typeof warehouseControllerFindAllType>>,
        QueryKey,
        WarehouseControllerFindAllTypeParams['page']
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof warehouseControllerFindAllType>>,
          TError,
          Awaited<ReturnType<typeof warehouseControllerFindAllType>>,
          QueryKey
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): DefinedUseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useWarehouseControllerFindAllTypeInfinite<
  TData = InfiniteData<
    Awaited<ReturnType<typeof warehouseControllerFindAllType>>,
    WarehouseControllerFindAllTypeParams['page']
  >,
  TError = unknown
>(
  params?: WarehouseControllerFindAllTypeParams,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof warehouseControllerFindAllType>>,
        TError,
        TData,
        Awaited<ReturnType<typeof warehouseControllerFindAllType>>,
        QueryKey,
        WarehouseControllerFindAllTypeParams['page']
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof warehouseControllerFindAllType>>,
          TError,
          Awaited<ReturnType<typeof warehouseControllerFindAllType>>,
          QueryKey
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useWarehouseControllerFindAllTypeInfinite<
  TData = InfiniteData<
    Awaited<ReturnType<typeof warehouseControllerFindAllType>>,
    WarehouseControllerFindAllTypeParams['page']
  >,
  TError = unknown
>(
  params?: WarehouseControllerFindAllTypeParams,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof warehouseControllerFindAllType>>,
        TError,
        TData,
        Awaited<ReturnType<typeof warehouseControllerFindAllType>>,
        QueryKey,
        WarehouseControllerFindAllTypeParams['page']
      >
    >
  },
  queryClient?: QueryClient
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }

export function useWarehouseControllerFindAllTypeInfinite<
  TData = InfiniteData<
    Awaited<ReturnType<typeof warehouseControllerFindAllType>>,
    WarehouseControllerFindAllTypeParams['page']
  >,
  TError = unknown
>(
  params?: WarehouseControllerFindAllTypeParams,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof warehouseControllerFindAllType>>,
        TError,
        TData,
        Awaited<ReturnType<typeof warehouseControllerFindAllType>>,
        QueryKey,
        WarehouseControllerFindAllTypeParams['page']
      >
    >
  },
  queryClient?: QueryClient
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
  const queryOptions = getWarehouseControllerFindAllTypeInfiniteQueryOptions(params, options)

  const query = useInfiniteQuery(queryOptions, queryClient) as UseInfiniteQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>
  }

  query.queryKey = queryOptions.queryKey

  return query
}

export const getWarehouseControllerFindAllTypeQueryOptions = <
  TData = Awaited<ReturnType<typeof warehouseControllerFindAllType>>,
  TError = unknown
>(
  params?: WarehouseControllerFindAllTypeParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof warehouseControllerFindAllType>>, TError, TData>>
  }
) => {
  const { query: queryOptions } = options ?? {}

  const queryKey = queryOptions?.queryKey ?? getWarehouseControllerFindAllTypeQueryKey(params)

  const queryFn: QueryFunction<Awaited<ReturnType<typeof warehouseControllerFindAllType>>> = ({ signal }) =>
    warehouseControllerFindAllType(params, signal)

  return { queryKey, queryFn, staleTime: 10000, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof warehouseControllerFindAllType>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> }
}

export type WarehouseControllerFindAllTypeQueryResult = NonNullable<
  Awaited<ReturnType<typeof warehouseControllerFindAllType>>
>
export type WarehouseControllerFindAllTypeQueryError = unknown

export function useWarehouseControllerFindAllType<
  TData = Awaited<ReturnType<typeof warehouseControllerFindAllType>>,
  TError = unknown
>(
  params: undefined | WarehouseControllerFindAllTypeParams,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof warehouseControllerFindAllType>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof warehouseControllerFindAllType>>,
          TError,
          Awaited<ReturnType<typeof warehouseControllerFindAllType>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useWarehouseControllerFindAllType<
  TData = Awaited<ReturnType<typeof warehouseControllerFindAllType>>,
  TError = unknown
>(
  params?: WarehouseControllerFindAllTypeParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof warehouseControllerFindAllType>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof warehouseControllerFindAllType>>,
          TError,
          Awaited<ReturnType<typeof warehouseControllerFindAllType>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useWarehouseControllerFindAllType<
  TData = Awaited<ReturnType<typeof warehouseControllerFindAllType>>,
  TError = unknown
>(
  params?: WarehouseControllerFindAllTypeParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof warehouseControllerFindAllType>>, TError, TData>>
  },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }

export function useWarehouseControllerFindAllType<
  TData = Awaited<ReturnType<typeof warehouseControllerFindAllType>>,
  TError = unknown
>(
  params?: WarehouseControllerFindAllTypeParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof warehouseControllerFindAllType>>, TError, TData>>
  },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
  const queryOptions = getWarehouseControllerFindAllTypeQueryOptions(params, options)

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>
  }

  query.queryKey = queryOptions.queryKey

  return query
}

export const warehouseControllerCreate = (createWarehouseDto: CreateWarehouseDto, signal?: AbortSignal) => {
  return customInstance<ApiResponse<ApiResponse<void>>>({
    url: `/warehouse`,
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data: createWarehouseDto,
    signal
  })
}

export const getWarehouseControllerCreateMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof warehouseControllerCreate>>,
    TError,
    { data: CreateWarehouseDto },
    TContext
  >
}): UseMutationOptions<
  Awaited<ReturnType<typeof warehouseControllerCreate>>,
  TError,
  { data: CreateWarehouseDto },
  TContext
> => {
  const mutationKey = ['warehouseControllerCreate']
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } }

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof warehouseControllerCreate>>,
    { data: CreateWarehouseDto }
  > = props => {
    const { data } = props ?? {}

    return warehouseControllerCreate(data)
  }

  return { mutationFn, ...mutationOptions }
}

export type WarehouseControllerCreateMutationResult = NonNullable<Awaited<ReturnType<typeof warehouseControllerCreate>>>
export type WarehouseControllerCreateMutationBody = CreateWarehouseDto
export type WarehouseControllerCreateMutationError = unknown

export const useWarehouseControllerCreate = <TError = unknown, TContext = unknown>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof warehouseControllerCreate>>,
      TError,
      { data: CreateWarehouseDto },
      TContext
    >
  },
  queryClient?: QueryClient
): UseMutationResult<
  Awaited<ReturnType<typeof warehouseControllerCreate>>,
  TError,
  { data: CreateWarehouseDto },
  TContext
> => {
  const mutationOptions = getWarehouseControllerCreateMutationOptions(options)

  return useMutation(mutationOptions, queryClient)
}
export const warehouseControllerFindAll = (params?: WarehouseControllerFindAllParams, signal?: AbortSignal) => {
  return customInstance<ApiResponse<void>>({ url: `/warehouse`, method: 'GET', params, signal })
}

export const getWarehouseControllerFindAllQueryKey = (params?: WarehouseControllerFindAllParams) => {
  return [`/warehouse`, ...(params ? [params] : [])] as const
}

export const getWarehouseControllerFindAllInfiniteQueryOptions = <
  TData = InfiniteData<
    Awaited<ReturnType<typeof warehouseControllerFindAll>>,
    WarehouseControllerFindAllParams['page']
  >,
  TError = unknown
>(
  params?: WarehouseControllerFindAllParams,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof warehouseControllerFindAll>>,
        TError,
        TData,
        Awaited<ReturnType<typeof warehouseControllerFindAll>>,
        QueryKey,
        WarehouseControllerFindAllParams['page']
      >
    >
  }
) => {
  const { query: queryOptions } = options ?? {}

  const queryKey = queryOptions?.queryKey ?? getWarehouseControllerFindAllQueryKey(params)

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof warehouseControllerFindAll>>,
    QueryKey,
    WarehouseControllerFindAllParams['page']
  > = ({ signal, pageParam }) => warehouseControllerFindAll({ ...params, page: pageParam || params?.['page'] }, signal)

  return { queryKey, queryFn, staleTime: 10000, ...queryOptions } as UseInfiniteQueryOptions<
    Awaited<ReturnType<typeof warehouseControllerFindAll>>,
    TError,
    TData,
    Awaited<ReturnType<typeof warehouseControllerFindAll>>,
    QueryKey,
    WarehouseControllerFindAllParams['page']
  > & { queryKey: DataTag<QueryKey, TData, TError> }
}

export type WarehouseControllerFindAllInfiniteQueryResult = NonNullable<
  Awaited<ReturnType<typeof warehouseControllerFindAll>>
>
export type WarehouseControllerFindAllInfiniteQueryError = unknown

export function useWarehouseControllerFindAllInfinite<
  TData = InfiniteData<
    Awaited<ReturnType<typeof warehouseControllerFindAll>>,
    WarehouseControllerFindAllParams['page']
  >,
  TError = unknown
>(
  params: undefined | WarehouseControllerFindAllParams,
  options: {
    query: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof warehouseControllerFindAll>>,
        TError,
        TData,
        Awaited<ReturnType<typeof warehouseControllerFindAll>>,
        QueryKey,
        WarehouseControllerFindAllParams['page']
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof warehouseControllerFindAll>>,
          TError,
          Awaited<ReturnType<typeof warehouseControllerFindAll>>,
          QueryKey
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): DefinedUseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useWarehouseControllerFindAllInfinite<
  TData = InfiniteData<
    Awaited<ReturnType<typeof warehouseControllerFindAll>>,
    WarehouseControllerFindAllParams['page']
  >,
  TError = unknown
>(
  params?: WarehouseControllerFindAllParams,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof warehouseControllerFindAll>>,
        TError,
        TData,
        Awaited<ReturnType<typeof warehouseControllerFindAll>>,
        QueryKey,
        WarehouseControllerFindAllParams['page']
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof warehouseControllerFindAll>>,
          TError,
          Awaited<ReturnType<typeof warehouseControllerFindAll>>,
          QueryKey
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useWarehouseControllerFindAllInfinite<
  TData = InfiniteData<
    Awaited<ReturnType<typeof warehouseControllerFindAll>>,
    WarehouseControllerFindAllParams['page']
  >,
  TError = unknown
>(
  params?: WarehouseControllerFindAllParams,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof warehouseControllerFindAll>>,
        TError,
        TData,
        Awaited<ReturnType<typeof warehouseControllerFindAll>>,
        QueryKey,
        WarehouseControllerFindAllParams['page']
      >
    >
  },
  queryClient?: QueryClient
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }

export function useWarehouseControllerFindAllInfinite<
  TData = InfiniteData<
    Awaited<ReturnType<typeof warehouseControllerFindAll>>,
    WarehouseControllerFindAllParams['page']
  >,
  TError = unknown
>(
  params?: WarehouseControllerFindAllParams,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof warehouseControllerFindAll>>,
        TError,
        TData,
        Awaited<ReturnType<typeof warehouseControllerFindAll>>,
        QueryKey,
        WarehouseControllerFindAllParams['page']
      >
    >
  },
  queryClient?: QueryClient
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
  const queryOptions = getWarehouseControllerFindAllInfiniteQueryOptions(params, options)

  const query = useInfiniteQuery(queryOptions, queryClient) as UseInfiniteQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>
  }

  query.queryKey = queryOptions.queryKey

  return query
}

export const getWarehouseControllerFindAllQueryOptions = <
  TData = Awaited<ReturnType<typeof warehouseControllerFindAll>>,
  TError = unknown
>(
  params?: WarehouseControllerFindAllParams,
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof warehouseControllerFindAll>>, TError, TData>> }
) => {
  const { query: queryOptions } = options ?? {}

  const queryKey = queryOptions?.queryKey ?? getWarehouseControllerFindAllQueryKey(params)

  const queryFn: QueryFunction<Awaited<ReturnType<typeof warehouseControllerFindAll>>> = ({ signal }) =>
    warehouseControllerFindAll(params, signal)

  return { queryKey, queryFn, staleTime: 10000, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof warehouseControllerFindAll>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> }
}

export type WarehouseControllerFindAllQueryResult = NonNullable<Awaited<ReturnType<typeof warehouseControllerFindAll>>>
export type WarehouseControllerFindAllQueryError = unknown

export function useWarehouseControllerFindAll<
  TData = Awaited<ReturnType<typeof warehouseControllerFindAll>>,
  TError = unknown
>(
  params: undefined | WarehouseControllerFindAllParams,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof warehouseControllerFindAll>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof warehouseControllerFindAll>>,
          TError,
          Awaited<ReturnType<typeof warehouseControllerFindAll>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useWarehouseControllerFindAll<
  TData = Awaited<ReturnType<typeof warehouseControllerFindAll>>,
  TError = unknown
>(
  params?: WarehouseControllerFindAllParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof warehouseControllerFindAll>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof warehouseControllerFindAll>>,
          TError,
          Awaited<ReturnType<typeof warehouseControllerFindAll>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useWarehouseControllerFindAll<
  TData = Awaited<ReturnType<typeof warehouseControllerFindAll>>,
  TError = unknown
>(
  params?: WarehouseControllerFindAllParams,
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof warehouseControllerFindAll>>, TError, TData>> },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }

export function useWarehouseControllerFindAll<
  TData = Awaited<ReturnType<typeof warehouseControllerFindAll>>,
  TError = unknown
>(
  params?: WarehouseControllerFindAllParams,
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof warehouseControllerFindAll>>, TError, TData>> },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
  const queryOptions = getWarehouseControllerFindAllQueryOptions(params, options)

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>
  }

  query.queryKey = queryOptions.queryKey

  return query
}

export const warehouseControllerFindOneType = (id: string, signal?: AbortSignal) => {
  return customInstance<ApiResponse<ApiResponse<void>>>({ url: `/warehouse/type/${id}`, method: 'GET', signal })
}

export const getWarehouseControllerFindOneTypeQueryKey = (id: string) => {
  return [`/warehouse/type/${id}`] as const
}

export const getWarehouseControllerFindOneTypeInfiniteQueryOptions = <
  TData = InfiniteData<Awaited<ReturnType<typeof warehouseControllerFindOneType>>>,
  TError = unknown
>(
  id: string,
  options?: {
    query?: Partial<UseInfiniteQueryOptions<Awaited<ReturnType<typeof warehouseControllerFindOneType>>, TError, TData>>
  }
) => {
  const { query: queryOptions } = options ?? {}

  const queryKey = queryOptions?.queryKey ?? getWarehouseControllerFindOneTypeQueryKey(id)

  const queryFn: QueryFunction<Awaited<ReturnType<typeof warehouseControllerFindOneType>>> = ({ signal }) =>
    warehouseControllerFindOneType(id, signal)

  return { queryKey, queryFn, enabled: !!id, staleTime: 10000, ...queryOptions } as UseInfiniteQueryOptions<
    Awaited<ReturnType<typeof warehouseControllerFindOneType>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> }
}

export type WarehouseControllerFindOneTypeInfiniteQueryResult = NonNullable<
  Awaited<ReturnType<typeof warehouseControllerFindOneType>>
>
export type WarehouseControllerFindOneTypeInfiniteQueryError = unknown

export function useWarehouseControllerFindOneTypeInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof warehouseControllerFindOneType>>>,
  TError = unknown
>(
  id: string,
  options: {
    query: Partial<UseInfiniteQueryOptions<Awaited<ReturnType<typeof warehouseControllerFindOneType>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof warehouseControllerFindOneType>>,
          TError,
          Awaited<ReturnType<typeof warehouseControllerFindOneType>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): DefinedUseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useWarehouseControllerFindOneTypeInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof warehouseControllerFindOneType>>>,
  TError = unknown
>(
  id: string,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<Awaited<ReturnType<typeof warehouseControllerFindOneType>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof warehouseControllerFindOneType>>,
          TError,
          Awaited<ReturnType<typeof warehouseControllerFindOneType>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useWarehouseControllerFindOneTypeInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof warehouseControllerFindOneType>>>,
  TError = unknown
>(
  id: string,
  options?: {
    query?: Partial<UseInfiniteQueryOptions<Awaited<ReturnType<typeof warehouseControllerFindOneType>>, TError, TData>>
  },
  queryClient?: QueryClient
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }

export function useWarehouseControllerFindOneTypeInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof warehouseControllerFindOneType>>>,
  TError = unknown
>(
  id: string,
  options?: {
    query?: Partial<UseInfiniteQueryOptions<Awaited<ReturnType<typeof warehouseControllerFindOneType>>, TError, TData>>
  },
  queryClient?: QueryClient
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
  const queryOptions = getWarehouseControllerFindOneTypeInfiniteQueryOptions(id, options)

  const query = useInfiniteQuery(queryOptions, queryClient) as UseInfiniteQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>
  }

  query.queryKey = queryOptions.queryKey

  return query
}

export const getWarehouseControllerFindOneTypeQueryOptions = <
  TData = Awaited<ReturnType<typeof warehouseControllerFindOneType>>,
  TError = unknown
>(
  id: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof warehouseControllerFindOneType>>, TError, TData>>
  }
) => {
  const { query: queryOptions } = options ?? {}

  const queryKey = queryOptions?.queryKey ?? getWarehouseControllerFindOneTypeQueryKey(id)

  const queryFn: QueryFunction<Awaited<ReturnType<typeof warehouseControllerFindOneType>>> = ({ signal }) =>
    warehouseControllerFindOneType(id, signal)

  return { queryKey, queryFn, enabled: !!id, staleTime: 10000, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof warehouseControllerFindOneType>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> }
}

export type WarehouseControllerFindOneTypeQueryResult = NonNullable<
  Awaited<ReturnType<typeof warehouseControllerFindOneType>>
>
export type WarehouseControllerFindOneTypeQueryError = unknown

export function useWarehouseControllerFindOneType<
  TData = Awaited<ReturnType<typeof warehouseControllerFindOneType>>,
  TError = unknown
>(
  id: string,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof warehouseControllerFindOneType>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof warehouseControllerFindOneType>>,
          TError,
          Awaited<ReturnType<typeof warehouseControllerFindOneType>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useWarehouseControllerFindOneType<
  TData = Awaited<ReturnType<typeof warehouseControllerFindOneType>>,
  TError = unknown
>(
  id: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof warehouseControllerFindOneType>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof warehouseControllerFindOneType>>,
          TError,
          Awaited<ReturnType<typeof warehouseControllerFindOneType>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useWarehouseControllerFindOneType<
  TData = Awaited<ReturnType<typeof warehouseControllerFindOneType>>,
  TError = unknown
>(
  id: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof warehouseControllerFindOneType>>, TError, TData>>
  },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }

export function useWarehouseControllerFindOneType<
  TData = Awaited<ReturnType<typeof warehouseControllerFindOneType>>,
  TError = unknown
>(
  id: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof warehouseControllerFindOneType>>, TError, TData>>
  },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
  const queryOptions = getWarehouseControllerFindOneTypeQueryOptions(id, options)

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>
  }

  query.queryKey = queryOptions.queryKey

  return query
}

export const warehouseControllerUpdateType = (id: string, updateWarehouseTypeDto: UpdateWarehouseTypeDto) => {
  return customInstance<ApiResponse<ApiResponse<void>>>({
    url: `/warehouse/type/${id}`,
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json' },
    data: updateWarehouseTypeDto
  })
}

export const getWarehouseControllerUpdateTypeMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof warehouseControllerUpdateType>>,
    TError,
    { id: string; data: UpdateWarehouseTypeDto },
    TContext
  >
}): UseMutationOptions<
  Awaited<ReturnType<typeof warehouseControllerUpdateType>>,
  TError,
  { id: string; data: UpdateWarehouseTypeDto },
  TContext
> => {
  const mutationKey = ['warehouseControllerUpdateType']
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } }

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof warehouseControllerUpdateType>>,
    { id: string; data: UpdateWarehouseTypeDto }
  > = props => {
    const { id, data } = props ?? {}

    return warehouseControllerUpdateType(id, data)
  }

  return { mutationFn, ...mutationOptions }
}

export type WarehouseControllerUpdateTypeMutationResult = NonNullable<
  Awaited<ReturnType<typeof warehouseControllerUpdateType>>
>
export type WarehouseControllerUpdateTypeMutationBody = UpdateWarehouseTypeDto
export type WarehouseControllerUpdateTypeMutationError = unknown

export const useWarehouseControllerUpdateType = <TError = unknown, TContext = unknown>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof warehouseControllerUpdateType>>,
      TError,
      { id: string; data: UpdateWarehouseTypeDto },
      TContext
    >
  },
  queryClient?: QueryClient
): UseMutationResult<
  Awaited<ReturnType<typeof warehouseControllerUpdateType>>,
  TError,
  { id: string; data: UpdateWarehouseTypeDto },
  TContext
> => {
  const mutationOptions = getWarehouseControllerUpdateTypeMutationOptions(options)

  return useMutation(mutationOptions, queryClient)
}
export const warehouseControllerRemoveType = (id: string) => {
  return customInstance<ApiResponse<void>>({ url: `/warehouse/type/${id}`, method: 'DELETE' })
}

export const getWarehouseControllerRemoveTypeMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof warehouseControllerRemoveType>>,
    TError,
    { id: string },
    TContext
  >
}): UseMutationOptions<Awaited<ReturnType<typeof warehouseControllerRemoveType>>, TError, { id: string }, TContext> => {
  const mutationKey = ['warehouseControllerRemoveType']
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } }

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof warehouseControllerRemoveType>>,
    { id: string }
  > = props => {
    const { id } = props ?? {}

    return warehouseControllerRemoveType(id)
  }

  return { mutationFn, ...mutationOptions }
}

export type WarehouseControllerRemoveTypeMutationResult = NonNullable<
  Awaited<ReturnType<typeof warehouseControllerRemoveType>>
>

export type WarehouseControllerRemoveTypeMutationError = unknown

export const useWarehouseControllerRemoveType = <TError = unknown, TContext = unknown>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof warehouseControllerRemoveType>>,
      TError,
      { id: string },
      TContext
    >
  },
  queryClient?: QueryClient
): UseMutationResult<Awaited<ReturnType<typeof warehouseControllerRemoveType>>, TError, { id: string }, TContext> => {
  const mutationOptions = getWarehouseControllerRemoveTypeMutationOptions(options)

  return useMutation(mutationOptions, queryClient)
}
export const warehouseControllerFindOne = (id: string, signal?: AbortSignal) => {
  return customInstance<ApiResponse<void>>({ url: `/warehouse/${id}`, method: 'GET', signal })
}

export const getWarehouseControllerFindOneQueryKey = (id: string) => {
  return [`/warehouse/${id}`] as const
}

export const getWarehouseControllerFindOneInfiniteQueryOptions = <
  TData = InfiniteData<Awaited<ReturnType<typeof warehouseControllerFindOne>>>,
  TError = unknown
>(
  id: string,
  options?: {
    query?: Partial<UseInfiniteQueryOptions<Awaited<ReturnType<typeof warehouseControllerFindOne>>, TError, TData>>
  }
) => {
  const { query: queryOptions } = options ?? {}

  const queryKey = queryOptions?.queryKey ?? getWarehouseControllerFindOneQueryKey(id)

  const queryFn: QueryFunction<Awaited<ReturnType<typeof warehouseControllerFindOne>>> = ({ signal }) =>
    warehouseControllerFindOne(id, signal)

  return { queryKey, queryFn, enabled: !!id, staleTime: 10000, ...queryOptions } as UseInfiniteQueryOptions<
    Awaited<ReturnType<typeof warehouseControllerFindOne>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> }
}

export type WarehouseControllerFindOneInfiniteQueryResult = NonNullable<
  Awaited<ReturnType<typeof warehouseControllerFindOne>>
>
export type WarehouseControllerFindOneInfiniteQueryError = unknown

export function useWarehouseControllerFindOneInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof warehouseControllerFindOne>>>,
  TError = unknown
>(
  id: string,
  options: {
    query: Partial<UseInfiniteQueryOptions<Awaited<ReturnType<typeof warehouseControllerFindOne>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof warehouseControllerFindOne>>,
          TError,
          Awaited<ReturnType<typeof warehouseControllerFindOne>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): DefinedUseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useWarehouseControllerFindOneInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof warehouseControllerFindOne>>>,
  TError = unknown
>(
  id: string,
  options?: {
    query?: Partial<UseInfiniteQueryOptions<Awaited<ReturnType<typeof warehouseControllerFindOne>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof warehouseControllerFindOne>>,
          TError,
          Awaited<ReturnType<typeof warehouseControllerFindOne>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useWarehouseControllerFindOneInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof warehouseControllerFindOne>>>,
  TError = unknown
>(
  id: string,
  options?: {
    query?: Partial<UseInfiniteQueryOptions<Awaited<ReturnType<typeof warehouseControllerFindOne>>, TError, TData>>
  },
  queryClient?: QueryClient
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }

export function useWarehouseControllerFindOneInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof warehouseControllerFindOne>>>,
  TError = unknown
>(
  id: string,
  options?: {
    query?: Partial<UseInfiniteQueryOptions<Awaited<ReturnType<typeof warehouseControllerFindOne>>, TError, TData>>
  },
  queryClient?: QueryClient
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
  const queryOptions = getWarehouseControllerFindOneInfiniteQueryOptions(id, options)

  const query = useInfiniteQuery(queryOptions, queryClient) as UseInfiniteQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>
  }

  query.queryKey = queryOptions.queryKey

  return query
}

export const getWarehouseControllerFindOneQueryOptions = <
  TData = Awaited<ReturnType<typeof warehouseControllerFindOne>>,
  TError = unknown
>(
  id: string,
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof warehouseControllerFindOne>>, TError, TData>> }
) => {
  const { query: queryOptions } = options ?? {}

  const queryKey = queryOptions?.queryKey ?? getWarehouseControllerFindOneQueryKey(id)

  const queryFn: QueryFunction<Awaited<ReturnType<typeof warehouseControllerFindOne>>> = ({ signal }) =>
    warehouseControllerFindOne(id, signal)

  return { queryKey, queryFn, enabled: !!id, staleTime: 10000, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof warehouseControllerFindOne>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> }
}

export type WarehouseControllerFindOneQueryResult = NonNullable<Awaited<ReturnType<typeof warehouseControllerFindOne>>>
export type WarehouseControllerFindOneQueryError = unknown

export function useWarehouseControllerFindOne<
  TData = Awaited<ReturnType<typeof warehouseControllerFindOne>>,
  TError = unknown
>(
  id: string,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof warehouseControllerFindOne>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof warehouseControllerFindOne>>,
          TError,
          Awaited<ReturnType<typeof warehouseControllerFindOne>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useWarehouseControllerFindOne<
  TData = Awaited<ReturnType<typeof warehouseControllerFindOne>>,
  TError = unknown
>(
  id: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof warehouseControllerFindOne>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof warehouseControllerFindOne>>,
          TError,
          Awaited<ReturnType<typeof warehouseControllerFindOne>>
        >,
        'initialData'
      >
  },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }
export function useWarehouseControllerFindOne<
  TData = Awaited<ReturnType<typeof warehouseControllerFindOne>>,
  TError = unknown
>(
  id: string,
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof warehouseControllerFindOne>>, TError, TData>> },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }

export function useWarehouseControllerFindOne<
  TData = Awaited<ReturnType<typeof warehouseControllerFindOne>>,
  TError = unknown
>(
  id: string,
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof warehouseControllerFindOne>>, TError, TData>> },
  queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
  const queryOptions = getWarehouseControllerFindOneQueryOptions(id, options)

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData, TError>
  }

  query.queryKey = queryOptions.queryKey

  return query
}

export const warehouseControllerUpdate = (id: string, updateWarehouseDto: UpdateWarehouseDto) => {
  return customInstance<ApiResponse<ApiResponse<void>>>({
    url: `/warehouse/${id}`,
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json' },
    data: updateWarehouseDto
  })
}

export const getWarehouseControllerUpdateMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof warehouseControllerUpdate>>,
    TError,
    { id: string; data: UpdateWarehouseDto },
    TContext
  >
}): UseMutationOptions<
  Awaited<ReturnType<typeof warehouseControllerUpdate>>,
  TError,
  { id: string; data: UpdateWarehouseDto },
  TContext
> => {
  const mutationKey = ['warehouseControllerUpdate']
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } }

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof warehouseControllerUpdate>>,
    { id: string; data: UpdateWarehouseDto }
  > = props => {
    const { id, data } = props ?? {}

    return warehouseControllerUpdate(id, data)
  }

  return { mutationFn, ...mutationOptions }
}

export type WarehouseControllerUpdateMutationResult = NonNullable<Awaited<ReturnType<typeof warehouseControllerUpdate>>>
export type WarehouseControllerUpdateMutationBody = UpdateWarehouseDto
export type WarehouseControllerUpdateMutationError = unknown

export const useWarehouseControllerUpdate = <TError = unknown, TContext = unknown>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof warehouseControllerUpdate>>,
      TError,
      { id: string; data: UpdateWarehouseDto },
      TContext
    >
  },
  queryClient?: QueryClient
): UseMutationResult<
  Awaited<ReturnType<typeof warehouseControllerUpdate>>,
  TError,
  { id: string; data: UpdateWarehouseDto },
  TContext
> => {
  const mutationOptions = getWarehouseControllerUpdateMutationOptions(options)

  return useMutation(mutationOptions, queryClient)
}
export const warehouseControllerRemove = (id: string) => {
  return customInstance<ApiResponse<void>>({ url: `/warehouse/${id}`, method: 'DELETE' })
}

export const getWarehouseControllerRemoveMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<Awaited<ReturnType<typeof warehouseControllerRemove>>, TError, { id: string }, TContext>
}): UseMutationOptions<Awaited<ReturnType<typeof warehouseControllerRemove>>, TError, { id: string }, TContext> => {
  const mutationKey = ['warehouseControllerRemove']
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } }

  const mutationFn: MutationFunction<Awaited<ReturnType<typeof warehouseControllerRemove>>, { id: string }> = props => {
    const { id } = props ?? {}

    return warehouseControllerRemove(id)
  }

  return { mutationFn, ...mutationOptions }
}

export type WarehouseControllerRemoveMutationResult = NonNullable<Awaited<ReturnType<typeof warehouseControllerRemove>>>

export type WarehouseControllerRemoveMutationError = unknown

export const useWarehouseControllerRemove = <TError = unknown, TContext = unknown>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof warehouseControllerRemove>>,
      TError,
      { id: string },
      TContext
    >
  },
  queryClient?: QueryClient
): UseMutationResult<Awaited<ReturnType<typeof warehouseControllerRemove>>, TError, { id: string }, TContext> => {
  const mutationOptions = getWarehouseControllerRemoveMutationOptions(options)

  return useMutation(mutationOptions, queryClient)
}
