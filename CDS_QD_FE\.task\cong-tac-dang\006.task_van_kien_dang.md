# Quy trình xây dựng giao diện trang "Quản lý Văn kiện Đảng"

---

**Trang: Quản lý Văn kiện Đảng (`/${locale}/cong-tac-dang/van-kien`)**

- **<PERSON><PERSON><PERSON> (Tiêu đề hiển thị):** `Th<PERSON> viện Văn kiện Đảng`
- **<PERSON><PERSON><PERSON> đích chính của Trang:** C<PERSON> cấp một kho lưu trữ tập trung, an toàn và có tổ chức cho các loại văn kiện, tài liệu của <PERSON>ả<PERSON>. Ng<PERSON>ời dùng (c<PERSON> quyền) c<PERSON> thể duy<PERSON>, tì<PERSON> ki<PERSON>, tả<PERSON> lên, xem và tải về các văn kiện này.

---

#### I. Chức năng trang `VanKienDangPage`

1.  **<PERSON><PERSON><PERSON> năng "Hiển thị Dan<PERSON> sách/Duyệt Kho Văn kiện"**

    - **Tổng quan:** <PERSON>rang chính hiển thị danh sách các văn kiện Đảng đã được lưu trữ. Giao diện cần cho phép người dùng duyệt qua kho văn kiện một cách linh hoạt.
    - **Chi tiết:**
      - **Chế độ hiển thị mặc định:** Có thể là một bảng liệt kê các văn kiện với các thông tin tóm tắt, hỗ trợ phân trang.
      - **Chức năng duyệt kho (tùy chọn, bổ sung cho bảng):**
        - **Duyệt theo cấu trúc thư mục (nếu có):** Nếu văn kiện được tổ chức theo một cấu trúc thư mục logic (ví dụ: theo năm, theo loại nghị quyết, theo cấp ban hành), giao diện có thể hiển thị một panel dạng cây thư mục ở bên trái. Khi người dùng chọn một thư mục, bảng danh sách văn kiện sẽ cập nhật tương ứng.
        - **Duyệt theo Danh mục Loại Văn kiện:** Một bộ lọc hoặc panel cho phép chọn từ danh mục `DmLoaiVanKienDang` (ví dụ: Nghị quyết, Chỉ thị, Quy định, Hướng dẫn...).
        - **Duyệt theo Danh mục Chủ đề/Lĩnh vực:** Một bộ lọc hoặc panel cho phép chọn từ danh mục `DmChuDeLinhVucDang` (ví dụ: Xây dựng Đảng, Kiểm tra giám sát, Tư tưởng văn hóa...).
      - Thông tin mỗi văn kiện được trình bày trên một hàng của bảng (hoặc một item trong chế độ xem lưới nếu có).

2.  **Chức năng "Tìm kiếm và Lọc Nâng cao Văn kiện"**

    - **Tổng quan:** Cung cấp các công cụ mạnh mẽ để người dùng tìm kiếm chính xác văn kiện cần thiết.
    - **Chi tiết:**
      - **Tìm kiếm theo Metadata:**
        - **Ô tìm kiếm chung:** Cho phép nhập từ khóa tìm kiếm trong các trường như Số hiệu Văn kiện, Tên Văn kiện/Trích yếu.
        - **Lọc theo "Số hiệu Văn kiện":** Ô nhập liệu.
        - **Lọc theo "Tên Văn kiện/Trích yếu":** Ô nhập liệu.
        - **Lọc theo "Ngày Ban hành":** Chọn khoảng ngày (Từ ngày - Đến ngày).
        - **Lọc theo "Cơ quan Ban hành":** Trường lựa chọn hoặc nhập liệu (dữ liệu từ danh mục `DmCoQuanToChuc` hoặc nhập tự do `CoQuanBanHanhID` trong `VanKienTaiLieuDang` nếu là text).
        - **Lọc theo "Loại Văn kiện":** Trường lựa chọn từ danh mục `DmLoaiVanKienDang`.
        - **Lọc theo "Chủ đề/Lĩnh vực":** Trường lựa chọn từ danh mục `DmChuDeLinhVucDang`.
        - **Lọc theo "Độ mật":** Trường lựa chọn từ danh mục `DmDoMat`. (Kết quả tìm kiếm và hiển thị phải tuân thủ quyền truy cập theo độ mật của người dùng).
      - **Tìm kiếm Toàn văn (Full-text search, nếu có OCR):**
        - Một ô nhập liệu riêng cho phép tìm kiếm từ khóa bên trong nội dung của các file văn kiện (nếu hệ thống đã thực hiện OCR và индексация nội dung `NoiDungTextTrichXuat`).
      - **Nút "Tìm kiếm" / "Áp dụng bộ lọc".**
      - **Nút "Đặt lại" / "Xóa bộ lọc".**

3.  **Chức năng "Tải lên Văn kiện mới"**

    - **Tổng quan:** Cho phép người dùng (có quyền) đóng góp văn kiện mới vào thư viện.
    - **Chi tiết:**
      - Trên trang danh sách, có một nút "Tải lên Văn kiện mới".
      - Khi nhấn nút này, hệ thống sẽ mở ra một giao diện (thường là một cửa sổ/dialog lớn hoặc một trang riêng) cho phép người dùng nhập thông tin mô tả (metadata) và tải lên file văn kiện.
      - **Form nhập thông tin metadata và upload file:**
        - `SoHieuVanKien`: Số hiệu văn kiện.
        - `TenVanKienTrichYeu`: Tên/Trích yếu nội dung văn kiện (bắt buộc).
        - `NgayBanHanh`: Ngày ban hành.
        - `CoQuanBanHanhID` (hoặc trường text `CoQuanBanHanh`): Cơ quan/Tổ chức ban hành (lựa chọn từ danh mục `DmCoQuanToChuc` hoặc nhập).
        - `LoaiVanKienID`: Lựa chọn Loại văn kiện (bắt buộc) từ `DmLoaiVanKienDang`.
        - `ChuDeLinhVucID`: Lựa chọn Chủ đề/Lĩnh vực từ `DmChuDeLinhVucDang`.
        - `DoMatID`: Lựa chọn Độ mật (bắt buộc) từ `DmDoMat`.
        - `PhamViPhoBien`: Phạm vi phổ biến (text).
        - `FileURL`: Trường tải lên file văn kiện (PDF, DOCX...). Bắt buộc. (Hệ thống cần xử lý lưu trữ file an toàn, có thể mã hóa dựa trên `DoMatID`).
        - `(Tùy chọn) NoiDungTextTrichXuat`: Nếu hệ thống có OCR phía server, trường này có thể được điền tự động sau khi upload. Hoặc cho phép người dùng dán nội dung text vào.
        - `(Tùy chọn) PhienBan`: Phiên bản của văn kiện.
        - `(Tùy chọn) NguonGocTaiLieu`: Nguồn gốc của tài liệu.
        - `(Thông tin hệ thống) NguoiTaiLenID`: (Lấy tự động từ user đang đăng nhập).
        - `(Thông tin hệ thống) NgayTaiLen`: (Lấy tự động).
      - Nút "Lưu Văn kiện" và "Hủy".
      - Sau khi lưu, văn kiện mới sẽ xuất hiện trong danh sách (với các kiểm soát hiển thị dựa trên quyền và độ mật).

4.  **Chức năng "Xem chi tiết Thông tin (Metadata) Văn kiện" (hành động trên dòng)**

    - **Tổng quan:** Cho phép người dùng xem các thông tin mô tả chi tiết của một văn kiện cụ thể.
    - **Chi tiết:**
      - Mỗi hàng trong bảng danh sách sẽ có hành động "Xem chi tiết metadata" (nút bấm hoặc biểu tượng).
      - Khi nhấn vào, một cửa sổ/dialog hoặc một vùng hiển thị sẽ xuất hiện, trình bày đầy đủ các trường metadata của văn kiện đó (Số hiệu, Tên, Ngày BH, Cơ quan BH, Loại, Chủ đề, Độ mật, Phạm vi, Người tải lên, Ngày tải lên, Số lượt xem, Số lượt tải...).

5.  **Chức năng "Xem/Tải file Nội dung Văn kiện" (hành động trên dòng)**

    - **Tổng quan:** Cho phép người dùng truy cập vào nội dung đầy đủ của file văn kiện.
    - **Chi tiết:**
      - Mỗi hàng trong bảng danh sách sẽ có hành động "Xem file" và/hoặc "Tải file" (nút bấm hoặc biểu tượng).
      - **"Xem file":** Nếu file là định dạng có thể xem trực tiếp trên trình duyệt (ví dụ PDF), hệ thống sẽ mở file trong một tab mới hoặc một trình xem tích hợp.
      - **"Tải file":** Hệ thống sẽ cho phép người dùng tải file văn kiện về máy.
      - **Kiểm soát quyền truy cập nghiêm ngặt:** Cả hai hành động này phải kiểm tra quyền của người dùng dựa trên `DoMatID` của văn kiện và quyền hạn được gán cho người dùng đó. Nếu người dùng không có quyền truy cập độ mật tương ứng, các nút này sẽ bị vô hiệu hóa hoặc ẩn đi.
      - Nếu file được lưu trữ mã hóa, hệ thống cần giải mã động trước khi cho xem/tải.
      - Mỗi lần xem/tải có thể cập nhật các trường `SoLuotXem`, `SoLuotTaiVe`.

6.  **Chức năng "Sửa Thông tin (Metadata) Văn kiện" (hành động trên dòng)**

    - **Tổng quan:** Cho phép người dùng (có quyền) chỉnh sửa các thông tin mô tả của một văn kiện đã được tải lên.
    - **Chi tiết:**
      - Mỗi hàng sẽ có hành động "Sửa metadata".
      - Khi nhấn vào, mở giao diện tương tự form "Tải lên Văn kiện mới" nhưng các trường đã điền sẵn thông tin của văn kiện đang chọn. Người dùng có thể thay đổi các trường metadata.
      - Việc thay đổi file (`FileURL`) thường có nghĩa là tải lên một phiên bản mới hoặc một file đính chính.
      - Thay đổi `DoMatID` cần được kiểm soát chặt chẽ.

7.  **Chức năng "Quản lý Trạng thái Văn kiện" (hành động trên dòng, tùy chọn)**

    - **Tổng quan:** Cho phép thay đổi trạng thái của văn kiện (ví dụ: Đang sử dụng, Ngừng sử dụng, Lưu trữ).
    - **Chi tiết:**
      - Có thể có hành động "Thay đổi trạng thái".
      - Mở một lựa chọn các trạng thái từ `DmTrangThaiVanKien`.
      - Cập nhật trường `TrangThaiVanKienID`.

8.  **Chức năng "Xóa Văn kiện" (hành động trên dòng)**
    - **Tổng quan:** Cho phép người dùng (có quyền hạn cao) xóa một văn kiện khỏi thư viện.
    - **Chi tiết:**
      - Hành động "Xóa".
      - Cần có hộp thoại xác nhận.
      - Việc xóa có thể là xóa mềm (thay đổi `TrangThaiVanKienID` thành "Đã xóa/Lưu trữ sâu") hoặc xóa cứng (xóa bản ghi và file vật lý - cần cân nhắc kỹ lưỡng).

---

#### II. Bảng dữ liệu được sử dụng trên trang `VanKienDangPage`

- **Bảng `VanKienTaiLieuDang` (PartyDocuments)**

  - `ID: number` (BIGINT, PK, AUTO_INCREMENT)
  - `SoHieuVanKien: string` (VARCHAR(100))
  - `TenVanKienTrichYeu: string` (NVARCHAR(500), NOT NULL)
  - `NgayBanHanh: date` (DATE)
  - `CoQuanBanHanhID: number` (BIGINT, FK REFERENCES DmCoQuanToChuc(ID)) (Hoặc có thể là trường `CoQuanBanHanh_Text: string` nếu không dùng danh mục cố định)
  - `LoaiVanKienID: number` (INT, FK REFERENCES DmLoaiVanKienDang(ID))
  - `ChuDeLinhVucID: number` (INT, FK REFERENCES DmChuDeLinhVucDang(ID))
  - `DoMatID: number` (INT, NOT NULL, FK REFERENCES DmDoMat(ID))
  - `PhamViPhoBien: string` (NTEXT)
  - `FileURL: string` (VARCHAR(255), NOT NULL)
  - `NoiDungTextTrichXuat: string` (NTEXT NULL) - Nội dung text từ OCR
  - `NguoiTaiLenID: string` (VARCHAR(50)) - ID Người tải lên
  - `NgayTaiLen: datetime` (TIMESTAMP, DEFAULT CURRENT_TIMESTAMP)
  - `TrangThaiVanKienID: number` (INT, FK REFERENCES DmTrangThaiVanKien(ID)) - Trạng thái (Còn hiệu lực, Hết hiệu lực, Bị thay thế...)
  - `PhienBan: string` (VARCHAR(20), DEFAULT '1.0') - (Trường này không có trong tài liệu gốc, nhưng được đề cập trong mô tả chức năng trang Quản lý Tài liệu Chính trị, cân nhắc thêm nếu cần)
  - `NguonGocTaiLieu: string` (NVARCHAR(255)) - (Trường này không có trong tài liệu gốc, cân nhắc thêm nếu cần)
  - `SoLuotXem: number` (INT, DEFAULT 0) - (Trường này không có trong tài liệu gốc, cân nhắc thêm nếu cần)
  - `SoLuotTaiVe: number` (INT, DEFAULT 0) - (Trường này không có trong tài liệu gốc, cân nhắc thêm nếu cần)

- **Danh mục `DmLoaiVanKienDang`**

  - `ID: number` (PK)
  - `TenLoaiVanKien: string` (Ví dụ: Nghị quyết, Chỉ thị, Quy định, Hướng dẫn, Công văn, Báo cáo...)

- **Danh mục `DmChuDeLinhVucDang`**

  - `ID: number` (PK)
  - `TenChuDe: string` (Ví dụ: Xây dựng Đảng, Công tác Kiểm tra, Công tác Dân vận, Quân sự - Quốc phòng...)

- **Danh mục `DmDoMat`** (Đã liệt kê ở phần Nghị quyết Đảng)

  - `ID: number` (PK)
  - `TenDoMat: string`

- **Danh mục `DmCoQuanToChuc`** (Dùng cho `CoQuanBanHanhID` nếu là lựa chọn cố định)

  - `ID: number` (PK)
  - `TenCoQuan: string`

- **Danh mục `DmTrangThaiVanKien`**
  - `ID: number` (PK)
  - `TenTrangThai: string` (Ví dụ: Đang sử dụng, Hết hiệu lực, Lưu trữ, Bị thay thế)

---

#### III. Liên kết với page khác

1.  **Trang Chi tiết Buổi Sinh hoạt Đảng / Nghị quyết Đảng:**
    - Các file Nghị quyết, tài liệu chuẩn bị cho buổi sinh hoạt có thể được coi là một dạng Văn kiện Đảng và có thể được quản lý tập trung tại đây, hoặc có cơ chế liên kết/đồng bộ.
2.  **Các module khác (Quản lý Quân nhân, Công tác Chính trị):**
    - Các tài liệu, quyết định từ các module khác nếu mang tính chất văn kiện chung của Đảng cũng có thể được đưa vào hoặc liên kết đến Thư viện Văn kiện Đảng này.

---

Trang "Thư viện Văn kiện Đảng" sẽ là một công cụ quan trọng để lưu trữ và phổ biến các văn kiện chính thức của Đảng một cách có hệ thống và bảo mật.
