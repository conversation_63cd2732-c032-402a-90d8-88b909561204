/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { HoSoVuViecKyLuatDangVienEntity } from '~/database/typeorm/entities/hoSoVuViecKyLuatDangVien.entity';

@Injectable()
export class HoSoVuViecKyLuatDangVienRepository extends Repository<HoSoVuViecKyLuatDangVienEntity> {
    constructor(private dataSource: DataSource) {
        super(HoSoVuViecKyLuatDangVienEntity, dataSource.createEntityManager());
    }
}
