# Quy trình xây dựng giao diện trang "Thêm mới Hồ sơ Quân nhân"

Tài liệu này mô tả chi tiết quy trình xây dựng giao diện trang "Thêm mới Hồ sơ Quân nhân" với vai trò Tech Lead/Senior Developer, bám sát các component của Vuexy Next.js Admin Template.

---

## 1. C<PERSON>u trúc thư mục và quy chuẩn đặt tên

1. **Cấu trúc thư mục**

   - **Page chính**:

     ```
     src/app/[lang]/(dashboard)/quan-ly-quan-nhan/them-moi/page.tsx
     ```

   - **Components**:

     ```
     src/views/quan-ly-quan-nhan/them-moi/
       ├─ ThemMoiQuanNhanPage.tsx  // Component chính, quản lý form
       ├─ CardThongTinCoBan.tsx    // Card thông tin cơ bản
       ├─ CardLyLichCaNhan.tsx     // Card lý lịch cá nhân
       ├─ FormActions.tsx          // Các nút hành động form
       └─ mockData.ts              // Dữ liệu mẫu và types
     ```

2. **Quy ước đặt tên**

   - **Component & file**: PascalCase, không dấu (Ví dụ: `CardThongTinCoBan.tsx`).
   - **Biến, hàm**: camelCase, hàm xử lý sự kiện bắt đầu bằng `handle` (Ví dụ: `handleSaveAndNew`).
   - **Hằng số**: UPPER_SNAKE_CASE (Ví dụ: `DM_DON_VI`).

---

## 2. Các bước thực hiện chi tiết

### Bước 1: Tạo page.tsx

- Trong `src/app/[lang]/(dashboard)/quan-ly-quan-nhan/them-moi/page.tsx` khởi tạo:

  ```tsx
  import ThemMoiQuanNhanPage from '@/views/quan-ly-quan-nhan/them-moi/ThemMoiQuanNhanPage'

  export default function Page() {
    return <ThemMoiQuanNhanPage />
  }
  ```

---

### Bước 2: Định nghĩa mockData / Types

- **mockData.ts**

  - Interface `QuanNhanFormValues` mô tả đầy đủ các thuộc tính form.
  - Các types: `QuanNhanStatus`, `CapBac`, `ChucVu`, `DanToc`, `TonGiao`, v.v.
  - Các danh mục dữ liệu mẫu: `DM_DON_VI`, `DM_CAP_BAC`, `DM_CHUC_VU`, `DM_TINH_TP`, v.v.
  - Giá trị mặc định cho form (`defaultValues`).

- Ví dụ một số types và danh mục:

  ```typescript
  export type QuanNhanStatus = 'active' | 'inactive' | 'leave' | 'deceased'

  export const DM_CAP_BAC: CapBac[] = [
    'Thiếu úy',
    'Trung úy',
    'Thượng úy',
    'Đại úy',
    'Thiếu tá',
    'Trung tá',
    'Thượng tá',
    'Đại tá',
    'Thiếu tướng',
    'Trung tướng',
    'Thượng tướng',
    'Đại tướng'
  ]

  export const DM_TRANG_THAI: { value: QuanNhanStatus; label: string }[] = [
    { value: 'active', label: 'Đang công tác' },
    { value: 'inactive', label: 'Tạm nghỉ' },
    { value: 'leave', label: 'Xuất ngũ' },
    { value: 'deceased', label: 'Từ trần' }
  ]
  ```

---

### Bước 3: Tạo component chính – ThemMoiQuanNhanPage.tsx

- **Chức năng**:

  - Quản lý state form với React Hook Form và Yup validation.
  - Render Breadcrumbs, Tiêu đề, hai card thông tin, khu vực action.
  - Xử lý các hành động form: submit, save and new, save draft, cancel.

- **Cấu trúc chính**:

  ```tsx
  'use client'

  // Imports...

  const ThemMoiQuanNhanPage = () => {
    // States
    const [isSubmitting, setIsSubmitting] = useState<boolean>(false)

    // Hooks
    const router = useRouter()
    const { lang: locale } = useParams()

    // Form methods
    const methods = useForm<QuanNhanFormValues>({
      defaultValues,
      resolver: yupResolver(validationSchema),
      mode: 'onChange'
    })

    // Handle submit, save and new, save draft functions...

    return (
      <FormProvider {...methods}>
        <form onSubmit={methods.handleSubmit(onSubmit)}>
          <Grid container spacing={6}>
            {/* Breadcrumbs */}
            {/* Title */}
            {/* Cards */}
            {/* Form Actions */}
          </Grid>
        </form>
      </FormProvider>
    )
  }
  ```

---

### Bước 4: Card "Thông tin Cơ bản" – CardThongTinCoBan.tsx

- **Layout cải tiến**:

  - Ảnh chân dung đặt ở góc trái với tỷ lệ 2:3 (120x180px).
  - Các trường thông tin được bố trí bên cạnh và bên dưới ảnh.
  - Sử dụng Grid system với responsive breakpoints (xs, sm, md).

- **Cấu trúc**:

  ```tsx
  <Card>
    <CardHeader title='Thông tin Cơ bản' />
    <CardContent>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <Grid container spacing={4}>
            {/* Ảnh chân dung */}
            <Grid item xs={12} sm={3} md={2}>
              {/* Upload image component */}
            </Grid>

            {/* Các trường bên cạnh ảnh */}
            <Grid item xs={12} sm={9} md={10}>
              <Grid container spacing={3}>
                {/* Các trường thông tin cơ bản */}
              </Grid>
            </Grid>

            {/* Các trường thông tin quân vụ */}
            <Grid item xs={12} sm={6} md={3}>
              {/* Ngày nhập ngũ */}
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              {/* Đơn vị hiện tại */}
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              {/* Cấp bậc */}
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              {/* Chức vụ */}
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </CardContent>
  </Card>
  ```

- **Fields**:

  - Ảnh chân dung với tải lên ảnh
  - Thông tin nhân thân: Số hiệu QN, Họ tên, Tên thường dùng, Ngày sinh, CCCD, Giới tính
  - Thông tin liên hệ: Email, Số điện thoại, Trạng thái hồ sơ
  - Thông tin quân vụ: Ngày nhập ngũ, Đơn vị, Cấp bậc, Chức vụ

---

### Bước 5: Card "Thông tin Lý lịch Cá nhân" – CardLyLichCaNhan.tsx

- **Layout cải tiến**:

  - Chia thành 6 phần rõ ràng với tiêu đề phân nhóm.
  - Sử dụng Grid system với responsive breakpoints (xs, md).
  - Các trường được nhóm lại một cách hợp lý theo chủ đề.

- **Cấu trúc phân nhóm**:

  ```tsx
  <Card>
    <CardHeader title='Thông tin Lý lịch Cá nhân' />
    <CardContent>
      <Grid container spacing={3}>
        {/* SECTION 1: Thông tin Quê quán và Nơi ở */}
        <Grid item xs={12}>
          <Typography variant='subtitle2' sx={{ fontWeight: 600, mb: 1 }}>
            1. Thông tin Quê quán và Nơi ở
          </Typography>
        </Grid>
        {/* Fields for section 1 */}

        {/* SECTION 2: Thông tin Dân tộc, Tôn giáo, Thành phần */}
        <Grid item xs={12}>
          <Typography variant='subtitle2' sx={{ fontWeight: 600, mb: 1, mt: 2 }}>
            2. Thông tin Dân tộc, Tôn giáo, Thành phần
          </Typography>
        </Grid>
        {/* Fields for section 2 */}

        {/* SECTION 3-6: Other sections */}
      </Grid>
    </CardContent>
  </Card>
  ```

- **Các phần thông tin**:

  1. **Thông tin Quê quán và Nơi ở**:

     - Quê quán: Tỉnh/TP, Quận/Huyện, Xã/Phường
     - Nơi ở hiện tại: Tỉnh/TP, Quận/Huyện, Xã/Phường

  2. **Thông tin Dân tộc, Tôn giáo, Thành phần**:

     - Dân tộc, Tôn giáo
     - Thành phần gia đình khi nhập ngũ
     - Nghề nghiệp trước khi nhập ngũ

  3. **Thông tin Trình độ**:

     - Trình độ văn hóa phổ thông
     - Trình độ Lý luận Chính trị
     - Trình độ Ngoại ngữ
     - Trình độ Tin học

  4. **Thông tin Năng khiếu và Đặc điểm**:

     - Năng khiếu, Sở trường
     - Đặc điểm Lịch sử Bản thân

  5. **Thông tin Đảng, Đoàn và Hộ khẩu**:

     - Ngày vào Đảng chính thức
     - Ngày vào Đoàn TNCS Hồ Chí Minh
     - Số Sổ Hộ khẩu
     - Tình trạng Hôn nhân

  6. **Ghi chú**:
     - Ghi chú Lý lịch

---

### Bước 6: Khu vực Hành động Form – FormActions.tsx

- **Layout**:

  ```tsx
  <Box display='flex' justifyContent='flex-end' gap={2} mt={3}>
    {/* Buttons with icons */}
  </Box>
  ```

- **Buttons**:

  - `<Button variant="contained" color="primary" startIcon={<i className="tabler-device-floppy-plus" />}>Lưu và Thêm mới</Button>`
  - `<Button variant="contained" startIcon={<i className="tabler-device-floppy" />}>Lưu</Button>`
  - `<Button variant="outlined" startIcon={<i className="tabler-file-pencil" />}>Lưu tạm</Button>`
  - `<Button variant="text" startIcon={<i className="tabler-x" />}>Hủy</Button>`

- **Xử lý sự kiện**:

  - `onSubmit`: Lưu form và chuyển đến trang danh sách
  - `onSaveAndNew`: Lưu form và reset để thêm mới
  - `onSaveDraft`: Lưu nháp
  - `handleCancel`: Hủy và quay lại trang danh sách

---

### Bước 7: Quản lý Form & Validation

- **React Hook Form + Yup**:

  ```tsx
  const validationSchema = yup.object().shape({
    soHieuQuanNhan: yup.string().required('Số hiệu Quân nhân là bắt buộc'),
    hoTenKhaiSinh: yup.string().required('Họ và tên Khai sinh là bắt buộc'),
    ngaySinh: yup.date().nullable().required('Ngày sinh là bắt buộc'),
    email: yup.string().email('Email không hợp lệ').nullable(),
    soDienThoai: yup
      .string()
      .nullable()
      .matches(/^[0-9]{10,11}$/, { message: 'Số điện thoại không hợp lệ', excludeEmptyString: true })
  })

  const methods = useForm<QuanNhanFormValues>({
    defaultValues,
    resolver: yupResolver(validationSchema),
    mode: 'onChange'
  })
  ```

- **Controller Pattern**:

  ```tsx
  <Controller
    name='soHieuQuanNhan'
    control={control}
    rules={{ required: 'Bắt buộc' }}
    render={({ field }) => (
      <TextField
        {...field}
        label='Số hiệu QN'
        placeholder='Nhập số hiệu QN'
        fullWidth
        error={!!errors.soHieuQuanNhan}
        helperText={errors.soHieuQuanNhan?.message}
        InputLabelProps={{ sx: { mb: 0.5, color: 'grey.200' } }}
      />
    )}
  />
  ```

---

### Bước 8: Xử lý Submit và Thông báo

- **Submit Handler**:

  ```tsx
  const onSubmit = useCallback(
    async (data: QuanNhanFormValues) => {
      setIsSubmitting(true)
      try {
        // Gọi API
        await new Promise(resolve => setTimeout(resolve, 1000))
        toast.success('Lưu hồ sơ quân nhân thành công')
        router.push(getLocalizedUrl('quan-ly-quan-nhan/danh-sach', locale as Locale))
      } catch (error) {
        console.error('Error submitting form:', error)
        toast.error('Có lỗi xảy ra khi lưu hồ sơ')
      } finally {
        setIsSubmitting(false)
      }
    },
    [router, locale]
  )
  ```

- **Toast Notifications**:

  ```tsx
  import { toast } from 'react-toastify'

  // Success notification
  toast.success('Lưu hồ sơ quân nhân thành công')

  // Error notification
  toast.error('Có lỗi xảy ra khi lưu hồ sơ')
  ```

---

## 3. Cải tiến và Tối ưu hóa

### Cải tiến Giao diện

1. **Bố cục Ảnh chân dung**:

   - Đặt ảnh ở góc trái với tỷ lệ 2:3 (120x180px)
   - Sử dụng input file thông thường thay vì AppReactDropzone
   - Thêm hiệu ứng hover và cursor pointer

2. **Phân nhóm Thông tin Lý lịch**:

   - Chia thành 6 phần rõ ràng với tiêu đề phân nhóm
   - Thêm font-weight và margin cho tiêu đề các phần
   - Sử dụng Grid system với responsive breakpoints

3. **Cải thiện Form Fields**:
   - Thêm InputLabelProps cho tất cả các trường
   - Sử dụng placeholder phù hợp
   - Thêm disabled state cho các trường phụ thuộc

### Tối ưu hóa Performance

1. **Sử dụng useCallback**:

   - Tối ưu các hàm xử lý sự kiện để tránh re-render không cần thiết

2. **Lazy Loading**:

   - Sử dụng dynamic import cho các component lớn (nếu cần)

3. **Memoization**:
   - Sử dụng useMemo cho các tính toán phức tạp

---

## 4. Lưu ý quan trọng

1. **Responsive Design**:

   - Grid với breakpoint `xs, sm, md` để form hiển thị tốt trên mọi kích thước màn hình
   - Điều chỉnh layout cho phù hợp với từng kích thước màn hình

2. **Validation**:

   - Sử dụng Yup schema validation
   - Hiển thị lỗi ngay dưới mỗi trường
   - Kiểm tra định dạng email, số điện thoại

3. **UX Considerations**:

   - Disabled state cho các trường phụ thuộc (ví dụ: Quận/Huyện phụ thuộc vào Tỉnh/TP)
   - Thêm placeholder phù hợp
   - Sử dụng icons cho các buttons

4. **Error Handling**:

   - Try/catch blocks cho tất cả các hàm async
   - Toast notifications cho success/error
   - Console logging cho debugging

5. **i18n Support**:
   - Sử dụng getLocalizedUrl cho navigation
   - Chuẩn bị cho việc thêm translations trong tương lai

---

Với quy trình và chi tiết component như trên, chúng ta đảm bảo form "Thêm mới Hồ sơ Quân nhân" không chỉ đầy đủ chức năng mà còn có giao diện đẹp, dễ sử dụng, đồng nhất với toàn bộ Vuexy Next.js Admin Template, dễ bảo trì và mở rộng sau này.
