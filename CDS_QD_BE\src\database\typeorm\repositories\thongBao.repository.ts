/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { ThongBaoEntity } from '~/database/typeorm/entities/thongBao.entity';

@Injectable()
export class ThongBaoRepository extends Repository<ThongBaoEntity> {
    constructor(private dataSource: DataSource) {
        super(ThongBaoEntity, dataSource.createEntityManager());
    }
}
