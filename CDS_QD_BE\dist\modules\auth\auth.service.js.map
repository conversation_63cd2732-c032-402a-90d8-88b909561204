{"version": 3, "sources": ["../../../src/modules/auth/auth.service.ts"], "sourcesContent": ["import { Injectable, InternalServerErrorException, UnauthorizedException } from '@nestjs/common';\nimport { TokenService, UtilService } from '@shared/services';\nimport { USER_STATUS } from '~/common/enums/enum';\nimport { AccountRepository } from '~/database/typeorm/repositories/account.repository';\nimport { UserRepository } from '~/database/typeorm/repositories/user.repository';\nimport { MailService } from '~/modules/mail/mail.service';\nimport { CacheService } from '~/shared/services/cache.service';\n\n@Injectable()\nexport class AuthService {\n    private readonly RESETPASSWORDTIMEOUT = 1800000; // miliseconds (30 mins)\n    private readonly SECRETKEY = 'sYzB9UTkuLQ0d1DNPZabC4Q29iJ32xGX';\n    private readonly INITVECTOR = '3dMYNoQo2CSYDpSD';\n    private readonly SECRETSTRING = '6H2su82wAS85KowZ';\n\n    constructor(\n        private readonly tokenService: TokenService,\n        private readonly mailService: MailService,\n        private readonly utilService: UtilService,\n        private readonly userRepository: UserRepository,\n        private readonly accountRepository: AccountRepository,\n        private readonly cacheService: CacheService,\n    ) {}\n\n    public async login(data: { username: string; password: string }) {\n        try {\n            const account = await this.accountRepository.findOne({\n                select: ['id', 'username', 'password', 'secretToken', 'isActive'],\n                where: {\n                    username: data.username,\n                },\n            });\n\n            if (!account) {\n                throw new UnauthorizedException('Wrong username or password');\n            }\n\n            if (!account.isActive) {\n                throw new UnauthorizedException('User disabled');\n            }\n\n            if (!this.tokenService.isPasswordCorrect(data.password, account.password)) {\n                throw new UnauthorizedException('Wrong username or password');\n            }\n\n            const secretToken = this.utilService.generateString();\n            const tokenData = this.tokenService.createAuthToken({\n                id: account.id,\n                password: '', // Giá trị placeholder, không sử dụng trong tokenService mới\n                secretToken,\n            });\n            const refreshTokenData = this.tokenService.createRefreshToken({\n                id: account.id,\n                password: '', // Giá trị placeholder, không sử dụng trong tokenService mới\n                secretToken,\n            });\n            this.accountRepository.update(account.id, { secretToken });\n\n            const user = await this.userRepository.findOne({\n                where: { accountId: account.id, status: USER_STATUS.ACTIVE },\n                relations: ['role'],\n            });\n            if (!user) throw new UnauthorizedException('User not found');\n\n            this.cacheService.delete(`account:${account.id}`);\n            return {\n                result: true,\n                message: 'Login successfully',\n                data: {\n                    id: account.id,\n                    session: tokenData.authToken,\n                    expired: tokenData.authTokenExpiresIn,\n                    refreshToken: refreshTokenData.refreshToken,\n                    role: user.role,\n                },\n            };\n        } catch (err) {\n            throw new UnauthorizedException('Login error');\n        }\n    }\n\n    public async logout(data: { session: string }) {\n        const user = await this.tokenService.verifyAuthToken({ authToken: data.session });\n        if (user.id) {\n            const accountId = (await this.userRepository.findOneBy({ id: +user.id })).accountId;\n            if (accountId) {\n                this.accountRepository.update(accountId, { secretToken: null });\n                this.cacheService.delete(`account:${accountId}`);\n            }\n        }\n\n        return {\n            result: true,\n            message: 'Success',\n            data: null,\n        };\n    }\n\n    public async forgotPassword(data: { email: string }) {\n        try {\n            if (!this.utilService.validateEmail(data.email)) {\n                return {\n                    result: false,\n                    message: 'Email is invalid',\n                    data: null,\n                };\n            }\n\n            const user = await this.userRepository.findOne({\n                select: ['email', 'hoTen', 'status', 'account'],\n                where: { email: data.email },\n                relations: ['account'],\n            });\n            if (!user) {\n                return {\n                    result: false,\n                    message: 'User not found',\n                    data: null,\n                };\n            }\n\n            if (user.status === USER_STATUS.DISABLED) {\n                return {\n                    result: false,\n                    message: 'User disabled',\n                    data: {\n                        is_active: false,\n                    },\n                };\n            }\n\n            const encrypted = this.utilService.aesEncrypt({ email: user.email, password: user.account.password }, this.RESETPASSWORDTIMEOUT);\n            const link = `${process.env.FE_URL}/reset-password?token=${encrypted}`;\n            // gửi mail link reset password cho user\n            this.mailService.sendForgotPassword({\n                emailTo: user.email,\n                subject: 'Reset your password',\n                name: user.hoTen,\n                link: link,\n            });\n\n            return {\n                result: true,\n                message: 'Reset-password link has been sent to your email',\n                data: null,\n            };\n        } catch (err) {\n            throw new InternalServerErrorException({\n                result: false,\n                message: 'Forgot password error',\n                data: null,\n                statusCode: 500,\n            });\n        }\n    }\n\n    public async resetPassword(data: { token: string; password: string }) {\n        try {\n            const validateToken = this.validateToken(data.token);\n            if (!validateToken.result) {\n                return {\n                    result: false,\n                    message: 'Token invalid',\n                    data: null,\n                };\n            }\n\n            const email = validateToken.email;\n            const password = validateToken.password;\n            const user = await this.userRepository.findOne({\n                select: ['id', 'account'],\n                where: { email: email },\n                relations: ['account'],\n            });\n            if (!user) {\n                return {\n                    result: false,\n                    message: 'User not found',\n                    data: null,\n                };\n            }\n\n            if (user.account.password !== password) {\n                return {\n                    result: false,\n                    message: 'Token expired',\n                    data: null,\n                };\n            }\n\n            const { salt, hash } = this.tokenService.hashPassword(data.password);\n            const res = await this.accountRepository.update(user.account.id, {\n                password: hash,\n                salt,\n            });\n\n            return {\n                result: res.affected > 0,\n                message: res.affected > 0 ? 'Password reset successfully' : 'Cannot reset password',\n                data: null,\n            };\n        } catch (err) {\n            throw new InternalServerErrorException({\n                result: false,\n                message: 'Reset password error',\n                data: null,\n                statusCode: 500,\n            });\n        }\n    }\n\n    public async renewAuthToken(data: { refreshToken }) {\n        const refreshTokenData = this.tokenService.verifyRefreshToken({ refreshToken: data.refreshToken });\n        if (!refreshTokenData) {\n            return {\n                session: null,\n                refreshToken: null,\n            };\n        }\n\n        // Lấy thông tin account để tạo token mới\n        const account = await this.accountRepository.findOne({\n            select: ['id', 'secretToken'],\n            where: { id: refreshTokenData.id },\n        });\n\n        if (!account) {\n            return {\n                session: null,\n                refreshToken: null,\n            };\n        }\n\n        const authTokenData = this.tokenService.createAuthToken({\n            id: refreshTokenData.id,\n            password: '', // Giá trị placeholder, không sử dụng trong tokenService mới\n            secretToken: refreshTokenData.secretToken,\n        }).authToken;\n\n        const newRefreshTokenData = this.tokenService.createRefreshToken({\n            id: refreshTokenData.id,\n            password: '', // Giá trị placeholder, không sử dụng trong tokenService mới\n            secretToken: refreshTokenData.secretToken,\n        });\n\n        return {\n            session: authTokenData,\n            refreshToken: newRefreshTokenData.refreshToken,\n        };\n    }\n\n    private validateToken(token: string) {\n        const decrypted = this.utilService.aesDecrypt(token);\n        if (!decrypted) return { result: false, email: null, password: null };\n        return {\n            result: true,\n            email: decrypted.email,\n            password: decrypted.password,\n        };\n    }\n}\n"], "names": ["AuthService", "login", "data", "account", "accountRepository", "findOne", "select", "where", "username", "UnauthorizedException", "isActive", "tokenService", "isPasswordCorrect", "password", "secretToken", "utilService", "generateString", "tokenData", "createAuthToken", "id", "refreshTokenData", "createRefreshToken", "update", "user", "userRepository", "accountId", "status", "USER_STATUS", "ACTIVE", "relations", "cacheService", "delete", "result", "message", "session", "authToken", "expired", "authTokenExpiresIn", "refreshToken", "role", "err", "logout", "verifyAuthToken", "findOneBy", "forgotPassword", "validateEmail", "email", "DISABLED", "is_active", "encrypted", "aesEncrypt", "RESETPASSWORDTIMEOUT", "link", "process", "env", "FE_URL", "mailService", "sendForgotPassword", "emailTo", "subject", "name", "hoTen", "InternalServerErrorException", "statusCode", "resetPassword", "validateToken", "token", "salt", "hash", "hashPassword", "res", "affected", "renewAuthToken", "verifyRefreshToken", "authTokenData", "newRefreshTokenData", "decrypted", "aesDecrypt", "constructor", "SECRETKEY", "INITVECTOR", "SECRETSTRING"], "mappings": "oGASaA,qDAAAA,qCATmE,0CACtC,6CACd,4DACM,wFACH,kFACH,oDACC,olBAGtB,IAAA,AAAMA,YAAN,MAAMA,YAeT,MAAaC,MAAMC,IAA4C,CAAE,CAC7D,GAAI,CACA,MAAMC,QAAU,MAAM,IAAI,CAACC,iBAAiB,CAACC,OAAO,CAAC,CACjDC,OAAQ,CAAC,KAAM,WAAY,WAAY,cAAe,WAAW,CACjEC,MAAO,CACHC,SAAUN,KAAKM,QAAQ,AAC3B,CACJ,GAEA,GAAI,CAACL,QAAS,CACV,MAAM,IAAIM,6BAAqB,CAAC,6BACpC,CAEA,GAAI,CAACN,QAAQO,QAAQ,CAAE,CACnB,MAAM,IAAID,6BAAqB,CAAC,gBACpC,CAEA,GAAI,CAAC,IAAI,CAACE,YAAY,CAACC,iBAAiB,CAACV,KAAKW,QAAQ,CAAEV,QAAQU,QAAQ,EAAG,CACvE,MAAM,IAAIJ,6BAAqB,CAAC,6BACpC,CAEA,MAAMK,YAAc,IAAI,CAACC,WAAW,CAACC,cAAc,GACnD,MAAMC,UAAY,IAAI,CAACN,YAAY,CAACO,eAAe,CAAC,CAChDC,GAAIhB,QAAQgB,EAAE,CACdN,SAAU,GACVC,WACJ,GACA,MAAMM,iBAAmB,IAAI,CAACT,YAAY,CAACU,kBAAkB,CAAC,CAC1DF,GAAIhB,QAAQgB,EAAE,CACdN,SAAU,GACVC,WACJ,GACA,IAAI,CAACV,iBAAiB,CAACkB,MAAM,CAACnB,QAAQgB,EAAE,CAAE,CAAEL,WAAY,GAExD,MAAMS,KAAO,MAAM,IAAI,CAACC,cAAc,CAACnB,OAAO,CAAC,CAC3CE,MAAO,CAAEkB,UAAWtB,QAAQgB,EAAE,CAAEO,OAAQC,iBAAW,CAACC,MAAM,AAAC,EAC3DC,UAAW,CAAC,OAAO,AACvB,GACA,GAAI,CAACN,KAAM,MAAM,IAAId,6BAAqB,CAAC,kBAE3C,IAAI,CAACqB,YAAY,CAACC,MAAM,CAAC,CAAC,QAAQ,EAAE5B,QAAQgB,EAAE,CAAC,CAAC,EAChD,MAAO,CACHa,OAAQ,KACRC,QAAS,qBACT/B,KAAM,CACFiB,GAAIhB,QAAQgB,EAAE,CACde,QAASjB,UAAUkB,SAAS,CAC5BC,QAASnB,UAAUoB,kBAAkB,CACrCC,aAAclB,iBAAiBkB,YAAY,CAC3CC,KAAMhB,KAAKgB,IAAI,AACnB,CACJ,CACJ,CAAE,MAAOC,IAAK,CACV,MAAM,IAAI/B,6BAAqB,CAAC,cACpC,CACJ,CAEA,MAAagC,OAAOvC,IAAyB,CAAE,CAC3C,MAAMqB,KAAO,MAAM,IAAI,CAACZ,YAAY,CAAC+B,eAAe,CAAC,CAAEP,UAAWjC,KAAKgC,OAAO,AAAC,GAC/E,GAAIX,KAAKJ,EAAE,CAAE,CACT,MAAMM,UAAY,AAAC,CAAA,MAAM,IAAI,CAACD,cAAc,CAACmB,SAAS,CAAC,CAAExB,GAAI,CAACI,KAAKJ,EAAE,AAAC,EAAC,EAAGM,SAAS,CACnF,GAAIA,UAAW,CACX,IAAI,CAACrB,iBAAiB,CAACkB,MAAM,CAACG,UAAW,CAAEX,YAAa,IAAK,GAC7D,IAAI,CAACgB,YAAY,CAACC,MAAM,CAAC,CAAC,QAAQ,EAAEN,UAAU,CAAC,CACnD,CACJ,CAEA,MAAO,CACHO,OAAQ,KACRC,QAAS,UACT/B,KAAM,IACV,CACJ,CAEA,MAAa0C,eAAe1C,IAAuB,CAAE,CACjD,GAAI,CACA,GAAI,CAAC,IAAI,CAACa,WAAW,CAAC8B,aAAa,CAAC3C,KAAK4C,KAAK,EAAG,CAC7C,MAAO,CACHd,OAAQ,MACRC,QAAS,mBACT/B,KAAM,IACV,CACJ,CAEA,MAAMqB,KAAO,MAAM,IAAI,CAACC,cAAc,CAACnB,OAAO,CAAC,CAC3CC,OAAQ,CAAC,QAAS,QAAS,SAAU,UAAU,CAC/CC,MAAO,CAAEuC,MAAO5C,KAAK4C,KAAK,AAAC,EAC3BjB,UAAW,CAAC,UAAU,AAC1B,GACA,GAAI,CAACN,KAAM,CACP,MAAO,CACHS,OAAQ,MACRC,QAAS,iBACT/B,KAAM,IACV,CACJ,CAEA,GAAIqB,KAAKG,MAAM,GAAKC,iBAAW,CAACoB,QAAQ,CAAE,CACtC,MAAO,CACHf,OAAQ,MACRC,QAAS,gBACT/B,KAAM,CACF8C,UAAW,KACf,CACJ,CACJ,CAEA,MAAMC,UAAY,IAAI,CAAClC,WAAW,CAACmC,UAAU,CAAC,CAAEJ,MAAOvB,KAAKuB,KAAK,CAAEjC,SAAUU,KAAKpB,OAAO,CAACU,QAAQ,AAAC,EAAG,IAAI,CAACsC,oBAAoB,EAC/H,MAAMC,KAAO,CAAC,EAAEC,QAAQC,GAAG,CAACC,MAAM,CAAC,sBAAsB,EAAEN,UAAU,CAAC,CAEtE,IAAI,CAACO,WAAW,CAACC,kBAAkB,CAAC,CAChCC,QAASnC,KAAKuB,KAAK,CACnBa,QAAS,sBACTC,KAAMrC,KAAKsC,KAAK,CAChBT,KAAMA,IACV,GAEA,MAAO,CACHpB,OAAQ,KACRC,QAAS,kDACT/B,KAAM,IACV,CACJ,CAAE,MAAOsC,IAAK,CACV,MAAM,IAAIsB,oCAA4B,CAAC,CACnC9B,OAAQ,MACRC,QAAS,wBACT/B,KAAM,KACN6D,WAAY,GAChB,EACJ,CACJ,CAEA,MAAaC,cAAc9D,IAAyC,CAAE,CAClE,GAAI,CACA,MAAM+D,cAAgB,IAAI,CAACA,aAAa,CAAC/D,KAAKgE,KAAK,EACnD,GAAI,CAACD,cAAcjC,MAAM,CAAE,CACvB,MAAO,CACHA,OAAQ,MACRC,QAAS,gBACT/B,KAAM,IACV,CACJ,CAEA,MAAM4C,MAAQmB,cAAcnB,KAAK,CACjC,MAAMjC,SAAWoD,cAAcpD,QAAQ,CACvC,MAAMU,KAAO,MAAM,IAAI,CAACC,cAAc,CAACnB,OAAO,CAAC,CAC3CC,OAAQ,CAAC,KAAM,UAAU,CACzBC,MAAO,CAAEuC,MAAOA,KAAM,EACtBjB,UAAW,CAAC,UAAU,AAC1B,GACA,GAAI,CAACN,KAAM,CACP,MAAO,CACHS,OAAQ,MACRC,QAAS,iBACT/B,KAAM,IACV,CACJ,CAEA,GAAIqB,KAAKpB,OAAO,CAACU,QAAQ,GAAKA,SAAU,CACpC,MAAO,CACHmB,OAAQ,MACRC,QAAS,gBACT/B,KAAM,IACV,CACJ,CAEA,KAAM,CAAEiE,IAAI,CAAEC,IAAI,CAAE,CAAG,IAAI,CAACzD,YAAY,CAAC0D,YAAY,CAACnE,KAAKW,QAAQ,EACnE,MAAMyD,IAAM,MAAM,IAAI,CAAClE,iBAAiB,CAACkB,MAAM,CAACC,KAAKpB,OAAO,CAACgB,EAAE,CAAE,CAC7DN,SAAUuD,KACVD,IACJ,GAEA,MAAO,CACHnC,OAAQsC,IAAIC,QAAQ,CAAG,EACvBtC,QAASqC,IAAIC,QAAQ,CAAG,EAAI,8BAAgC,wBAC5DrE,KAAM,IACV,CACJ,CAAE,MAAOsC,IAAK,CACV,MAAM,IAAIsB,oCAA4B,CAAC,CACnC9B,OAAQ,MACRC,QAAS,uBACT/B,KAAM,KACN6D,WAAY,GAChB,EACJ,CACJ,CAEA,MAAaS,eAAetE,IAAsB,CAAE,CAChD,MAAMkB,iBAAmB,IAAI,CAACT,YAAY,CAAC8D,kBAAkB,CAAC,CAAEnC,aAAcpC,KAAKoC,YAAY,AAAC,GAChG,GAAI,CAAClB,iBAAkB,CACnB,MAAO,CACHc,QAAS,KACTI,aAAc,IAClB,CACJ,CAGA,MAAMnC,QAAU,MAAM,IAAI,CAACC,iBAAiB,CAACC,OAAO,CAAC,CACjDC,OAAQ,CAAC,KAAM,cAAc,CAC7BC,MAAO,CAAEY,GAAIC,iBAAiBD,EAAE,AAAC,CACrC,GAEA,GAAI,CAAChB,QAAS,CACV,MAAO,CACH+B,QAAS,KACTI,aAAc,IAClB,CACJ,CAEA,MAAMoC,cAAgB,IAAI,CAAC/D,YAAY,CAACO,eAAe,CAAC,CACpDC,GAAIC,iBAAiBD,EAAE,CACvBN,SAAU,GACVC,YAAaM,iBAAiBN,WAAW,AAC7C,GAAGqB,SAAS,CAEZ,MAAMwC,oBAAsB,IAAI,CAAChE,YAAY,CAACU,kBAAkB,CAAC,CAC7DF,GAAIC,iBAAiBD,EAAE,CACvBN,SAAU,GACVC,YAAaM,iBAAiBN,WAAW,AAC7C,GAEA,MAAO,CACHoB,QAASwC,cACTpC,aAAcqC,oBAAoBrC,YAAY,AAClD,CACJ,CAEA,AAAQ2B,cAAcC,KAAa,CAAE,CACjC,MAAMU,UAAY,IAAI,CAAC7D,WAAW,CAAC8D,UAAU,CAACX,OAC9C,GAAI,CAACU,UAAW,MAAO,CAAE5C,OAAQ,MAAOc,MAAO,KAAMjC,SAAU,IAAK,EACpE,MAAO,CACHmB,OAAQ,KACRc,MAAO8B,UAAU9B,KAAK,CACtBjC,SAAU+D,UAAU/D,QAAQ,AAChC,CACJ,CApPAiE,YACI,AAAiBnE,YAA0B,CAC3C,AAAiB6C,WAAwB,CACzC,AAAiBzC,WAAwB,CACzC,AAAiBS,cAA8B,CAC/C,AAAiBpB,iBAAoC,CACrD,AAAiB0B,YAA0B,CAC7C,MANmBnB,aAAAA,kBACA6C,YAAAA,iBACAzC,YAAAA,iBACAS,eAAAA,oBACApB,kBAAAA,uBACA0B,aAAAA,kBAXJqB,qBAAuB,UACvB4B,UAAY,wCACZC,WAAa,wBACbC,aAAe,kBAS7B,CA8OP"}