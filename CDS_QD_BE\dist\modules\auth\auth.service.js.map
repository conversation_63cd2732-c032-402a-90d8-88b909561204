{"version": 3, "sources": ["../../../src/modules/auth/auth.service.ts"], "sourcesContent": ["import { Injectable, InternalServerErrorException, UnauthorizedException } from '@nestjs/common';\nimport { TokenService, UtilService } from '@shared/services';\nimport { USER_STATUS } from '~/common/enums/enum';\nimport { AccountRepository } from '~/database/typeorm/repositories/account.repository';\nimport { UserRepository } from '~/database/typeorm/repositories/user.repository';\nimport { MailService } from '~/modules/mail/mail.service';\nimport { CacheService } from '~/shared/services/cache.service';\n\n@Injectable()\nexport class AuthService {\n    private readonly RESETPASSWORDTIMEOUT = 1800000; // miliseconds (30 mins)\n    private readonly SECRETKEY = 'sYzB9UTkuLQ0d1DNPZabC4Q29iJ32xGX';\n    private readonly INITVECTOR = '3dMYNoQo2CSYDpSD';\n    private readonly SECRETSTRING = '6H2su82wAS85KowZ';\n\n    constructor(\n        private readonly tokenService: TokenService,\n        private readonly mailService: MailService,\n        private readonly utilService: UtilService,\n        private readonly userRepository: UserRepository,\n        private readonly accountRepository: AccountRepository,\n        private readonly cacheService: CacheService,\n    ) {}\n\n    public async login(data: { username: string; password: string }) {\n        try {\n            const account = await this.accountRepository.findOne({\n                select: ['id', 'username', 'password', 'secretToken', 'isActive'],\n                where: {\n                    username: data.username,\n                },\n            });\n\n            if (!account) {\n                throw new UnauthorizedException('Wrong username or password');\n            }\n\n            if (!account.isActive) {\n                throw new UnauthorizedException('User disabled');\n            }\n\n            if (!this.tokenService.isPasswordCorrect(data.password, account.password)) {\n                throw new UnauthorizedException('Wrong username or password');\n            }\n\n            const secretToken = this.utilService.generateString();\n            const tokenData = this.tokenService.createAuthToken({\n                id: account.id,\n                password: '', // Giá trị placeholder, không sử dụng trong tokenService mới\n                secretToken,\n            });\n            const refreshTokenData = this.tokenService.createRefreshToken({\n                id: account.id,\n                password: '', // Giá trị placeholder, không sử dụng trong tokenService mới\n                secretToken,\n            });\n            this.accountRepository.update(account.id, { secretToken });\n\n            const user = await this.userRepository.findOneUserWithRolesByAccountId(account.id);\n            if (!user || user.status !== USER_STATUS.ACTIVE) throw new UnauthorizedException('User not found');\n\n            // Get all roles for the user (both single and multiple)\n            const allRoles = await this.userRepository.getAllUserRoles(user.id);\n\n            // Prepare role data for backward compatibility\n            const primaryRole = user.role || (allRoles.length > 0 ? allRoles[0] : null);\n\n            this.cacheService.delete(`account:${account.id}`);\n            return {\n                result: true,\n                message: 'Login successfully',\n                data: {\n                    id: account.id,\n                    session: tokenData.authToken,\n                    expired: tokenData.authTokenExpiresIn,\n                    refreshToken: refreshTokenData.refreshToken,\n                    role: primaryRole, // Backward compatibility - single role\n                    roles: allRoles, // New field - multiple roles\n                },\n            };\n        } catch (err) {\n            throw new UnauthorizedException('Login error');\n        }\n    }\n\n    public async logout(data: { session: string }) {\n        const user = await this.tokenService.verifyAuthToken({ authToken: data.session });\n        if (user.id) {\n            const accountId = (await this.userRepository.findOneBy({ id: +user.id })).accountId;\n            if (accountId) {\n                this.accountRepository.update(accountId, { secretToken: null });\n                this.cacheService.delete(`account:${accountId}`);\n            }\n        }\n\n        return {\n            result: true,\n            message: 'Success',\n            data: null,\n        };\n    }\n\n    public async forgotPassword(data: { email: string }) {\n        try {\n            if (!this.utilService.validateEmail(data.email)) {\n                return {\n                    result: false,\n                    message: 'Email is invalid',\n                    data: null,\n                };\n            }\n\n            const user = await this.userRepository.findOne({\n                select: ['email', 'hoTen', 'status', 'account'],\n                where: { email: data.email },\n                relations: ['account'],\n            });\n            if (!user) {\n                return {\n                    result: false,\n                    message: 'User not found',\n                    data: null,\n                };\n            }\n\n            if (user.status === USER_STATUS.DISABLED) {\n                return {\n                    result: false,\n                    message: 'User disabled',\n                    data: {\n                        is_active: false,\n                    },\n                };\n            }\n\n            const encrypted = this.utilService.aesEncrypt({ email: user.email, password: user.account.password }, this.RESETPASSWORDTIMEOUT);\n            const link = `${process.env.FE_URL}/reset-password?token=${encrypted}`;\n            // gửi mail link reset password cho user\n            this.mailService.sendForgotPassword({\n                emailTo: user.email,\n                subject: 'Reset your password',\n                name: user.hoTen,\n                link: link,\n            });\n\n            return {\n                result: true,\n                message: 'Reset-password link has been sent to your email',\n                data: null,\n            };\n        } catch (err) {\n            throw new InternalServerErrorException({\n                result: false,\n                message: 'Forgot password error',\n                data: null,\n                statusCode: 500,\n            });\n        }\n    }\n\n    public async resetPassword(data: { token: string; password: string }) {\n        try {\n            const validateToken = this.validateToken(data.token);\n            if (!validateToken.result) {\n                return {\n                    result: false,\n                    message: 'Token invalid',\n                    data: null,\n                };\n            }\n\n            const email = validateToken.email;\n            const password = validateToken.password;\n            const user = await this.userRepository.findOne({\n                select: ['id', 'account'],\n                where: { email: email },\n                relations: ['account'],\n            });\n            if (!user) {\n                return {\n                    result: false,\n                    message: 'User not found',\n                    data: null,\n                };\n            }\n\n            if (user.account.password !== password) {\n                return {\n                    result: false,\n                    message: 'Token expired',\n                    data: null,\n                };\n            }\n\n            const { salt, hash } = this.tokenService.hashPassword(data.password);\n            const res = await this.accountRepository.update(user.account.id, {\n                password: hash,\n                salt,\n            });\n\n            return {\n                result: res.affected > 0,\n                message: res.affected > 0 ? 'Password reset successfully' : 'Cannot reset password',\n                data: null,\n            };\n        } catch (err) {\n            throw new InternalServerErrorException({\n                result: false,\n                message: 'Reset password error',\n                data: null,\n                statusCode: 500,\n            });\n        }\n    }\n\n    public async renewAuthToken(data: { refreshToken }) {\n        const refreshTokenData = this.tokenService.verifyRefreshToken({ refreshToken: data.refreshToken });\n        if (!refreshTokenData) {\n            return {\n                session: null,\n                refreshToken: null,\n            };\n        }\n\n        // Lấy thông tin account để tạo token mới\n        const account = await this.accountRepository.findOne({\n            select: ['id', 'secretToken'],\n            where: { id: refreshTokenData.id },\n        });\n\n        if (!account) {\n            return {\n                session: null,\n                refreshToken: null,\n            };\n        }\n\n        const authTokenData = this.tokenService.createAuthToken({\n            id: refreshTokenData.id,\n            password: '', // Giá trị placeholder, không sử dụng trong tokenService mới\n            secretToken: refreshTokenData.secretToken,\n        }).authToken;\n\n        const newRefreshTokenData = this.tokenService.createRefreshToken({\n            id: refreshTokenData.id,\n            password: '', // Giá trị placeholder, không sử dụng trong tokenService mới\n            secretToken: refreshTokenData.secretToken,\n        });\n\n        return {\n            session: authTokenData,\n            refreshToken: newRefreshTokenData.refreshToken,\n        };\n    }\n\n    private validateToken(token: string) {\n        const decrypted = this.utilService.aesDecrypt(token);\n        if (!decrypted) return { result: false, email: null, password: null };\n        return {\n            result: true,\n            email: decrypted.email,\n            password: decrypted.password,\n        };\n    }\n}\n"], "names": ["AuthService", "login", "data", "account", "accountRepository", "findOne", "select", "where", "username", "UnauthorizedException", "isActive", "tokenService", "isPasswordCorrect", "password", "secretToken", "utilService", "generateString", "tokenData", "createAuthToken", "id", "refreshTokenData", "createRefreshToken", "update", "user", "userRepository", "findOneUserWithRolesByAccountId", "status", "USER_STATUS", "ACTIVE", "allRoles", "getAllUserRoles", "primaryRole", "role", "length", "cacheService", "delete", "result", "message", "session", "authToken", "expired", "authTokenExpiresIn", "refreshToken", "roles", "err", "logout", "verifyAuthToken", "accountId", "findOneBy", "forgotPassword", "validateEmail", "email", "relations", "DISABLED", "is_active", "encrypted", "aesEncrypt", "RESETPASSWORDTIMEOUT", "link", "process", "env", "FE_URL", "mailService", "sendForgotPassword", "emailTo", "subject", "name", "hoTen", "InternalServerErrorException", "statusCode", "resetPassword", "validateToken", "token", "salt", "hash", "hashPassword", "res", "affected", "renewAuthToken", "verifyRefreshToken", "authTokenData", "newRefreshTokenData", "decrypted", "aesDecrypt", "constructor", "SECRETKEY", "INITVECTOR", "SECRETSTRING"], "mappings": "oGASaA,qDAAAA,qCATmE,0CACtC,6CACd,4DACM,wFACH,kFACH,oDACC,olBAGtB,IAAA,AAAMA,YAAN,MAAMA,YAeT,MAAaC,MAAMC,IAA4C,CAAE,CAC7D,GAAI,CACA,MAAMC,QAAU,MAAM,IAAI,CAACC,iBAAiB,CAACC,OAAO,CAAC,CACjDC,OAAQ,CAAC,KAAM,WAAY,WAAY,cAAe,WAAW,CACjEC,MAAO,CACHC,SAAUN,KAAKM,QAAQ,AAC3B,CACJ,GAEA,GAAI,CAACL,QAAS,CACV,MAAM,IAAIM,6BAAqB,CAAC,6BACpC,CAEA,GAAI,CAACN,QAAQO,QAAQ,CAAE,CACnB,MAAM,IAAID,6BAAqB,CAAC,gBACpC,CAEA,GAAI,CAAC,IAAI,CAACE,YAAY,CAACC,iBAAiB,CAACV,KAAKW,QAAQ,CAAEV,QAAQU,QAAQ,EAAG,CACvE,MAAM,IAAIJ,6BAAqB,CAAC,6BACpC,CAEA,MAAMK,YAAc,IAAI,CAACC,WAAW,CAACC,cAAc,GACnD,MAAMC,UAAY,IAAI,CAACN,YAAY,CAACO,eAAe,CAAC,CAChDC,GAAIhB,QAAQgB,EAAE,CACdN,SAAU,GACVC,WACJ,GACA,MAAMM,iBAAmB,IAAI,CAACT,YAAY,CAACU,kBAAkB,CAAC,CAC1DF,GAAIhB,QAAQgB,EAAE,CACdN,SAAU,GACVC,WACJ,GACA,IAAI,CAACV,iBAAiB,CAACkB,MAAM,CAACnB,QAAQgB,EAAE,CAAE,CAAEL,WAAY,GAExD,MAAMS,KAAO,MAAM,IAAI,CAACC,cAAc,CAACC,+BAA+B,CAACtB,QAAQgB,EAAE,EACjF,GAAI,CAACI,MAAQA,KAAKG,MAAM,GAAKC,iBAAW,CAACC,MAAM,CAAE,MAAM,IAAInB,6BAAqB,CAAC,kBAGjF,MAAMoB,SAAW,MAAM,IAAI,CAACL,cAAc,CAACM,eAAe,CAACP,KAAKJ,EAAE,EAGlE,MAAMY,YAAcR,KAAKS,IAAI,EAAKH,CAAAA,SAASI,MAAM,CAAG,EAAIJ,QAAQ,CAAC,EAAE,CAAG,IAAG,EAEzE,IAAI,CAACK,YAAY,CAACC,MAAM,CAAC,CAAC,QAAQ,EAAEhC,QAAQgB,EAAE,CAAC,CAAC,EAChD,MAAO,CACHiB,OAAQ,KACRC,QAAS,qBACTnC,KAAM,CACFiB,GAAIhB,QAAQgB,EAAE,CACdmB,QAASrB,UAAUsB,SAAS,CAC5BC,QAASvB,UAAUwB,kBAAkB,CACrCC,aAActB,iBAAiBsB,YAAY,CAC3CV,KAAMD,YACNY,MAAOd,QACX,CACJ,CACJ,CAAE,MAAOe,IAAK,CACV,MAAM,IAAInC,6BAAqB,CAAC,cACpC,CACJ,CAEA,MAAaoC,OAAO3C,IAAyB,CAAE,CAC3C,MAAMqB,KAAO,MAAM,IAAI,CAACZ,YAAY,CAACmC,eAAe,CAAC,CAAEP,UAAWrC,KAAKoC,OAAO,AAAC,GAC/E,GAAIf,KAAKJ,EAAE,CAAE,CACT,MAAM4B,UAAY,AAAC,CAAA,MAAM,IAAI,CAACvB,cAAc,CAACwB,SAAS,CAAC,CAAE7B,GAAI,CAACI,KAAKJ,EAAE,AAAC,EAAC,EAAG4B,SAAS,CACnF,GAAIA,UAAW,CACX,IAAI,CAAC3C,iBAAiB,CAACkB,MAAM,CAACyB,UAAW,CAAEjC,YAAa,IAAK,GAC7D,IAAI,CAACoB,YAAY,CAACC,MAAM,CAAC,CAAC,QAAQ,EAAEY,UAAU,CAAC,CACnD,CACJ,CAEA,MAAO,CACHX,OAAQ,KACRC,QAAS,UACTnC,KAAM,IACV,CACJ,CAEA,MAAa+C,eAAe/C,IAAuB,CAAE,CACjD,GAAI,CACA,GAAI,CAAC,IAAI,CAACa,WAAW,CAACmC,aAAa,CAAChD,KAAKiD,KAAK,EAAG,CAC7C,MAAO,CACHf,OAAQ,MACRC,QAAS,mBACTnC,KAAM,IACV,CACJ,CAEA,MAAMqB,KAAO,MAAM,IAAI,CAACC,cAAc,CAACnB,OAAO,CAAC,CAC3CC,OAAQ,CAAC,QAAS,QAAS,SAAU,UAAU,CAC/CC,MAAO,CAAE4C,MAAOjD,KAAKiD,KAAK,AAAC,EAC3BC,UAAW,CAAC,UAAU,AAC1B,GACA,GAAI,CAAC7B,KAAM,CACP,MAAO,CACHa,OAAQ,MACRC,QAAS,iBACTnC,KAAM,IACV,CACJ,CAEA,GAAIqB,KAAKG,MAAM,GAAKC,iBAAW,CAAC0B,QAAQ,CAAE,CACtC,MAAO,CACHjB,OAAQ,MACRC,QAAS,gBACTnC,KAAM,CACFoD,UAAW,KACf,CACJ,CACJ,CAEA,MAAMC,UAAY,IAAI,CAACxC,WAAW,CAACyC,UAAU,CAAC,CAAEL,MAAO5B,KAAK4B,KAAK,CAAEtC,SAAUU,KAAKpB,OAAO,CAACU,QAAQ,AAAC,EAAG,IAAI,CAAC4C,oBAAoB,EAC/H,MAAMC,KAAO,CAAC,EAAEC,QAAQC,GAAG,CAACC,MAAM,CAAC,sBAAsB,EAAEN,UAAU,CAAC,CAEtE,IAAI,CAACO,WAAW,CAACC,kBAAkB,CAAC,CAChCC,QAASzC,KAAK4B,KAAK,CACnBc,QAAS,sBACTC,KAAM3C,KAAK4C,KAAK,CAChBT,KAAMA,IACV,GAEA,MAAO,CACHtB,OAAQ,KACRC,QAAS,kDACTnC,KAAM,IACV,CACJ,CAAE,MAAO0C,IAAK,CACV,MAAM,IAAIwB,oCAA4B,CAAC,CACnChC,OAAQ,MACRC,QAAS,wBACTnC,KAAM,KACNmE,WAAY,GAChB,EACJ,CACJ,CAEA,MAAaC,cAAcpE,IAAyC,CAAE,CAClE,GAAI,CACA,MAAMqE,cAAgB,IAAI,CAACA,aAAa,CAACrE,KAAKsE,KAAK,EACnD,GAAI,CAACD,cAAcnC,MAAM,CAAE,CACvB,MAAO,CACHA,OAAQ,MACRC,QAAS,gBACTnC,KAAM,IACV,CACJ,CAEA,MAAMiD,MAAQoB,cAAcpB,KAAK,CACjC,MAAMtC,SAAW0D,cAAc1D,QAAQ,CACvC,MAAMU,KAAO,MAAM,IAAI,CAACC,cAAc,CAACnB,OAAO,CAAC,CAC3CC,OAAQ,CAAC,KAAM,UAAU,CACzBC,MAAO,CAAE4C,MAAOA,KAAM,EACtBC,UAAW,CAAC,UAAU,AAC1B,GACA,GAAI,CAAC7B,KAAM,CACP,MAAO,CACHa,OAAQ,MACRC,QAAS,iBACTnC,KAAM,IACV,CACJ,CAEA,GAAIqB,KAAKpB,OAAO,CAACU,QAAQ,GAAKA,SAAU,CACpC,MAAO,CACHuB,OAAQ,MACRC,QAAS,gBACTnC,KAAM,IACV,CACJ,CAEA,KAAM,CAAEuE,IAAI,CAAEC,IAAI,CAAE,CAAG,IAAI,CAAC/D,YAAY,CAACgE,YAAY,CAACzE,KAAKW,QAAQ,EACnE,MAAM+D,IAAM,MAAM,IAAI,CAACxE,iBAAiB,CAACkB,MAAM,CAACC,KAAKpB,OAAO,CAACgB,EAAE,CAAE,CAC7DN,SAAU6D,KACVD,IACJ,GAEA,MAAO,CACHrC,OAAQwC,IAAIC,QAAQ,CAAG,EACvBxC,QAASuC,IAAIC,QAAQ,CAAG,EAAI,8BAAgC,wBAC5D3E,KAAM,IACV,CACJ,CAAE,MAAO0C,IAAK,CACV,MAAM,IAAIwB,oCAA4B,CAAC,CACnChC,OAAQ,MACRC,QAAS,uBACTnC,KAAM,KACNmE,WAAY,GAChB,EACJ,CACJ,CAEA,MAAaS,eAAe5E,IAAsB,CAAE,CAChD,MAAMkB,iBAAmB,IAAI,CAACT,YAAY,CAACoE,kBAAkB,CAAC,CAAErC,aAAcxC,KAAKwC,YAAY,AAAC,GAChG,GAAI,CAACtB,iBAAkB,CACnB,MAAO,CACHkB,QAAS,KACTI,aAAc,IAClB,CACJ,CAGA,MAAMvC,QAAU,MAAM,IAAI,CAACC,iBAAiB,CAACC,OAAO,CAAC,CACjDC,OAAQ,CAAC,KAAM,cAAc,CAC7BC,MAAO,CAAEY,GAAIC,iBAAiBD,EAAE,AAAC,CACrC,GAEA,GAAI,CAAChB,QAAS,CACV,MAAO,CACHmC,QAAS,KACTI,aAAc,IAClB,CACJ,CAEA,MAAMsC,cAAgB,IAAI,CAACrE,YAAY,CAACO,eAAe,CAAC,CACpDC,GAAIC,iBAAiBD,EAAE,CACvBN,SAAU,GACVC,YAAaM,iBAAiBN,WAAW,AAC7C,GAAGyB,SAAS,CAEZ,MAAM0C,oBAAsB,IAAI,CAACtE,YAAY,CAACU,kBAAkB,CAAC,CAC7DF,GAAIC,iBAAiBD,EAAE,CACvBN,SAAU,GACVC,YAAaM,iBAAiBN,WAAW,AAC7C,GAEA,MAAO,CACHwB,QAAS0C,cACTtC,aAAcuC,oBAAoBvC,YAAY,AAClD,CACJ,CAEA,AAAQ6B,cAAcC,KAAa,CAAE,CACjC,MAAMU,UAAY,IAAI,CAACnE,WAAW,CAACoE,UAAU,CAACX,OAC9C,GAAI,CAACU,UAAW,MAAO,CAAE9C,OAAQ,MAAOe,MAAO,KAAMtC,SAAU,IAAK,EACpE,MAAO,CACHuB,OAAQ,KACRe,MAAO+B,UAAU/B,KAAK,CACtBtC,SAAUqE,UAAUrE,QAAQ,AAChC,CACJ,CAxPAuE,YACI,AAAiBzE,YAA0B,CAC3C,AAAiBmD,WAAwB,CACzC,AAAiB/C,WAAwB,CACzC,AAAiBS,cAA8B,CAC/C,AAAiBpB,iBAAoC,CACrD,AAAiB8B,YAA0B,CAC7C,MANmBvB,aAAAA,kBACAmD,YAAAA,iBACA/C,YAAAA,iBACAS,eAAAA,oBACApB,kBAAAA,uBACA8B,aAAAA,kBAXJuB,qBAAuB,UACvB4B,UAAY,wCACZC,WAAa,wBACbC,aAAe,kBAS7B,CAkPP"}