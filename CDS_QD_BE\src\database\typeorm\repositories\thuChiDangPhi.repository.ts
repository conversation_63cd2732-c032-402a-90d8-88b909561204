/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { ThuChiDangPhiEntity } from '~/database/typeorm/entities/thuChiDangPhi.entity';

@Injectable()
export class ThuChiDangPhiRepository extends Repository<ThuChiDangPhiEntity> {
    constructor(private dataSource: DataSource) {
        super(ThuChiDangPhiEntity, dataSource.createEntityManager());
    }
}
