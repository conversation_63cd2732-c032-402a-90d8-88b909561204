# Lazy Loading trong Next.js 14

## 1. Tổng quan
- Next.js hỗ trợ lazy loading thông qua `next/dynamic`
- Tự động code-splitting cho các components
- Hỗ tr<PERSON> cả Server Components và Client Components

## 2. <PERSON><PERSON><PERSON> triể<PERSON> khai cơ bản

### 2.1. <PERSON><PERSON> dụng `next/dynamic`
```tsx
import dynamic from 'next/dynamic';

const LazyComponent = dynamic(() => import('./HeavyComponent'));

function MyPage() {
  return (
    <div>
      <h1>Trang chủ</h1>
      <LazyComponent />
    </div>
  );
}
```

### 2.2. <PERSON><PERSON>i loading state
```tsx
const LazyComponent = dynamic(
  () => import('./HeavyComponent'),
  {
    loading: () => <p>Đang tải...</p>
  }
);
```

## 3. Tối ưu hóa

### 3.1. Tắt SSR khi cần
```tsx
const LazyChart = dynamic(
  () => import('@/components/Chart'),
  { 
    ssr: false, // Tắt SSR cho components phụ thuộc vào browser APIs
    loading: () => <SkeletonChart />
  }
);
```

### 3.2. Import có tên (named imports)
```tsx
const LazyButton = dynamic(
  () => import('@/components/ui').then(mod => mod.Button),
  { loading: () => <ButtonSkeleton /> }
);
```

## 4. Best Practices

### 4.1. Components nên lazy load
- Các components lớn, ít sử dụng
- Thư viện bên thứ ba nặng
- Components phụ thuộc vào browser APIs
- Các sections "below the fold"

### 4.2. Components không nên lazy load
- Các components nhỏ, thường xuyên sử dụng
- Components cần thiết cho trang
- Components trong critical rendering path

## 5. Kết hợp với Suspense
```tsx
import { Suspense } from 'react';

function Page() {
  return (
    <div>
      <Header />
      <Suspense fallback={<Spinner />}>
        <LazyComponent />
      </Suspense>
    </div>
  );
}
```

## 6. Lưu ý
- Không sử dụng dynamic import với template strings
- Đường dẫn import phải là string literal
- Đặt dynamic() ở cấp độ module, không phải trong render

## Tài liệu tham khảo
- [Next.js Documentation: Lazy Loading](https://nextjs.org/docs/14/pages/building-your-application/optimizing/lazy-loading)
- [React Documentation: Code-Splitting](https://react.dev/reference/react/lazy)
