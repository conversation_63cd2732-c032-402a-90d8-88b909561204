/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { GiaTriDanhMucEntity } from '~/database/typeorm/entities/giaTriDanhMuc.entity';

@Injectable()
export class GiaTriDanhMucRepository extends Repository<GiaTriDanhMucEntity> {
    constructor(private dataSource: DataSource) {
        super(GiaTriDanhMucEntity, dataSource.createEntityManager());
    }
}
