/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { KeHoachGiaoDucChinhTriEntity } from '~/database/typeorm/entities/keHoachGiaoDucChinhTri.entity';

@Injectable()
export class KeHoachGiaoDucChinhTriRepository extends Repository<KeHoachGiaoDucChinhTriEntity> {
    constructor(private dataSource: DataSource) {
        super(KeHoachGiaoDucChinhTriEntity, dataSource.createEntityManager());
    }
}
