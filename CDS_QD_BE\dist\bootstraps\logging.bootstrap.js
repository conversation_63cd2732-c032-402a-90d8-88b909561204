"use strict";Object.defineProperty(exports,"__esModule",{value:true});Object.defineProperty(exports,"bootstrapLogging",{enumerable:true,get:function(){return bootstrapLogging}});const _nestjspino=require("nestjs-pino");function bootstrapLogging(app){app.useLogger(app.get(_nestjspino.Logger));app.useGlobalInterceptors(new _nestjspino.LoggerErrorInterceptor)}
//# sourceMappingURL=logging.bootstrap.js.map