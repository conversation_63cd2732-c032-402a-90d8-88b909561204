# Quy trình xây dựng giao diện trang danh sách hồ sơ Đảng viên

---

**Module: Công Tác <PERSON>ảng**

**I. Trang Danh sách Hồ sơ Đảng viên (`/${locale}/cong-tac-dang/danh-sach-dang-vien`)**

- **T<PERSON><PERSON> (Tiêu đề hiển thị trên trình duyệt và trong giao diện):** `Danh sách Đảng viên`

- **Mụ<PERSON> đích chính của Trang:**

  - Cung cấp một cái nhìn tổng quan, có tổ chức về tất cả các đảng viên trong phạm vi quản lý của người dùng.
  - Cho phép người dùng nhanh chóng tìm kiếm và lọc ra các đảng viên theo những tiêu chí cụ thể.
  - <PERSON><PERSON> điểm khởi đầu để người dùng có thể truy cập vào thông tin chi tiết hoặc thực hiện các thao tác quản lý cơ bản đối với từng hồ sơ đảng viên.

- **Bố cục tổng quan của Trang:**

  1.  **Thanh điều hướng phân cấp (Breadcrumbs):** Hiển thị đường dẫn hiện tại trong hệ thống.
  2.  **Tiêu đề trang:** Tên của trang, ví dụ "Danh sách Đảng viên".
  3.  **(Có thể có) Khu vực thống kê nhanh/tóm tắt:** Ví dụ: tổng số đảng viên, số đảng viên chính thức, số đảng viên dự bị. (Khu vực này tùy chọn, có thể có hoặc không tùy theo thiết kế tổng thể).
  4.  **Khu vực Lọc và Tìm kiếm:** Một khu vực riêng biệt, thường nằm phía trên bảng dữ liệu, cho phép người dùng nhập các tiêu chí để thu hẹp danh sách hiển thị.
  5.  **Khu vực Hiển thị Danh sách (Bảng dữ liệu):** Khu vực chính của trang, hiển thị thông tin các đảng viên dưới dạng bảng, có hỗ trợ phân trang.
  6.  **Khu vực Phân trang:** Các điều khiển để di chuyển giữa các trang của danh sách.

- **Chi tiết các Thành phần và Chức năng trong Trang Danh sách Hồ sơ Đảng viên:**

  1.  **Thanh điều hướng phân cấp (Breadcrumbs):**

      - **Nội dung ví dụ:** `Trang chủ / Công Tác Đảng / Danh sách Đảng viên`
      - **Chức năng:** Giúp người dùng biết mình đang ở đâu và dễ dàng quay lại các mục cha.

  2.  **Tiêu đề Trang:**

      - **Nội dung:** `Danh sách Đảng viên`
      - **Chức năng:** Khẳng định nội dung chính của trang.

  3.  **Khu vực Lọc và Tìm kiếm:**

      - **Bố cục:** Có thể là một hàng các trường nhập liệu/lựa chọn, hoặc một panel có thể mở rộng/thu gọn để tiết kiệm không gian.
      - **Chức năng tìm kiếm:**
        - **Ô tìm kiếm chung:** Cho phép người dùng nhập từ khóa (ví dụ: Mã Đảng viên, Số thẻ Đảng viên, Họ tên Đảng viên) để tìm kiếm nhanh. Placeholder có thể là "Nhập Mã ĐV, Số thẻ, Họ tên...".
      - **Chức năng lọc chi tiết:**
        - **Lọc theo "Tổ chức Đảng sinh hoạt":**
          - **Kiểu hiển thị:** Trường lựa chọn (dropdown, select). Nếu cấu trúc tổ chức Đảng phức tạp và phân cấp, có thể sử dụng dạng cây lựa chọn (tree select).
          - **Dữ liệu nguồn:** Danh sách các Tổ chức Đảng từ bảng `ToChucDang`.
        - **Lọc theo "Trạng thái Đảng tịch":**
          - **Kiểu hiển thị:** Trường lựa chọn (dropdown, select).
          - **Dữ liệu nguồn:** Các giá trị từ danh mục `DmTrangThaiDangTich` (ví dụ: Chính thức, Dự bị, Đình chỉ SH, Đã khai trừ, Chuyển đi, Chuyển đến chưa SH).
        - **Lọc theo "Chức vụ Đảng hiện tại":**
          - **Kiểu hiển thị:** Trường lựa chọn (dropdown, select).
          - **Dữ liệu nguồn:** Các giá trị từ danh mục `DmChucVuDang`.
        - **(Tùy chọn nâng cao) Lọc theo "Ngày vào Đảng chính thức":** Có thể là hai ô chọn ngày (Từ ngày - Đến ngày) để lọc đảng viên trong một khoảng thời gian kết nạp nhất định.
      - **Nút thực thi lọc/tìm kiếm:**
        - **Nút "Tìm kiếm" / "Áp dụng":** Sau khi người dùng nhập/chọn các tiêu chí, nhấn nút này để hệ thống cập nhật lại bảng danh sách.
      - **Nút xóa bộ lọc:**
        - **Nút "Đặt lại" / "Xóa bộ lọc":** Xóa tất cả các tiêu chí đã nhập/chọn trong khu vực lọc và hiển thị lại toàn bộ danh sách đảng viên.

  4.  **Khu vực Hiển thị Danh sách Đảng viên (Dạng Bảng):**

      - **Mục đích:** Trình bày thông tin cốt lõi của từng đảng viên một cách có cấu trúc.
      - **Các cột thông tin chính cần hiển thị cho mỗi đảng viên trong bảng:**
        - **Cột "Mã Đảng viên":** Hiển thị giá trị từ trường `MaDangVien` của bảng `DangVien`.
        - **Cột "Số thẻ Đảng viên":** Hiển thị giá trị từ trường `SoTheDangVien` của bảng `DangVien`.
        - **Cột "Họ và tên":** Hiển thị giá trị từ trường `HoVaTen` (lấy từ bảng `QuanNhan` nếu có liên kết qua `SoHieuQuanNhan`, hoặc từ trường `HoVaTen` của bảng `DangVien` nếu đảng viên không phải là quân nhân hoặc không có liên kết trực tiếp trong ngữ cảnh này).
        - **Cột "Ngày vào Đảng chính thức":** Hiển thị giá trị từ trường `NgayVaoDangChinhThuc` của bảng `DangVien`. Định dạng ngày tháng năm (dd/mm/yyyy).
        - **Cột "Tổ chức Đảng Sinh hoạt":** Hiển thị tên của Tổ chức Đảng, lấy từ bảng `ToChucDang` dựa vào `ToChucDangSinhHoatID` của bảng `DangVien`.
        - **Cột "Trạng thái Đảng tịch":** Hiển thị tên của Trạng thái Đảng tịch, lấy từ danh mục `DmTrangThaiDangTich` dựa vào `TrangThaiDangTichID` của bảng `DangVien`.
        - **Cột "Chức vụ Đảng hiện tại":** Hiển thị tên của Chức vụ Đảng, lấy từ danh mục `DmChucVuDang` dựa vào `ChucVuDangHienTaiID` của bảng `DangVien` (nếu có).
        - **Cột "Hành động":** Chứa các nút/biểu tượng để thực hiện thao tác trên dòng dữ liệu đó.
      - **Chức năng của bảng:**
        - **Sắp xếp dữ liệu:** Cho phép người dùng nhấn vào tiêu đề của mỗi cột để sắp xếp danh sách tăng dần hoặc giảm dần theo giá trị của cột đó.
        - **Hiển thị tổng số kết quả:** Thông báo cho người dùng biết có bao nhiêu hồ sơ đảng viên phù hợp với tiêu chí tìm kiếm/lọc hiện tại.
      - **Các hành động trên mỗi dòng của bảng (trong cột "Hành động"):**
        - **Hành động "Xem chi tiết":**
          - **Kiểu hiển thị:** Một nút bấm hoặc biểu tượng (ví dụ: hình con mắt, hoặc chữ "Xem").
          - **Chức năng:** Khi người dùng nhấn vào, hệ thống sẽ điều hướng sang trang xem chi tiết hồ sơ của đảng viên tương ứng (`/${locale}/cong-tac-dang/chi-tiet-dang-vien/{idDangVien}`).
        - **Hành động "Sửa":**
          - **Kiểu hiển thị:** Một nút bấm hoặc biểu tượng (ví dụ: hình bút chì, hoặc chữ "Sửa").
          - **Chức năng:** Khi người dùng nhấn vào, hệ thống sẽ điều hướng sang trang chỉnh sửa hồ sơ của đảng viên tương ứng (`/${locale}/cong-tac-dang/chinh-sua-dang-vien/{idDangVien}`). (Chức năng này cần kiểm tra quyền của người dùng).
        - **(Tùy chọn) Các hành động nhanh khác (có thể nằm trong một menu thả xuống cho mỗi dòng):**
          - **"Chuyển sinh hoạt Đảng":** Kích hoạt một quy trình/form cho phép thực hiện nghiệp vụ chuyển sinh hoạt Đảng cho đảng viên này.
          - **"Thay đổi Trạng thái Đảng tịch":** Kích hoạt một quy trình/form cho phép thay đổi trạng thái đảng tịch (ví dụ: công nhận chính thức, đình chỉ, khai trừ - cần các bước phê duyệt).
          - **"Xem lịch sử thay đổi" (Audit Log của riêng đảng viên này):** Điều hướng đến một giao diện xem lịch sử các thay đổi liên quan đến hồ sơ đảng viên này.

  5.  **Khu vực Phân trang:**

      - **Mục đích:** Giúp người dùng duyệt qua danh sách đảng viên nếu có nhiều kết quả không thể hiển thị hết trên một trang.
      - **Các thành phần:**
        - Hiển thị thông tin về trang hiện tại và tổng số trang (ví dụ: "Trang 1 / 10").
        - Các nút điều hướng: "Trang đầu", "Trang trước", "Trang sau", "Trang cuối".
        - Có thể có ô nhập liệu để nhảy đến một trang cụ thể.
        - Tùy chọn thay đổi số lượng mục hiển thị trên mỗi trang (ví dụ: 10, 20, 50 mục/trang).

  6.  **Lưu ý quan trọng về nút "Thêm mới Đảng viên":**
      - Trên trang `DanhSachDangVienPage` này, **thường sẽ không có nút "Thêm mới Đảng viên" trực tiếp.** Lý do là việc tạo một hồ sơ đảng viên mới là kết quả của một quy trình nghiệp vụ phức tạp hơn, đó là "Phát triển Đảng viên". Một người chỉ trở thành đảng viên và có hồ sơ trong danh sách này sau khi đã hoàn thành các bước trong quy trình phát triển và được chính thức kết nạp/công nhận. Do đó, chức năng "Thêm mới" sẽ nằm trong module/trang "Phát triển Đảng viên".

---

**I. Bảng dữ liệu chính được sử dụng để hiển thị danh sách:**

1.  **Bảng `DangVien` (PartyMembers)**
    - Mục đích: Lưu trữ thông tin cơ bản và cốt lõi của đảng viên. Đây là bảng trung tâm cho trang danh sách này.
    - **Các trường được sử dụng trực tiếp hoặc gián tiếp từ bảng `DangVien`:**
      - `MaDangVien` (VARCHAR(20), PK): **Hiển thị trực tiếp** trong cột "Mã Đảng viên". Dùng để định danh duy nhất.
      - `SoHieuQuanNhan` (VARCHAR(20), FK NULL, UNIQUE REFERENCES QuanNhan(SoHieuQuanNhan)): Dùng để **liên kết** lấy thông tin Họ tên, Ngày sinh, Giới tính từ bảng `QuanNhan` (nếu đảng viên là quân nhân).
      - `HoVaTen` (NVARCHAR(100), NOT NULL): **Hiển thị trực tiếp** trong cột "Họ và tên" (sẽ ưu tiên lấy từ `QuanNhan.HoVaTenKhaiSinh` nếu có `SoHieuQuanNhan`, nếu không thì lấy từ đây). _Lưu ý: Tài liệu gốc không có trường HoVaTen trực tiếp trong bảng DangVien, nó được lấy từ bảng QuanNhan. Nếu thiết kế cuối cùng có trường này trong DangVien (ví dụ cho đảng viên không phải QN), thì sẽ dùng nó._
      - `TenThuongDung` (NVARCHAR(100)): Có thể được dùng cho tìm kiếm, hoặc hiển thị nếu cần. (Tương tự `HoVaTen`, ưu tiên từ `QuanNhan`).
      - `NgaySinh` (DATE): **Hiển thị gián tiếp** (lấy từ `QuanNhan.NgaySinh` nếu có `SoHieuQuanNhan`, hoặc từ trường `NgaySinh` của `DangVien` nếu được thêm vào cho đảng viên không phải QN).
      - `GioiTinh` (INT): **Hiển thị gián tiếp** (lấy từ `QuanNhan.GioiTinh` hoặc từ trường `GioiTinh` của `DangVien` nếu có).
      - `NgayVaoDangChinhThuc` (DATE): **Hiển thị trực tiếp** trong cột "Ngày vào Đảng chính thức". Dùng cho lọc.
      - `NgayVaoDangDuBi` (DATE, NOT NULL): Có thể dùng cho lọc hoặc tính toán thâm niên Đảng.
      - `SoTheDangVien` (VARCHAR(20), UNIQUE): **Hiển thị trực tiếp** trong cột "Số thẻ Đảng viên". Dùng cho tìm kiếm.
      - `ToChucDangSinhHoatID` (BIGINT, FK REFERENCES ToChucDang(ID), NOT NULL): Dùng để **liên kết** lấy tên Tổ chức Đảng từ bảng `ToChucDang` để **hiển thị** trong cột "Tổ chức Đảng Sinh hoạt" và dùng cho **lọc**.
      - `TrangThaiDangTichID` (INT, FK REFERENCES DmTrangThaiDangTich(ID), NOT NULL): Dùng để **liên kết** lấy tên Trạng thái Đảng tịch từ danh mục `DmTrangThaiDangTich` để **hiển thị** trong cột "Trạng thái Đảng tịch" và dùng cho **lọc**.
      - `ChucVuDangHienTaiID` (INT, FK REFERENCES DmChucVuDang(ID) NULL): Dùng để **liên kết** lấy tên Chức vụ Đảng từ danh mục `DmChucVuDang` để **hiển thị** trong cột "Chức vụ Đảng hiện tại" (nếu có) và dùng cho **lọc**.
      - `NgayTao` (TIMESTAMP): Có thể dùng để sắp xếp "Mới nhất".
      - `NguoiTaoID` (VARCHAR(50)): Thông tin tham khảo.
      - `NgayCapNhat` (TIMESTAMP): Thông tin tham khảo.
      - `NguoiCapNhatID` (VARCHAR(50)): Thông tin tham khảo.
      - `IsEncrypted` (BOOLEAN): Để biết bản ghi có chứa thông tin mã hóa hay không (ít khi hiển thị trực tiếp).

**II. Các bảng dữ liệu liên quan (Foreign Key) được sử dụng để lấy thông tin hiển thị hoặc lọc:**

1.  **Bảng `QuanNhan` (Servicemen)** (Nếu `DangVien.SoHieuQuanNhan` có giá trị)

    - Mục đích: Lấy thông tin cá nhân cơ bản nếu đảng viên là quân nhân.
    - **Các trường được sử dụng:**
      - `SoHieuQuanNhan` (PK): Để join với `DangVien.SoHieuQuanNhan`.
      - `HoVaTenKhaiSinh` (NVARCHAR(100), NOT NULL): Để **hiển thị** trong cột "Họ và tên".
      - `TenThuongDung` (NVARCHAR(100)): Để **hiển thị** (nếu `HoVaTenKhaiSinh` không phù hợp) hoặc tìm kiếm.
      - `NgaySinh` (DATE, NOT NULL): Để **hiển thị**.
      - `GioiTinh` (INT, NOT NULL): Để **hiển thị**.

2.  **Bảng `ToChucDang` (PartyOrganizations)**
    - Mục đích: Lấy tên Tổ chức Đảng mà đảng viên đang sinh hoạt.
    - **Các trường được sử dụng:**
      - `ID` (PK): Để join với `DangVien.ToChucDangSinhHoatID`.
      - `TenToChucDang` (NVARCHAR(255), NOT NULL): Để **hiển thị** trong cột "Tổ chức Đảng Sinh hoạt" và trong bộ lọc "Tổ chức Đảng".
      - (Có thể dùng `ToChucDangCapTrenID` nếu bộ lọc Tổ chức Đảng dạng cây).

**III. Các bảng Danh mục (Dm\*) được sử dụng để lấy thông tin hiển thị hoặc cho bộ lọc:**

1.  **Danh mục `DmTrangThaiDangTich` (Bảng trạng thái đảng tịch)**

    - Mục đích: Lấy tên hiển thị cho trạng thái đảng tịch.
    - **Các trường được sử dụng:**
      - `ID` (PK): Để join với `DangVien.TrangThaiDangTichID`.
      - `TenTrangThaiDangTich` (hoặc tên tương ứng): Để **hiển thị** trong cột "Trạng thái Đảng tịch" và trong bộ lọc "Trang thái Đảng tịch".

2.  **Danh mục `DmChucVuDang` (Bảng chức vụ Đảng)**
    - Mục đích: Lấy tên hiển thị cho chức vụ Đảng.
    - **Các trường được sử dụng:**
      - `ID` (PK): Để join với `DangVien.ChucVuDangHienTaiID`.
      - `TenChucVuDang` (hoặc tên tương ứng): Để **hiển thị** trong cột "Chức vụ Đảng hiện tại" và trong bộ lọc "Chức vụ Đảng".

**IV. Tóm tắt các trường dữ liệu chính được sử dụng trên Trang Danh sách Hồ sơ Đảng viên:**

- **Từ `DangVien`:**

  - `MaDangVien` (Hiển thị, Tìm kiếm, ID cho hành động)
  - `SoTheDangVien` (Hiển thị, Tìm kiếm)
  - `NgayVaoDangChinhThuc` (Hiển thị, Lọc, Sắp xếp)
  - `ToChucDangSinhHoatID` (FK để Lọc và lấy Tên hiển thị)
  - `TrangThaiDangTichID` (FK để Lọc và lấy Tên hiển thị)
  - `ChucVuDangHienTaiID` (FK để Lọc và lấy Tên hiển thị)
  - `SoHieuQuanNhan` (FK để liên kết lấy thông tin từ bảng `QuanNhan`)

- **Từ `QuanNhan` (thông qua `DangVien.SoHieuQuanNhan`):**

  - `HoVaTenKhaiSinh` (Hiển thị cột Họ tên, Tìm kiếm)
  - (Các trường khác như `NgaySinh`, `GioiTinh` có thể được xem xét hiển thị hoặc dùng cho bộ lọc nâng cao)

- **Từ `ToChucDang` (thông qua `DangVien.ToChucDangSinhHoatID`):**

  - `TenToChucDang` (Hiển thị cột Tổ chức Đảng, Dữ liệu cho bộ lọc)

- **Từ `DmTrangThaiDangTich` (thông qua `DangVien.TrangThaiDangTichID`):**

  - `TenTrangThaiDangTich` (hoặc tên tương ứng) (Hiển thị cột Trạng thái Đảng tịch, Dữ liệu cho bộ lọc)

- **Từ `DmChucVuDang` (thông qua `DangVien.ChucVuDangHienTaiID`):**
  - `TenChucVuDang` (hoặc tên tương ứng) (Hiển thị cột Chức vụ Đảng, Dữ liệu cho bộ lọc)

## Danh sách này đảm bảo rằng tất cả các thông tin cần thiết để hiển thị, tìm kiếm, lọc và thực hiện các hành động cơ bản trên trang Danh sách Hồ sơ Đảng viên đều được bao gồm, bám sát theo cấu trúc dữ liệu đã được định nghĩa trong tài liệu.
