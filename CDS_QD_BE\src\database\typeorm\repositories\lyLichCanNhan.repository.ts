/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { LyLichCanNhanEntity } from '~/database/typeorm/entities/lyLichCanNhan.entity';

@Injectable()
export class LyLichCanNhanRepository extends Repository<LyLichCanNhanEntity> {
    constructor(private dataSource: DataSource) {
        super(LyLichCanNhanEntity, dataSource.createEntityManager());
    }
}
