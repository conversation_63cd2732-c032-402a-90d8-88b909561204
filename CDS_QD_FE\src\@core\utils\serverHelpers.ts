// Removed server-only dependencies and provided client-safe fallbacks

// Type Imports
import type { Settings } from '@core/contexts/settingsContext'
import type { DemoName, SystemMode } from '@core/types'

// Config Imports
import themeConfig from '@configs/themeConfig'
import demoConfigs from '@configs/demoConfigs'

const IS_SERVER = typeof window === 'undefined'

export const getDemoName = (): DemoName | null => {
  // This function previously relied on server headers.
  // Returning null as a default. You may need to implement a new way to determine the demo name.
  if (IS_SERVER) {
    // If you still need to read this on the server in a compatible environment (e.g. App Router Route Handler / Server Component)
    // you might try a dynamic import or pass it differently.
    // For now, to fix the error, we avoid direct usage of next/headers here.
    try {
      const headers = require('next/headers').headers
      const headersList = headers()
      return headersList.get('X-server-header') as DemoName | null
    } catch (e) {
      // Fallback if next/headers is not available
      return null
    }
  }
  return null
}

export const getSettingsFromCookie = (): Settings => {
  const demoName = getDemoName() // Uses the potentially modified getDemoName
  const cookieName = demoName
    ? themeConfig.settingsCookieName.replace('demo-1', demoName)
    : themeConfig.settingsCookieName

  if (!IS_SERVER) {
    try {
      const value = localStorage.getItem(cookieName)
      return value ? JSON.parse(value) : {}
    } catch (e) {
      // console.error("Error reading settings from localStorage", e);
      return {}
    }
  }
  // Fallback for server-side if cookies from next/headers are not read here
  // Or, if you have a server-only path that calls this, it would need to handle cookie parsing.
  return {}
}

export const getMode = () => {
  const settingsCookie = getSettingsFromCookie()
  const demoName = getDemoName()

  const demoConfigMode = demoName && demoConfigs && demoConfigs[demoName] ? demoConfigs[demoName].mode : undefined

  const _mode = settingsCookie.mode || demoConfigMode || themeConfig.mode

  return _mode
}

export const getSystemMode = (): SystemMode => {
  const currentMode = getMode()

  if (!IS_SERVER) {
    try {
      const colorPref = localStorage.getItem('colorPref') as SystemMode | null
      return (currentMode === 'system' ? colorPref || 'light' : currentMode) || 'light'
    } catch (e) {
      // console.error("Error reading colorPref from localStorage", e);
      return (currentMode === 'system' ? 'light' : currentMode) || 'light'
    }
  }

  // Fallback for server-side.
  // If 'system' mode relies on a cookie, it needs to be readable on the server.
  return (currentMode === 'system' ? 'light' : currentMode) || 'light'
}

export const getServerMode = () => {
  const mode = getMode()
  const systemMode = getSystemMode()

  return mode === 'system' ? systemMode : mode
}

export const getSkin = () => {
  const settingsCookie = getSettingsFromCookie()

  return settingsCookie.skin || 'default'
}
